CREATE
OR <PERSON><PERSON><PERSON>CE PROCEDURE recalculate_asset_total_value(p_asset_id IN NUMBER) AS
BEGIN
UPDATE DT_assets
SET total_value = (SELECT NVL(SUM(ACQUISITION_VALUE_DF), 0)
                   FROM DT_elements
                   WHERE asset_id = p_asset_id)
WHERE id = p_asset_id;
END recalculate_asset_total_value;
/
CREATE
OR REPLACE TRIGGER compound_trigger_recalculate_asset_total_value
FOR INSERT OR
UPDATE OR
DELETE
ON dt_elements
    COMPOUND TRIGGER
    TYPE t_asset_id_table IS TABLE OF dt_elements.asset_id%TYPE INDEX BY PLS_INTEGER;
v_asset_ids
t_asset_id_table;
    v_count
PLS_INTEGER := 0;

    BEFORE
STATEMENT IS
BEGIN
        v_asset_ids
:= t_asset_id_table();
        v_count
:= 0;
END BEFORE
STATEMENT;

    AFTER
EACH ROW IS
BEGIN
        -- For INSERT
        IF
INSERTING THEN
            v_count := v_count + 1;
            v_asset_ids
(v_count) := :NEW.asset_id;
END IF;

        -- For DELETE
        IF
DELETING THEN
            v_count := v_count + 1;
            v_asset_ids
(v_count) := :OLD.asset_id;
END IF;

        -- For UPDATE
        IF
UPDATING THEN
            IF :OLD.ACQUISITION_VALUE_DF != :NEW.ACQUISITION_VALUE_DF OR :OLD.asset_id != :NEW.asset_id THEN
                v_count := v_count + 1;
                v_asset_ids
(v_count) := :NEW.asset_id;

                IF
:OLD.asset_id != :NEW.asset_id THEN
                    v_count := v_count + 1;
                    v_asset_ids
(v_count) := :OLD.asset_id;
END IF;
END IF;
END IF;
END AFTER
EACH ROW;

    AFTER
STATEMENT IS
BEGIN
        DECLARE
v_distinct_asset_ids t_asset_id_table;
            v_distinct_count
PLS_INTEGER := 0;
BEGIN
            -- Remove duplicates
FOR i IN 1 .. v_count LOOP
                IF NOT v_distinct_asset_ids.EXISTS(v_asset_ids(i)) THEN
                    v_distinct_count := v_distinct_count + 1;
                    v_distinct_asset_ids
(v_distinct_count) := v_asset_ids(i);
END IF;
END LOOP;

            -- Recalculate asset total value
FOR i IN 1 .. v_distinct_count LOOP
                recalculate_asset_total_value(v_distinct_asset_ids(i));
END LOOP;
END;
END AFTER
STATEMENT;
END compound_trigger_recalculate_asset_total_value;
/



