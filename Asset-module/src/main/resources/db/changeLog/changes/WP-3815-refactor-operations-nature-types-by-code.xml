<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="20250316-update-operation-nature-mapping-relations" author="DT-<PERSON><PERSON><PERSON><PERSON>">
        <comment>Update operation_nature_mapping to use code-based foreign keys instead of ID-based</comment>
        <sql>
        delete from dt_operation_nature_type_mapping;
        delete from dt_operation_nature_mapping;
        </sql>
        <addColumn tableName="dt_operation_nature_mapping">
            <column name="operation_nature_code" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <addUniqueConstraint
                tableName="dt_operation_nature_mapping"
                columnNames="operation_nature_code"
                constraintName="uk_operation_nature_mapping_code"/>


        <!-- Add new code columns -->
        <addColumn tableName="dt_operation_nature_mapping">
            <column name="operation_code" type="VARCHAR(255)"/>
            <column name="nature_code" type="VARCHAR(255)"/>
        </addColumn>

        <!-- Make the new columns not nullable -->
        <addNotNullConstraint tableName="dt_operation_nature_mapping" columnName="operation_code"/>
        <addNotNullConstraint tableName="dt_operation_nature_mapping" columnName="nature_code"/>
        <addUniqueConstraint tableName="dt_natures" columnNames="code" constraintName="uk_nature_code"/>
        <!-- Create new foreign key constraints -->
        <addForeignKeyConstraint baseTableName="dt_operation_nature_mapping"
                                 baseColumnNames="operation_code"
                                 constraintName="fk_operation_nature_mapping_operation_code"
                                 referencedTableName="dt_operations"
                                 referencedColumnNames="code"/>

        <addForeignKeyConstraint baseTableName="dt_operation_nature_mapping"
                                 baseColumnNames="nature_code"
                                 constraintName="fk_operation_nature_mapping_nature_code"
                                 referencedTableName="dt_natures"
                                 referencedColumnNames="code"/>

        <!-- Drop old foreign keys -->
        <dropForeignKeyConstraint baseTableName="dt_operation_nature_mapping"
                                  constraintName="fk_operation_nature_mapping_operation"/>
        <dropForeignKeyConstraint baseTableName="dt_operation_nature_mapping"
                                  constraintName="fk_operation_nature_mapping_nature"/>

        <!-- Drop old columns -->
        <dropColumn tableName="dt_operation_nature_mapping" columnName="operation_id"/>
        <dropColumn tableName="dt_operation_nature_mapping" columnName="nature_id"/>
        <!-- Update the new columns with data from related tables -->
        <sql>
            delete from dt_operation_nature_type_mapping;
            delete from dt_operation_nature_mapping;
            INSERT INTO public.dt_operation_nature_mapping
                (id, nature_status, operation_nature_code, operation_code, nature_code)
            VALUES
                (1, true, 'FACCLI_MORA', 'FACCLI', 'MORA'),
                (2, true, 'FACCLI_PREL', 'FACCLI', 'PREL'),
                (3, true, 'FACCLI_LOYE', 'FACCLI', 'LOYE'),
                (4, true, 'FACCLI_PRES', 'FACCLI', 'PRES'),
                (5, true, 'FACCLI_FREES', 'FACCLI', 'FREES'),
                (6, true, 'FACCLI_RECO', 'FACCLI', 'RECO'),
                (7, true, 'FACCLI_GARA', 'FACCLI', 'GARA'),
                (8, true, 'FACCHD_ASSET', 'FACCHD', 'ASSET'),
                (9, true, 'FACCHD_COMM', 'FACCHD', 'COMM'),
                (10, true, 'FACCHD_REVS', 'FACCHD', 'REVS'),
                (11, true, 'CMDEHB_ACQU', 'CMDEHB', 'ACQU'),
                (12, true, 'CMDEHB_APPO', 'CMDEHB', 'APPO'),
                (13, true, 'CMDEHB_FRNA', 'CMDEHB', 'FRNA'),
                (14, true, 'FACCES_CESS', 'FACCES', 'CESS'),
                (15, true, 'FACCES_CDPK', 'FACCES', 'CDPK'),
                (16, true, 'FACCES_INDM', 'FACCES', 'INDM'),
                (17, true, 'FACCES_PRES', 'FACCES', 'PRES'),
                (18, true, 'FACNPA_ACQU', 'FACNPA', 'ACQU'),
                (19, true, 'FACNPA_APPO', 'FACNPA', 'APPO'),
                (20, true, 'FACNPA_FRNA', 'FACNPA', 'FRNA'),
                (21, true, 'CONMEL_MILO', 'CONMEL', 'MILO'),
                (22, true, 'ENCAIS_NIMP', 'ENCAIS', 'NIMP'),
                (23, true, 'DECAIS_FFOU', 'DECAIS', 'FFOU'),
                (24, true, 'DECAIS_NIMP', 'DECAIS', 'NIMP');
        </sql>
    </changeSet>

    <changeSet id="20250317-update-operation-nature-type-mapping-relations" author="DT-AhmedKhiari">
        <comment>Update dt_operation_nature_type_mapping to use code-based foreign keys instead of ID-based</comment>
        <addColumn tableName="dt_operation_nature_type_mapping">
            <column name="operation_nature_mapping_code" type="VARCHAR(255)"/>
            <column name="operation_nature_type_mapping_code" type="VARCHAR(255)"/>
            <column name="type_code" type="VARCHAR(255)"/>
        </addColumn>

        <addUniqueConstraint
                tableName="dt_operation_nature_type_mapping"
                columnNames="operation_nature_type_mapping_code"
                constraintName="uk_operation_nature_type_mapping_code"/>
        <!-- Add new code columns -->




        <!-- Make the new columns not nullable -->
        <addNotNullConstraint tableName="dt_operation_nature_type_mapping" columnName="operation_nature_mapping_code"/>
        <addNotNullConstraint tableName="dt_operation_nature_type_mapping" columnName="type_code"/>

        <!-- Create new foreign key constraints -->
        <addForeignKeyConstraint baseTableName="dt_operation_nature_type_mapping"
                                 baseColumnNames="operation_nature_mapping_code"
                                 constraintName="fk_operation_nature_type_mapping_onm_code"
                                 referencedTableName="dt_operation_nature_mapping"
                                 referencedColumnNames="operation_nature_code"/>

        <addForeignKeyConstraint baseTableName="dt_operation_nature_type_mapping"
                                 baseColumnNames="type_code"
                                 constraintName="fk_operation_nature_type_mapping_type_code"
                                 referencedTableName="dt_line_types"
                                 referencedColumnNames="code"/>

        <!-- Drop old foreign keys -->
        <dropForeignKeyConstraint baseTableName="dt_operation_nature_type_mapping"
                                  constraintName="fk_operation_nature_type_mapping_onm"/>
        <dropForeignKeyConstraint baseTableName="dt_operation_nature_type_mapping"
                                  constraintName="fk_operation_nature_type_mapping_type"/>

        <!-- Drop old columns -->
        <dropColumn tableName="dt_operation_nature_type_mapping" columnName="operation_nature_mapping_id"/>
        <dropColumn tableName="dt_operation_nature_type_mapping" columnName="type_id"/>
        <!-- Update the new columns with data from related tables -->
        <sql>
            INSERT INTO public.dt_operation_nature_type_mapping
            (id, type_status, operation_nature_mapping_code, operation_nature_type_mapping_code, type_code)
            VALUES
                (138, true, 'FACCLI_FREES', 'FACCLI_FREES_FATT', 'FATT'),
                (139, true, 'FACCLI_FREES', 'FACCLI_FREES_FDEC', 'FDEC'),
                (107, true, 'FACCLI_MORA', 'FACCLI_MORA_MORAT', 'MORAT'),
                (108, true, 'FACCLI_PREL', 'FACCLI_PREL_PRELOY', 'PRELOY'),
                (109, true, 'FACCLI_LOYE', 'FACCLI_LOYE_RLOCINT', 'RLOCINT'),
                (110, true, 'FACCLI_PRES', 'FACCLI_PRES_RMAINT', 'RMAINT'),
                (111, true, 'FACCLI_FREES', 'FACCLI_FREES_RPDTFIN', 'RPDTFIN'),
                (112, true, 'FACCLI_FREES', 'FACCLI_FREES_RINDMRE', 'RINDMRE'),
                (113, true, 'FACCLI_RECO', 'FACCLI_RECO_RRELOC', 'RRELOC'),
                (114, true, 'FACCLI_PRES', 'FACCLI_PRES_RVTASSU', 'RVTASSU'),
                (115, true, 'FACCLI_PRES', 'FACCLI_PRES_RFRDOSS', 'RFRDOSS'),
                (116, true, 'FACCLI_GARA', 'FACCLI_GARA_RGDOS', 'RGDOS'),
                (117, true, 'FACCLI_GARA', 'FACCLI_GARA_RGENC', 'RGENC'),
                (118, true, 'FACCLI_FREES', 'FACCLI_FREES_FREMAN', 'FREMAN'),
                (119, true, 'FACCLI_FREES', 'FACCLI_FREES_FREJET', 'FREJET'),
                (120, true, 'FACCLI_FREES', 'FACCLI_FREES_RADRESS', 'RADRESS'),
                (121, true, 'FACCLI_FREES', 'FACCLI_FREES_RRIB', 'RRIB'),
                (122, true, 'FACCLI_FREES', 'FACCLI_FREES_RMECH', 'RMECH'),
                (123, true, 'FACCLI_FREES', 'FACCLI_FREES_RMPLA', 'RMPLA'),
                (124, true, 'FACCLI_FREES', 'FACCLI_FREES_RTRTC', 'RTRTC'),
                (125, true, 'FACCLI_PRES', 'FACCLI_PRES_ASSURC', 'ASSURC'),
                (126, true, 'FACCLI_PRES', 'FACCLI_PRES_ASSUVIE', 'ASSUVIE'),
                (127, true, 'FACCLI_FREES', 'FACCLI_FREES_FDENOM', 'FDENOM'),
                (128, true, 'FACCLI_FREES', 'FACCLI_FREES_FCOPIE', 'FCOPIE'),
                (129, true, 'FACCLI_FREES', 'FACCLI_FREES_FDUPF', 'FDUPF'),
                (130, true, 'FACCLI_FREES', 'FACCLI_FREES_FDUPC', 'FDUPC'),
                (131, true, 'FACCLI_LOYE', 'FACCLI_LOYE_FECH', 'FECH'),
                (132, true, 'FACCLI_FREES', 'FACCLI_FREES_FSIN', 'FSIN'),
                (133, true, 'FACCLI_FREES', 'FACCLI_FREES_FPAY', 'FPAY'),
                (134, true, 'FACCLI_FREES', 'FACCLI_FREES_FQUAN', 'FQUAN'),
                (135, true, 'FACCLI_FREES', 'FACCLI_FREES_FREAM', 'FREAM'),
                (136, true, 'FACCLI_FREES', 'FACCLI_FREES_FTRAN', 'FTRAN'),
                (137, true, 'FACCLI_FREES', 'FACCLI_FREES_FRECH', 'FRECH'),
                (140, true, 'FACCLI_FREES', 'FACCLI_FREES_FREPR', 'FREPR'),
                (141, true, 'FACCLI_FREES', 'FACCLI_FREES_FRET', 'FRET'),
                (142, true, 'FACCLI_FREES', 'FACCLI_FREES_FRECO', 'FRECO'),
                (143, true, 'FACCLI_FREES', 'FACCLI_FREES_FINDEM', 'FINDEM'),
                (144, true, 'FACCLI_FREES', 'FACCLI_FREES_FMEP', 'FMEP'),
                (145, true, 'FACCLI_FREES', 'FACCLI_FREES_FGEST', 'FGEST'),
                (146, true, 'FACCLI_FREES', 'FACCLI_FREES_FSEPA', 'FSEPA'),
                (147, true, 'FACCLI_FREES', 'FACCLI_FREES_FDIV', 'FDIV'),
                (148, true, 'FACCLI_RECO', 'FACCLI_RECO_RELOC', 'RELOC'),
                (149, true, 'FACCLI_FREES', 'FACCLI_FREES_FREMISE', 'FREMISE'),
                (150, true, 'FACCLI_FREES', 'FACCLI_FREES_FDAUDI', 'FDAUDI'),
                (151, true, 'FACCLI_FREES', 'FACCLI_FREES_DEPK', 'DEPK'),
                (152, true, 'FACCLI_RECO', 'FACCLI_RECO_LMADIS', 'LMADIS'),
                (153, true, 'FACCHD_ASSET', 'FACCHD_ASSET_DLOCINT', 'DLOCINT'),
                (154, true, 'FACCHD_COMM', 'FACCHD_COMM_DCOMAP', 'DCOMAP'),
                (155, true, 'FACCHD_REVS', 'FACCHD_REVS_RMAINT', 'RMAINT'),
                (156, true, 'FACCHD_REVS', 'FACCHD_REVS_RVTASSU', 'RVTASSU'),
                (157, true, 'FACCHD_REVS', 'FACCHD_REVS_REVLOY', 'REVLOY'),
                (158, true, 'CMDEHB_ACQU', 'CMDEHB_ACQU_ABIE', 'ABIE'),
                (159, true, 'CMDEHB_ACQU', 'CMDEHB_ACQU_ACCN', 'ACCN'),
                (160, true, 'CMDEHB_ACQU', 'CMDEHB_ACQU_OPTI', 'OPTI'),
                (161, true, 'CMDEHB_ACQU', 'CMDEHB_ACQU_RMCL', 'RMCL'),
                (162, true, 'CMDEHB_APPO', 'CMDEHB_APPO_APPO', 'APPO'),
                (163, true, 'CMDEHB_FRNA', 'CMDEHB_FRNA_CGRI', 'CGRI'),
                (164, true, 'CMDEHB_FRNA', 'CMDEHB_FRNA_FLIV', 'FLIV'),
                (165, true, 'CMDEHB_FRNA', 'CMDEHB_FRNA_MALU', 'MALU'),
                (166, true, 'CMDEHB_FRNA', 'CMDEHB_FRNA_PACO', 'PACO'),
                (167, true, 'CMDEHB_FRNA', 'CMDEHB_FRNA_RACO', 'RACO'),
                (168, true, 'FACCES_CESS', 'FACCES_CESS_CANT', 'CANT'),
                (169, true, 'FACCES_CESS', 'FACCES_CESS_CCTX', 'CCTX'),
                (170, true, 'FACCES_CESS', 'FACCES_CESS_CFIN', 'CFIN'),
                (171, true, 'FACCES_CESS', 'FACCES_CESS_CSIN', 'CSIN'),
                (172, true, 'FACCES_CESS', 'FACCES_CESS_CTNL', 'CTNL'),
                (173, true, 'FACCES_CDPK', 'FACCES_CDPK_DEPK', 'DEPK'),
                (174, true, 'FACCES_INDM', 'FACCES_INDM_INDR', 'INDR'),
                (175, true, 'FACCES_PRES', 'FACCES_PRES_VR00', 'VR00'),
                (176, true, 'FACNPA_ACQU', 'FACNPA_ACQU_ABIE', 'ABIE'),
                (177, true, 'FACNPA_ACQU', 'FACNPA_ACQU_ACCN', 'ACCN'),
                (178, true, 'FACNPA_ACQU', 'FACNPA_ACQU_OPTI', 'OPTI'),
                (179, true, 'FACNPA_ACQU', 'FACNPA_ACQU_RMCL', 'RMCL'),
                (180, true, 'FACNPA_APPO', 'FACNPA_APPO_APPO', 'APPO'),
                (181, true, 'FACNPA_FRNA', 'FACNPA_FRNA_CGRI', 'CGRI'),
                (182, true, 'FACNPA_FRNA', 'FACNPA_FRNA_FLIV', 'FLIV'),
                (183, true, 'FACNPA_FRNA', 'FACNPA_FRNA_MALU', 'MALU'),
                (184, true, 'FACNPA_FRNA', 'FACNPA_FRNA_PACO', 'PACO'),
                (185, true, 'FACNPA_FRNA', 'FACNPA_FRNA_RACO', 'RACO'),
                (186, true, 'CONMEL_MILO', 'CONMEL_MILO_BLOC', 'BLOC'),
                (187, true, 'CONMEL_MILO', 'CONMEL_MILO_EBLC', 'EBLC'),
                (188, true, 'CONMEL_MILO', 'CONMEL_MILO_IRDU', 'IRDU'),
                (189, true, 'CONMEL_MILO', 'CONMEL_MILO_LRDU', 'LRDU'),
                (190, true, 'CONMEL_MILO', 'CONMEL_MILO_VIB', 'VIB'),
                (191, true, 'CONMEL_MILO', 'CONMEL_MILO_VIBF', 'VIBF'),
                (192, true, 'ENCAIS_NIMP', 'ENCAIS_NIMP_NIMP', 'NIMP'),
                (194, true, 'DECAIS_NIMP', 'DECAIS_NIMP_NIMP', 'NIMP'),
                (193, NULL, 'DECAIS_FFOU', 'DECAIS_FFOU_COMM', 'COMM');
        </sql>
    </changeSet>
</databaseChangeLog>