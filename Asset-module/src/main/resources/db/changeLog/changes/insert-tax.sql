insert into DT_TAXES (ID, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID) values
                                                                                                               (1,null,null,null,null,null,'FR',2)        ,
                                                                                                               (2,null,null,null,null,null,'FR',1)        ,
                                                                                                               (3,null,null,null,null,null,'FR',4)       ,
                                                                                                               (4,null,null,null,null,null,'FR',6)       ,
                                                                                                               (5,null,null,null,null,null,'FR',5)      ,
                                                                                                               (6,null,null,null,null,null,'FR',7)      ,
                                                                                                               (7,null,null,null,null,null,'CH',8)      ,
                                                                                                               (8,null,null,null,null,null,'CH',9)     ,
                                                                                                               (9,null,null,null,null,null,'CH',10)     ,
                                                                                                               (10,null,null,null,null,null,'CH',11)   ,
                                                                                                               (11,null,null,null,null,null,'CH',12)   ,
                                                                                                               (12,null,null,null,null,null,'DE',13)    ,
                                                                                                               (13,null,null,null,null,null,'DE',14)   ,
                                                                                                               (14,null,null,null,null,null,'DE',15)    ,
                                                                                                               (15,null,null,null,null,null,'DE',16)   ,
                                                                                                               (16,null,null,null,null,null,'DE',17)   ,
                                                                                                               (17,null,null,null,null,null,'IT',18)    ,
                                                                                                               (18,null,null,null,null,null,'IT',19)   ,
                                                                                                               (19,null,null,null,null,null,'IT',20)    ,
                                                                                                               (20,null,null,null,null,null,'IT',21)   ,
                                                                                                               (21,null,null,null,null,null,'IT',22)   ,
                                                                                                               (22,null,null,null,null,null,'ES',23)    ,
                                                                                                               (23,null,null,null,null,null,'ES',24)   ,
                                                                                                               (24,null,null,null,null,null,'ES',25)    ,
                                                                                                               (25,null,null,null,null,null,'ES',26)   ,
                                                                                                               (26,null,null,null,null,null,'BE',27)    ,
                                                                                                               (27,null,null,null,null,null,'BE',28)   ,
                                                                                                               (28,null,null,null,null,null,'BE',29)    ,
                                                                                                               (29,null,null,null,null,null,'BE',30)   ,
                                                                                                               (30,null,null,null,null,null,'LU',31)    ,
                                                                                                               (31,null,null,null,null,null,'LU',32)   ,
                                                                                                               (32,null,null,null,null,null,'LU',33)    ,
                                                                                                               (33,null,null,null,null,null,'LU',34)   ,
                                                                                                               (34,null,null,null,null,null,'LU',35)   ,
                                                                                                               (35,null,null,null,null,null,'LU',36)  ,
                                                                                                               (36,null,null,null,null,null,'LU',37)  ,
                                                                                                               (37,null,null,null,null,null,'LU',38)  ,
                                                                                                               (38,null,null,null,null,null,'FR',39)   ,
                                                                                                               (39,null,null,null,null,null,'FR',40)    ,
                                                                                                               (40,null,null,null,null,null,'FR',41)   ,
                                                                                                               (41,null,null,null,null,null,'BE',42)   ,
                                                                                                               (42,null,null,null,null,null,'LU',44)  ,
                                                                                                               (43,null,null,null,null,null,'BE',45);