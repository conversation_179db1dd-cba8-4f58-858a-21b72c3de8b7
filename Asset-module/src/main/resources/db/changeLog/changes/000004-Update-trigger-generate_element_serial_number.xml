<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:pro="http://www.liquibase.org/xml/ns/pro"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd
      http://www.liquibase.org/xml/ns/pro
      http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd">
    <changeSet id="1809020467255-52" author="Ahmed">
        <sql endDelimiter="/">
            create or replace TRIGGER generate_element_serial_number
    BEFORE INSERT ON DT_ELEMENTS
    FOR EACH ROW
DECLARE
            country_code VARCHAR2(2);
seq_num NUMBER;
padding_length NUMBER;
max_seq_num NUMBER;
asset_serial_number VARCHAR2(200);
asset_category_id NUMBER;
asset_cat_id NUMBER;
existing_elements_count NUMBER;
            BEGIN
            SELECT a.reference
            INTO asset_serial_number
            FROM dt_assets a
            WHERE a.id = :new.asset_id;

            -- Check if there are existing elements for the given asset_id
            SELECT COUNT(*)
            INTO existing_elements_count
            FROM dt_elements e
            WHERE e.asset_id = :new.asset_id
              AND e.deleted_at IS NULL;

            IF existing_elements_count = 0 THEN
    -- If no existing elements, use the new element's category_id
    asset_cat_id := :new.category_id;
            ELSE
    -- Get the Category of the first created element
            SELECT category_id
            INTO asset_cat_id
            FROM (
                     SELECT e.category_id
                     FROM dt_elements e
                     WHERE e.asset_id = :new.asset_id
                       AND e.deleted_at IS NULL
                     ORDER BY e.created_at ASC
                 )
            WHERE ROWNUM = 1;
            END IF;


-- Get the Category id of the element
            SELECT c.CODE
            INTO  asset_category_id
            FROM dt_categories c
            WHERE c.id = asset_cat_id;

-- Generate the sequence number
            SELECT DT_ELEMENTS_SERIAL_NUMBER_seq.nextval
            INTO seq_num
            FROM dual;

-- Determine the maximum sequence number
            SELECT MAX(seq_num)
            INTO max_seq_num
            FROM DT_ELEMENTS;

-- Calculate the padding length dynamically
            padding_length := 5; -- Default padding length
IF LENGTH(TO_CHAR(max_seq_num)) > padding_length THEN
        padding_length := LENGTH(TO_CHAR(max_seq_num)) + 1; -- Increase padding length if necessary
            END IF;

    -- Generate asset code with dynamic padding
    :new.serial_number := asset_serial_number || asset_category_id || 'EL' || LPAD(seq_num, padding_length, '0');
            END;
            /
        </sql>
    </changeSet>
</databaseChangeLog>