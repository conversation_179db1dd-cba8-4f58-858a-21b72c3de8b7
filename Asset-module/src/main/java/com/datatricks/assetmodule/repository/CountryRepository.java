package com.datatricks.assetmodule.repository;

import com.datatricks.assetmodule.model.Country;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface CountryRepository extends JpaRepository<Country, Long>, JpaSpecificationExecutor<Country> {

    Country findByLabel(String country);

    Optional<Country> findByCode(String code);

    void deleteByCode(String code);
}
