package com.datatricks.assetmodule.repository;

import com.datatricks.assetmodule.model.Rental;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface RentalRepository extends JpaRepository<Rental, Long>, JpaSpecificationExecutor<Rental> {
    Optional<Rental> findByIdAndDeletedAtIsNull(Long rentalId);
    List<Rental> findAllByDeletedAtIsNull();
    boolean existsByIdAndDeletedAtIsNull(Long rentalId);
}
