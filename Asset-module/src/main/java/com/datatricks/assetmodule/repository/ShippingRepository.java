package com.datatricks.assetmodule.repository;

import com.datatricks.assetmodule.model.Shipping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface ShippingRepository extends JpaRepository<Shipping, Long>, JpaSpecificationExecutor<Shipping> {
    Optional<Shipping> findByIdAndDeletedAtIsNull(Long shippingId);

    boolean existsByIdAndDeletedAtIsNull(Long id);
}
