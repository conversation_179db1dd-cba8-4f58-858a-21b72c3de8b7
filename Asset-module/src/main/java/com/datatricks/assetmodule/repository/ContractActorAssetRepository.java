package com.datatricks.assetmodule.repository;

import com.datatricks.assetmodule.model.ContractActorAsset;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ContractActorAssetRepository extends JpaRepository<ContractActorAsset, Long>, JpaSpecificationExecutor<ContractActorAsset> {
    Optional<ContractActorAsset> findByContractIdAndActorIdAndAssetIdAndDeletedAtIsNull(Long contractId, Long actorId, Long assetId);
    List<ContractActorAsset> findByContractIdAndActorIdAndDeletedAtIsNull(Long contractId, Long actorId);

    Optional<ContractActorAsset> findByIdAndAssetIdAndDeletedAtIsNull(ContractActorAsset contractActorAssetId, Long assetId);

    @Query(value = "select\n" +
            "    cas.* from dt_contract_actor_asset cas\n" +
            "    where cas.deleted_at is null\n" +
            "    and cas.id not in (\n" +
            "        select distinct(ta.contract_actor_asset_id) from dt_timetable_assets ta\n" +
            "        join (\n" +
            "            select r.timetable_id from dt_rentals r\n" +
            "                join dt_timetables tt on tt.id = r.timetable_id\n" +
            "                where r.deleted_at is null) as s on s.timetable_id = ta.timetable_id\n" +
            "        where ta.deleted_at is null\n" +
            "        )", nativeQuery = true)
    List<ContractActorAsset> getAssetsNotAffectedToAnyRental();

    Set<ContractActorAsset> findByContractIdAndDeletedAtIsNull(Long contractId);

    @Query(value = "select " +
            "cas.* from dt_contract_actor_asset cas\n" +
            "    where cas.deleted_at is null\n" +
            "    and cas.id not in (\n" +
            "        select distinct(ta.contract_actor_asset_id) from dt_timetable_assets ta\n" +
            "        join (\n" +
            "            select r.timetable_id from dt_accessories r\n" +
            "                join dt_timetables tt on tt.id = r.timetable_id\n" +
            "                where r.deleted_at is null) as s on s.timetable_id = ta.timetable_id\n" +
            "        where ta.deleted_at is null\n" +
            "        )", nativeQuery = true)
    List<ContractActorAsset> getAssetsNotAffectedToAnyAccessory();


    ContractActorAsset findByAssetIdAndDeletedAtIsNull(Long assetId);

    Optional<ContractActorAsset> findByIdAndDeletedAtIsNull(Long id);

    boolean existsByIdAndDeletedAtIsNull(Long id);
}
