package com.datatricks.assetmodule.repository;

import com.datatricks.assetmodule.model.Element;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface ElementRepository extends JpaRepository<Element, Long>, JpaSpecificationExecutor<Element> {
    Optional<Element> findByIdAndDeletedAtIsNull(Long elementId);

    Element findByIdAndSerialNumberAndDeletedAtIsNull(Long id, String serialNumber);

    Element findBySerialNumberAndDeletedAtIsNull(String serialNumber);

    boolean existsByIdAndDeletedAtIsNull(Long elementId);

    Element findByAssetIdAndDeletedAtIsNull(Long id);

    Element findFirstByAssetIdAndDeletedAtIsNull(Long id);

    @Query("SELECT e FROM Element e WHERE e.asset.id = :assetId AND e.deletedAt IS NULL ORDER BY e.createdAt ASC LIMIT 1")
    Element findFirstElementOfAssetByAssetIdAndDeletedAtIsNull(@Param("assetId") Long assetId);

    @Query("SELECT COALESCE(SUM(e.acquisitionValueHt), 0) FROM Element e WHERE e.asset.id = :assetId AND e.deletedAt IS NULL")
    Double sumAcquisitionValueHtByAssetIdAndDeletedAtIsNull(@Param("assetId") Long assetId);

    Optional<Element> findByIdAndAssetIdAndDeletedAtIsNull(Long elementId, Long assetId);
}
