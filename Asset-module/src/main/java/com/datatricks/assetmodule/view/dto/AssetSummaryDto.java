package com.datatricks.assetmodule.view.dto;

import com.datatricks.assetmodule.model.dto.PageableDto;
import com.datatricks.assetmodule.model.dto.PhaseDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AssetSummaryDto implements PageableDto {

    private Long id;

    @JsonProperty("reference")
    private String reference;

    @JsonProperty("label")
    private String label;

    @JsonProperty("total_value")
    private double totalValue;

    @JsonProperty("contract_reference")
    private String contractReference;

    @JsonProperty("supplier_name")
    private String supplierName;

    @JsonProperty("description")
    private String description;

    @JsonProperty("phase")
    private PhaseDto phase;
}
