package com.datatricks.assetmodule.exception.handler;

import com.datatricks.assetmodule.exception.MultipleResourcesNotFoundException;
import com.datatricks.assetmodule.exception.ResourcesNotFoundException;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Arrays;
import java.util.List;

@RestControllerAdvice
public class ResourcesNotFoundHandler {

    @Value("${api.response.activateDebugInfo}")
    private boolean isDebugActive;
    @ApiResponse(responseCode = "404", description = "Resource Not Found",
                content = @Content(schema = @Schema(implementation = TechnicalError.class)))
    @ExceptionHandler(ResourcesNotFoundException.class)
    public ResponseEntity<TechnicalError> handleConstraintViolation(ResourcesNotFoundException ex) {
        TechnicalError technicalErrorResponse = new TechnicalError(ex.getField() + "_NOT_FOUND");
        technicalErrorResponse.setMessage(ex.getMessage());
        if (isDebugActive) {
            technicalErrorResponse.getTechnicalDetail().setDetail(ex.getLocalizedMessage());
            technicalErrorResponse.getTechnicalDetail().setSource(ex.getModuleName());
        }

        return new ResponseEntity<>(technicalErrorResponse, HttpStatus.NOT_FOUND);
    }


    @ApiResponse(responseCode = "404", description = "Resource Not Found",
            content = @Content(schema = @Schema(implementation = BusinessError.class)))
    @ExceptionHandler(MultipleResourcesNotFoundException.class)
    public ResponseEntity<BusinessError> handleMultipleConstraintViolation(MultipleResourcesNotFoundException ex) {
        BusinessError businessErrorResponse = new BusinessError("NOT_FOUND");
        businessErrorResponse.setMessage("the input provided is invalid");
        List<String> errors = Arrays.asList(ex.getMessage().split("\n"));
        errors.forEach(error -> businessErrorResponse
                .getBusinessDetails()
                .add(new BusinessDetails(error.split(" ")[0].toLowerCase(), error)));
        return new ResponseEntity<>(businessErrorResponse, HttpStatus.NOT_FOUND);
    }
}
