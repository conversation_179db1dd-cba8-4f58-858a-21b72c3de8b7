package com.datatricks.assetmodule.utils;

import org.hibernate.HibernateException;

import java.util.UUID;

public class CustomIdentifierGenerator {

    // Private constructor to hide the implicit public one
    private CustomIdentifierGenerator() {
        throw new IllegalStateException("Utility class");
    }

    public static String generate(String prefix) throws HibernateException {
        UUID uuid = UUID.randomUUID();
        return prefix + uuid.toString().replace("-", "").substring(0, 12);
    }
}
