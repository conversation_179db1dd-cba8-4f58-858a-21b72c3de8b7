package com.datatricks.assetmodule.utils;

import java.io.InputStream;

import org.springframework.core.io.InputStreamResource;

public class MultipartInputStream extends InputStreamResource {
    private final String filename;

    public MultipartInputStream(InputStream inputStream, String filename) {
        super(inputStream);
        this.filename = filename;
    }

    @Override
    public String getFilename() {
        return filename;
    }

    @Override
    public long contentLength() {
        return -1;
    }
}