package com.datatricks.assetmodule.utils;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ControllerRegistry {

    private final Map<Class<?>, Class<?>> entityToControllerMap = new ConcurrentHashMap<>();

    public Class<?> getControllerForEntity(Class<?> entityClass) {
        return entityToControllerMap.computeIfAbsent(entityClass, this::findControllerClass);
    }

    private Class<?> findControllerClass(Class<?> entityClass) {
        String controllerName = entityClass.getSimpleName() + "Controller";
        String packageName = entityClass.getPackage().getName();
        try {
            return Class.forName(packageName + "." + controllerName);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Controller not found for entity: " + entityClass.getSimpleName(), e);
        }
    }
}
