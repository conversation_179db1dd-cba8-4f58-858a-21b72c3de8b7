package com.datatricks.assetmodule.utils;

import com.toedter.spring.hateoas.jsonapi.JsonApiModelBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collection;
import java.util.Map;

@Component
public class GenericJsonApiModelAssembler implements JsonApiModelAssemblerInterface<Object> {

    private static final Logger logger = LoggerFactory.getLogger(GenericJsonApiModelAssembler.class);

    private final ControllerRegistry controllerRegistry;

    public GenericJsonApiModelAssembler(ControllerRegistry controllerRegistry) {
        this.controllerRegistry = controllerRegistry;
    }

    @Override
    public RepresentationModel<?> toJsonApiModel(Object entity, String[] fields) {
        try {
            // Initialize the builder without the self link
            JsonApiModelBuilder builder = JsonApiModelBuilder.jsonApiModel()
                    .model(entity);

            // Handle fields if specified
            if (fields != null && fields.length > 0) {
                String entityType = entity.getClass().getSimpleName().toLowerCase() + "s"; // e.g., Movie -> movies
                builder = builder.fields(entityType, fields);
            }

            // Dynamically handle relationships
            Map<String, Method> relationshipGetters = RelationshipUtils.getRelationshipGetters(entity.getClass());
            for (Map.Entry<String, Method> entry : relationshipGetters.entrySet()) {
                String relation = entry.getKey();
                Method getter = entry.getValue();

                // Convert relation name to snake_case
                String snakeCaseRelation = toSnakeCase(relation);

                // Check if 'fields' includes this relationship
                if (fields == null || Arrays.asList(fields).contains(relation)) {
                    try {
                        Object related = getter.invoke(entity);
                        if (related != null) {
                            if (related instanceof Collection<?>) {
                                Collection<?> relatedCollection = (Collection<?>) related;
                                builder = builder.relationship(snakeCaseRelation, relatedCollection);
                            } else {
                                builder = builder.relationship(snakeCaseRelation, related);
                            }
                        }
                    } catch (Exception e) {
                        // Log the exception appropriately
                        logger.error("Error processing relationship '{}' for entity '{}'", relation, entity, e);
                    }
                }
            }

            return builder.build();
        } catch (Exception e) {
            logger.error("Error assembling JSON:API model for entity: {}", entity, e);
            throw new RuntimeException("Failed to assemble JSON:API model", e);
        }
    }

    /**
     * Converts a given string from camelCase or PascalCase to snake_case.
     * For example: "legalCategory" -> "legal_category"
     *              "bankAccounts" -> "bank_accounts"
     */
    private String toSnakeCase(String input) {
        // Convert from camelCase or PascalCase to snake_case
        return input.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * Retrieves the ID of the entity. Assumes the entity has a method getId().
     */
    private Object getEntityId(Object entity) {
        try {
            Method getIdMethod = entity.getClass().getMethod("getId");
            return getIdMethod.invoke(entity);
        } catch (Exception e) {
            logger.error("Failed to get entity ID for entity: {}", entity, e);
            throw new RuntimeException("Failed to get entity ID", e);
        }
    }

    /**
     * Determines the controller class for the given entity class.
     * Assumes controller naming convention: EntityNameController (e.g., MovieController).
     */
    private Class<?> getControllerClass(Class<?> entityClass) {
        return controllerRegistry.getControllerForEntity(entityClass);
    }
}

