package com.datatricks.assetmodule.service;

import com.datatricks.assetmodule.utils.GenericJsonApiModelAssembler;
import com.datatricks.assetmodule.utils.RelationshipUtils;
import com.toedter.spring.hateoas.jsonapi.JsonApiModelBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class InclusionService {

    private static final Logger logger = LoggerFactory.getLogger(InclusionService.class);
    private static final String INCLUDE_ALL_FLAG = "*"; // Define the flag

    private final GenericJsonApiModelAssembler genericAssembler;

    // Constructor Injection of the Assembler
    public InclusionService(GenericJsonApiModelAssembler genericAssembler) {
        this.genericAssembler = genericAssembler;
    }

    /**
     * Processes the include parameters and adds the related entities to the JSON:API model builder for collections.
     *
     * @param mainEntities        The primary entities.
     * @param includeParams       The include parameters from the request.
     * @param jsonApiModelBuilder The builder for the JSON:API response.
     * @param <T>                 The type of the primary entity.
     */
    public <T> void processIncludes(Collection<T> mainEntities, String[] includeParams,
                                    JsonApiModelBuilder jsonApiModelBuilder) {

        if (includeParams == null || includeParams.length == 0) {
            return;
        }

        if (mainEntities.isEmpty()) {
            return;
        }

        Class<?> entityClass = mainEntities.iterator().next().getClass();
        Map<String, Method> relationshipGetters = RelationshipUtils.getRelationshipGetters(entityClass);

        boolean includeAll = Arrays.asList(includeParams).contains(INCLUDE_ALL_FLAG);

        Set<Object> processedEntities = new HashSet<>();

        if (includeAll) {
            logger.info("Include all relationships for entities of type: {}", entityClass.getSimpleName());
            // Iterate over all relationships
            for (Map.Entry<String, Method> entry : relationshipGetters.entrySet()) {
                String relation = entry.getKey();
                Method getter = entry.getValue();

                Set<Object> relatedEntities = mainEntities.stream()
                        .map(entity -> {
                            try {
                                return getter.invoke(entity);
                            } catch (Exception e) {
                                logger.error("Error invoking getter for relationship '{}': {}", relation, e.getMessage(), e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .flatMap(rel -> {
                            if (rel instanceof Collection<?>) {
                                return ((Collection<?>) rel).stream();
                            } else {
                                return Stream.of(rel);
                            }
                        })
                        .collect(Collectors.toSet());

                // Convert related entities to RepresentationModel and add to included
                relatedEntities.forEach(entity -> {
                    if (!processedEntities.contains(entity)) {
                        RepresentationModel<?> includedModel = genericAssembler.toJsonApiModel(entity, null);
                        jsonApiModelBuilder.included(includedModel);
                        processedEntities.add(entity);
                        // Recursively include relationships of the related entity
                        processIncludesRecursive(entity, jsonApiModelBuilder, processedEntities);
                    }
                });
            }
        } else {
            // Process specific include parameters
            for (String include : includeParams) {
                Method getter = relationshipGetters.get(include);
                if (getter != null) {
                    Set<Object> relatedEntities = mainEntities.stream()
                            .map(entity -> {
                                try {
                                    return getter.invoke(entity);
                                } catch (Exception e) {
                                    logger.error("Error invoking getter for relationship '{}': {}", include, e.getMessage(), e);
                                    return null;
                                }
                            })
                            .filter(Objects::nonNull)
                            .flatMap(rel -> {
                                if (rel instanceof Collection<?>) {
                                    return ((Collection<?>) rel).stream();
                                } else {
                                    return Stream.of(rel);
                                }
                            })
                            .collect(Collectors.toSet());

                    // Convert related entities to RepresentationModel and add to included
                    relatedEntities.forEach(entity -> {
                        if (!processedEntities.contains(entity)) {
                            RepresentationModel<?> includedModel = genericAssembler.toJsonApiModel(entity, null);
                            jsonApiModelBuilder.included(includedModel);
                            processedEntities.add(entity);
                            // Recursively include relationships of the related entity
                            processIncludesRecursive(entity, jsonApiModelBuilder, processedEntities);
                        }
                    });
                } else {
                    // Optionally handle unknown include parameters, e.g., log a warning
                    logger.warn("Unknown include parameter: {}", include);
                }
            }
        }
    }

    /**
     * Processes the include parameters and adds the related entities to the JSON:API model builder for a single entity.
     *
     * @param mainEntity          The primary entity.
     * @param includeParams       The include parameters from the request.
     * @param jsonApiModelBuilder The builder for the JSON:API response.
     * @param <T>                 The type of the primary entity.
     */
    public <T> void processIncludes(T mainEntity, String[] includeParams,
                                    JsonApiModelBuilder jsonApiModelBuilder) {
        if (includeParams == null || includeParams.length == 0 || mainEntity == null) {
            return;
        }

        Class<?> entityClass = mainEntity.getClass();
        Map<String, Method> relationshipGetters = RelationshipUtils.getRelationshipGetters(entityClass);

        boolean includeAll = Arrays.asList(includeParams).contains(INCLUDE_ALL_FLAG);

        Set<Object> processedEntities = new HashSet<>();

        if (includeAll) {
            logger.info("Include all relationships for entity of type: {}", entityClass.getSimpleName());
            // Iterate over all relationships
            for (Map.Entry<String, Method> entry : relationshipGetters.entrySet()) {
                String relation = entry.getKey();
                Method getter = entry.getValue();

                try {
                    Object relatedEntity = getter.invoke(mainEntity);
                    if (relatedEntity != null) {
                        if (relatedEntity instanceof Collection<?>) {
                            Collection<?> relEntities = (Collection<?>) relatedEntity;
                            relEntities.forEach(entity -> {
                                if (!processedEntities.contains(entity)) {
                                    RepresentationModel<?> includedModel = genericAssembler.toJsonApiModel(entity, null);
                                    jsonApiModelBuilder.included(includedModel);
                                    processedEntities.add(entity);
                                    // Recursively include relationships of the related entity
                                    processIncludesRecursive(entity, jsonApiModelBuilder, processedEntities);
                                }
                            });
                        } else {
                            if (!processedEntities.contains(relatedEntity)) {
                                RepresentationModel<?> includedModel = genericAssembler.toJsonApiModel(relatedEntity, null);
                                jsonApiModelBuilder.included(includedModel);
                                processedEntities.add(relatedEntity);
                                // Recursively include relationships of the related entity
                                processIncludesRecursive(relatedEntity, jsonApiModelBuilder, processedEntities);
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("Error processing include '{}': {}", relation, e.getMessage(), e);
                }
            }
        } else {
            // Process specific include parameters
            for (String include : includeParams) {
                Method getter = relationshipGetters.get(include);
                if (getter != null) {
                    try {
                        Object relatedEntity = getter.invoke(mainEntity);
                        if (relatedEntity != null) {
                            if (relatedEntity instanceof Collection<?>) {
                                Collection<?> relEntities = (Collection<?>) relatedEntity;
                                relEntities.forEach(entity -> {
                                    if (!processedEntities.contains(entity)) {
                                        RepresentationModel<?> includedModel = genericAssembler.toJsonApiModel(entity, null);
                                        jsonApiModelBuilder.included(includedModel);
                                        processedEntities.add(entity);
                                        // Recursively include relationships of the related entity
                                        processIncludesRecursive(entity, jsonApiModelBuilder, processedEntities);
                                    }
                                });
                            } else {
                                if (!processedEntities.contains(relatedEntity)) {
                                    RepresentationModel<?> includedModel = genericAssembler.toJsonApiModel(relatedEntity, null);
                                    jsonApiModelBuilder.included(includedModel);
                                    processedEntities.add(relatedEntity);
                                    // Recursively include relationships of the related entity
                                    processIncludesRecursive(relatedEntity, jsonApiModelBuilder, processedEntities);
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.error("Error processing include '{}': {}", include, e.getMessage(), e);
                    }
                } else {
                    // Optionally handle unknown include parameters
                    logger.warn("Unknown include parameter: {}", include);
                }
            }
        }
    }

    /**
     * Recursively processes includes for a related entity.
     *
     * @param entity              The related entity.
     * @param jsonApiModelBuilder The builder for the JSON:API response.
     * @param processedEntities   The set of already processed entities to avoid infinite loops.
     * @param <T>                 The type of the entity.
     */
    private <T> void processIncludesRecursive(T entity, JsonApiModelBuilder jsonApiModelBuilder,
                                              Set<Object> processedEntities) {
        if (entity == null) {
            return;
        }

        Class<?> entityClass = entity.getClass();
        Map<String, Method> relationshipGetters = RelationshipUtils.getRelationshipGetters(entityClass);

        for (Map.Entry<String, Method> entry : relationshipGetters.entrySet()) {
            String relation = entry.getKey();
            Method getter = entry.getValue();

            try {
                Object relatedEntity = getter.invoke(entity);
                if (relatedEntity != null) {
                    if (relatedEntity instanceof Collection<?>) {
                        Collection<?> relEntities = (Collection<?>) relatedEntity;
                        relEntities.forEach(relEntity -> {
                            if (!processedEntities.contains(relEntity)) {
                                RepresentationModel<?> includedModel = genericAssembler.toJsonApiModel(relEntity, null);
                                jsonApiModelBuilder.included(includedModel);
                                processedEntities.add(relEntity);
                                // Recursive call
                                processIncludesRecursive(relEntity, jsonApiModelBuilder, processedEntities);
                            }
                        });
                    } else {
                        if (!processedEntities.contains(relatedEntity)) {
                            RepresentationModel<?> includedModel = genericAssembler.toJsonApiModel(relatedEntity, null);
                            jsonApiModelBuilder.included(includedModel);
                            processedEntities.add(relatedEntity);
                            // Recursive call
                            processIncludesRecursive(relatedEntity, jsonApiModelBuilder, processedEntities);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("Error processing nested include '{}': {}", relation, e.getMessage(), e);
            }
        }
    }

    /**
     * Processes the include parameters and adds the related entities to the JSON:API model builder for a list of any objects.
     *
     * @param mainEntities        The primary entities as a List of Objects.
     * @param includeParams       The include parameters from the request.
     * @param jsonApiModelBuilder The builder for the JSON:API response.
     */
    public void processIncludes(List<Object> mainEntities, String[] includeParams,
                                JsonApiModelBuilder jsonApiModelBuilder) {

        if (includeParams == null || includeParams.length == 0) {
            return;
        }

        if (mainEntities.isEmpty()) {
            return;
        }

        // Iterate over each entity in the list
        for (Object mainEntity : mainEntities) {
            if (mainEntity == null) {
                continue;
            }

            Class<?> entityClass = mainEntity.getClass();
            Map<String, Method> relationshipGetters = RelationshipUtils.getRelationshipGetters(entityClass);

            boolean includeAll = Arrays.asList(includeParams).contains(INCLUDE_ALL_FLAG);

            Set<Object> processedEntities = new HashSet<>();
            processedEntities.add(mainEntity); // Add main entity to prevent reprocessing

            if (includeAll) {
                logger.info("Include all relationships for entity of type: {}", entityClass.getSimpleName());
                // Iterate over all relationships
                for (Map.Entry<String, Method> entry : relationshipGetters.entrySet()) {
                    String relation = entry.getKey();
                    Method getter = entry.getValue();

                    try {
                        Object relatedEntity = getter.invoke(mainEntity);
                        if (relatedEntity != null) {
                            if (relatedEntity instanceof Collection<?>) {
                                Collection<?> relEntities = (Collection<?>) relatedEntity;
                                for (Object relEntity : relEntities) {
                                    processRelatedEntity(relEntity, jsonApiModelBuilder, processedEntities);
                                }
                            } else {
                                processRelatedEntity(relatedEntity, jsonApiModelBuilder, processedEntities);
                            }
                        }
                    } catch (Exception e) {
                        logger.error("Error invoking getter for relationship '{}': {}", relation, e.getMessage(), e);
                    }
                }
            } else {
                // Process specific include parameters
                for (String include : includeParams) {
                    Method getter = relationshipGetters.get(include);
                    if (getter != null) {
                        try {
                            Object relatedEntity = getter.invoke(mainEntity);
                            if (relatedEntity != null) {
                                if (relatedEntity instanceof Collection<?>) {
                                    Collection<?> relEntities = (Collection<?>) relatedEntity;
                                    for (Object relEntity : relEntities) {
                                        processRelatedEntity(relEntity, jsonApiModelBuilder, processedEntities);
                                    }
                                } else {
                                    processRelatedEntity(relatedEntity, jsonApiModelBuilder, processedEntities);
                                }
                            }
                        } catch (Exception e) {
                            logger.error("Error invoking getter for relationship '{}': {}", include, e.getMessage(), e);
                        }
                    } else {
                        // Optionally handle unknown include parameters
                        logger.warn("Unknown include parameter: {}", include);
                    }
                }
            }
        }
    }

    /**
     * Processes a single related entity and adds it to the JSON:API model builder.
     *
     * @param relatedEntity       The related entity to process.
     * @param jsonApiModelBuilder The builder for the JSON:API response.
     * @param processedEntities   The set of already processed entities to avoid infinite loops.
     */
    private void processRelatedEntity(Object relatedEntity, JsonApiModelBuilder jsonApiModelBuilder,
                                      Set<Object> processedEntities) {
        if (relatedEntity == null || processedEntities.contains(relatedEntity)) {
            return;
        }

        RepresentationModel<?> includedModel = genericAssembler.toJsonApiModel(relatedEntity, null);
        jsonApiModelBuilder.included(includedModel);
        processedEntities.add(relatedEntity);

        // Recursively process relationships of the related entity
        processIncludesRecursive(relatedEntity, jsonApiModelBuilder, processedEntities);
    }
}

