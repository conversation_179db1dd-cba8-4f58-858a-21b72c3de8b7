package com.datatricks.assetmodule.service;

import com.datatricks.assetmodule.exception.ResourcesNotFoundException;
import com.datatricks.assetmodule.exception.handler.InformativeMessage;
import com.datatricks.assetmodule.model.Amortization;
import com.datatricks.assetmodule.model.Element;
import com.datatricks.assetmodule.model.dto.AmortizationDto;
import com.datatricks.assetmodule.model.dto.PageDto;
import com.datatricks.assetmodule.model.dto.SingleResultDto;
import com.datatricks.assetmodule.repository.AmortizationRepository;
import com.datatricks.assetmodule.repository.ElementRepository;
import com.datatricks.assetmodule.utils.JpaQueryFilters;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class AmortizationService {

    private final AmortizationRepository amortizationRepository;

    private final ElementRepository elementRepository;
    private final ModelMapper modelMapper;
    private static final String AMORTIZATION_NOT_FOUND = "Amortization not found";
    private static final String MODULE = "Amortization";

    @Transactional
    public ResponseEntity<PageDto<AmortizationDto>> getAmortizations(Long assetId, Long elementId, Map<String, String> params) {
        JpaQueryFilters<Amortization> filters = new JpaQueryFilters<>(params, Amortization.class);
        Page<Amortization> page = amortizationRepository.findAll(filters.getSpecification(), filters.getPageable());
        List<AmortizationDto> filteredAmortizations = page.stream()
                .map(amortization -> this.modelMapper.map(amortization, AmortizationDto.class))
                .toList();
        return ResponseEntity
                .ok(PageDto.<AmortizationDto>builder()
                        .data(filteredAmortizations)
                        .total(page.getTotalElements()).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AmortizationDto>> getAmortizationById(Long assetId, Long elementId, Long amortizationId) {
        Amortization amortization = amortizationRepository.findByIdAndDeletedAtIsNull(amortizationId).orElse(null);
        if (amortization != null) {
            AmortizationDto amortizationDto = this.modelMapper.map(amortization, AmortizationDto.class);
            return ResponseEntity.ok(SingleResultDto.<AmortizationDto>builder().data(amortizationDto).build());
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AmortizationDto>> createAmortization(Long assetId, Long elementId, AmortizationDto newAmortization) {
        Amortization amortization = createNewAmortization(newAmortization);
        Amortization savedAmortization = amortizationRepository.save(amortization);
        AmortizationDto amortizationDto = this.modelMapper.map(savedAmortization, AmortizationDto.class);
        return ResponseEntity.ok(SingleResultDto.<AmortizationDto>builder().data(amortizationDto).build());
    }

    private static Amortization createNewAmortization(AmortizationDto newAmortization) {

        Amortization amortization = new Amortization();
        amortization.setAmortizationLaw(newAmortization.getAmortizationLaw());
        amortization.setName(newAmortization.getName());
        amortization.setStartDate(newAmortization.getStartDate());
        amortization.setEndDate(newAmortization.getEndDate());
        amortization.setRentalBase(newAmortization.getRentalBase());
        amortization.setFiscalPeriods(newAmortization.getFiscalPeriods());
        amortization.setCreatedAt(new Date());
        return amortization;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AmortizationDto>> updateAmortization(Long assetId, Long elementId, Long amortizationId, AmortizationDto amortization) {
        Amortization existingAmortization = amortizationRepository
                .findByIdAndDeletedAtIsNull(amortizationId)
                .orElseThrow(() -> new ResourcesNotFoundException(AMORTIZATION_NOT_FOUND, MODULE, MODULE));
        existingAmortization.setAmortizationLaw(amortization.getAmortizationLaw());
        existingAmortization.setName(amortization.getName());
        existingAmortization.setStartDate(amortization.getStartDate());
        existingAmortization.setEndDate(amortization.getEndDate());
        existingAmortization.setRentalBase(amortization.getRentalBase());
        existingAmortization.setFiscalPeriods(amortization.getFiscalPeriods());
        existingAmortization.setModifiedAt(new Date());
        Amortization updatedAmortization = amortizationRepository.save(existingAmortization);
        AmortizationDto amortizationDto = this.modelMapper.map(updatedAmortization, AmortizationDto.class);
        return ResponseEntity.ok(SingleResultDto.<AmortizationDto>builder().data(amortizationDto).build());

    }

    public ResponseEntity<InformativeMessage> deleteAmortization(Long assetId, Long elementId, Long amortizationId) {
        Element element = elementRepository
                .findByIdAndAssetIdAndDeletedAtIsNull(elementId, assetId)
                .orElseThrow(() -> new ResourcesNotFoundException("Element not found", "ELEMENT", "ELEMENT"));
        element.setAmortization(null);
        element.setModifiedAt(new Date());
        elementRepository.save(element);

        Amortization amortization = amortizationRepository
                .findByIdAndDeletedAtIsNull(amortizationId)
                .orElseThrow(() -> new ResourcesNotFoundException(AMORTIZATION_NOT_FOUND, MODULE, MODULE));
        amortization.setDeletedAt(new Date());
        amortizationRepository.save(amortization);
        return ResponseEntity.ok(new InformativeMessage("Amortization deleted successfully"));
    }
}
