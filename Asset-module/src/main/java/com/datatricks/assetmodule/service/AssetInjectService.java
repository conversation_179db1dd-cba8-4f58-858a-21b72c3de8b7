package com.datatricks.assetmodule.service;

import com.datatricks.assetmodule.exception.MultipleResourcesNotFoundException;
import com.datatricks.assetmodule.exception.ResourcesNotFoundException;
import com.datatricks.assetmodule.model.*;
import com.datatricks.assetmodule.model.Currency;
import com.datatricks.assetmodule.model.dto.*;
import com.datatricks.assetmodule.model.inject.*;
import com.datatricks.assetmodule.repository.*;
import com.datatricks.assetmodule.utils.GenericJsonApiModelAssembler;
import com.toedter.spring.hateoas.jsonapi.JsonApiModelBuilder;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ValidationException;
import jakarta.validation.Validator;
import org.modelmapper.ModelMapper;
import org.springframework.hateoas.PagedModel;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class AssetInjectService {
    private final PhaseRepository phaseRepository;
    private final MilestoneRepository milestoneRepository;
    private final CountryRepository countryRepository;
    private final AssetService assetService;
    private final ElementService elementService;
    private final ShippingService shippingService;
    private final Validator validator;
    private final GenericJsonApiModelAssembler genericAssembler;
    private final ModelMapper modelMapper;
    private static final String ASSOCIATED_TO_ASSET = "BIEN";
    private static final String MODULE = "ASSET-INJECT";
    private final InclusionService inclusionService;
    private final CategoryRepository categoryRepository;
    private final CurrencyRepository currencyRepository;
    private final ActorRepository actorRepository;
    private final AddressRepository addressRepository;
    private final TaxRateRepository taxRateRepository;
    private final TaxRepository taxRepository;

    public AssetInjectService(PhaseRepository phaseRepository,
                              MilestoneRepository milestoneRepository,
                              CountryRepository countryRepository,
                              AssetService assetService,
                              ElementService elementService,
                              ShippingService shippingService,
                              Validator validator,
                              GenericJsonApiModelAssembler genericAssembler,
                              InclusionService inclusionService,
                              ModelMapper modelMapper, CategoryRepository categoryRepository,
                              CurrencyRepository currencyRepository,
                              ActorRepository actorRepository,
                              AddressRepository addressRepository,
                              AmortizationRepository amortizationRepository,
                              TaxRateRepository taxRateRepository,
                              TaxRepository taxRepository) {
        this.modelMapper = modelMapper;
        this.phaseRepository = phaseRepository;
        this.milestoneRepository = milestoneRepository;
        this.countryRepository = countryRepository;
        this.assetService = assetService;
        this.elementService = elementService;
        this.shippingService = shippingService;
        this.validator = validator;
        this.genericAssembler = genericAssembler;
        this.inclusionService = inclusionService;
        this.categoryRepository = categoryRepository;
        this.currencyRepository = currencyRepository;
        this.actorRepository = actorRepository;
        this.addressRepository = addressRepository;
        this.taxRateRepository = taxRateRepository;
        this.taxRepository = taxRepository;
    }

    @Transactional
    public SingleResultDto<AssetInjectDto> injectAsset(AssetInjectDto asset) {

        if (asset == null) {
            throw new ResourcesNotFoundException("Asset content must not be null", "ASSET", MODULE);
        }
        AssetInjectDto assetInjectDto = modelMapper.map(asset, AssetInjectDto.class);

        // check if sub entities exist
        checkAssetSubEntitiesExists(assetInjectDto);

        // validations
        validateAsset(assetInjectDto);

        // call services
        NewAssetDto newAssetDto = modelMapper.map(assetInjectDto, NewAssetDto.class);
        newAssetDto.setBusinessReference(assetInjectDto.getCompany().getReference());
        AssetDto createdAsset = createAsset(newAssetDto);
        asset.getCompany().setReference(createdAsset.getCompany().getReference());
        asset.getCompany().setId(createdAsset.getCompany().getId());
        asset.setReference(createdAsset.getReference());
        asset.setElements(createAssetElement(asset, createdAsset.getId()));
        asset.getContractActorAsset().setId(createdAsset.getContractActorAsset().getId());
        return SingleResultDto.<AssetInjectDto>builder()
                .data(asset)
                .build();
    }

    public void checkAssetSubEntitiesExists(AssetInjectDto asset) {
        Set<String> notFoundEntities = new HashSet<>();
        if (asset.getCountry() != null) {
            Optional<Country> activity = countryRepository.findByCode(asset.getCountry().getCode()).or(
                    () -> {
                        notFoundEntities.add("Country not found for code: " + asset.getCountry().getCode());
                        return Optional.empty();
                    });
            activity.ifPresent(c -> asset.getCountry().setId(c.getId()));
        } else {
            notFoundEntities.add("Activity must not be null");
        }

        if (asset.getPhase() != null) {
            Optional<Phase> phase = phaseRepository.findByCodeAndAssociatedTo(asset.getPhase().getCode(), ASSOCIATED_TO_ASSET).or(
                    () -> {
                        notFoundEntities.add("Phase not found for code: " + asset.getPhase().getCode());
                        return Optional.empty();
                    });
            phase.ifPresent(p -> asset.getPhase().setId(p.getId()));
        } else {
            notFoundEntities.add("Phase must not be null");
        }

        if (asset.getMilestone() != null) {
            Optional<Milestone> milestone = milestoneRepository.findByCode(asset.getMilestone().getCode()).or(
                    () -> {
                        notFoundEntities.add("Milestone not found for code: " + asset.getMilestone().getCode());
                        return Optional.empty();
                    });
            milestone.ifPresent(m -> asset.getMilestone().setId(m.getId()));
        } else {
            notFoundEntities.add("Milestones must not be null");
        }

        if (asset.getElements() != null) {
            for (ElementInjectDto element : asset.getElements()) {
                if (element.getPhase() != null) {
                    Optional<Phase> phase = phaseRepository.findByCodeAndAssociatedTo(element.getPhase().getCode(), ASSOCIATED_TO_ASSET).or(
                            () -> {
                                notFoundEntities.add("Phase not found for code: " + element.getPhase().getCode());
                                return Optional.empty();
                            });
                    phase.ifPresent(p -> element.getPhase().setId(p.getId()));
                } else {
                    notFoundEntities.add("Phase must not be null");
                }
                if (element.getMilestone() != null) {
                    Optional<Milestone> milestone = milestoneRepository.findByCode(element.getMilestone().getCode()).or(
                            () -> {
                                notFoundEntities.add("Milestone not found for code: " + element.getMilestone().getCode());
                                return Optional.empty();
                            });
                    milestone.ifPresent(m -> element.getMilestone().setId(m.getId()));
                } else {
                    notFoundEntities.add("Milestones must not be null");
                }

                if (element.getCategory() != null) {
                    Optional<Category> category = categoryRepository.findFirstByCode(element.getCategory().getCode()).or(
                            () -> {
                                notFoundEntities.add("Category not found for code: " + element.getCategory().getCode());
                                return Optional.empty();
                            });
                    category.ifPresent(c -> element.getCategory().setId(c.getId()));
                } else {
                    notFoundEntities.add("Category must not be null");
                }

                if (element.getCurrency() != null) {
                    Optional<Currency> currency = currencyRepository.findFirstByCode(element.getCurrency().getCode()).or(
                            () -> {
                                notFoundEntities.add("Currency not found for code: " + element.getCurrency().getCode());
                                return Optional.empty();
                            });
                    currency.ifPresent(c -> element.getCurrency().setId(c.getId()));
                } else {
                    notFoundEntities.add("Currency must not be null");
                }

                if (element.getTax() != null) {
                    Optional<TaxeRate> taxRate = taxRateRepository.findByCodeAndDeletedAtIsNull(element.getTax().getCode()).or(
                            () -> {
                                notFoundEntities.add("Tax rate not found for code: " + element.getTax().getCode());
                                return Optional.empty();
                            });
                    Optional<Tax> tax = taxRepository.findByCodeAndDeletedAtIsNull(element.getTax().getCode()).or(
                            () -> {
                                notFoundEntities.add("Tax not found for code: " + element.getTax().getCode());
                                return Optional.empty();
                            });
                } else {
                    notFoundEntities.add("Tax rate must not be null");
                }


                if (element.getSupplier() != null) {
                    Optional<Actor> supplier = actorRepository.findByReferenceAndDeletedAtIsNull(element.getSupplier().getReference()).or(
                            () -> {
                                notFoundEntities.add("Supplier not found for reference: " + element.getSupplier().getReference());
                                return Optional.empty();
                            });
                    supplier.ifPresent(s -> element.getSupplier().setId(s.getId()));
                } else {
                    notFoundEntities.add("Supplier must not be null");
                }

                if (element.getClientAddress() != null) {
                    Optional<Address> address = addressRepository.findByReferenceAndDeletedAtIsNull(element.getClientAddress().getReference()).or(
                            () -> {
                                notFoundEntities.add("Address not found for reference: " + element.getClientAddress().getReference());
                                return Optional.empty();
                            });
                    address.ifPresent(a -> element.getClientAddress().setId(a.getId()));
                }

                if (element.getProviderAddress() != null) {
                    Optional<Address> address = addressRepository.findByReferenceAndDeletedAtIsNull(element.getProviderAddress().getReference()).or(
                            () -> {
                                notFoundEntities.add("Address not found for reference: " + element.getProviderAddress().getReference());
                                return Optional.empty();
                            });
                    address.ifPresent(a -> element.getProviderAddress().setId(a.getId()));
                }
            }
        }

        if (!notFoundEntities.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (String entity : notFoundEntities) {
                sb.append(entity).append("\n");
            }
            // You can throw an exception or handle the validation errors as needed
            throw new MultipleResourcesNotFoundException(sb.toString(), "ASSET", MODULE);
        }
    }

    public void validateAsset(AssetInjectDto assetInjectDto) {

        Set<Set<ConstraintViolation<Object>>> violations = new HashSet<>();
        NewAssetDto newAssetDto = modelMapper.map(assetInjectDto, NewAssetDto.class);
        newAssetDto.setBusinessReference(assetInjectDto.getCompany().getReference());

        if (!validator.validate(newAssetDto).isEmpty()) {
            violations.add(validator.validate(newAssetDto));
        }

        if (assetInjectDto.getElements() != null) {
            for (ElementInjectDto element : assetInjectDto.getElements()) {
                NewElementDto elementDto = modelMapper.map(element, NewElementDto.class);
                if (!validator.validate(elementDto).isEmpty()) {
                    violations.add(validator.validate(elementDto));
                }
                if (element.getAmortization() != null) {
                    AmortizationDto amortizationDto = modelMapper.map(element.getAmortization(), AmortizationDto.class);
                    if (!validator.validate(amortizationDto).isEmpty()) {
                        violations.add(validator.validate(amortizationDto));
                    }
                }
                if (element.getShipping() != null) {
                    ShippingDto shippingDto = modelMapper.map(element.getShipping(), ShippingDto.class);
                    if (!validator.validate(shippingDto).isEmpty()) {
                        violations.add(validator.validate(shippingDto));
                    }
                }
            }
        }

        if (!violations.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (Set<ConstraintViolation<Object>> violationSet : violations) {
                for (ConstraintViolation<Object> violation : violationSet) {
                    sb.append(violation.getMessage()).append("\n");
                }
            }
            // You can throw an exception or handle the validation errors as needed
            throw new ValidationException(sb.toString());
        }
    }

    public AssetDto createAsset(NewAssetDto newAssetDto) {
        return Objects.requireNonNull(assetService.createAsset(newAssetDto).getBody()).getData();
    }

    public Set<ElementInjectDto> createAssetElement(AssetInjectDto assetInjectDto, Long assetId) {
        Set<ElementInjectDto> elements = new HashSet<>();
        if (assetInjectDto.getElements() != null) {
            for (ElementInjectDto element : assetInjectDto.getElements()) {
                NewElementDto elementDto = modelMapper.map(element, NewElementDto.class);
                elementDto.setAssetReference(assetInjectDto.getReference());
                ElementDto createdElement = elementService.createElement(assetId, elementDto).getBody().getData();
                element.setId(createdElement.getId());
                if (createdElement.getAmortization() != null) {
                    element.setAmortization(modelMapper.map(createdElement.getAmortization(), AmortizationInjectDto.class));
                }
                if (createdElement.getShipping() != null) {
                    element.setShipping(modelMapper.map(createdElement.getShipping(), ShippingInjectDto.class));
                }
                if (createdElement.getClientAddress() != null) {
                    element.setClientAddress(modelMapper.map(createdElement.getClientAddress(), AddressInjectDto.class));
                }
                if (createdElement.getProviderAddress() != null) {
                    element.setProviderAddress(modelMapper.map(createdElement.getProviderAddress(), AddressInjectDto.class));
                }
                elements.add(element);
            }
        }
        return elements;
    }

    @Transactional
    public RepresentationModel<?> getResponses(List<AssetInjectDto> assets) {

        List<? extends RepresentationModel<?>> assetResource = assets.stream()
                .map(asset -> genericAssembler.toJsonApiModel(asset, null))
                .toList();

        PagedModel.PageMetadata pageMetadata = new PagedModel.PageMetadata(
                assets.size(),
                1,
                assets.size(),
                1
        );

        PagedModel<? extends RepresentationModel<?>> pagedModel = PagedModel.of(assetResource, pageMetadata);

        JsonApiModelBuilder jsonApiModelBuilder = JsonApiModelBuilder.jsonApiModel()
                .model(pagedModel);

        inclusionService.processIncludes(assets, new String[]{"*"}, jsonApiModelBuilder);
        return jsonApiModelBuilder.build();
    }
}
