package com.datatricks.assetmodule.service;

import com.datatricks.assetmodule.exception.BusinessException;
import com.datatricks.assetmodule.exception.ResourcesNotFoundException;
import com.datatricks.assetmodule.exception.TechnicalException;
import com.datatricks.assetmodule.exception.handler.InformativeMessage;
import com.datatricks.assetmodule.model.*;
import com.datatricks.assetmodule.model.dto.*;
import com.datatricks.assetmodule.producer.AssetProducer;
import com.datatricks.assetmodule.repository.*;
import com.datatricks.assetmodule.utils.JpaQueryFilters;
import com.datatricks.assetmodule.utils.PatchHelper;
import com.datatricks.assetmodule.utils.TransactionSynchronizationUtil;
import com.datatricks.assetmodule.view.AssetSummary;
import com.datatricks.assetmodule.view.dto.AssetSummaryDto;
import com.datatricks.kafkacommondomain.enums.OperationType;
import jakarta.persistence.EntityManager;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.datatricks.assetmodule.utils.AssetUtils.handleException;

@Service
@AllArgsConstructor
public class AssetService {
    private final AssetRepository assetRepository;
    private final MilestoneRepository milestoneRepository;
    private final ModelMapper modelMapper;
    private final CountryRepository countryRepository;
    private final PhaseRepository phaseRepository;
    private final ContractActorAssetService contractActorAssetService;
    private final EntityManager entityManager;
    private final ActorRepository actorRepository;
    private final ElementRepository elementRepository;
    private final ContractActorAssetRepository contractActorAssetRepository;
    private final ContractActorRepository contractActorRepository;
    private final AssetSummaryRepository assetSummaryRepository;
    private final AssetProducer assetProducer;
    private final TransactionSynchronizationUtil transactionSynchronizationUtil;
    private static final String MODULE = "Asset";
    private final CategoryRepository categoryRepository;
    private static final String CONTRACT_ACTOR_NOT_FOUND = "Contract Actor not found";
    private static final String PHASE_NOT_FOUND = "Phase not found";
    private static final String COMPANY_NOT_FOUND = "Business not found";
    private static final String CONTRACT = "CONTRACT";
    private static final String PHASE = "PHASE";
    private static final String COMPANY = "COMPANY";
    private final PatchHelper<PatchAssetDto> patchHelper;
    private static final String ASSET_NOT_FOUND = "Asset not found";
    private static final String COUNTRY_NOT_FOUND = "Country not found";
    private static final String MILESTONE_NOT_FOUND = "Milestone not found";
    private static final String CATEGORY_NOT_FOUND = "Category not found";
    private static final String ERROR_CREATING_ASSET = "Error while creating asset";
    private static final String ERROR_UPDATING_ASSET = "Error while updating asset";
    private static final String ERROR_DELETING_ASSET = "Error while deleting asset";
    private static final String RESOURCE_DELETED_SUCCESSFULLY = "Resource with ID %d has been deleted successfully";
    private final RentalRepository rentalRepository;
    private final AccessoryRepository accessoryRepository;
    private final TimetableAssetRepository timetableAssetRepository;

    @Transactional(readOnly = true)
    public ResponseEntity<PageDto<AssetSummaryDto>> getAssets(Map<String, String> params) {
        JpaQueryFilters<AssetSummary> filters = new JpaQueryFilters<>(params, AssetSummary.class);
        Page<AssetSummary> page = assetSummaryRepository.findAll(filters.getSpecification(), filters.getPageable());
        List<AssetSummaryDto> filteredAssets = page.stream()
                .map(asset -> modelMapper.map(asset, AssetSummaryDto.class))
                .toList();

        return ResponseEntity
                .ok(PageDto.<AssetSummaryDto>builder()
                        .data(filteredAssets)
                        .total(page.getTotalElements()).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AssetDto>> getAsset(String assetReference) {
        Asset asset = assetRepository
                .findByReferenceAndDeletedAtIsNull(assetReference)
                .orElseThrow(() -> new ResourcesNotFoundException(ASSET_NOT_FOUND, MODULE, MODULE));

        AssetDto assetDto = this.modelMapper.map(asset, AssetDto.class);

        ContractActorAssetDto contractActorAsset = contractActorAssetService.fetchContractActorAssetById(asset.getId());
        if (contractActorAsset != null) {
            assetDto.setContractActorAsset(modelMapper.map(contractActorAsset, SimplifiedContractActorAssetDto.class));
        }

        var element = elementRepository.findFirstElementOfAssetByAssetIdAndDeletedAtIsNull(assetDto.getId());
        if (element != null) {
            assetDto.setSupplier(this.modelMapper.map(element.getSupplier(), SimplifiedActorDto.class));
        }

        return ResponseEntity.ok(SingleResultDto.<AssetDto>builder().data(assetDto).build());
    }

    @Transactional
    public ResponseEntity<PageDto<ContractActorAssetDto>> getAssetsForRentals() {
        List<ContractActorAssetDto> contractActorAssets = contractActorAssetRepository.getAssetsNotAffectedToAnyRental().stream()
                .map(c -> modelMapper.map(c, ContractActorAssetDto.class))
                .toList();

        return ResponseEntity
                .ok(PageDto.<ContractActorAssetDto>builder()
                        .data(contractActorAssets)
                        .total(contractActorAssets.size()).build());

    }

    @Transactional
    public ResponseEntity<PageDto<ContractActorAssetDto>> getAssetsForAccessories() {
        List<ContractActorAssetDto> contractActorAssets = contractActorAssetRepository.getAssetsNotAffectedToAnyAccessory().stream()
                .map(c -> modelMapper.map(c, ContractActorAssetDto.class))
                .toList();

        return ResponseEntity
                .ok(PageDto.<ContractActorAssetDto>builder()
                        .data(contractActorAssets)
                        .total(contractActorAssets.size()).build());
    }

    @Transactional
    public ResponseEntity<PageDto<ContractActorAssetDto>> getAssetsOfRentals(Long contractActorId, Long rentalId) {

        contractActorRepository
                .findByIdAndDeletedAtIsNull(contractActorId)
                .orElseThrow(() -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, CONTRACT, MODULE));
        var rental = rentalRepository
                .findByIdAndDeletedAtIsNull(rentalId)
                .orElseThrow(() -> new ResourcesNotFoundException("Rental not found", "Rental not found", MODULE));

        List<ContractActorAssetDto> assets = timetableAssetRepository.findAllByTimetableIdAndDeletedAtIsNull(rental.getTimetable().getId()).stream()
                .map(timetableAsset -> {
                    ContractActorAssetDto response = this.modelMapper.map(timetableAsset.getContractActorAsset(), ContractActorAssetDto.class);
                    response.getRentals().add(modelMapper.map(rental, RentalDto.class));
                    return response;
                })
                .toList();

        return ResponseEntity
                .ok(PageDto.<ContractActorAssetDto>builder()
                        .data(assets)
                        .total(assets.size()).build());
    }

    @Transactional
    public ResponseEntity<PageDto<ContractActorAssetDto>> getAssetsOfAccessory(Long contractActorId, Long accessoryId) {

        contractActorRepository
                .findByIdAndDeletedAtIsNull(contractActorId)
                .orElseThrow(() -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, CONTRACT, MODULE));
        var accessory = accessoryRepository
                .findByIdAndDeletedAtIsNull(accessoryId)
                .orElseThrow(() -> new ResourcesNotFoundException("Accessory not found", "Accessory not found", MODULE));

        List<ContractActorAssetDto> assets = timetableAssetRepository.findAllByTimetableIdAndDeletedAtIsNull(accessory.getTimetable().getId()).stream()
                .map(timetableAsset -> {
                    ContractActorAssetDto response = this.modelMapper.map(timetableAsset.getContractActorAsset(), ContractActorAssetDto.class);
                    response.getAccessories().add(modelMapper.map(accessory, AccessoryDto.class));
                    return response;
                })
                .toList();

        return ResponseEntity
                .ok(PageDto.<ContractActorAssetDto>builder()
                        .data(assets)
                        .total(assets.size()).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AssetDto>> createAsset(NewAssetDto newAsset) {

        Asset asset = new Asset();
        asset.setOrderNumber(newAsset.getOrderNumber());
        asset.setDescription(newAsset.getDescription());
        asset.setLabel(newAsset.getLabel());
        asset.setCreatedAt(new Date());

        var phase = phaseRepository
                .findFirstByCode(newAsset.getPhaseCode())
                .orElseThrow(() -> new ResourcesNotFoundException(PHASE_NOT_FOUND, PHASE, MODULE));
        asset.setPhase(phase);

        var company = actorRepository
                .findByReferenceAndDeletedAtIsNull(newAsset.getBusinessReference())
                .orElseThrow(() -> new ResourcesNotFoundException(COMPANY_NOT_FOUND, COMPANY, MODULE));
        asset.setCompany(company);

        var country = countryRepository
                .findByCode(company.getCountry().getCode())
                .orElse(new Country(76L, "FR", "France"));
        asset.setCountry(country);

        var milestone = milestoneRepository
                .findFirstByCode(newAsset.getMilestoneCode())
                .orElseThrow(() -> new ResourcesNotFoundException(MILESTONE_NOT_FOUND, "MILESTONE", MODULE));
        asset.setMilestone(milestone);

        Asset savedAsset = assetRepository.saveAndFlush(asset);
        //Clear the entity manager so the trigger can be fired to generate new reference
        entityManager.clear();

        Optional<Asset> optionalAsset = assetRepository.findByIdAndDeletedAtIsNull(savedAsset.getId());
        newAsset.setContractActorAsset(new NewContractActorAssetDto());
        try {
            if (optionalAsset.isPresent()) {
                asset = optionalAsset.get();

                // send message to kafka
                // Note: Variable used in lambda expression should be final or effectively final
                Asset finalAsset = asset;
                assetProducer.sendAssetMessage(finalAsset, OperationType.POST, MODULE);

                newAsset.getContractActorAsset().setRentalId(
                        newAsset.getContractActorAsset().getRentalId() == null ? 0 : newAsset.getContractActorAsset().getRentalId());
                newAsset.getContractActorAsset().setAccessoryId(
                        newAsset.getContractActorAsset().getAccessoryId() == null ? 0 : newAsset.getContractActorAsset().getAccessoryId());

                var contractActorAssetResult = contractActorAssetService.createContractActorAsset(
                        savedAsset.getId(),
                        newAsset.getContractActorAsset())
                        .getBody();
                AssetDto assetDto = this.modelMapper.map(asset, AssetDto.class);
                if (contractActorAssetResult != null) {
                    assetDto.setContractActorAsset(modelMapper.map(contractActorAssetResult.getData(), SimplifiedContractActorAssetDto.class));
                }

                return ResponseEntity.ok(SingleResultDto.<AssetDto>builder().data(assetDto).build());

            } else {
                // Handle error if asset is not found (unlikely in this scenario)
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AssetDto>> updateAsset(String assetReference, NewAssetDto newAssetDto) {
        Asset assetToUpdate = assetRepository.findByReferenceAndDeletedAtIsNull(assetReference).orElse(null);
        try {
            if (assetToUpdate != null) {

                assetToUpdate.setOrderNumber(newAssetDto.getOrderNumber());
                assetToUpdate.setDescription(newAssetDto.getDescription());
                assetToUpdate.setLabel(newAssetDto.getLabel());
                assetToUpdate.setModifiedAt(new Date());
                if (assetToUpdate.getPhase() != null && !Objects.equals(assetToUpdate.getPhase().getCode(), newAssetDto.getPhaseCode())) {
                    assetToUpdate.setPhase(phaseRepository
                            .findFirstByCode(newAssetDto.getPhaseCode())
                            .orElseThrow(() -> new ResourcesNotFoundException(PHASE_NOT_FOUND, PHASE, MODULE)));
                }

                var company = actorRepository
                        .findByReferenceAndDeletedAtIsNull(newAssetDto.getBusinessReference())
                        .orElseThrow(() -> new ResourcesNotFoundException(COMPANY_NOT_FOUND, COMPANY, MODULE));
                assetToUpdate.setCompany(company);
                newAssetDto.setContractActorAsset(new NewContractActorAssetDto());
                Asset savedAsset = assetRepository.save(assetToUpdate);
//                Long rentalId = newAssetDto.getContractActorAsset().getRentalId() == null ? 0 : newAssetDto.getContractActorAsset().getRentalId();
//                Long accessoryId = newAssetDto.getContractActorAsset().getAccessoryId() == null ? 0 : newAssetDto.getContractActorAsset().getAccessoryId();
//                var contractActorAsset = contractActorAssetService
//                        .updateContractActorAsset(newAssetDto.getContractActorAsset().getContractReference(), newAssetDto.getContractActorAsset().getActorReference(), rentalId, accessoryId, assetToUpdate.getId(), newAssetDto.getContractActorAsset()).getBody();
//
//                assert contractActorAsset != null;
//                assetDto.setContractActorAsset(contractActorAsset.getData());
                AssetDto assetDto = this.modelMapper.map(savedAsset, AssetDto.class);
                // send message to kafka
                transactionSynchronizationUtil.executeAfterCommit(() -> assetProducer.sendAssetMessage(savedAsset, OperationType.PUT, MODULE));

                return ResponseEntity.ok(SingleResultDto.<AssetDto>builder().data(assetDto).build());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (BusinessException be) {
            throw be;
        }
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteAsset(String assetReference) {
        Asset asset = assetRepository.findByReferenceAndDeletedAtIsNull(assetReference).orElse(null);
        if (asset == null) {
            return ResponseEntity.notFound().build();
        }
        try {
            asset.setDeletedAt(new Date());
            contractActorAssetService.deleteContractActorAssetByAssetId(asset.getId());
            asset.getElements().forEach(element -> element.setDeletedAt(new Date()));
            assetRepository.save(asset);

            // send message to kafka
            transactionSynchronizationUtil.executeAfterCommit(() -> assetProducer.sendAssetMessage(asset, OperationType.DELETE, MODULE));

            return ResponseEntity.ok(new InformativeMessage(String.format(RESOURCE_DELETED_SUCCESSFULLY, asset.getId())));
        } catch (BusinessException be) {
            throw be;
        }
    }

    @Transactional
    public ResponseEntity<PatchResponseDto> patchAsset(String assetReference, PatchDto<PatchAssetDto> patchDto) {
        Asset asset = assetRepository.findByReferenceAndDeletedAtIsNull(assetReference)
                .orElseThrow(() -> new ResourcesNotFoundException(ASSET_NOT_FOUND, MODULE, MODULE));
        try {
            PatchResult<PatchAssetDto> patchResult = patchHelper.applyPatch(
                    new PatchAssetDto(asset),
                    patchDto.getData().getAttributes().getValidationGroups(),
                    patchDto.getData().getAttributes());
            Asset updatedAsset = new Asset(patchResult.getUpdated());
            updatedAsset.setId(asset.getId());
            updatedAsset.setReference(asset.getReference());
            updatedAsset.setCreatedAt(asset.getCreatedAt());
            updatedAsset.setTotalValue(asset.getTotalValue());

            updatedAsset.setPhase(phaseRepository
                    .findFirstByCode(patchResult.getUpdated().getPhaseCode())
                    .orElseThrow(() -> new ResourcesNotFoundException(PHASE_NOT_FOUND, PHASE, MODULE)));

            updatedAsset.setCompany(new Actor(actorRepository
                    .findIdByReference(patchResult.getUpdated().getBusinessReference())
                    .orElseThrow(() -> new ResourcesNotFoundException(COMPANY_NOT_FOUND, COMPANY, MODULE))));

            updatedAsset.setMilestone(milestoneRepository
                    .findFirstByCode(patchResult.getUpdated().getMilestoneCode())
                    .orElseThrow(() -> new ResourcesNotFoundException(MILESTONE_NOT_FOUND, "MILESTONE", MODULE)));

            updatedAsset.setElements(asset.getElements());
            updatedAsset.setModifiedAt(new Date());

            Asset savedAsset = assetRepository.save(updatedAsset);
            transactionSynchronizationUtil.executeAfterCommit(() -> assetProducer.sendAssetMessage(savedAsset, OperationType.PUT, MODULE));
            patchResult.getResponse().getData().put("id", savedAsset.getId());
            return ResponseEntity.ok(PatchResponseDto.builder()
                    .data(patchResult.getResponse().getData())
                    .build());
        } catch (IllegalAccessException e) {
            throw new TechnicalException(ERROR_UPDATING_ASSET, e.getMessage(), e.getClass().getName());
        }
    }
}
