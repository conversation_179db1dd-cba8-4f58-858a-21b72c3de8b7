package com.datatricks.assetmodule.service;

import com.datatricks.assetmodule.exception.ConflictException;
import com.datatricks.assetmodule.exception.ResourcesNotFoundException;
import com.datatricks.assetmodule.exception.TechnicalException;
import com.datatricks.assetmodule.exception.handler.InformativeMessage;
import com.datatricks.assetmodule.model.*;
import com.datatricks.assetmodule.model.Currency;
import com.datatricks.assetmodule.model.dto.*;
import com.datatricks.assetmodule.producer.AssetProducer;
import com.datatricks.assetmodule.repository.*;
import com.datatricks.assetmodule.utils.AssetUtils;
import com.datatricks.assetmodule.utils.JpaQueryFilters;
import com.datatricks.assetmodule.utils.TransactionSynchronizationUtil;
import com.datatricks.kafkacommondomain.enums.OperationType;
import com.datatricks.kafkacommondomain.model.ElementDraftDto;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.persistence.EntityManager;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service
@AllArgsConstructor
public class ElementService {
    private final FileSystemService fileSystemService;
    private final ElementRepository elementRepository;
    private final AssetRepository assetRepository;
    private final AmortizationRepository amortizationRepository;
    private final AddressRepository addressRepository;
    private final TaxRepository taxRepository;
    private final CurrencyRepository currencyRepository;
    private final CategoryRepository categoryRepository;
    private final ShippingRepository shippingRepository;
    private final ModelMapper modelMapper;
    private final AmortizationService amortizationService;
    private final ShippingService shippingService;
    private final PhaseRepository phaseRepository;
    private final MilestoneRepository milestoneRepository;
    private final ActorRepository actorRepository;
    private final ContractActorAssetRepository contractActorAssetRepository;
    private final AssetProducer assetProducer;
    private final TaxRateRepository taxRateRepository;
    private final TransactionSynchronizationUtil transactionSynchronizationUtil;
    private final EntityManager entityManager;
    private static final String MODULE = "ELEMENT";
    private static final String ELEMENT_NOT_FOUND = "Element not found";
    private static final String ELEMENT = "ELEMENT";
    private static final String ACTOR = "ACTOR";
    private static final String ASSET_NOT_FOUND = "Asset not found";
    private static final String ASSET = "ASSET";
    private static final String CLIENT_ADDRESS_CONFLICT = "The address provided is does not belongs to the client";
    private static final String PROVIDER_ADDRESS_CONFLICT = "The address provided is does not belongs to the provider";
    private static final String ADDRESS_NOT_BILLING = "The address provided is not a billing address";
    private static final String ADDRESS_NOT_FOUND = "Address not found";
    private static final String CLIENT_NOT_FOUND = "Client not found";
    private static final String PROVIDER_NOT_FOUND = "Provider not found";
    private static final String DELIVERED_MILESTONE_CODE = "DELIV";

    @Transactional
    public ResponseEntity<PageDto<ElementDto>> getElements(Long assetId, Map<String, String> params) {
        JpaQueryFilters<Element> filters = new JpaQueryFilters<>(params, Element.class);
        Specification<Element> assetIdSpec = (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("asset").get("id"), assetId);
        Page<Element> page = elementRepository.findAll(filters.getSpecification().and(assetIdSpec), filters.getPageable());

        List<ElementDto> filteredElements = page.stream()
                .map(element -> {
                    ElementDto dto = this.modelMapper.map(element, ElementDto.class);
                    dto.setAcquisitionValue(AssetUtils.calculateTTC(
                            element.getAcquisitionValueHt(),
                            BigDecimal.valueOf(element.getTaxRate().getRate())));
                    if (element.getImageName() != null && !element.getImageName().isBlank()) {
                        String presignedUrl = fileSystemService.getFile(element.getImageName())
                        .getBody().getData().getUrl();
                        dto.setImageUrl(presignedUrl);
                        dto.setImageName(element.getImageName());
                    }
                    return dto;
                })
                .toList();
        return ResponseEntity.ok(PageDto.<ElementDto>builder()
                .data(filteredElements)
                .total(page.getTotalElements()).build());
    }

    @Transactional
    public ResponseEntity<com.datatricks.kafkacommondomain.model.PageDto<ElementDraftDto>> getElementsByContractReference(String contractRef, Map<String, String> params) {

        params.put("PEqual_contract_reference", contractRef);
        JpaQueryFilters<Element> filters = new JpaQueryFilters<>(params, Element.class);
        Page<Element> page = elementRepository.findAll(filters.getSpecification(), filters.getPageable());

        List<ElementDraftDto> filteredElements = page.stream()
                .map(element -> {
                    ElementDraftDto map = this.modelMapper.map(element, ElementDraftDto.class);
                    map.setTaxRate(element.getTaxRate().getRate());
                    map.setAcquisitionValue(AssetUtils.calculateTTC(
                            element.getAcquisitionValueHt(),
                            BigDecimal.valueOf(element.getTaxRate().getRate())));
                    return map;
                })
                .toList();

        HashMap<String, String> metaParam = new HashMap<>();
        metaParam.put("PEqual_contract_reference", contractRef);
        metaParam.put("limit", "99999");
        JpaQueryFilters<Element> metaFilters = new JpaQueryFilters<>(metaParam, Element.class);
        Page<Element> meta = elementRepository.findAll(metaFilters.getSpecification(), metaFilters.getPageable());

        final long[] deliveredCount = {0};
        List<Element> metaElements = meta.stream()
                .peek(element -> {
                    if (DELIVERED_MILESTONE_CODE.equals(element.getMilestone().getCode())) {
                        deliveredCount[0]++;
                    }
                })
                .toList();

        ElementCountHeader header = ElementCountHeader.builder()
                .deliveredCount(deliveredCount[0])
                .nonDeliveredCount(metaElements.size() - deliveredCount[0])
                .total(metaElements.size())
                .build();

        ObjectNode metaObject = JsonNodeFactory.instance.objectNode();
        metaObject.putPOJO("element_count_header", header);
        return ResponseEntity.ok(com.datatricks.kafkacommondomain.model.PageDto.<ElementDraftDto>builder()
                .data(filteredElements)
                .meta(metaObject)
                .total(page.getTotalElements()).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementDto>> getElementById(Long assetId, Long elementId) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        ElementDto elementDto = this.modelMapper.map(element, ElementDto.class);
        elementDto.setAcquisitionValue(AssetUtils.calculateTTC(
                element.getAcquisitionValueHt(),
                BigDecimal.valueOf(element.getTaxRate().getRate())));
        ContractActorAsset contractActorAsset = contractActorAssetRepository.findByAssetIdAndDeletedAtIsNull(assetId);
        elementDto.getAsset().setContractActorAsset(modelMapper.map(contractActorAsset, SimplifiedContractActorAssetDto.class));
        if (element.getImageName() != null && !element.getImageName().isBlank()) {
            String presignedUrl = fileSystemService.getFile(element.getImageName())
                .getBody().getData().getUrl();
            elementDto.setImageUrl(presignedUrl);
            elementDto.setImageName(element.getImageName());
        }
        return ResponseEntity.ok(SingleResultDto.<ElementDto>builder().data(elementDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementBaseDto>> getElementBaseById(Long assetId, Long elementId) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        ElementBaseDto elementBaseDto = this.modelMapper.map(element, ElementBaseDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementBaseDto>builder().data(elementBaseDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementPaymentDto>> getElementPaymentById(Long assetId, Long elementId) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        ElementPaymentDto elementPaymentDto = this.modelMapper.map(element, ElementPaymentDto.class);
        elementPaymentDto.setAcquisitionValue(AssetUtils.calculateTTC(
                element.getAcquisitionValueHt(),
                BigDecimal.valueOf(element.getTaxRate().getRate())));
        return ResponseEntity.ok(SingleResultDto.<ElementPaymentDto>builder().data(elementPaymentDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementClientAddressDto>> getElementClientAddressById(Long assetId, Long elementId) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        ElementClientAddressDto elementAddressDto = this.modelMapper.map(element, ElementClientAddressDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementClientAddressDto>builder().data(elementAddressDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementProviderAddressDto>> getElementProviderAddressById(Long assetId, Long elementId) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        ElementProviderAddressDto elementAddressDto = this.modelMapper.map(element, ElementProviderAddressDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementProviderAddressDto>builder().data(elementAddressDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementShippingDto>> getElementShippingById(Long assetId, Long elementId) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        ElementShippingDto elementShippingDto = this.modelMapper.map(element, ElementShippingDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementShippingDto>builder().data(elementShippingDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementAmortizationDto>> getElementAmortizationById(Long assetId, Long elementId) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        ElementAmortizationDto elementAmortizationDto = this.modelMapper.map(element, ElementAmortizationDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementAmortizationDto>builder().data(elementAmortizationDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementExtraDto>> getElementExtraById(Long assetId, Long elementId) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        ElementExtraDto elementExtraDto = this.modelMapper.map(element, ElementExtraDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementExtraDto>builder().data(elementExtraDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementDto>> createElement(Long assetId, NewElementDto newElement) {

        Asset asset = assetRepository.findByIdAndDeletedAtIsNull(assetId).orElseThrow(
                () -> new ResourcesNotFoundException(ASSET_NOT_FOUND, ASSET, MODULE));
        Element element = new Element();
        element.setAsset(asset);
        List<Address> providerPossibleAddresses = addressRepository
                .findByActorIdReferenceAndActorIdDeletedAtIsNull(newElement.getSupplierReference()).stream()
                .filter(Address::getIsBilling)
                .toList();
        if (!providerPossibleAddresses.isEmpty()) {
            newElement.setProviderAddressReference(providerPossibleAddresses.getFirst().getReference());
        }
        buildElement(newElement, element);

        // This will be move to assign contract to asset
        // var contractActorAsset = contractActorAssetRepository.findByAssetIdAndDeletedAtIsNull(assetId);
//        if (contractActorAsset != null) {
//            element.setContract(contractActorAsset.getContract() != null ? new Contract(contractActorAsset.getContract().getId()) : null);
//        }

        if (newElement.getAmortization() != null) {
            try {
                SingleResultDto<AmortizationDto> result = amortizationService.createAmortization(assetId, element.getId(), newElement.getAmortization()).getBody();
                assert result != null;
                element.setAmortization(this.modelMapper.map(result.getData(), Amortization.class));
            } catch (Exception e) {
                throw new TechnicalException("Error creating amortization", e.getMessage(), MODULE);
            }
        } else {
            throw new ConflictException("Amortization is required", "AMORTIZATION_REQUIRED", "Amortization is required in order to create an element", MODULE);
        }
        // This will be remove maybe????
        if (newElement.getShipping() != null) {
            try {
                SingleResultDto<ShippingDto> result = shippingService.createShipping(assetId, element.getId(), newElement.getShipping()).getBody();
                assert result != null;
                element.setShipping(this.modelMapper.map(result.getData(), Shipping.class));
            } catch (Exception e) {
                throw new TechnicalException("Error creating shipping", e.getMessage(), MODULE);
            }
        }
        element.setCreatedAt(new Date());
        Element savedElement = elementRepository.saveAndFlush(element);
        Double sumOfElements = elementRepository.sumAcquisitionValueHtByAssetIdAndDeletedAtIsNull(assetId);
        asset.setTotalValue(sumOfElements);
        assetRepository.save(asset);
        entityManager.clear();

        Element elementAfterTrigger = elementRepository.findByIdAndDeletedAtIsNull(element.getId()).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));

        // send message to kafka
        transactionSynchronizationUtil.executeAfterCommit(() -> {
            assetProducer.sendAssetMessage(asset, OperationType.PUT, MODULE);
            assetProducer.sendElementMessage(elementAfterTrigger, OperationType.POST, MODULE);
        });

        ElementDto elementDto = this.modelMapper.map(savedElement, ElementDto.class);
        elementDto.setAcquisitionValue(AssetUtils.calculateTTC(elementDto.getAcquisitionValue(), BigDecimal.valueOf(element.getTaxRate().getRate())));
        return ResponseEntity.ok(SingleResultDto.<ElementDto>builder().data(elementDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementDto>> updateElement(Long assetId, Long elementId, NewElementDto element) {
        Element existingElement = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElse(null);
        if (existingElement != null) {
            buildElement(element, existingElement);
            buildElementAmortization(element, existingElement, assetId);
            existingElement.setModifiedAt(new Date());

            buildElementShipping(element, assetId, existingElement);
//            var contractActorAsset = contractActorAssetRepository.findByAssetIdAndDeletedAtIsNull(assetId);
//            if (contractActorAsset != null) {
//                existingElement.setContract(contractActorAsset.getContract() != null ? new Contract(contractActorAsset.getContract().getId()) : null);
//            }
            Asset asset = assetRepository.findByIdAndDeletedAtIsNull(assetId).orElseThrow(() -> new ResourcesNotFoundException(ASSET_NOT_FOUND, ASSET, MODULE));

            Element updatedElement = elementRepository.saveAndFlush(existingElement);
            Double sumOfElements = elementRepository.sumAcquisitionValueHtByAssetIdAndDeletedAtIsNull(assetId);
            asset.setTotalValue(sumOfElements);
            assetRepository.save(asset);

            // send message to kafka
            transactionSynchronizationUtil.executeAfterCommit(() -> {
                assetProducer.sendAssetMessage(asset, OperationType.PUT, MODULE);
                assetProducer.sendElementMessage(updatedElement, OperationType.PUT, MODULE);
            });

            ElementDto elementDto = this.modelMapper.map(updatedElement, ElementDto.class);
            elementDto.setAcquisitionValue(AssetUtils.calculateTTC(elementDto.getAcquisitionValue(), BigDecimal.valueOf(updatedElement.getTaxRate().getRate())));
            return ResponseEntity.ok(SingleResultDto.<ElementDto>builder().data(elementDto).build());
        } else {
            throw new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE);
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementBaseDto>> updateElementBase(
            Long assetId,
            Long elementId,
            NewElementBaseDto NewElementBaseDto) {
        Asset asset = assetRepository.findByIdAndDeletedAtIsNull(assetId).orElseThrow(
                () -> new ResourcesNotFoundException(ASSET_NOT_FOUND, ASSET, MODULE));
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        NewElementDto newElementDto = modelMapper.map(NewElementBaseDto, NewElementDto.class);
        buildElementBase(newElementDto, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);
        Double sumOfElements = elementRepository.sumAcquisitionValueHtByAssetIdAndDeletedAtIsNull(assetId);
        asset.setTotalValue(sumOfElements);
        assetRepository.save(asset);

        // send message to kafka
        transactionSynchronizationUtil.executeAfterCommit(() -> {
            assetProducer.sendAssetMessage(asset, OperationType.PUT, MODULE);
            assetProducer.sendElementMessage(updatedElement, OperationType.POST, MODULE);
        });

        ElementBaseDto elementBase = this.modelMapper.map(updatedElement, ElementBaseDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementBaseDto>builder().data(elementBase).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementPaymentDto>> updateElementPayment(
            Long assetId,
            Long elementId,
            NewElementPaymentDto newElementPaymentDto) {
        Asset asset = assetRepository.findByIdAndDeletedAtIsNull(assetId).orElseThrow(
                () -> new ResourcesNotFoundException(ASSET_NOT_FOUND, ASSET, MODULE));
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        NewElementDto newElementDto = modelMapper.map(newElementPaymentDto, NewElementDto.class);
        buildElementPayment(newElementDto, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);
        Double sumOfElements = elementRepository.sumAcquisitionValueHtByAssetIdAndDeletedAtIsNull(assetId);
        asset.setTotalValue(sumOfElements);
        assetRepository.save(asset);

        // send message to kafka
        transactionSynchronizationUtil.executeAfterCommit(() -> {
                    assetProducer.sendAssetMessage(asset, OperationType.PUT, MODULE);
                    assetProducer.sendElementMessage(updatedElement, OperationType.POST, MODULE);
        });
        ElementPaymentDto elementPaymentDto = this.modelMapper.map(updatedElement, ElementPaymentDto.class);
        elementPaymentDto.setAcquisitionValue(AssetUtils.calculateTTC(updatedElement.getAcquisitionValueHt(), BigDecimal.valueOf(updatedElement.getTaxRate().getRate())));
        return ResponseEntity.ok(SingleResultDto.<ElementPaymentDto>builder().data(elementPaymentDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementProviderAddressDto>> updateElementProviderAddress(
            Long assetId,
            Long elementId,
            NewElementProviderAddressDto newElementProviderAddressDto) {
        Asset asset = assetRepository.findByIdAndDeletedAtIsNull(assetId).orElseThrow(
                () -> new ResourcesNotFoundException(ASSET_NOT_FOUND, ASSET, MODULE));
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        NewElementDto newElementDto = modelMapper.map(newElementProviderAddressDto, NewElementDto.class);
        buildElementProviderAddress(newElementDto, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);
        Double sumOfElements = elementRepository.sumAcquisitionValueHtByAssetIdAndDeletedAtIsNull(assetId);
        asset.setTotalValue(sumOfElements);
        assetRepository.save(asset);

        // send message to kafka
        transactionSynchronizationUtil.executeAfterCommit(() -> {
                    assetProducer.sendAssetMessage(asset, OperationType.PUT, MODULE);
                    assetProducer.sendElementMessage(updatedElement, OperationType.POST, MODULE);
        });
        ElementProviderAddressDto elementAddress = this.modelMapper.map(updatedElement, ElementProviderAddressDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementProviderAddressDto>builder().data(elementAddress).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementClientAddressDto>> updateElementClientAddress(
            Long assetId,
            Long elementId,
            NewElementClientAddressDto newElementClientAddressDto) {
        Asset asset = assetRepository.findByIdAndDeletedAtIsNull(assetId).orElseThrow(
                () -> new ResourcesNotFoundException(ASSET_NOT_FOUND, ASSET, MODULE));
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        NewElementDto newElementDto = modelMapper.map(newElementClientAddressDto, NewElementDto.class);
        buildElementClientAddress(newElementDto, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);
        Double sumOfElements = elementRepository.sumAcquisitionValueHtByAssetIdAndDeletedAtIsNull(assetId);
        asset.setTotalValue(sumOfElements);
        assetRepository.save(asset);

        // send message to kafka
        transactionSynchronizationUtil.executeAfterCommit(() -> {
                    assetProducer.sendAssetMessage(asset, OperationType.PUT, MODULE);
                    assetProducer.sendElementMessage(updatedElement, OperationType.POST, MODULE);
        });
        ElementClientAddressDto elementAddress = this.modelMapper.map(updatedElement, ElementClientAddressDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementClientAddressDto>builder().data(elementAddress).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementShippingDto>> updateElementShipping(
            Long assetId,
            Long elementId,
            NewElementShippingDto newElementShippingDto) {
        Asset asset = assetRepository.findByIdAndDeletedAtIsNull(assetId).orElseThrow(
                () -> new ResourcesNotFoundException(ASSET_NOT_FOUND, ASSET, MODULE));
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        NewElementDto newElementDto = modelMapper.map(newElementShippingDto, NewElementDto.class);
        buildElementShipping(newElementDto, assetId, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);
        Double sumOfElements = elementRepository.sumAcquisitionValueHtByAssetIdAndDeletedAtIsNull(assetId);
        asset.setTotalValue(sumOfElements);
        assetRepository.save(asset);

        // send message to kafka
        transactionSynchronizationUtil.executeAfterCommit(() -> {
                    assetProducer.sendAssetMessage(asset, OperationType.PUT, MODULE);
                    assetProducer.sendElementMessage(updatedElement, OperationType.POST, MODULE);
        });
        ElementShippingDto elementShipping = this.modelMapper.map(updatedElement, ElementShippingDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementShippingDto>builder().data(elementShipping).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementAmortizationDto>> updateElementAmortization(
            Long assetId,
            Long elementId,
            NewElementAmortizationDto newElementAmortizationDto) {
        Asset asset = assetRepository.findByIdAndDeletedAtIsNull(assetId).orElseThrow(
                () -> new ResourcesNotFoundException(ASSET_NOT_FOUND, ASSET, MODULE));
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        NewElementDto newElementDto = modelMapper.map(newElementAmortizationDto, NewElementDto.class);
        buildElementAmortization(newElementDto, element, assetId);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);
        Double sumOfElements = elementRepository.sumAcquisitionValueHtByAssetIdAndDeletedAtIsNull(assetId);
        asset.setTotalValue(sumOfElements);
        assetRepository.save(asset);

        // send message to kafka
        transactionSynchronizationUtil.executeAfterCommit(() -> {
                    assetProducer.sendAssetMessage(asset, OperationType.PUT, MODULE);
                    assetProducer.sendElementMessage(updatedElement, OperationType.POST, MODULE);
        });
        ElementAmortizationDto elementAmortization = this.modelMapper.map(updatedElement, ElementAmortizationDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementAmortizationDto>builder().data(elementAmortization).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ElementExtraDto>> updateElementExtra(
            Long assetId,
            Long elementId,
            NewElementExtraDto newElementExtraDto) {
        Asset asset = assetRepository.findByIdAndDeletedAtIsNull(assetId).orElseThrow(
                () -> new ResourcesNotFoundException(ASSET_NOT_FOUND, ASSET, MODULE));
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId).orElseThrow(
                () -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        NewElementDto newElementDto = modelMapper.map(newElementExtraDto, NewElementDto.class);
        buildElementExtra(newElementDto, element);
        element.setModifiedAt(new Date());
        Element updatedElement = elementRepository.saveAndFlush(element);
        Double sumOfElements = elementRepository.sumAcquisitionValueHtByAssetIdAndDeletedAtIsNull(assetId);
        asset.setTotalValue(sumOfElements);
        assetRepository.save(asset);

        // send message to kafka
        transactionSynchronizationUtil.executeAfterCommit(() -> {
                    assetProducer.sendAssetMessage(asset, OperationType.PUT, MODULE);
                    assetProducer.sendElementMessage(updatedElement, OperationType.POST, MODULE);
        });
        ElementExtraDto elementExtra = this.modelMapper.map(updatedElement, ElementExtraDto.class);
        return ResponseEntity.ok(SingleResultDto.<ElementExtraDto>builder().data(elementExtra).build());
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteElement(Long assetId, Long elementId) {
        Element element = elementRepository.findByIdAndDeletedAtIsNull(elementId)
                .orElseThrow(() -> new ResourcesNotFoundException(ELEMENT_NOT_FOUND, ELEMENT, MODULE));
        element.setDeletedAt(new Date());
        element.getAmortization().setDeletedAt(new Date());
        elementRepository.saveAndFlush(element);

        Double sumOfElements = elementRepository.sumAcquisitionValueHtByAssetIdAndDeletedAtIsNull(assetId);

        // Until we switch to use reference instead of ID, I have to fetch the asset again
        Asset asset = assetRepository.findByIdAndDeletedAtIsNull(assetId).orElseThrow(() -> new ResourcesNotFoundException(ASSET_NOT_FOUND, ASSET, MODULE));
        asset.setTotalValue(sumOfElements);
        assetRepository.save(asset);

        // send message to kafka
        transactionSynchronizationUtil.executeAfterCommit(() -> {
            assetProducer.sendAssetMessage(asset, OperationType.PUT, MODULE);
            assetProducer.sendElementMessage(element, OperationType.DELETE, MODULE);
        });
        return ResponseEntity.ok(new InformativeMessage("Resource with ID " + elementId + " has been deleted successfully"));

    }

    @Transactional
    public ResponseEntity<PageDto<AddressDto>> getCustomerAddress(Long assetId) {
        if (!assetRepository.existsByIdAndDeletedAtIsNull(assetId)) {
            return ResponseEntity.notFound().build();
        }
        Asset asset = assetRepository.findByIdAndDeletedAtIsNull(assetId).orElseThrow(() -> new ResourcesNotFoundException(ASSET_NOT_FOUND, ASSET, MODULE));
        assert asset != null;
        List<Address> addresses = addressRepository.findAllByActorIdId(contractActorAssetRepository.findByAssetIdAndDeletedAtIsNull(assetId).getActor().getId());
        List<AddressDto> filteredAddresses = addresses.stream()
                .map(address -> this.modelMapper.map(address, AddressDto.class))
                .toList();
        return ResponseEntity.ok(PageDto.<AddressDto>builder().data(filteredAddresses).total(filteredAddresses.size()).build());
    }

    private void buildElement(NewElementDto newElement, Element element) {
        buildElementBase(newElement, element);
        buildElementPayment(newElement, element);
        if (newElement.getClientAddressReference() != null) {
            buildElementClientAddress(newElement, element);
        }
        if (newElement.getProviderAddressReference() != null) {
            buildElementProviderAddress(newElement, element);
        }
        buildElementExtra(newElement, element);
    }

    private void buildElementBase(NewElementDto newElement, Element element) {
        Phase phase = phaseRepository.findFirstByCode(newElement.getPhaseCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Phase not found", "PHASE", MODULE));
        Milestone milestone = milestoneRepository.findFirstByCode(newElement.getMilestoneCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Milestone not found", "MILESTONE", MODULE));
        Category category = categoryRepository.findFirstByCode(newElement.getCategoryCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Category not found", "CATEGORY", MODULE));
        Actor supplier = actorRepository.findByReferenceAndDeletedAtIsNull(newElement.getSupplierReference()).orElseThrow(
                () -> new ResourcesNotFoundException("Supplier not found", "SUPPLIER", MODULE));

        element.setLabel(newElement.getLabel());
        element.setExternalReference(newElement.getExternalReference());
        element.setSupplier(supplier);
        element.setOrderNumber(newElement.getOrderNumber());
        element.setBrand(newElement.getBrand());
        element.setCategory(category);
        element.setCondition(newElement.getCondition());
        element.setRegistration(newElement.getRegistration());
        element.setVin(newElement.getVin());
        element.setPhase(phase);
        element.setMilestone(milestone);
        element.setShortDescription(newElement.getShortDescription());
        element.setDescription(newElement.getDescription());
        element.setImageName(newElement.getImageName());
    }

    private void buildElementPayment(NewElementDto newElement, Element element) {
        TaxeRate taxRate = taxRateRepository.findByCodeAndDeletedAtIsNull(newElement.getTaxCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Tax rate not found", "TAX_RATE", MODULE));

        // calculate acquisition value after tax
        if (newElement.getAcquisitionValue().compareTo(
                AssetUtils.calculateTTC(newElement.getAcquisitionValueHt(), BigDecimal.valueOf(taxRate.getRate()))) == 0) {
            element.setAcquisitionValueHt(newElement.getAcquisitionValueHt());
        } else {
            throw new ResourcesNotFoundException("Acquisition value after tax is not correct", "ACQUISITION_VALUE_AFTER_TAX", MODULE);
        }
        Currency currency = currencyRepository.findFirstByCode(newElement.getCurrencyCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Currency not found", "CURRENCY", MODULE));
        element.setTax(taxRate.getTax());
        element.setTaxRate(taxRate);
        element.setCurrency(currency);
        element.setStartDate(newElement.getStartDate());
        element.setEndDate(newElement.getEndDate());
    }

    private void buildElementClientAddress(NewElementDto newElement, Element element) {
        if (newElement.getClientAddressReference() != null && !newElement.getClientAddressReference().isBlank()) {
            ContractActorAsset contractActorAsset = contractActorAssetRepository.findByAssetIdAndDeletedAtIsNull(element.getAsset().getId());
            if (contractActorAsset.getActor() == null) {
                throw new ResourcesNotFoundException(CLIENT_NOT_FOUND, "CLIENT_NOT_FOUND", MODULE);
            }

            List<Address> clientPossibleAddresses = addressRepository.findByActorIdReferenceAndActorIdDeletedAtIsNull(
                    contractActorAsset.getActor().getReference());
            if (clientPossibleAddresses.isEmpty()) {
                throw new ResourcesNotFoundException(ADDRESS_NOT_FOUND, "ADDRESS_NOT_FOUND", MODULE);
            }
            List<Address> clientAddress = clientPossibleAddresses.stream()
                    .filter(address -> address.getReference().equals(newElement.getClientAddressReference()))
                    .toList();
            if (clientAddress.isEmpty()) {
                throw new ConflictException(CLIENT_ADDRESS_CONFLICT, "CLIENT_ADDRESS_CONFLICT", CLIENT_ADDRESS_CONFLICT, MODULE);
            }
            Address billingAddress = clientAddress.stream()
                    .filter(Address::getIsBilling)
                    .findFirst()
                    .orElseThrow(() -> new ConflictException(ADDRESS_NOT_BILLING, "ADDRESS_NOT_BILLING", ADDRESS_NOT_BILLING, MODULE));

            element.setClientAddress(billingAddress);
        }

        element.setCustomerAddressAssignmentDate(newElement.getCustomerAddressAssignmentDate());
    }

    private void buildElementProviderAddress(NewElementDto newElement, Element element) {
        if (newElement.getProviderAddressReference() != null && !newElement.getProviderAddressReference().isBlank()) {
            if (element.getSupplier() == null) {
                throw new ResourcesNotFoundException(PROVIDER_NOT_FOUND, "PROVIDER_NOT_FOUND", MODULE);
            }

            List<Address> providerPossibleAddresses = addressRepository.findByActorIdReferenceAndActorIdDeletedAtIsNull(
                    element.getSupplier().getReference());
            if (providerPossibleAddresses.isEmpty()) {
                throw new ResourcesNotFoundException(ADDRESS_NOT_FOUND, "ADDRESS_NOT_FOUND", MODULE);
            }
            List<Address> providerAddress = providerPossibleAddresses.stream()
                    .filter(address -> address.getReference().equals(newElement.getProviderAddressReference()))
                    .toList();
            if (providerAddress.isEmpty()) {
                throw new ConflictException(PROVIDER_ADDRESS_CONFLICT, "PROVIDER_ADDRESS_CONFLICT", PROVIDER_ADDRESS_CONFLICT, MODULE);
            }
            Address billingAddress = providerAddress.stream()
                    .filter(Address::getIsBilling)
                    .findFirst()
                    .orElseThrow(() -> new ConflictException(ADDRESS_NOT_BILLING, "ADDRESS_NOT_BILLING", ADDRESS_NOT_BILLING, MODULE));

            element.setProviderAddress(billingAddress);
        }
        element.setSupplierAddressAssignmentDate(newElement.getSupplierAddressAssignmentDate());
    }

    private void buildElementExtra(NewElementDto newElement, Element element) {
        element.setLicense(newElement.getLicense());
        element.setModel(newElement.getModel());
    }

    private void buildElementAmortization(NewElementDto newElement, Element element, Long assetId) {
        if (newElement.getAmortization() != null) {
            if (!amortizationRepository.existsByIdAndDeletedAtIsNull(newElement.getAmortization().getId())) {
                throw new ResourcesNotFoundException("Amortization not found", "AMORTIZATION", MODULE);
            }
            amortizationService.updateAmortization(
                    assetId,
                    element.getId(),
                    newElement.getAmortization().getId(),
                    newElement.getAmortization());
        }
    }

    private void buildElementShipping(NewElementDto newElement, Long assetId, Element element) {
        if (newElement.getShipping() != null) {
            if (!shippingRepository.existsByIdAndDeletedAtIsNull(newElement.getShipping().getId())) {
                ResponseEntity<SingleResultDto<ShippingDto>> newShipping = shippingService.createShipping(
                        assetId,
                        element.getId(),
                        newElement.getShipping());
                if (newShipping.getBody() != null) {
                    element.setShipping(modelMapper.map(newShipping.getBody().getData(), Shipping.class));
                }
            } else {
                ResponseEntity<SingleResultDto<ShippingDto>> updatedShipping = shippingService.updateShipping(
                        assetId,
                        element.getId(),
                        newElement.getShipping().getId(),
                        newElement.getShipping());
                if (updatedShipping.getBody() != null) {
                    element.setShipping(modelMapper.map(updatedShipping.getBody().getData(), Shipping.class));
                }
            }
        }
    }
}
