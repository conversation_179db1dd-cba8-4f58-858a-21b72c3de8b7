package com.datatricks.assetmodule.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NewElementExtraDto implements PageableDto {

    private Long id;

    @JsonProperty("model")
    @Schema(description = "Model of the element", example = "BMW")
    private String model;

    @JsonProperty("license")
    @Schema(description = "License of the element", example = "AA-123-BB")
    private String license;
}
