package com.datatricks.assetmodule.model.response;

import com.datatricks.assetmodule.model.dto.PageableDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActorResponse implements PageableDto {
    private Long id;
    @JsonProperty("reference")
    private String reference;
    @JsonProperty("external_reference")
    private String externalReference;

}
