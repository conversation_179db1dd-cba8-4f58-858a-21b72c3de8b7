package com.datatricks.assetmodule.model.dto;

import com.datatricks.assetmodule.model.ActorTypes;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SimplifiedActorDto implements PageableDto{
    @JsonProperty("id")
    @NotNull(message = "actor.id: Id is required")
    private Long id;

    @JsonProperty("reference")
    private String reference;

    @JsonProperty("external_reference")
    private String externalReference;

    @JsonProperty("country")
    private CountryDto country;

    @JsonProperty("short_name")
    private String shortName;

    @JsonProperty("name")
    @NotBlank(message = "name:please provide a name")
    private String name;

    @JsonProperty("type")
    private ActorTypes type;

    @JsonProperty("national_identity")
    private String nationalIdentity;
}
