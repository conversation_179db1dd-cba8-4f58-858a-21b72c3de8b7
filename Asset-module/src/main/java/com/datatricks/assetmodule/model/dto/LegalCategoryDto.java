package com.datatricks.assetmodule.model.dto;

import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LegalCategoryDto implements PageableDto {
    @NotNull(message = "legal_category.id:please provide a legal category id")
    @Schema(description = "Legal category identifier", example = "7")
    @JsonApiId
    private Long id;

    @JsonApiType
    private String myType = "legal_category";

    @Schema(description = "Legal category code", example = "1700", type = "string")
    private String code;

    @Schema(description = "Legal category label", example = "Agent commercial", type = "string")
    private String label;
}
