package com.datatricks.assetmodule.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AssetDto implements PageableDto {
    private Long id;

    @JsonProperty("reference")
    @Schema(description = "Reference", example = "PA00001SB")
    private String reference;

    @NotNull(message = "orderNumber: Order number is required")
    @JsonProperty("order_number")
    private int orderNumber;

    @JsonProperty("description")
    private String description;

    @NotBlank(message = "label: Label is required")
    @JsonProperty("label")
    private String label;

    @JsonProperty("phase")
    @NotNull(message = "phase: Phase is required")
    @Valid
    private PhaseDto phase;

    @JsonProperty("milestone")
    @NotNull(message = "milestone: Milestone is required")
    @Valid
    private MilestoneDto milestone;

    @JsonProperty("country")
    @NotNull(message = "country: Country is required")
    @Valid
    private CountryDto country;

    @JsonProperty("company")
    @NotNull(message = "company: Company is required")
    @Valid
    private SimplifiedActorDto company;

    @JsonProperty("contract_actor_asset")
    @NotNull(message = "contract_actor_asset: Contract actor asset is required")
    @Valid
    private SimplifiedContractActorAssetDto contractActorAsset;

    @JsonProperty("supplier")
    @NotNull(message = "supplier: Supplier is required")
    @Valid
    private SimplifiedActorDto supplier;

    @JsonProperty("total_price")
    private double totalValue;

}
