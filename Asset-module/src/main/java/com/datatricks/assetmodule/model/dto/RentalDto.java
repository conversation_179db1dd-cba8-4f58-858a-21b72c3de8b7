package com.datatricks.assetmodule.model.dto;

import com.datatricks.assetmodule.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor

public class RentalDto implements PageableDto {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("analytic_input")
    private String allocationCode;

    @JsonProperty("nature")
    private String type;

    @JsonProperty("type_arrangement")
    private String arrangementType;

    @JsonProperty("title")
    private String title;

    @JsonProperty("financing_amount")
    private Double originalAmount;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @JsonProperty("nominal_rate")
    private Double nominalRate;

    @JsonProperty("equivalent_rate")
    private Double equivalentRate;

    @JsonProperty("residual_value")
    private Double residualValue;

    @JsonProperty("vr")
    private Double vr;

    @JsonProperty("is_separate_billing")
    private Boolean separateInvoice;

    @JsonProperty("is_suspended_billing")
    private Boolean suspendedInvoice;

    @JsonProperty("autoExtension")
    private Boolean mobileExtension;

    @JsonProperty("tax")
    private String tax;

	@JsonProperty("status")
	private String status;

    @JsonProperty("timetable_id")
    private Long timetableId;

    @JsonProperty("contract_actor_id")
    private Long contractActorId;
}
