package com.datatricks.assetmodule.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ElementCountHeader {

    @JsonProperty("delivered_count")
    private long deliveredCount;

    @JsonProperty("non_delivered_count")
    private long nonDeliveredCount;

    @JsonProperty("total_count")
    private long total;
}
