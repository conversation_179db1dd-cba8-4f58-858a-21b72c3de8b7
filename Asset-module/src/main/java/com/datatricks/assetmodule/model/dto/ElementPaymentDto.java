package com.datatricks.assetmodule.model.dto;

import com.datatricks.assetmodule.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ElementPaymentDto implements PageableDto {

    private Long id;

    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @NotNull(message = "acquisition_value_HT: acquisition value HT is required")
    @JsonProperty("acquisition_value_HT")
    @Schema(description = "Acquisition value HT of the element", example = "1000")
    private BigDecimal acquisitionValueHt;

    @NotNull(message = "acquisition_value: acquisition value is required")
    @JsonProperty("acquisition_value")
    @Schema(description = "Acquisition value of the element", example = "1200")
    private BigDecimal acquisitionValue;

    @NotNull(message = "tax: tax is required")
    @JsonProperty("tax")
    private TaxDto tax;

    @JsonProperty("currency")
    private CurrencyDto currency;
}
