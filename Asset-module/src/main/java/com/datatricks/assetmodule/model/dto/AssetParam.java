package com.datatricks.assetmodule.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AssetParam implements PageableDto {
    @JsonIgnore
    private Long id;

    @JsonProperty("order_number")
    @Schema(description = "Order number", example = "1")
    private int orderNumber;

    @JsonProperty("description")
    @Schema(description = "Description", example = "Description")
    private String description;

    @JsonProperty("label")
    private String label;

    @JsonProperty("phase_code")
    @Schema(description = "Phase code", example = "INI")
    private String phaseCode;

    @JsonProperty("milestone_code")
    @Schema(description = "Milestone code", example = "EXTEND")
    private String milestoneCode;

    @JsonProperty("category_code")
    @Schema(description = "Category code", example = "266000")
    private String categoryCode;

    @JsonProperty("country_code")
    @Schema(description = "Country code", example = "FR")
    private String countryCode;

    @JsonProperty("business_reference")
    @Schema(description = "Business reference", example = "ACT_6546161")
    private String businessReference;

    @JsonProperty("supplier_reference")
    @Schema(description = "Supplier reference", example = "ACT_6546161")
    private String supplierReference;
}
