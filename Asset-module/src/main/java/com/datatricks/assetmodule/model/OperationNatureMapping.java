package com.datatricks.assetmodule.model;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "operation_nature_mapping")
public class OperationNatureMapping {
    @Id
    private Long id;

    @Column(name = "operation_nature_code", unique = true)
    private String operationNatureCode;

    @ManyToOne
    @JoinColumn(name = "operation_code", referencedColumnName = "code")
    private Operation operation;

    @ManyToOne
    @JoinColumn(name = "nature_code", referencedColumnName = "code")
    private Nature nature;

    @Column(name = "nature_status")
    private Boolean natureStatus;

    public OperationNatureMapping(Long operationNatureMappingId) {
        this.id = operationNatureMappingId;
    }

    public OperationNatureMapping(String operationNatureMappingCode) {
        this.operationNatureCode = operationNatureMappingCode;
    }
}
