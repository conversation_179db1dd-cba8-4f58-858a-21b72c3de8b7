package com.datatricks.assetmodule.model.dto;

import com.datatricks.assetmodule.model.ElementCondition;
import com.datatricks.assetmodule.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NewElementDto implements PageableDto{

    private Long id;

    @JsonProperty("serial_number")
    @Schema(description = "Serial number of the element", example = "PA00001SB802010EL00001")
    private String serialNumber;

    @JsonProperty("external_reference")
    @Schema(description = "External reference of the element", example = "EXT_139842293")
    private String externalReference;

    @NotBlank(message = "label: Label is required")
    @JsonProperty("label")
    @Schema(description = "Label of the element", example = "Element")
    private String label;

    @NotNull(message = "order_number: Order number is required")
    @JsonProperty("order_number")
    @Schema(description = "Order number of the element", example = "139842293")
    private long orderNumber;

    @JsonProperty("brand")
    @Schema(description = "Brand of the element", example = "Volkswagen")
    private String brand;

    @JsonProperty("model")
    @Schema(description = "Model of the element", example = "BMW")
    private String model;

    @JsonProperty("description")
    @Schema(description = "Description of the element", example = "Car")
    private String description;

    @JsonProperty("license")
    @Schema(description = "License of the element", example = "AA-123-BB")
    private String license;

    @JsonProperty("vin")
    @Schema(description = "VIN of the element", example = "WBA3B9C50DF587798")
    private String vin;

    @JsonProperty("short_description")
    @Schema(description = "Short description of the element", example = "Car")
    private String shortDescription;

    @NotNull(message = "acquisition_value_HT: acquisition value HT is required")
    @JsonProperty("acquisition_value_HT")
    @Schema(description = "Acquisition value HT of the element", example = "1000")
    private BigDecimal acquisitionValueHt;

    @NotNull(message = "acquisition_value: acquisition value is required")
    @JsonProperty("acquisition_value")
    @Schema(description = "Acquisition value of the element", example = "1200")
    private BigDecimal acquisitionValue;

    @JsonProperty("amortization")
    @NotNull(message = "amortization: amortization is required")
    @Schema(description = "Amortization of the element", type = "object")
    private AmortizationDto amortization;

    @JsonProperty("asset_reference")
    @Schema(description = "Asset of the element", example = "PA00001SB")
    private String assetReference;

    @JsonProperty("client_address_reference")
    @Schema(description = "Client address of the element", example = "ADDR_ee2bb21fee4b")
    private String clientAddressReference;

    @JsonProperty("provider_address_reference")
    @Schema(description = "Provider address of the element", example = "ADDR_ee2bb21fee4b")
    private String providerAddressReference;

    @NotNull(message = "tax: tax is required")
    @JsonProperty("tax_code")
    @Schema(description = "Tax of the element", example = "TVA")
    private String taxCode;

    @NotNull(message = "phase: phase is required")
    @JsonProperty("phase_code")
    @Schema(description = "Phase code of the element", example = "INI")
    private String phaseCode;

    @NotNull(message = "milestone: milestone is required")
    @JsonProperty("milestone_code")
    private String milestoneCode;

    @JsonProperty("condition")
    @NotNull(message = "condition: condition is required")
    @Schema(description = "Condition of the element", example = "NEW, OLD or REFURBISHED", allowableValues = {"NEW", "OLD", "REFURBISHED"})
    private ElementCondition condition;

    @NotNull(message = "supplier: supplier is required")
    @JsonProperty("supplier_reference")
    @Schema(description = "Supplier reference of the element", example = "ACT_479051855")
    private String supplierReference;

    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start date of the element", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End date of the element", example = "2021-01-01")
    private LocalDate endDate;

    @JsonProperty("customer_address_assignment_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Customer address assignment date of the element", example = "2021-01-01")
    private Date customerAddressAssignmentDate;

    @JsonProperty("supplier_address_assignment_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Supplier address assignment date of the element", example = "2021-01-01")
    private Date supplierAddressAssignmentDate;

    @JsonProperty("currency_code")
    @Schema(description = "Currency code of the element", example = "EUR")
    private String currencyCode;

    @NotNull(message = "category: category is required")
    @JsonProperty("category_code")
    @Schema(description = "Category code of the element", example = "282218")
    private String categoryCode;

    @JsonProperty("shipping")
    private ShippingDto shipping;

    @JsonProperty("registration")
    private String registration;

    @JsonProperty("image_name")
    @Schema(description = "S3 bucket image name")
    private String imageName;

    @JsonProperty("custom_fields")
    private List<Object> customFields;
}
