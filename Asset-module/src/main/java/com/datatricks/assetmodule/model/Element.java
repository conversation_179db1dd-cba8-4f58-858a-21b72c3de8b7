package com.datatricks.assetmodule.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;
import com.datatricks.assetmodule.utils.DateUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "elements")
public class Element extends BaseEntity {

    @Id
    @GeneratedValue
    @JsonProperty("id")
    private Long id;

    @Column(name = "serial_number", unique = true)
    @JsonProperty("serial_number")
    private String serialNumber;

    @Column(name = "external_reference")
    @JsonProperty("external_reference")
    private String externalReference;

    @Column(name = "label")
    @JsonProperty("label")
    private String label;

    @Column(name = "order_number")
    @JsonProperty("order_number")
    private long orderNumber;

    @Column(name = "brand")
    @JsonProperty("brand")
    private String brand;

    @Column(name = "model")
    @JsonProperty("model")
    private String model;

    @Column(name = "description")
    @JsonProperty("description")
    private String description;

    @Column(name = "license")
    @JsonProperty("license")
    private String license;

    @Column(name = "vin")
    @JsonProperty("vin")
    private String vin;

    @Column(name = "customer_address_assignment_date")
    @JsonProperty("customer_address_assignment_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date customerAddressAssignmentDate;

    @Column(name = "supplier_address_assignment_date")
    @JsonProperty("supplier_address_assignment_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date supplierAddressAssignmentDate;

    @Column(name = "short_description")
    @JsonProperty("short_description")
    private String shortDescription;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @Column(name = "acquisition_value_HT")
    @JsonProperty("acquisition_value_HT")
    private BigDecimal acquisitionValueHt;

    @Column(name = "condition")
    @Enumerated(EnumType.STRING)
    @JsonProperty("condition")
    private ElementCondition condition;

    @Column(name = "registration")
    @JsonProperty("registration")
    private String registration;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "amortization_id")
    @JsonProperty("amortization")
    private Amortization amortization;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "asset_id")
    @JsonProperty("asset")
    private Asset asset;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_address_id")
    @JsonProperty("client_address")
    private Address clientAddress;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "provider_address_id")
    @JsonProperty("provider_address")
    private Address providerAddress;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tax_id")
    @JsonProperty("tax")
    private Tax tax;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tax_rate_id")
    @JsonProperty("tax_rate")
    private TaxeRate taxRate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "phase_id")
    @JsonProperty("phase")
    private Phase phase;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "milestone_id")
    @JsonProperty("milestone")
    private Milestone milestone;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "currency_id")
    @JsonProperty("currency")
    private Currency currency;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id")
    @JsonProperty("category")
    private Category category;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shipping_id")
    @JsonProperty("shipping")
    private Shipping shipping;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "supplier_id")
    @JsonProperty("supplier")
    private Actor supplier;

    @Column(name = "image_name")
    @JsonProperty("image_name")
    private String imageName;
}

