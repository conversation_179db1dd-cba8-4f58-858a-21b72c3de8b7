package com.datatricks.assetmodule.model;

import com.datatricks.assetmodule.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.util.Date;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "addresses")
public class Address extends BaseEntity {

    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @Column(name = "is_billing")
    @JsonProperty("is_billing")
    private Boolean isBilling;

    @Column(name = "is_delivery")
    @JsonProperty("is_delivery")
    private Boolean isDelivery;

    @Column(name = "summary")
    @JsonProperty("summary")
    private String summary;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date endDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actorId")
    private Actor actorId;

    public Address(Long id) {
        this.id = id;
    }
}
