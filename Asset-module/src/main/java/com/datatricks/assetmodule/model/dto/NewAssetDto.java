package com.datatricks.assetmodule.model.dto;

import com.datatricks.assetmodule.model.Asset;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NewAssetDto implements PageableDto {
    @JsonIgnore
    private Long id;

    @NotNull(message = "orderNumber: Order number is required")
    @JsonProperty("order_number")
    @Schema(description = "Order number", example = "1")
    private int orderNumber;

    @JsonProperty("description")
    @Schema(description = "Description", example = "Description")
    private String description;

    @NotBlank(message = "label: Label is required")
    @JsonProperty("label")
    @Schema(description = "Label", example = "Label")
    private String label;

    @JsonProperty("phase_code")
    @NotNull(message = "phase_code: Phase is required")
    @Schema(description = "Phase code", example = "INI")
    private String phaseCode;

    @JsonProperty("milestone_code")
    @NotNull(message = "milestone_code: Milestone is required")
    @Schema(description = "Milestone code", example = "EXTEND")
    private String milestoneCode;

    @JsonProperty("business_reference")
    @NotNull(message = "business_reference: Company is required")
    @Schema(description = "Business reference", example = "ACT_6546161")
    private String businessReference;

    @JsonProperty("contract_actor_asset")
    @Schema(description = "Contract actor asset", type = "object")
    private NewContractActorAssetDto contractActorAsset;

    @JsonProperty("supplier_reference")
    @Schema(description = "Supplier reference", example = "ACT_6546161")
    private String supplierReference;

    public NewAssetDto(Asset asset) {
        this.orderNumber = asset.getOrderNumber();
        this.description = asset.getDescription();
        this.label = asset.getLabel();
        this.phaseCode = asset.getPhase().getCode();
        this.milestoneCode = asset.getMilestone().getCode();
        this.businessReference = asset.getCompany().getReference();
    }
}
