package com.datatricks.assetmodule.model.dto;

import com.datatricks.assetmodule.model.ElementCondition;
import com.datatricks.assetmodule.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ElementDto implements PageableDto {

    private Long id;

    @JsonProperty("serial_number")
    @Schema(description = "Serial number of the element", example = "PA00001SB802010EL00001")
    private String serialNumber;

    @JsonProperty("external_reference")
    @Schema(description = "External reference of the element", example = "EXT_139842293")
    private String externalReference;

    @NotBlank(message = "label: Label is required")
    @JsonProperty("label")
    @Schema(description = "Label of the element", example = "Element")
    private String label;

    @NotNull(message = "order_number: Order number is required")
    @JsonProperty("order_number")
    @Schema(description = "Order number of the element", example = "139842293")
    private long orderNumber;

    @JsonProperty("brand")
    @Schema(description = "Brand of the element", example = "Volkswagen")
    private String brand;

    @JsonProperty("model")
    @Schema(description = "Model of the element", example = "BMW")
    private String model;

    @JsonProperty("description")
    @Schema(description = "Description of the element", example = "Car")
    private String description;

    @JsonProperty("license")
    @Schema(description = "License of the element", example = "AA-123-BB")
    private String license;

    @JsonProperty("vin")
    @Schema(description = "VIN of the element", example = "WBA3B9C50DF587798")
    private String vin;

    @JsonProperty("short_description")
    @Schema(description = "Short description of the element", example = "Car")
    private String shortDescription;

    @NotNull(message = "acquisition_value_HT: acquisition value HT is required")
    @JsonProperty("acquisition_value_HT")
    @Schema(description = "Acquisition value HT of the element", example = "1000")
    private BigDecimal acquisitionValueHt;

    @NotNull(message = "acquisition_value: acquisition value is required")
    @JsonProperty("acquisition_value")
    @Schema(description = "Acquisition value of the element", example = "1200")
    private BigDecimal acquisitionValue;

    @JsonProperty("amortization")
    @NotNull(message = "amortization: amortization is required")
    @Schema(description = "Amortization of the element", type = "object")
    private AmortizationDto amortization;

    @JsonProperty("asset")
    @Schema(description = "Asset of the element", type = "object")
    private AssetDto asset;

    @JsonProperty("client_address")
    @NotNull(message = "client_address: client address is required")
    private AddressDto clientAddress;

    @JsonProperty("provider_address")
    @NotNull(message = "provider_address: provider address is required")
    private AddressDto providerAddress;

    @NotNull(message = "tax: tax is required")
    @JsonProperty("tax")
    private TaxDto tax;

    @NotNull(message = "phase: phase is required")
    @JsonProperty("phase")
    private PhaseDto phase;

    @NotNull(message = "milestone: milestone is required")
    @JsonProperty("milestone")
    private MilestoneDto milestone;

    @JsonProperty("condition")
    @NotNull(message = "condition: condition is required")
    private ElementCondition condition;

    @NotNull(message = "supplier: supplier is required")
    @JsonProperty("supplier")
    private SimplifiedActorDto supplier;

    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @JsonProperty("customer_address_assignment_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date customerAddressAssignmentDate;

    @JsonProperty("supplier_address_assignment_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date supplierAddressAssignmentDate;

    @JsonProperty("currency")
    private CurrencyDto currency;

    @NotNull(message = "category: category is required")
    @JsonProperty("category")
    private CategoryDto category;

    @JsonProperty("shipping")
    private ShippingDto shipping;

    @JsonProperty("registration")
    private String registration;

    @JsonProperty("image_url")
    private String imageUrl;

    @JsonProperty("image_name")
    private String imageName;
}
