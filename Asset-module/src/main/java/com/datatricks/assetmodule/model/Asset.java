package com.datatricks.assetmodule.model;

import com.datatricks.assetmodule.model.dto.NewAssetDto;
import com.datatricks.assetmodule.model.dto.PatchAssetDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.util.Set;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "assets")
public class Asset extends BaseEntity {

    @Id
    @GeneratedValue
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @Column(name = "label")
    @JsonProperty("label")
    private String label;

    @Column(name = "order_number")
    @JsonProperty("order_number")
    private int orderNumber;

    @Column(name = "description")
    @JsonProperty("description")
    private String description;

    @Column(name = "total_value")
    @JsonProperty("total_value")
    private double totalValue;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "country_id")
    @JsonProperty("country")
    private Country country;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "milestone_id")
    @JsonProperty("milestone")
    private Milestone milestone;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "phase_id")
    @JsonProperty("phase")
    private Phase phase;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "company_id")
    @JsonProperty("company")
    private Actor company;

    @OneToMany(mappedBy = "asset")
    @JsonProperty("elements")
    private Set<Element> elements;

    @OneToMany(mappedBy = "asset")
    private Set<ContractActorAsset> contractActorAssets;

    public Asset(Long assetId) {
        this.id = assetId;
    }

    public Asset(NewAssetDto updated) {
        this.orderNumber = updated.getOrderNumber();
        this.label = updated.getLabel();
        this.description = updated.getDescription();
    }
    public Asset(PatchAssetDto updated) {
        this.orderNumber = updated.getOrderNumber();
        this.label = updated.getLabel();
        this.description = updated.getDescription();
    }
}
