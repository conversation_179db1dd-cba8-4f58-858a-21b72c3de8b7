package com.datatricks.assetmodule.controller;

import com.datatricks.assetmodule.model.inject.AssetInjectDto;
import com.datatricks.assetmodule.repository.AssetRepository;
import com.datatricks.assetmodule.service.AssetInjectService;
import com.datatricks.assetmodule.service.InclusionService;
import com.datatricks.assetmodule.utils.AssetUtils;
import com.datatricks.assetmodule.utils.GenericJsonApiModelAssembler;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.transaction.Transactional;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.hateoas.EntityModel;
import org.springframework.hateoas.PagedModel;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import static com.toedter.spring.hateoas.jsonapi.MediaTypes.JSON_API_VALUE;

@RestController
@RequestMapping("/api")
@Tag(name = "Asset Inject", description = "Asset Inject API")
public class AssetInjectController {
    private final AssetRepository repository;
    private final GenericJsonApiModelAssembler genericAssembler;
    private final InclusionService inclusionService;
    private final AssetInjectService injectService;

    public AssetInjectController(AssetRepository repository,
                                 GenericJsonApiModelAssembler genericAssembler,
                                 InclusionService inclusionService,
                                 AssetInjectService injectService) {
        this.repository = repository;
        this.genericAssembler = genericAssembler;
        this.inclusionService = inclusionService;
        this.injectService = injectService;

    }

    @PostMapping(value = "/v1/assets/inject", consumes = JSON_API_VALUE, produces = JSON_API_VALUE)
    @Transactional
    public ResponseEntity<RepresentationModel<?>> injectAssets(@RequestBody PagedModel<EntityModel<AssetInjectDto>> asset) {
        List<AssetInjectDto> assetList = new ArrayList<>();
        try {
            asset.getContent().forEach(assetDto -> {
                assetList.add(injectService.injectAsset(assetDto.getContent()).getData());
            });
            return ResponseEntity.ok(injectService.getResponses(assetList));
        } catch (Exception e) {
            throw AssetUtils.handleException(e);
        }
    }
}