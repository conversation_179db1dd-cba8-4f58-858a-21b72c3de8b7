const redis = require("redis");
const config = require("../config/app-config");

class RedisService {
    constructor() {
        this.publisher = this.createClient();
        this.subscriber = this.createClient();
        this.connect();
    }

    createClient() {
        const client = redis.createClient({
            url: `redis://${config.redis.host}:${config.redis.port}`
        });

        client.on("error", (err) => console.log("Redis Client Error", err));
        client.on("reconnecting", () => console.log("Redis Client reconnecting"));
        client.on("ready", () => console.log("Redis Client ready"));

        return client;
    }

    async connect() {
        await this.publisher.connect();
        await this.subscriber.connect();
    }

    async publish(channel, message) {
        try {
            await this.publisher.publish(channel, JSON.stringify(message));
            console.log(`Message published to channel ${channel}`);
            // Always queue the message, even if publish succeeds
            await this.queueMessage(channel, message);
        } catch (error) {
            console.error("Error publishing message:", error);
            await this.queueMessage(channel, message);
        }
    }

    async queueMessage(channel, message) {
        try {
            await this.publisher.rPush(`queue:${channel}`, JSON.stringify(message));
            console.log(`Message queued to channel ${channel}`);
        } catch (error) {
            console.error("Error queuing message:", error);
        }
    }

    async processQueue(channel) {
        try {
            let message;
            // Process messages in a loop
            while ((message = await this.publisher.lPop(`queue:${channel}`)) !== null) {
                try {
                    const parsedMessage = JSON.parse(message);
                    await this.publish(channel, parsedMessage);
                    // Add a delay to avoid rapid re-queuing
                    await new Promise(resolve => setTimeout(resolve, 5000));
                } catch (parseError) {
                    console.error("Error parsing message from queue:", parseError);
                    // Optionally handle invalid JSON (e.g., move to an error queue or log for manual inspection)
                }
            }
            console.log(`All queued messages processed for channel ${channel}`);
        } catch (error) {
            console.error("Error processing message queue:", error);
        }
    }
    

    async subscribe(channel, callback) {
        try {
            await this.subscriber.subscribe(channel, (message) => {
                try {
                    const parsedMessage = JSON.parse(message);
                    callback(parsedMessage);
                } catch (error) {
                    console.error("Error processing received message:", error);
                }
            });
            console.log(`Subscribed to channel ${channel}`);
            
            // Process queue after subscribing
            await this.processQueue(channel);
        } catch (error) {
            console.error("Error subscribing to channel:", error);
        }
    }

    async moveToProcessingQueue(mainQueue, processingQueue, notificationData) {
        try {
            await this.publisher.rPush(`queue:${processingQueue}`, JSON.stringify(notificationData));
            await this.publisher.lRem(`queue:${mainQueue}`, 0, JSON.stringify(notificationData));
        } catch (error) {
            console.error("Error moving message to processing queue:", error);
        }
    }

    async requeueMessage(processingQueue, mainQueue, notificationData) {
        try {
            await this.publisher.rPush(`queue:${mainQueue}`, JSON.stringify(notificationData));
            await this.publisher.lRem(`queue:${processingQueue}`, 0, JSON.stringify(notificationData));
        } catch (error) {
            console.error("Error re-queuing message:", error);
        }
    }

    async removeMessage(queue, notificationData) {
        try {
            await this.publisher.lRem(`queue:${queue}`, 0, JSON.stringify(notificationData));
        } catch (error) {
            console.error("Error removing message from queue:", error);
        }
    }
}

module.exports = new RedisService();
