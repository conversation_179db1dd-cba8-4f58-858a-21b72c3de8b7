const { connectToDatabase, Notification} = require("../db");

class PushController {
    constructor(socketService, notificationController) {
        this.socketService = socketService;
        this.notificationController = notificationController;
    }
  
    async processNotification(notificationData) {
        try {
            console.log("Processing new ticket notification:", notificationData);
            const formattedNotificationData = await this.notificationController.formatNotificationData(notificationData);
           
            // Create a notification
            const notification = await this.create(formattedNotificationData);
    
            // Emit the notification via socket
            this.socketService.emitNotification(notification.email, formattedNotificationData);
            console.log("Notification emitted via socket service");
    
            return notification;
        } catch (error) {
            console.error("Error processing push notification:", error);
            throw error;
        }
    }
  
    async create(notificationData) {
        try {
            await connectToDatabase();
            console.log("Creating notification:", notificationData);
            const { email, message, type } = notificationData;
  
            if (!email || !message || !type) {
                throw new Error("Email, message, and type are required");
            }
  
            const notification = new Notification(notificationData);
            await notification.save();
            console.log("Notification saved to database:", notification);
  
            return notification;
        } catch (error) {
            throw error;
        }
    }
}
  
module.exports = PushController;