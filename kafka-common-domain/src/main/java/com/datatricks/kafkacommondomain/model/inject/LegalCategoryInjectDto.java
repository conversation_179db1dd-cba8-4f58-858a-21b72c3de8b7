package com.datatricks.kafkacommondomain.model.inject;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LegalCategoryInjectDto {

    private Long id;

    @Schema(description = "Code of the Legal Category", example = "1100")
    @NotBlank(message = "legal_category.code: please provide a code")
    private String code;

    private String label;
}
