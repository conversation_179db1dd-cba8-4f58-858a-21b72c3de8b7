package com.datatricks.kafkacommondomain.model.inject;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class DelegationInjectDto {

    @JsonProperty("id")
    @Schema(description = "id of the delegation", example = "30")
    private Long id;

    @JsonProperty("label")
    @Schema(description = "label of the delegation", example = "Rien")
    private String label;

    @JsonProperty("code")
    @Schema(description = "code of the delegation", example = "3")
    private String code;

    @JsonProperty("short_label")
    @Schema(description = "short label of the delegation", example = "Rien")
    private String shortLabel;
}
