package com.datatricks.kafkacommondomain.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class PageDto<T extends PageableDto> {
    @JsonProperty("total_count")
    private long total;

    @JsonProperty("data")
    private List<T> data;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ObjectNode meta;
}

