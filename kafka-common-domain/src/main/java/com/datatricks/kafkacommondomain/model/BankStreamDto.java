package com.datatricks.kafkacommondomain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;

import java.util.Date;
import java.util.UUID;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BankStreamDto {

    @JsonProperty("id")
    private Integer id;

    private UUID reference;

    @JsonProperty("bank_id")
    @NotEmpty(message = "bank_id: bank_id cannot be empty")
    private String bank_id;

    @JsonProperty("country_code")
    @NotEmpty(message = "country_code: country_code cannot be empty")
    private String country_code;

    @JsonProperty("bank_name")
    @NotEmpty(message = "bank_name: bank_name cannot be empty")
    private String bank_name;

    @JsonProperty("code_swift")
    @NotEmpty(message = "code_swift: code_swift cannot be empty")
    private String code_swift;

    @JsonProperty("code_branch")
    @NotEmpty(message = "code_branch: code_branch cannot be empty")
    private String code_branch;

    @JsonProperty("city")
    @NotEmpty(message = "city: city cannot be empty")
    private String city;

    @JsonProperty("postal_code")
    @NotEmpty(message = "postal_code: postal_code cannot be empty")
    private String postal_code;

    @JsonProperty("address")
    @NotEmpty(message = "address: address cannot be empty")
    private String address;

    @JsonProperty("second_address")
    private String second_address;

    @JsonProperty("created_at")
    private Date createdAt;

    @JsonProperty("updated_at")
    private Date updatedAt;
}