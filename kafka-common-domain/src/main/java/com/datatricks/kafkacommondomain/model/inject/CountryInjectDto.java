package com.datatricks.kafkacommondomain.model.inject;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CountryInjectDto {

    @Schema(description = "Id of the country", example = "17")
    private Long id;

    @Schema(description = "Code of the country", example = "BH")
    @NotBlank(message = "country.code: please provide a code")
    private String code;

    @Schema(description = "Name of the country", example = "Bahrain")
    private String label;
}
