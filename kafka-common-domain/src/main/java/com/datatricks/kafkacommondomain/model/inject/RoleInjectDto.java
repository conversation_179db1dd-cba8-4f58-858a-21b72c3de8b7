package com.datatricks.kafkacommondomain.model.inject;

import com.datatricks.kafkacommondomain.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RoleInjectDto {

    @Schema(description = "id of the role", example = "1")
    private Long id;

    @NotBlank(message = "contract_actor.role.code:please provide a code")
    @Schema(description = "code of the role", example = "COLOC")
    private String code;

    @Schema(description = "label of the role", example = "Colocataire (Facturé)")
    private String label;

     @Schema(description = "description of the role", example = "FR")
    private String language;

    @Schema(description = "description of the role", example = "true")
    @JsonProperty("is_exclusive")
    private Boolean isExclusive;

    @Schema(description = "description of the role", example = "true")
    @JsonProperty("is_client")
    private Boolean isClient;

    @JsonProperty("is_principal")
    @NotNull(message = "is_principal:please provide a value")
    private Boolean isPrincipal;

    @JsonProperty("system_role")
    private Boolean systemRole;

    @JsonProperty("business_reference")
    private ActorSummaryInjectDto businessReference;

    @JsonProperty("invoice_edition_date_limit")
    private LocalDate invoiceEditionDateLimit;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;


    @JsonProperty("static_role")
    @NotNull(message = "static_role_code:please provide a value")
    @Schema(description = "static role code", example = "COLOC")
    @Valid
    private StaticRoleInjectDto staticRoleCode;
}
