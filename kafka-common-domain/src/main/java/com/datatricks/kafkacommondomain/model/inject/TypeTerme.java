package com.datatricks.kafkacommondomain.model.inject;

public enum TypeTerme {
    ENUM_AVANCE("à l'avance"),
    ENUM_ECHU("échu");

    private final String term;

    TypeTerme(String base) {
        this.term = base;
    }

    public String getTerm() {
        return term;
    }

    public static TypeTerme fromString(String term) {
        for (TypeTerme typeTerme : TypeTerme.values()) {
            if (typeTerme.term.equalsIgnoreCase(term)) {
                return typeTerme;
            }
        }
        throw new IllegalArgumentException("No constant with text " + term + " found");
    }
}
