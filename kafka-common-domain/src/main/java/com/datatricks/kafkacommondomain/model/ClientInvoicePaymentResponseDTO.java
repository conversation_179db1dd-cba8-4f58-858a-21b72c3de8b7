package com.datatricks.kafkacommondomain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ClientInvoicePaymentResponseDTO implements PageableDto {

    @Schema(description = "Id of client payment", example = "1")
    private Long id;

    @Schema(description = "Method of client payment")
    private PaymentMethodDto method;

    @Schema(description = "Type of client payment", example = "Target, Encashment, Decashment")
    private String type;

    @JsonProperty("bank_account")
    @Schema(description = "Bank account id of client payment")
    private BankAccountDto bankAccount;
}
