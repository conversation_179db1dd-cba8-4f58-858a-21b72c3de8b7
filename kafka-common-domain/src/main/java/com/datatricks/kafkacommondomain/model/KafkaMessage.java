package com.datatricks.kafkacommondomain.model;

import com.datatricks.kafkacommondomain.enums.OperationType;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KafkaMessage<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @JsonProperty("operation")
    private OperationType operation;

    @JsonProperty("class_name")
    private String className;

    @JsonProperty("module")
    private String module;

    @JsonProperty("data")
    private T data;
}

