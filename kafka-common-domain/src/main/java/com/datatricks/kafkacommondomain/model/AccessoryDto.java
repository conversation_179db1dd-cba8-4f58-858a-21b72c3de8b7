package com.datatricks.kafkacommondomain.model;

import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccessoryDto implements Serializable {
    private Long id;
    private LineTypeStreamDto lineType;
    private String title;
    private String status;
    private Long contractActorId;
    private Long timetableId;
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;
    @Temporal(TemporalType.TIMESTAMP)
    private Date modifiedAt;
    @Temporal(TemporalType.TIMESTAMP)
    private Date deletedAt;
}
