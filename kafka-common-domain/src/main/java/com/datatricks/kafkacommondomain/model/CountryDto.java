package com.datatricks.kafkacommondomain.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CountryDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 15645447L;
    @NotNull(message = "country.id:please provide a country id")
    @Schema(description = "Country identifier", example = "76")
    private Long id;
    @Schema(description = "Country code", example = "FR", type = "string")
    private String code;
    @Schema(description = "Country label", example = "France", type = "string")
    private String label;
}
