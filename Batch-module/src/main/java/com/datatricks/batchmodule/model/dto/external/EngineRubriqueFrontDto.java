package com.datatricks.batchmodule.model.dto.external;

import com.datatricks.batchmodule.enums.TypeUnitePeriode;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor
@Setter
@Getter
public class EngineRubriqueFrontDto {
    private int typeSign = 0;

    @JsonProperty("start_date")
    private String startDate;

    @JsonProperty("end_date")
    private String endDate;

    @JsonProperty("is_accessory")
    private boolean isAccessory;

    @JsonProperty("amount")
    private double financialAmount;

    @JsonProperty("tax_value")
    private double taxValue;

    @JsonProperty("multiple")
    private int multiple;

    @JsonProperty("rate_period")
    private TypeUnitePeriode ratePeriod;

    @JsonProperty("rate")
    private double rate;

    @JsonProperty("equivalent_rate")
    private double equivalentRate;

    @JsonProperty("decompte")
    private String decompte;

    @JsonProperty("base_decompte")
    private String base;

    @JsonProperty("vr")
    private double residualValue;

    @Valid
    @JsonProperty("flux")
    private List<EngineFluxFrontDto> fluxFrontDtoList = new ArrayList<>();

    public String toString() {
        return startDate + "\n" + endDate + "\n" + financialAmount + "\n" + taxValue + "\n" + multiple + "\n"
                + ratePeriod + "\n" + rate + "\n" + equivalentRate + "\n" + decompte + "\n" + base;
    }
}
