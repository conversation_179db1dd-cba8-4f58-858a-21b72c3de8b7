package com.datatricks.batchmodule.model.dto;

import com.datatricks.batchmodule.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class ContractResponseDto implements PageableDto {

    @Schema(description = "id of the contract.", example = "1")
    private Long id;

    @Schema(description = "Reference of the contract.", example = "SOC_9805862")
    private String reference;

    @Schema(description = "Title of the contract.", example = "title")
    private String title;

    @JsonProperty("refinancing_rate")
    @Schema(description = "Refinancing rate of the contract.", example = "1.5")
    private Double refinancingRate;

    @JsonProperty("business_type")
    @Schema(description = "Business type of the contract.", example = "A/R or NCAC or NCNC")
    private String businessType;

    @JsonProperty("market_type")
    @Schema(description = "Market type of the contract.", example = "[Autres, Divers, Healthcare, IT, Industrie, Mobile, Motors, NA]")
    private String marketType;

    @JsonProperty("external_reference")
    @Schema(description = "External reference of the contract.", example = "SOC_9805863")
    private String externalReference;

    @JsonProperty("lessor_reference")
    @Schema(description = "Lessor reference of the contract.", example = "SOC_9805865")
    private String lessorReference;

    @JsonProperty("agreement_as_service")
    @Schema(description = "Agreement as service of the contract.", example = "true")
    private Boolean agreementAsService;

    @JsonProperty("agreement_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Agreement date of the contract.", example = "2021-01-01")
    private LocalDate agreementDate;

    @JsonProperty("end_agreement_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End agreement date of the contract.", example = "2022-01-01")
    private LocalDate endAgreementDate;

    @JsonProperty("sign_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Sign date of the contract.", example = "2021-01-01")
    private LocalDate signDate;

    @JsonProperty("rental_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Rental date of the contract.", example = "2021-01-01")
    private LocalDate rentalDate;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start date of the contract.", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End date of the contract.", example = "2022-01-01")
    private LocalDate endDate;

    @JsonProperty("deadline")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Deadline of the contract.", example = "2021-01-02")
    private LocalDate deadline;

    @JsonProperty("lessor_selling_price")
    @Schema(description = "Lessor selling price of the contract.", example = "1.5")
    private Double lessorSellingPrice;

    @JsonProperty("amount_rental_base")
    @Schema(description = "Amount rental base of the contract.", example = "1.5")
    private Double amountRentalBase;

    @JsonProperty("amount_granted")
    @Schema(description = "Amount granted of the contract.", example = "1.5")
    private Double amountGranted;

    @JsonProperty("duration")
    @Schema(description = "Duration per days of the contract.", example = "365")
    private Integer duration;

    @JsonProperty("request_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Request date of the contract.", example = "2021-01-01")
    private LocalDate requestDate;

    @JsonProperty("lessor_payment_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Lessor payment date of the contract.", example = "2021-01-01")
    private LocalDate lessorPaymentDate;

    @JsonProperty("business_introducer")
    @Schema(description = "Business introducer of the contract", example = "Linus Torvalds")
    private String businessIntroducer;

    @JsonProperty("business")
    @Schema(description = "Business of the contract")
    private CompanyDto managementCompany;

    @JsonProperty("activity")
    @Schema(description = "Activity of the contract.")
    private ActivityDto activity;

    @JsonProperty("product")
    @Schema(description = "Product of the contract.")
    private ProductDto product;

    @JsonProperty("currency")
    @Schema(description = "Currency of the contract")
    private CurrencyDto currency;

    @JsonProperty("phase")
    @Schema(description = "Phase of the contract")
    private PhaseDto phase;

    @JsonProperty("milestone")
    @Schema(description = "Milestone of the contract")
    private MilestoneDto milestone;
}
