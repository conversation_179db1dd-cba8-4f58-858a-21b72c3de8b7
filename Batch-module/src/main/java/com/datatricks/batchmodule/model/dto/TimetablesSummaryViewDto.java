package com.datatricks.batchmodule.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
public class TimetablesSummaryViewDto {

    private Long id;

    private String companyReference;

    private String companyName;

    private String actorReference;

    private String actorName;

    private Long actorId;

    private String contractReference;

    private Long contractId;

    private Long companyId;

    private String contractActivityCode;

    private String contractProductCode;

    private String timetableStatus;

    private String timetableItemStatus;

    private Long levelId;

    private Float depreciation;

    private LocalDate dueDate;

    private LocalDate endDate;

    private Float interest;

    private Float nominalRate;

    private Float rate;

    private Float rent;

    private Float residualValue;

    private LocalDate startDate;

    private Float taxAmount;

    private Float amortization;

    private Float unpaid;

    private Long timetableId;

    private String roleCode;

    private String iban;

    private Long clientAddressId;

    private Long isSeparateBilling;

    private String lineTypeCode;

    private Double tax;

    private String taxCode;

    private Instant deletedAt;

    private Instant createdAt;

    private Instant modifiedAt;

}