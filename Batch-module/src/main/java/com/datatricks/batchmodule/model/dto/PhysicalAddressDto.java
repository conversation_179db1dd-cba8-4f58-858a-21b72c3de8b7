package com.datatricks.batchmodule.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PhysicalAddressDto {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("status")
    private boolean status;

    @JsonProperty("department")
    private String department;

    @JsonProperty("sub_department")
    private String subDepartment;

    @JsonProperty("building_name")
    private String buildingName;

    @JsonProperty("floor")
    private String floor;

    @JsonProperty("room")
    private String room;

    @JsonProperty("street_name")
    private String streetName;

    @JsonProperty("building_number")
    private String buildingNumber;

    @JsonProperty("post_box")
    private String postBox;

    @JsonProperty("town_location_name")
    private String townLocationName;

    @JsonProperty("post_code")
    private String postCode;

    @JsonProperty("town_name")
    private String townName;

    @JsonProperty("country")
    private String country;

    @JsonProperty("district_name")
    private String districtName;

    @JsonProperty("country_sub_division")
    private String countrySubDivision;
}
