package com.datatricks.batchmodule.model.dto;

import com.datatricks.batchmodule.enums.TypeBase;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import lombok.Getter;
import lombok.Setter;
import com.datatricks.batchmodule.utils.DateUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class RetributionDto implements PageableDto {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("nature")
    private String nature;

    @JsonProperty("type_arrangement")
    private String typeArrangement;

    @JsonProperty("title")
    private String title;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @JsonProperty("is_separate_billing")
    private Boolean isSeparateBilling;

    @JsonProperty("is_suspended_billing")
    private Boolean isSuspendedBilling;

    @JsonProperty("count")
    private TypeBase count;

    @JsonProperty("count_base")
    private TypeBase base;

    @JsonProperty("tax")
    private Double tax;

	@JsonProperty("status")
	private String status;

    @JsonProperty("timetable_id")
    private Long timetableId;

    @JsonProperty("contract_actor_id")
    private Long contractActorId;

    @JsonProperty("levels")
    @Valid
    private List<LevelDto> retributionLevelsList = new ArrayList<>();
}
