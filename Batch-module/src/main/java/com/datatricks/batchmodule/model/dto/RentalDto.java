package com.datatricks.batchmodule.model.dto;

import com.datatricks.batchmodule.enums.TypeBase;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import com.datatricks.batchmodule.utils.DateUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class RentalDto implements PageableDto {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("analytic_input")
    private String analyticInput;

    @JsonProperty("nature")
    private String nature;

    @JsonProperty("type_arrangement")
    private String typeArrangement;

    @JsonProperty("title")
    private String title;

    @JsonProperty("financing_amount")
    private Double financingAmount;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @JsonProperty("nominal_rate")
    private Double nominalRate;

    @JsonProperty("equivalent_rate")
    private Double equivalentRate;

    @JsonProperty("residual_value")
    private Double residualValue;

    @JsonProperty("vr")
    private Double vr;

    @JsonProperty("is_separate_billing")
    private Boolean isSeparateBilling;

    @JsonProperty("is_suspended_billing")
    private Boolean isSuspendedBilling;

    @JsonProperty("autoExtension")
    private Boolean autoExtension;

    @JsonProperty("count")
    private TypeBase count;

    @JsonProperty("count_base")
    private TypeBase base;

    @JsonProperty("tax")
    private Double tax;

	@JsonProperty("status")
	private String status;

    @JsonProperty("timetable_id")
    private Long timetableId;

    @JsonProperty("contract_actor_id")
    private Long contractActorId;

    @JsonProperty("levels")
    private List<LevelDto> rentalLevelsList = new ArrayList<>();
}
