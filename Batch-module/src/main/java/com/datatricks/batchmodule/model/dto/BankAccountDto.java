package com.datatricks.batchmodule.model.dto;

import com.datatricks.batchmodule.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class BankAccountDto implements PageableDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("address_id")
    private Integer addressId;

    @JsonProperty("bank_code")
    private String bankCode;

    @JsonProperty("country")
    private String country;

    @JsonProperty("international_number")
    private String internationalNumber;

    @JsonProperty("registration_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date registrationDate;

    @JsonProperty("swift_code")
    private String swiftCode;

    @JsonProperty("account_number")
    private String accountNumber;

    @JsonProperty("status")
    private String status;

    @JsonProperty("iban")
    private String iban;

    @JsonProperty("validity")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date validity;

    @JsonProperty("iban_key")
    private String ibanKey;

    @JsonProperty("rib_key")
    private String ribKey;

    @JsonProperty("title")
    private String title;

    @JsonProperty("type")
    private String type;

    @JsonProperty("replacement")
    private Boolean replacement;

    @JsonProperty("rib_replacement")
    private String ribReplacement;

    @JsonProperty("box_code")
    private String boxCode;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date endDate;

    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("start_date")
    private Date startDate;

	@JsonProperty("is_principal")
	private Boolean isPrincipal;
}
