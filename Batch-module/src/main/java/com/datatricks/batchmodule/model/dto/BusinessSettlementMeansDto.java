package com.datatricks.batchmodule.model.dto;

import com.datatricks.batchmodule.model.PaymentsType;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BusinessSettlementMeansDto implements PageableDto{

    private Long id;

    @NotNull(message = "paymentType: Payment type is required")
    @JsonProperty("payment_type")
    @Schema(description = "Payment type", example = "PAYMENT", type = "string")
    private PaymentsType paymentType;

    @NotNull(message = "paymentMethod: Payment method is required")
    @JsonProperty("payment_method")
    @Schema(description = "Payment method", type = "object")
    private PaymentMethodDto paymentMethod;

    @NotNull(message = "bankAccount: Bank account is required")
    @JsonProperty("bank_account")
    @Schema(description = "Bank account", type = "object")
    private BankAccountDto bankAccount = new BankAccountDto();
    @Schema(description = "Actor", type = "object")
    private SimplifiedActorDto actor;
}
