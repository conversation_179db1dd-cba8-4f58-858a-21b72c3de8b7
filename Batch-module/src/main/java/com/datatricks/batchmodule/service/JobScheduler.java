package com.datatricks.batchmodule.service;

import com.datatricks.batchmodule.enums.BatchJobStatus;
import com.datatricks.batchmodule.exception.BusinessException;
import com.datatricks.batchmodule.model.dto.BatchInstanceDto;
import com.datatricks.batchmodule.model.dto.JobResponseDto;
import com.datatricks.batchmodule.websocket.SseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.logging.log4j.Level;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import java.util.logging.Logger;

@Service
public class JobScheduler {

    private final JobLauncher jobLauncher;
    private final Job invoiceCountJob;
    private final BatchInstanceService batchInstanceService;
    private final Job invoiceJob;
    private final Logger logger = Logger.getLogger(JobScheduler.class.getName());
    private final TaskExecutor taskExecutor;
    private final ObjectMapper objectMapper;
    private final SseService messagingService;

    public JobScheduler(JobLauncher jobLauncher, Job invoiceCountJob, BatchInstanceService batchInstanceService, Job invoiceJob, @Qualifier("batchTaskExecutor") TaskExecutor taskExecutor, ObjectMapper objectMapper, SseService messagingService) {
        this.jobLauncher = jobLauncher;
        this.invoiceCountJob = invoiceCountJob;
        this.batchInstanceService = batchInstanceService;
        this.invoiceJob = invoiceJob;
        this.taskExecutor = taskExecutor;
        this.objectMapper = objectMapper;
        this.messagingService = messagingService;
    }

    public JobResponseDto startJob(BatchInstanceDto batchInstanceDto) {
        try {
            int rowCount = 0;
            assert batchInstanceService != null;
            BatchInstanceDto savedBatchInstance = batchInstanceService.saveBatchInstance(batchInstanceDto);
            String parametersMapAsJson = objectMapper.writeValueAsString(savedBatchInstance.getParameters());
            JobExecution jobExecution = jobLauncher.run(invoiceCountJob, new JobParametersBuilder()
                    .addLong("batchInstanceId", savedBatchInstance.getId())
                    .addLong("batchId", savedBatchInstance.getBatchId())
                    .addString("parameters", parametersMapAsJson)
                    .toJobParameters());

            rowCount = jobExecution.getExecutionContext().getInt("rowCount");
            savedBatchInstance.setTotalCount(rowCount);
            messagingService.sendBatchUpdate(savedBatchInstance);
            if (rowCount >= 0) {
                //Start the next job
                assert taskExecutor != null;
                int finalRowCount = rowCount;
                taskExecutor.execute(() -> {
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        objectMapper.registerModule(new JavaTimeModule());
                        savedBatchInstance.setTotalCount(finalRowCount);
                        savedBatchInstance.setProgress("Starting invoice processing job");
                        String parametersJson = objectMapper.writeValueAsString(savedBatchInstance);
                        JobParameters jobParameters = new JobParametersBuilder()
                                .addLong("batchInstanceId", savedBatchInstance.getId())
                                .addString("batchInstanceDto", parametersJson)
                                .toJobParameters();
                        messagingService.sendBatchUpdate(savedBatchInstance);
                        jobLauncher.run(invoiceJob, jobParameters);
                    } catch (Exception e) {
                        logger.severe("Error occurred while starting job: " + e.getMessage());
                        savedBatchInstance.setProgress("Error occurred while starting job: " + e.getMessage());
                        savedBatchInstance.setStatus(BatchJobStatus.FAILED);
                        savedBatchInstance.setLogLevel(Level.ERROR);
                        messagingService.sendBatchUpdate(savedBatchInstance);
                        throw new BusinessException("Error occurred while starting job: " + e.getMessage(), "ERROR_STARTING_JOB");
                    }
                });
            }
            return new JobResponseDto(savedBatchInstance.getId(), rowCount);
        } catch (Exception e) {
            throw new BusinessException("Error occurred while starting job: " + e.getMessage(), "ERROR_STARTING_JOB");
        }
    }
}
