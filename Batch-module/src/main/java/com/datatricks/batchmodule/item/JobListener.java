package com.datatricks.batchmodule.item;

import com.datatricks.batchmodule.exception.BusinessException;
import com.datatricks.batchmodule.listener.JobLoggerListener;
import com.datatricks.batchmodule.model.dto.BatchInstanceDto;
import com.datatricks.batchmodule.websocket.SseService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.logging.log4j.Level;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class JobListener implements JobExecutionListener {

    @Autowired
    private JobLoggerListener jobLoggerListener;

    @Autowired
    private SseService messagingService;

    @Override
    public void beforeJob(JobExecution jobExecution) {
        ExecutionContext jobContext = jobExecution.getExecutionContext();
        String parameters = jobExecution.getJobParameters().getString("batchInstanceDto");
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        BatchInstanceDto batchInstanceDto = new BatchInstanceDto();
        try {
            batchInstanceDto = objectMapper.readValue(parameters, BatchInstanceDto.class);
            jobContext.put("batchInstanceDto", batchInstanceDto);
        } catch (JsonProcessingException e) {
            batchInstanceDto.setProgress("Error parsing batchInstanceDto " + e.getMessage());
            jobLoggerListener.getLogger().log(Level.ERROR, "Error parsing batchInstanceDto {} ", e.getMessage());
            messagingService.sendBatchUpdate(batchInstanceDto);
            throw new BusinessException("Error parsing batchInstanceDto " + e.getMessage(), "ERROR_PARSING_BATCH_INSTANCE_DTO");
        }
    }
    @Override
    public void afterJob(JobExecution jobExecution) {
        // Implement if you need to do something after the job finishes
    }
}

