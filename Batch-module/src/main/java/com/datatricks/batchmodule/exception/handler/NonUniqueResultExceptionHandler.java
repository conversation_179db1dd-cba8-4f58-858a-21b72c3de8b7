package com.datatricks.batchmodule.exception.handler;

import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.persistence.NonUniqueResultException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class NonUniqueResultExceptionHandler {

    @Value("${api.response.activateDebugInfo}")
    private boolean isDebugActive;
    @ApiResponse(responseCode = "500", description = "Internal Server Error")
    @ExceptionHandler(NonUniqueResultException.class)
    public ResponseEntity<TechnicalError> handleConstraintViolation(NonUniqueResultException ex) {
        TechnicalError technicalErrorResponse = new TechnicalError("Internal_Server_Error");
        if (isDebugActive) {
            technicalErrorResponse.getTechnicalDetail().setDetail(ex.getLocalizedMessage());
        }

        return new ResponseEntity<>(technicalErrorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
