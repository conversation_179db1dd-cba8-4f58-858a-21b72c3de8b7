package com.datatricks.batchmodule.exception.handler;

import com.datatricks.batchmodule.exception.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class EntityNotFoundExceptionHandler {

    @Value("${api.response.activateDebugInfo}")
    private boolean isDebugActive;

    @ExceptionHandler(EntityNotFoundException.class)
    public ResponseEntity<TechnicalError> handleEntityNotFound(EntityNotFoundException ex) {
        TechnicalError technicalErrorResponse = new TechnicalError("Internal_Server_Error");
        if (isDebugActive) {
            technicalErrorResponse.getTechnicalDetail().setDetail(ex.getLocalizedMessage());
            technicalErrorResponse.getTechnicalDetail().setSource(ex.getModuleName());
            technicalErrorResponse.getTechnicalDetail().setDetail("The requested entity does not exist");
            technicalErrorResponse.setMessage(ex.getMessage());
        }
        return ResponseEntity.status(404).body(technicalErrorResponse);
    }
}