package com.datatricks.batchmodule.exception.handler;

import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.hibernate.query.SemanticException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mapping.PropertyReferenceException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class SemanticExceptionHandler {

    @Value("${api.response.activateDebugInfo}")
    private boolean isDebugActive;

    @ApiResponse(responseCode = "500", description = "Internal Server Error")
    @ExceptionHandler({SemanticException.class, PropertyReferenceException.class})
    public ResponseEntity<TechnicalError> handleConstraintViolation(Exception ex) {
        TechnicalError technicalErrorResponse = new TechnicalError("Internal_Server_Error");
        int firstIndexOfChar = ex.getLocalizedMessage().indexOf('\'') + 1;
        int secondIndexOfChar = ex.getLocalizedMessage().indexOf('\'', firstIndexOfChar);

        if (isDebugActive) {
            technicalErrorResponse
                    .getTechnicalDetail()
                    .setDetail(ex.getLocalizedMessage().substring(firstIndexOfChar, secondIndexOfChar));
            technicalErrorResponse.getTechnicalDetail().setDetail("unknown filtering key");
        }
        return new ResponseEntity<>(technicalErrorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
