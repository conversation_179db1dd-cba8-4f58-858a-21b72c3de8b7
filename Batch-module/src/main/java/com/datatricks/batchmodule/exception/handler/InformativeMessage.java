package com.datatricks.batchmodule.exception.handler;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class InformativeMessage {
    @Schema(description = "Message of successful operation", example = "Resource with {{ID}} has been deleted successfully")
    private String message;

    public InformativeMessage(String message) {
        this.message = message;
    }
}
