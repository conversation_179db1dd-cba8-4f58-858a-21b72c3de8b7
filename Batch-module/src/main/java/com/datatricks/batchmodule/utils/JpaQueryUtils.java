package com.datatricks.batchmodule.utils;

import com.datatricks.batchmodule.model.*;
import com.datatricks.batchmodule.model.dto.JpaQueryDto;

import java.util.Map;

public class JpaQueryUtils {

    private JpaQueryUtils() {
        // Utility class
    }

    // This class MUST remain immutable
    // spotless:off
    // @formatter:off

    // [         KEY      : [       KEY     : [                     VALUE1                     |              VALUE2
    //           |    VALUE3     |  VALUE4]]
    // [ Principal entity : [Param from URL : [ joinColumn between Principal and joined entity | column to filter in
    // joined entity | Joined entity | next join]]

    private static final Map<Class<?>, Map<String, JpaQueryDto>> correspondanceEntityMap = Map.of(
    );
    // @formatter:on
    // spotless:off
    public static JpaQueryDto getParameterMapping(Class<?> clazz, String param) {
        Map<String, JpaQueryDto> entityMap = correspondanceEntityMap.get(clazz);
        if (entityMap != null) {
            return entityMap.get(param);
        } else {
            return null;
        }
    }
}
