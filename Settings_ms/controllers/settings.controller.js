const { SettingModel } = require("../db");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");

async function getAll(req, res, next) {
    try {
        let whereCondition = {};
        const limit = req.query.limit ? req.query.limit : config.limit;
        const offset = req.query.offset ? req.query.offset*limit : config.offset*limit;
        const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
        const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;
        Object.keys(req.query).forEach(key => {
            if (key !== "offset" && key !== "limit" && key !== "sort_by" && key !== "order_by") {
                whereCondition[key] = req.query[key];
            }
        });
        // fetch from database
        const [settings, total_count]  = await Promise.all([
            SettingModel.findAll({
                order: [[sortBy, orderBy]],
                offset,
                limit,
                where: whereCondition
            }),
            SettingModel.count({ where: whereCondition }),
        ]);

        // send response
        res.send({ data: settings, total_count });

    } catch (error) {
        next(error)
    }
}

async function create(req, res, next) {
    try {
        const { module, key } = req.body;

        // Check if the setting already exists
        const existingSetting = await SettingModel.findOne({
            where: {
                module,
                key
            }
        });

        if (existingSetting) {
            throw new ConflictError("Setting already exists", "Setting");
        }

        // Create the setting
        const setting = await SettingModel.create(req.body);

        res.status(201).send({
            data: setting
        });
    } catch (error) {
        next(error)
    }
}

async function update(req, res, next) {

    try {
        const id = req.params.id;
        const { value, system_attribute } = req.body;

        const old_setting = await SettingModel.findOne({
            where: { id }
        });

        if (!old_setting) {
            throw new NotFoundError("Setting not found", "Setting");
        }

        await SettingModel.update(
            {
                value: value,
                system_attribute: system_attribute
            },
            {
                where: {
                    id: id
                }
            }
        );

        res.send({
            data: {
                id: id,
                module: old_setting.module,
                key: old_setting.key,
                value: value,
                system_attribute: system_attribute
            },
        });
    } catch (error) {
        next(error)
    }
}


async function getSystemAttributeById(id) {

    const setting = await SettingModel.findOne({
        where: { id },
        attributes: ["system_attribute"] // Only fetch the system_attribute field
    });

    if (!setting) {
        throw new NotFoundError("Setting not found", "Setting");
    }

    return { system_attribute: setting.system_attribute };
}

async function remove(req, res, next) {
    try {
        const id = req.params.id;

        // Use the getSystemAttributeById function to fetch the system_attribute
        const { system_attribute } = await getSystemAttributeById(id);

        if (system_attribute === true) {
            throw new ConflictError(`Setting ${id} cannot be deleted because system_attribute is true`, "Setting");
        }

        await SettingModel.destroy({
            where: { id }
        });

        res.send({
            data: {
                message: `Resource with ID ${id} has been deleted successfully`
            }
        });
    } catch (error) {
        next(error)
    }
}

async function updateByModule(req, res, next) {

    try {
        let ReqBodySetting = [];
        Object.keys(req.body).forEach(key => {
            if (key !== "offset" && key !== "limit" && key !== "sort_by" && key !== "order_by") {
                ReqBodySetting[key] = req.body[key];
            }
        });
        const moduleName = req.params.moduleName;

        // Check if the module exists
        const moduleExists = await SettingModel.findOne({
            where: {
                module: moduleName
            }
        });

        if (!moduleExists) {
            throw new NotFoundError(`Module name : ${moduleName} not found`, "Module");
        }

        const updatedSettings = await Promise.all(
            ReqBodySetting.map(async (setting) => {
                const updatedSetting = await SettingModel.update(
                    {
                        value: setting.value,
                        system_attribute: setting.system_attribute
                    },
                    {
                        where: {
                            module: moduleName,
                            key: setting.key
                        }
                    }
                );

                return updatedSetting;
            })
        );

        // Check if any rows were updated
        const totalUpdated = updatedSettings.reduce(
            (sum, updatedSetting) => sum + updatedSetting[0], 0);

        if(totalUpdated === 0) {
            throw new NotFoundError(`Setting not found`, "Setting");
        }
        // Accessing the last setting for response data
        const lastSetting = req.body[totalUpdated - 1];

        res.send({
            data: {
                module: moduleName,
                key: lastSetting.key, // Accessing the last setting's key
                value: lastSetting.value, // Accessing the last setting's value
                system_attribute: lastSetting.system_attribute, // Accessing the last setting's system_attribute
            }
        });
    } catch (error) {
        next(error)
    }
}

async function createByModule(req, res, next) {
    try {
        const moduleName = req.params.moduleName;
        const moduleSettings = req.body.settings;

        // Check if module exists
        const existingSettings = await SettingModel.findAll({
            where: { module: moduleName }
        });

        // Extract existing keys
        const existingKeys = existingSettings.map(setting => setting.key);

        // Filter out settings with existing keys
        const newSettings = moduleSettings.filter(setting => !existingKeys.includes(setting.key));

        // Create new settings
        const createdSettings = await Promise.all(newSettings.map(async (setting) => {
            try {
                const createdSetting = await SettingModel.create({
                    module: moduleName,
                    key: setting.key,
                    value: setting.value,
                    system_attribute: setting.system_attribute
                });
                return createdSetting;
            } catch (error) {
                next(error)
            }
        }));

        // Check for errors in individual settings
        const errors = createdSettings.filter(setting => setting.error);

        if (errors.length > 0) {
            next(errors[0]);
        }

        res.send({
            data: {
                createdSettings
            }
        });
    } catch (error) {
        next(error)
    }
}




module.exports = {
    getAll,
    create,
    update,
    remove,
    updateByModule,
    createByModule
};