const prodConfig = require("./config.prod");

const defaultConfig = {
    // Database connection config
    db: {
        // most of the cases this will be localhost
        // unless you are connecting to remote database
        host: "postgres",

        // name of the database connecting to
        name: "dt-settings",

        // database username
        user: "postgres",

        // datababse host port
        port: 5432,

        // password for above database user
        password: "changeme",

        // we are using Sequelize for connecting to database
        // Sequelize supports Mysql, SQlite, PostgreSQL and MSSQL
        // As applicable use either of 'mysql'|'sqlite'|'postgres'|'mssql'
        dialect: "postgres"
    },
    
    // A unique key used for signing JWT token
    // Please replace below key with your own
    JWTKey: "NL(K(]`R6u%_hSg",

    // PORT to run our express app
    httpPort: 8080
};

module.exports = {
    ...defaultConfig,
    ...prodConfig
};