const express = require("express");
const bodyParser = require("body-parser");
const SettingsRouter = require("./routes/settings.route");

const { httpPort } = require("./config/app-config");
const path = require("path");
const app = express();
// const cacheMiddleware = require("./middlewares/cache.middleware")
const ErrorHandlerModule = require("./error/handler/errorHandler")
const swaggerUi = require("swagger-ui-express");
const swaggerDocument = require("./swagger.json");

// require('dotenv').config({
//     path: `.env.${process.env.NODE_ENV}`, // Loads .env.local, .env.development, etc.
// });

// app.use(cacheMiddleware);

//register setting microservice to Eureka service registry
require("./microservice/register");


// let's have documentation at the root
app.use(express.static("doc"));

// parse application/x-www-form-urlencoded with increased limit
app.use(bodyParser.urlencoded({ extended: false, limit: "50mb" }));

// parse application/json with increased limit
app.use(bodyParser.json({ limit: "50mb" }));

app.get("/docs", function(req, res) {
    res.sendFile(path.join(__dirname + "/doc/index.html"));
});

/* Uncomment below lines if JWT authentication is to be used */
// it will be good to move below two imports to top of the file
const AuthRouter = require("./routes/auth.route");
const jwtMiddleware = require("./middlewares/jwt.middleware");

// router rules for auth
//app.use("/v1/auth", AuthRouter);

// NOTE - we are purposely adding jwt middleware after auth
// this is to skip token checks for above auth routes

//TODO 
//app.use(jwtMiddleware);

// employee api routes
//app.use(cors());app.use("/v1/employee", EmployeeRouter);

//settings api routes
app.use("/api/v1/settings", SettingsRouter); 
app.use(ErrorHandlerModule.errorHandler);

// custom error handlers
// this will also catch async errors since we are usign express-async-errors
// eslint-disable-next-line no-unused-vars
var options = {
    swaggerOptions: {
        url: "/v3/api-docs"
    },
}
app.get("/v3/api-docs", (req, res) => res.json(swaggerDocument));
app.use("/swagger-ui.html", swaggerUi.serveFiles(null, options), swaggerUi.setup(null, options));

app.listen(httpPort, () => console.log(`Settings API app listening on port ${httpPort}!`));