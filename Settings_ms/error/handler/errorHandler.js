const NotFound = require("../exception/NotFound")
const Conflict = require("../exception/Conflict")
const TechnicalError = require("../TechnicalError")
const BusinessError = require("../BussinessError")


const errorHandler = (err, req, res, next) => {
    if (err instanceof NotFound) {

        const technicalErrorResponse = new TechnicalError("RESOURCE_NOT_FOUND", "The requested resource was not found");
        technicalErrorResponse.details.detail = err.message;
        technicalErrorResponse.details.source = err.module;
        return res.status(err.statusCode).send(technicalErrorResponse);
    } else if (err instanceof Conflict) {

        const technicalErrorResponse = new TechnicalError("CONFLICT", "Conflict error");
        technicalErrorResponse.details.detail = err.message;
        technicalErrorResponse.details.source = err.module;
        return res.status(err.statusCode).send(technicalErrorResponse);
    }else if (err.isJoi) {

        const businessErrorResponse = new BusinessError("BAD_REQUEST", "Validation error");
        err.details.forEach(detail => {
            businessErrorResponse.addDetail(detail.path[detail.path.length - 1], detail.message);
        });
        return res.status(400).send(businessErrorResponse);

    }else {
        const technicalErrorResponse = new TechnicalError("INTERNAL_SERVER_ERROR", err.name? err.name : "INTERNAL_SERVER_ERROR");
        technicalErrorResponse.details.detail = err.message;
        technicalErrorResponse.details.source = err.stack;
        return res.status(500).send(technicalErrorResponse);
    }
}
module.exports = {errorHandler}