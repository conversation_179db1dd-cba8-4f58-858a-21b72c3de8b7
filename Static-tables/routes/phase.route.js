const express = require("express");
const router = express.Router();
const phasesValidation = require ("../validation-rules/phase.rule")
const validateMiddleware = require("../middlewares/validate.middleware");
const PhasesContoller = require("../controllers/phases.controller")
// below line has to be added in every route file
// this is perhaps some bug in express-async-errors
// adding below line only once in index.js doesn't works
require("express-async-errors");

router.get("/", PhasesContoller.search);
router.get("/:id", PhasesContoller.getOne);
router.post("/", validateMiddleware(phasesValidation.create), PhasesContoller.create);
router.delete("/:id", PhasesContoller.remove);
router.put("/:id",validateMiddleware(phasesValidation.create), PhasesContoller.update);

module.exports = router;