const express = require("express");
const router = express.Router();
const BankController = require("../controllers/bank.controller");
const validateMiddleware = require("../middlewares/validate.middleware");
const BankRule = require("../validation-rules/bank.rule");

router.get("/", BankController.search);
router.post("/", validateMiddleware(BankRule.create), BankController.create);
router.put("/:bankId", validateMiddleware(BankRule.update), BankController.update);
router.delete("/:bankId", BankController.remove);
router.get('/iban/:iban', BankController.getBankBranchByInterbankCode);
module.exports = router;