const express = require("express");
const router = express.Router();
const marketController = require("../controllers/markets.controller");
const validateMiddleware = require("../middlewares/validate.middleware");
const marketRule = require("../validation-rules/market.rule");

// Define routes with their corresponding controller methods
router.get("/", marketController.search);
router.get("/:code", marketController.getOne);
router.post(  "/",  validateMiddleware(marketRule.create),  marketController.create);
router.put(  "/:code",  validateMiddleware(marketRule.update),  marketController.update);
router.delete("/:code", marketController.remove);

module.exports = router;
