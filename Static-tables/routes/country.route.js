const express = require("express");
const router = express.Router();
const CountriesValidationRules = require ("../validation-rules/country.rule")
const validateMiddleware = require("../middlewares/validate.middleware");
const CountriesController = require("../controllers/countries.controller")
// below line has to be added in every route file
// this is perhaps some bug in express-async-errors
// adding below line only once in index.js doesn't works
require("express-async-errors");


router.get("/", CountriesController.search);
router.get("/:id", CountriesController.getOne);
router.post("/", validateMiddleware(CountriesValidationRules.create), CountriesController.create);
router.delete("/:id", CountriesController.remove);
router.put("/:id",validateMiddleware(CountriesValidationRules.create), CountriesController.update);

module.exports = router;