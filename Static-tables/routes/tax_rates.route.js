const express = require("express");
const router = express.Router();
const TauxValidationRules = require ("../validation-rules/tax_rate.rule")
const validateMiddleware = require("../middlewares/validate.middleware");
const TauxController = require("../controllers/tax_rates.controller")
// below line has to be added in every route file
// this is perhaps some bug in express-async-errors
// adding below line only once in index.js doesn't works
require("express-async-errors");

router.get("/", TauxController.search);
router.get("/:id", TauxController.getOne);
router.post("/", validateMiddleware(TauxValidationRules.create), TauxController.create);
router.delete("/:id", TauxController.remove);
router.put("/:id",validateMiddleware(TauxValidationRules.create), TauxController.update);

module.exports = router;