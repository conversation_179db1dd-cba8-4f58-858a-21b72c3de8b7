const express = require("express");
const router = express.Router();
const ServicesPackController = require("../controllers/services_pack.controller");
const  ServicesPackValidation = require("../validation-rules/service_pack.rule");
const validateMiddleware = require("../middlewares/validate.middleware");

require("express-async-errors");

router.post("/", validateMiddleware(ServicesPackValidation.create), ServicesPackController.createServicesPack);
router.get("/", ServicesPackController.search);
router.get("/:code", ServicesPackController.getOne);

router.put("/:code",validateMiddleware(ServicesPackValidation.create), ServicesPackController.updateServicesPack)
router.delete("/:code", ServicesPackController.remove);
router.delete('/:code/services/:serviceReference', ServicesPackController.removeServiceFromPack);

module.exports = router;
