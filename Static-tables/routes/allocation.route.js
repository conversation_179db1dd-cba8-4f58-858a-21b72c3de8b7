const express = require("express");
const router = express.Router();
const AllocationsValidationRules = require ("../validation-rules/allocation.rule")
const validateMiddleware = require("../middlewares/validate.middleware");
const AllocationsController = require("../controllers/allocations.controller")
// below line has to be added in every route file
// this is perhaps some bug in express-async-errors
// adding below line only once in index.js doesn't works
require("express-async-errors");

router.get("/", AllocationsController.search);
router.get("/:id", AllocationsController.getOne);
router.post("/", validateMiddleware(AllocationsValidationRules.create), AllocationsController.create);
router.delete("/:id", AllocationsController.remove);
router.put("/:id",validateMiddleware(AllocationsValidationRules.create), AllocationsController.update);
module.exports = router;