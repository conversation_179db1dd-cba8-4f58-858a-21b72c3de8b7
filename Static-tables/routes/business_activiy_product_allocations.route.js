const express = require("express");
const router = express.Router();
const Activity_Product_AllocationsValidationRules = require ("../validation-rules/business_activity_product_allocation.rule")
const validateMiddleware = require("../middlewares/validate.middleware");
const Business_Activiy_Product_allocationsController = require("../controllers/business_activiy_product_allocations.controller");


// below line has to be added in every route file
// this is perhaps some bug in express-async-errors
// adding below line only once in index.js doesn't works
require("express-async-errors");
// GET /api/v1/ listing of activity_product_allocations table
router.get("/businesses/activity-product-allocations", Business_Activiy_Product_allocationsController.search);
// GET /api/v1/{id} get activity_product_allocation by id
router.get("/businesses/activity-product-allocations/:id", Business_Activiy_Product_allocationsController.getOne);
//GET::/api/v1/businesses/{reference}/activities/{code}/products/{code}/allocations
router.get("/businesses/:reference/activities/:code/products/:productCode/allocations", Business_Activiy_Product_allocationsController.getAllocationsByActivityAndProduct,);

//GET::/api/v1/businesses/{reference}/activities/{code}/products/{code}/allocations/{code}
router.get("/businesses/:reference/activities/:code/products/:productCode/allocations/:allocationCode", Business_Activiy_Product_allocationsController.getAllocationByActivityAndProduct);

//POST::/api/v1/businesses/{reference}/activities/{code}/products/allocations
router.post("/businesses/:reference/activities/:code/products/:productCode/allocations",validateMiddleware(Activity_Product_AllocationsValidationRules.create), Business_Activiy_Product_allocationsController.createAllocationForProduct);

//DELETE::/api/v1/businesses/{reference}/activities/{code}/products/{code}/allocations/{code}
router.delete("/businesses/:reference/activities/:code/products/:productCode/allocations/:allocationCode", Business_Activiy_Product_allocationsController.deleteAllocationByActivityCodeAndProductCodeAndAllocationCode);




module.exports = router;

