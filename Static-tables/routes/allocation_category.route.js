const express = require("express");
const router = express.Router();
const Allocation_categoriesValidationRules = require ("../validation-rules/allocation_categorie.rule")
const validateMiddleware = require("../middlewares/validate.middleware");
const Allocation_categoriesController = require("../controllers/allocation_categories.controller")
// below line has to be added in every route file
// this is perhaps some bug in express-async-errors
// adding below line only once in index.js doesn't works
require("express-async-errors");

router.get("/", Allocation_categoriesController.search);
router.get("/:id", Allocation_categoriesController.getOne);
router.post("/", validateMiddleware(Allocation_categoriesValidationRules.create), Allocation_categoriesController.create);
router.put("/:id",validateMiddleware(Allocation_categoriesValidationRules.create), Allocation_categoriesController.update);
router.delete("/:id", Allocation_categoriesController.remove);

module.exports = router;