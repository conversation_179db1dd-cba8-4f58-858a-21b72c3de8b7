const express = require("express");
const router = express.Router();
const OperationValidationRules = require ("../validation-rules/operation.rule")
const validateMiddleware = require("../middlewares/validate.middleware");
const OperationController = require("../controllers/operation.controller")
const NatureController = require("../controllers/nature.controller")
const TypeController = require("../controllers/type.controller")

// below line has to be added in every route file
// this is perhaps some bug in express-async-errors
// adding below line only once in index.js doesn't works
require("express-async-errors");

router.get("/", OperationController.search);

router.get("/:operationCode/natures", NatureController.getNaturesByOperation);
router.patch("/:operationCode/natures/:id",validateMiddleware(OperationValidationRules.updateStatus), NatureController.update);


router.get("/:operationCode/natures/:natureCode/types", TypeController.search);
router.put("/:operationCode/natures/:natureCode/types/:id",validateMiddleware(OperationValidationRules.createType), TypeController.update);
router.post("/:operationCode/natures/:natureCode/types",validateMiddleware(OperationValidationRules.createType), TypeController.create);
router.delete("/types/:id", TypeController.remove);

router.get("/:id", OperationController.getOne);
router.get("/natures/types", OperationController.getOperationsMapping);
router.post("/", validateMiddleware(OperationValidationRules.create), OperationController.create);
router.delete("/:id", OperationController.remove);
router.patch("/:id",validateMiddleware(OperationValidationRules.create), OperationController.update);
router.put("/:id",validateMiddleware(OperationValidationRules.create), OperationController.update);


router.get('/by-nature/:natureCode?', OperationController.getOperationsByNature);
router.get('/nature-type-codes/:operationNatureCode', OperationController.getOperationNatureTypeCodes);


module.exports = router;