const express = require("express");
const router = express.Router();
const ProductsValidationRules = require ("../validation-rules/product.rule")
const validateMiddleware = require("../middlewares/validate.middleware");
const ProductsController = require("../controllers/products.controller")
// below line has to be added in every route file
// this is perhaps some bug in express-async-errors
// adding below line only once in index.js doesn't works
require("express-async-errors");

router.get("/", ProductsController.search);
router.get("/:code/types", ProductsController.getProductTypes);
router.get("/:id", ProductsController.getOne);
router.post("/", validateMiddleware(ProductsValidationRules.create), ProductsController.create);
router.delete("/:id", ProductsController.remove);
router.put("/:id",validateMiddleware(ProductsValidationRules.create), ProductsController.update);

module.exports = router;