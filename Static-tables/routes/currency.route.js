const express = require("express");
const router = express.Router();
const CurrencyValidation = require ("../validation-rules/currency.rule")
const validateMiddleware = require("../middlewares/validate.middleware");
const CurrencyController = require("../controllers/currencies.controller")
// below line has to be added in every route file
// this is perhaps some bug in express-async-errors
// adding below line only once in index.js doesn't works
require("express-async-errors");


router.get("/", CurrencyController.search);
router.get("/:id", CurrencyController.getOne);
router.post("/", validateMiddleware(CurrencyValidation.create), CurrencyController.create);
router.delete("/:id", CurrencyController.remove);
router.put("/:id",validateMiddleware(CurrencyValidation.create), CurrencyController.update);

module.exports = router;