const express = require("express");
const router = express.Router();
const ProfilesValidationRules = require ("../validation-rules/profile.rule")
const validateMiddleware = require("../middlewares/validate.middleware");
const ProfilesController = require("../controllers/profiles.controller")
require("express-async-errors");

router.get("/", ProfilesController.search);
router.post("/", validateMiddleware(ProfilesValidationRules.create), ProfilesController.create);
router.delete("/:id", ProfilesController.remove);
router.put("/:id",validateMiddleware(ProfilesValidationRules.create), ProfilesController.update);

module.exports = router;