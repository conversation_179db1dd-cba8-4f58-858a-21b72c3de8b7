const express = require("express");
const router = express.Router();
const TaxValidationRules = require ("../validation-rules/tax.rule")
const validateMiddleware = require("../middlewares/validate.middleware");
const TaxController = require("../controllers/taxes.controller")
const TaxMiddleware = require("../middlewares/tax_filter_by_country_code.middleware");
// below line has to be added in every route file
// this is perhaps some bug in express-async-errors
// adding below line only once in index.js doesn't works
require("express-async-errors");

router.get("/",TaxMiddleware, TaxController.search);
router.get("/:id",TaxMiddleware, TaxController.getOne);
router.post("/", validateMiddleware(TaxValidationRules.create), TaxController.create);
router.delete("/:code", TaxController.remove);
router.put("/:id", validateMiddleware(TaxValidationRules.update), TaxController.update);
router.patch("/:id",validateMiddleware(TaxValidationRules.update), TaxController.patch);

module.exports = router;