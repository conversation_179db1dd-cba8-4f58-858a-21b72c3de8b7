const express = require("express");
const router = express.Router();
const controller = require("../controllers/singleAuto.controller");
const SingleAutoValidation = require ("../validation-rules/single_auto.rule")
const validateMiddleware = require("../middlewares/validate.middleware");
const filterMiddleware = require("../middlewares/filter.middleware");

require("express-async-errors");

router.get("/",filterMiddleware, controller.getAllSingleAutos); 
router.get("/:id", controller.getSingleAutoById); 
router.get("/:brand_code/cars",filterMiddleware, controller.getSingleAutosByBrandCode); 
router.post("/bulk-create", validateMiddleware(SingleAutoValidation.upload),controller.uploadAutos); 
router.post("/", controller.createSingleAuto); 
router.put("/:id", controller.updateSingleAuto); 
router.delete("/:id", controller.deleteSingleAuto); 

module.exports = router;