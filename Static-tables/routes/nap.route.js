const express = require("express");
const router = express.Router();
const validateMiddleware = require("../middlewares/validate.middleware");
const NapsValidation = require ("../validation-rules/nap.rule");
const napController = require("../controllers/nap.controller");

router.get("/", napController.search);
router.get("/equipments", napController.getNapsWithEquipments);
router.get("/:code", napController.getOne);
router.post("/",validateMiddleware(NapsValidation.create), napController.create);
router.post("/bulk-create",validateMiddleware(NapsValidation.upload), napController.upload);
router.put("/:code",validateMiddleware(NapsValidation.update), napController.update);
router.delete("/:code", napController.remove);

module.exports = router;