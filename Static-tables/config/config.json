{"local": {"username": "postgres", "password": "U6GjpKQpsrwjZI", "database": "dt-static-tables", "host": "postgres", "port": 5432, "dialect": "postgres"}, "development": {"username": "postgres", "password": "U6GjpKQpsrwjZI", "database": "dt-static-tables", "host": "localhost", "port": 5432, "dialect": "postgres"}, "test": {"username": "dt-stables", "password": "sqdf5sSD30!", "database": "test-dt-static-tables", "host": "postgres-kyra-static-tables", "port": 5432, "dialect": "postgres"}, "integ": {"username": "dt-stables", "password": "sqdf5sSD30!", "database": "integ-dt-static-tables", "host": "postgres-kyra-static-tables", "port": 5432, "dialect": "postgres"}, "staging": {"username": "dt-stables", "password": "sqdf5sSD30!", "database": "staging-dt-static-tables", "host": "postgres-kyra-static-tables", "port": 5432, "dialect": "postgres"}}