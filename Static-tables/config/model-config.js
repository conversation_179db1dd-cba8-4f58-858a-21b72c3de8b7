/**
 * Configuration for static table models
 * Defines which models have active columns, their translation models, and specific attributes
 */

const modelConfigurations = {
  // Models with active column
  milestone: {
    hasActive: true,
    hasSystemAttribute: true,
    model: 'MilestoneModel',
    translationModel: 'MilestoneTranslationsModel',
    translationKey: 'milestone_code',
    entityName: 'Milestone',
    tableName: 'milestones',
    specificAttributes: ['rate', 'start_date', 'end_date'],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.milestone_translations?.[0]?.label || item.label,
      active: item.active,
      system_attribute: item.system_attribute,
      rate: item.rate,
      start_date: item.start_date,
      end_date: item.end_date,
    })
  },

  activity: {
    hasActive: true,
    hasSystemAttribute: true,
    model: 'ActivityModel',
    translationModel: 'ActivityTranslation',
    translationKey: 'activity_code',
    entityName: 'Activity',
    tableName: 'activities',
    specificAttributes: ['associated_to'],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.activity_translations?.[0]?.label || item.label,
      active: item.active,
      system_attribute: item.system_attribute,
      associated_to: item.associated_to,
    })
  },

  currency: {
    hasActive: true,
    hasSystemAttribute: false,
    model: 'CurrencyModel',
    translationModel: 'CurrencyTranslationsModel',
    translationKey: 'currency_code',
    entityName: 'Currency',
    tableName: 'currencies',
    specificAttributes: ['symbol', 'language', 'decimal_number', 'unit', 'final_effectiveDate', 'intermediate_period_start_date', 'default_currency', 'country_code'],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.currency_translations?.[0]?.label || item.label,
      active: item.active,
      symbol: item.symbol,
      language: item.language,
      decimal_number: item.decimal_number,
      unit: item.unit,
      final_effectiveDate: item.final_effectiveDate,
      intermediate_period_start_date: item.intermediate_period_start_date,
      default_currency: item.default_currency,
      country_code: item.country_code,
    })
  },

  // Models without active column
  phase: {
    hasActive: false,
    hasSystemAttribute: false,
    model: 'PhaseModel',
    translationModel: 'PhaseTranslationsModel',
    translationKey: 'phase_code',
    entityName: 'Phase',
    tableName: 'phases',
    specificAttributes: ['associated_to', 'associated_to_label'],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.phase_translations?.[0]?.label || item.label,
      language_code: language || config.language,
      associated_to: item.associated_to,
      associated_to_label: item.associated_to_label,
    })
  },

  country: {
    hasActive: false,
    hasSystemAttribute: false,
    model: 'CountriesModel',
    translationModel: 'CountryTranslationsModel',
    translationKey: 'country_code',
    entityName: 'Country',
    tableName: 'countries',
    specificAttributes: ['language', 'country_tax_code'],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.country_translations?.[0]?.label || item.label,
      language: item.language,
      country_tax_code: item.country_tax_code,
    })
  },

  delegation: {
    hasActive: false,
    hasSystemAttribute: false,
    model: 'DelegationModel',
    translationModel: 'DelegationTranslationsModel',
    translationKey: 'delegation_code',
    entityName: 'Delegation',
    tableName: 'delegations',
    specificAttributes: [],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.delegation_translations?.[0]?.label || item.label,
    })
  },

  // Additional models can be added here
  autoCatalog: {
    hasActive: true,
    hasSystemAttribute: true,
    model: 'AutoCatalogModel',
    translationModel: 'AutoCatalogTranslationsModel',
    translationKey: 'auto_catalog_code',
    entityName: 'AutoCatalog',
    tableName: 'auto_catalogs',
    specificAttributes: ['start_date', 'end_date', 'description'],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.auto_catalog_translations?.[0]?.label || item.label,
      active: item.active,
      system_attribute: item.system_attribute,
      start_date: item.start_date,
      end_date: item.end_date,
      description: item.description,
    })
  },

  market: {
    hasActive: true,
    hasSystemAttribute: true,
    model: 'MarketModel',
    translationModel: 'MarketTranslationsModel',
    translationKey: 'market_code',
    entityName: 'Market',
    tableName: 'markets',
    specificAttributes: ['description'],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.market_translations?.[0]?.label || item.label,
      active: item.active,
      system_attribute: item.system_attribute,
      description: item.description,
    })
  },

  profile: {
    hasActive: true,
    hasSystemAttribute: true,
    model: 'ProfileModel',
    translationModel: 'ProfileTranslationsModel',
    translationKey: 'profile_code',
    entityName: 'Profile',
    tableName: 'profiles',
    specificAttributes: ['description'],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.profile_translations?.[0]?.label || item.label,
      active: item.active,
      system_attribute: item.system_attribute,
      description: item.description,
    })
  },

  // Models without active column
  tax: {
    hasActive: true,
    hasSystemAttribute: false,
    model: 'TaxModel',
    translationModel: 'TaxTranslationsModel',
    translationKey: 'tax_code',
    entityName: 'Tax',
    tableName: 'taxes',
    specificAttributes: ['start_date', 'end_date', 'country', 'type', 'description'],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.tax_translations?.[0]?.label || item.label,
      start_date: item.start_date,
      end_date: item.end_date,
      country: item.country,
      country_name: item.countryDetails?.label,
      country_tax_code: item.countryDetails?.country_tax_code,
      type: item.type,
      active: item.active,
      description: item.description,
    })
  },

  taxRate: {
    hasActive: true,
    hasSystemAttribute: false,
    model: 'TaxrateModel',
    translationModel: 'TaxRateTranslationsModel',
    translationKey: 'tax_rate_code',
    entityName: 'TaxRate',
    tableName: 'tax_rates',
    specificAttributes: ['rate', 'start_date', 'end_date'],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.tax_rate_translations?.[0]?.label || item.label,
      rate: item.rate,
      start_date: item.start_date,
      end_date: item.end_date,
      tax_code: item.tax_code,
      active: item.active,
    })
  },

  // Additional models for migration
  allocation: {
    hasActive: false,
    hasSystemAttribute: false,
    model: 'AllocationModel',
    translationModel: 'AllocationTranslationsModel',
    translationKey: 'allocation_code',
    entityName: 'Allocation',
    tableName: 'allocations',
    specificAttributes: ['allocation_category_code', 'operation_code'],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.allocation_translations?.[0]?.label || item.label,
      allocation_category_code: item.allocation_category_code,
      operation_code: item.operation_code,
    })
  },

  allocationCategory: {
    hasActive: false,
    hasSystemAttribute: false,
    model: 'Allocation_CategoryModel',
    translationModel: null,
    translationKey: null,
    entityName: 'AllocationCategory',
    tableName: 'allocation_categories',
    specificAttributes: [],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.label,
    })
  },

  bank: {
    hasActive: false,
    hasSystemAttribute: false,
    model: 'BankModel',
    translationModel: null,
    translationKey: null,
    entityName: 'Bank',
    tableName: 'banks',
    specificAttributes: ['swift_code', 'country_code'],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.label,
      swift_code: item.swift_code,
      country_code: item.country_code,
    })
  },

  language: {
    hasActive: false,
    hasSystemAttribute: false,
    model: 'LanguageModel',
    translationModel: null,
    translationKey: null,
    entityName: 'Language',
    tableName: 'languages',
    specificAttributes: [],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.label,
    })
  },

  view: {
    hasActive: false,
    hasSystemAttribute: false,
    model: 'ViewModel',
    translationModel: null,
    translationKey: null,
    entityName: 'View',
    tableName: 'views',
    specificAttributes: [],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.label,
    })
  },

  // Enhanced models with relationships
  legalCategory: {
    hasActive: false,
    hasSystemAttribute: true,
    model: 'LegalCategoryModel',
    translationModel: 'LegalCategoryTranslationsModel',
    translationKey: 'legal_category_code',
    entityName: 'LegalCategory',
    tableName: 'legal_categories',
    specificAttributes: [],
    relationships: [
      {
        model: 'CountriesModel',
        includeTranslations: true,
        translationModel: 'CountryTranslationsModel',
      }
    ],
    relationshipValidations: [{
        model: 'CountriesModel',
        field: 'code',
        bodyField: 'country_code',
        entityName: 'Country'
    }],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.legal_category_translations?.[0]?.label || item.label,
      system_attribute: item.system_attribute,
      country: {
        id: item.country?.id,
        code: item.country?.code,
        label: item.country?.country_translations?.[0]?.label || item.country?.label,
      },
    })
  },

  equipment: {
    hasActive: true,
    hasSystemAttribute: true,
    model: 'EquipmentModel',
    translationModel: null,
    translationKey: null,
    entityName: 'Equipment',
    tableName: 'equipments',
    specificAttributes: ['category', 'type', 'market_code'],
    relationships: [
      {
        model: 'NapModel',
        as: 'category',
        attributes: { exclude: ["updatedAt", "createdAt"] },
      },
      {
        model: 'CountriesModel',
        attributes: { exclude: ["updatedAt", "createdAt"] },
      },
      {
        model: 'MarketModel',
        attributes: { exclude: ["updatedAt", "createdAt"] },
      }
    ],
    relationshipValidations: [
      {
        model: 'NapModel',
        field: 'code',
        bodyField: 'category',
        entityName: 'NAP Category'
      },
      {
        model: 'CountriesModel',
        field: 'code',
        bodyField: 'country_code',
        entityName: 'Country'
      },
      {
        model: 'MarketModel',
        field: 'code',
        bodyField: 'market_code',
        entityName: 'Market'
      }
    ],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.label,
      active: item.active,
      system_attribute: item.system_attribute,
      category: item.category,
      country: item.country,
      market: item.market,
    })
  },

  naf: {
    hasActive: false,
    hasSystemAttribute: true,
    model: 'NafModel',
    translationModel: 'NafTranslationsModel',
    translationKey: 'naf_code',
    entityName: 'Naf',
    tableName: 'nafs',
    specificAttributes: ['country_code'],
    relationships: [
      {
        model: 'CountriesModel',
        includeTranslations: true,
        translationModel: 'CountryTranslationsModel',
      }
    ],
    relationshipValidations: [
      {
        model: 'CountriesModel',
        field: 'code',
        bodyField: 'country_code',
        entityName: 'Country'
      }
    ],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.naf_translations?.[0]?.label || item.label,
      system_attribute: item.system_attribute,
      country_code: item.country_code,
      country: {
        id: item.country?.id,
        code: item.country?.code,
        label: item.country?.country_translations?.[0]?.label || item.country?.label,
      },
    })
  },

  product: {
    hasActive: true,
    hasSystemAttribute: true,
    model: 'ProductsModel',
    translationModel: 'ProductTranslationsModel',
    translationKey: 'product_code',
    entityName: 'Product',
    tableName: 'products',
    specificAttributes: ['activity_code', 'activity_associated_to', 'start_date', 'end_date'],
    relationships: [
      {
        model: 'ActivityModel',
        where: { associated_to: "DOSSIER" },
        includeTranslations: true,
        translationModel: 'ActivityTranslation',
      }
    ],
    relationshipValidations: [
      {
        model: 'ActivityModel',
        field: 'code',
        bodyField: 'activity_code',
        entityName: 'Activity'
      }
    ],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.product_translations?.[0]?.label || item.label,
      start_date: item.start_date,
      end_date: item.end_date,
      system_attribute: item.system_attribute,
      active: item.active,
      activity: {
        id: item.activity?.id,
        code: item.activity?.code,
        label: item.activity?.activity_translations?.[0]?.label || item.activity?.label,
        active: item.activity?.active,
      },
    })
  },

  nap: {
    hasActive: false,
    hasSystemAttribute: true,
    model: 'NapModel',
    translationModel: null,
    translationKey: null,
    entityName: 'Nap',
    tableName: 'naps',
    specificAttributes: [],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.label,
      system_attribute: item.system_attribute,
      country_code: item.country_code,
    })
  },

  nature: {
    hasActive: false,
    hasSystemAttribute: false,
    model: 'NatureModel',
    translationModel: 'NatureTranslationsModel',
    translationKey: 'nature_code',
    entityName: 'Nature',
    tableName: 'natures',
    specificAttributes: [],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.nature_translations?.[0]?.label || item.label,
    })
  },

  operation: {
    hasActive: true,
    hasSystemAttribute: true,
    model: 'OperationModel',
    translationModel: 'OperationTranslationsModel',
    translationKey: 'operation_code',
    entityName: 'Operation',
    tableName: 'operations',
    specificAttributes: [],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.operation_translations?.[0]?.label || item.label,
      active: item.active,
      system_attribute: item.system_attribute,
    })
  },

  role: {
    hasActive: true,
    hasSystemAttribute: true,
    model: 'RoleModel',
    translationModel: 'RoleTranslationsModel',
    translationKey: 'role_code',
    entityName: 'Role',
    tableName: 'roles',
    specificAttributes: [],
    relationships: [{
        model: 'StaticRoleModel',
        includeTranslations: true,
        translationModel: 'StaticRoleTranslationsModel',
    }],
    relationshipValidations: [{
        model: 'StaticRoleModel',
        field: 'code',
        bodyField: 'static_role_code',
        entityName: 'Static Role'
    }],
    responseMapping: (item, language, config) => ({
      id: item.id,
      code: item.code,
      label: item.role_translations?.[0]?.label || item.label,
      active: item.active,
      system_attribute: item.system_attribute,
      static_role: {
        id: item.static_role?.id,
        code: item.static_role?.code,
        label:
            item.static_role?.static_role_translations?.[0]?.label ||
            item.static_role?.label,
        is_exclusive: item.static_role?.is_exclusive,
        is_client: item.static_role?.is_client,
        associated_to: item.static_role?.associated_to,
      }
    })
  },
};

module.exports = {
  modelConfigurations,
  getModelConfig: (modelName) => modelConfigurations[modelName],
  hasActiveColumn: (modelName) => modelConfigurations[modelName]?.hasActive || false,
  hasSystemAttribute: (modelName) => modelConfigurations[modelName]?.hasSystemAttribute || false,

  // Helper functions
  getAllModelsWithActive: () => Object.keys(modelConfigurations).filter(key => modelConfigurations[key].hasActive),
  getAllModelsWithoutActive: () => Object.keys(modelConfigurations).filter(key => !modelConfigurations[key].hasActive),
  getModelsBySystemAttribute: () => Object.keys(modelConfigurations).filter(key => modelConfigurations[key].hasSystemAttribute),
};
