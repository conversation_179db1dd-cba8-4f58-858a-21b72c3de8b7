#!/usr/bin/env node

/**
 * Liquibase Status Script
 * Shows the status of database migrations
 */

require('dotenv').config({
  path: `.env.${process.env.NODE_ENV}`,
});

const LiquibaseConfig = require('../config/liquibase-config');

async function getStatus() {
  try {
    const liquibaseConfig = new LiquibaseConfig();
    await liquibaseConfig.getStatus();
    
    console.log('✅ Status check completed!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Status check failed:', error);
    process.exit(1);
  }
}

getStatus();
