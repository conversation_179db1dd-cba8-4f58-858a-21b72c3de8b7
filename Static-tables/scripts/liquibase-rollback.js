#!/usr/bin/env node

/**
 * Liquibase Rollback Script
 * Rolls back database migrations
 */

require('dotenv').config({
  path: `.env.${process.env.NODE_ENV}`,
});

const LiquibaseConfig = require('../config/liquibase-config');

async function rollbackMigrations() {
  try {
    const count = process.argv[2] ? parseInt(process.argv[2]) : 1;
    
    const liquibaseConfig = new LiquibaseConfig();
    await liquibaseConfig.rollback(count);
    
    console.log('✅ Rollback completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    process.exit(1);
  }
}

rollbackMigrations();
