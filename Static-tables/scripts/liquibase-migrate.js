#!/usr/bin/env node

/**
 * Liquibase Migration Script
 * Runs database migrations manually
 */

require('dotenv').config({
  path: `.env.${process.env.NODE_ENV}`,
});

const LiquibaseConfig = require('../config/liquibase-config');

async function runMigrations() {
  try {
    const liquibaseConfig = new LiquibaseConfig();
    await liquibaseConfig.runMigrations();
    
    console.log('✅ Migrations completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

runMigrations();
