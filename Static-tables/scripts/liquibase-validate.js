#!/usr/bin/env node

/**
 * Liquibase Validation Script
 * Validates the changelog files
 */

require('dotenv').config({
  path: `.env.${process.env.NODE_ENV}`,
});

const LiquibaseConfig = require('../config/liquibase-config');

async function validateChangelog() {
  try {
    const liquibaseConfig = new LiquibaseConfig();
    await liquibaseConfig.validate();
    
    console.log('✅ Changelog validation completed!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Changelog validation failed:', error);
    process.exit(1);
  }
}

validateChangelog();
