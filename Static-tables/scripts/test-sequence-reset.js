#!/usr/bin/env node

/**
 * Test Script for Sequence Reset Functionality
 * 
 * This script tests the sequence reset functionality by:
 * 1. Creating a test table with auto-increment ID
 * 2. Inserting data with explicit IDs
 * 3. Verifying sequence is out of sync
 * 4. Resetting the sequence
 * 5. Verifying sequence is correctly set
 * 6. Testing that new inserts work without conflicts
 * 7. Cleaning up test data
 */

require('dotenv').config({
  path: `.env.${process.env.NODE_ENV}`,
});

const { Pool } = require('pg');
const appConfig = require('../config/app-config');
const SequenceResetter = require('./reset-sequences');

class SequenceResetTester {
  constructor() {
    this.pool = new Pool({
      host: appConfig.db.host,
      port: appConfig.db.port,
      database: appConfig.db.name,
      user: appConfig.db.user,
      password: appConfig.db.password,
    });
    this.testTableName = 'test_sequence_reset_' + Date.now();
  }

  async createTestTable() {
    console.log(`📋 Creating test table: ${this.testTableName}`);
    
    await this.pool.query(`
      CREATE TABLE ${this.testTableName} (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ Test table created');
  }

  async insertTestDataWithExplicitIds() {
    console.log('📝 Inserting test data with explicit IDs...');
    
    await this.pool.query(`
      INSERT INTO ${this.testTableName} (id, name) VALUES 
        (100, 'Test Item 100'),
        (200, 'Test Item 200'),
        (300, 'Test Item 300')
    `);
    
    console.log('✅ Test data inserted with explicit IDs: 100, 200, 300');
  }

  async checkSequenceStatus() {
    const sequenceName = `${this.testTableName}_id_seq`;
    
    // Get current sequence value
    const seqResult = await this.pool.query(`
      SELECT last_value, is_called FROM ${sequenceName}
    `);
    const { last_value, is_called } = seqResult.rows[0];
    
    // Get max ID from table
    const maxResult = await this.pool.query(`
      SELECT MAX(id) as max_id FROM ${this.testTableName}
    `);
    const maxId = maxResult.rows[0].max_id;
    
    const nextValue = is_called ? last_value + 1 : last_value;
    
    console.log(`📊 Sequence Status:`);
    console.log(`   - Sequence name: ${sequenceName}`);
    console.log(`   - Current sequence value: ${last_value} (is_called: ${is_called})`);
    console.log(`   - Next sequence value: ${nextValue}`);
    console.log(`   - Max ID in table: ${maxId}`);
    console.log(`   - Conflict risk: ${nextValue <= maxId ? '⚠️  YES' : '✅ NO'}`);
    
    return {
      sequenceName,
      currentValue: last_value,
      nextValue,
      maxId,
      hasConflict: nextValue <= maxId
    };
  }

  async testInsertWithoutExplicitId() {
    console.log('🧪 Testing insert without explicit ID...');
    
    try {
      const result = await this.pool.query(`
        INSERT INTO ${this.testTableName} (name) VALUES ('Auto Generated ID') RETURNING id
      `);
      
      const newId = result.rows[0].id;
      console.log(`✅ Successfully inserted record with auto-generated ID: ${newId}`);
      return { success: true, id: newId };
    } catch (error) {
      console.log(`❌ Failed to insert record: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  async resetSequenceForTestTable() {
    console.log('🔄 Resetting sequence for test table...');
    
    const resetter = new SequenceResetter();
    const result = await resetter.resetSequence(this.testTableName, 'id');
    await resetter.close();
    
    if (result.success) {
      console.log(`✅ Sequence reset: ${result.oldValue} → ${result.newValue}`);
    } else {
      console.log(`❌ Failed to reset sequence: ${result.error}`);
    }
    
    return result;
  }

  async cleanupTestTable() {
    console.log(`🧹 Cleaning up test table: ${this.testTableName}`);
    
    await this.pool.query(`DROP TABLE IF EXISTS ${this.testTableName}`);
    console.log('✅ Test table cleaned up');
  }

  async close() {
    await this.pool.end();
  }

  async runFullTest() {
    console.log('🚀 Starting Sequence Reset Test');
    console.log('================================\n');
    
    try {
      // Step 1: Create test table
      await this.createTestTable();
      
      // Step 2: Check initial sequence status
      console.log('\n📊 Initial sequence status:');
      await this.checkSequenceStatus();
      
      // Step 3: Insert data with explicit IDs
      console.log('\n');
      await this.insertTestDataWithExplicitIds();
      
      // Step 4: Check sequence status after explicit inserts
      console.log('\n📊 Sequence status after explicit ID inserts:');
      const statusBefore = await this.checkSequenceStatus();
      
      // Step 5: Test insert without explicit ID (should fail if sequence is out of sync)
      console.log('\n');
      const insertTestBefore = await this.testInsertWithoutExplicitId();
      
      if (!insertTestBefore.success && statusBefore.hasConflict) {
        console.log('✅ Confirmed: Sequence conflict detected as expected');
      }
      
      // Step 6: Reset sequence
      console.log('\n');
      const resetResult = await this.resetSequenceForTestTable();
      
      if (!resetResult.success) {
        throw new Error(`Sequence reset failed: ${resetResult.error}`);
      }
      
      // Step 7: Check sequence status after reset
      console.log('\n📊 Sequence status after reset:');
      const statusAfter = await this.checkSequenceStatus();
      
      // Step 8: Test insert without explicit ID (should work now)
      console.log('\n');
      const insertTestAfter = await this.testInsertWithoutExplicitId();
      
      if (insertTestAfter.success) {
        console.log('✅ Confirmed: Insert works correctly after sequence reset');
      }
      
      // Step 9: Verify the new ID is correct
      if (insertTestAfter.success && insertTestAfter.id > statusBefore.maxId) {
        console.log(`✅ Confirmed: New ID (${insertTestAfter.id}) is greater than previous max (${statusBefore.maxId})`);
      }
      
      console.log('\n🎉 All tests passed! Sequence reset functionality is working correctly.');
      
      return {
        success: true,
        results: {
          statusBefore,
          statusAfter,
          insertTestBefore,
          insertTestAfter,
          resetResult
        }
      };
      
    } catch (error) {
      console.error('\n💥 Test failed:', error.message);
      return {
        success: false,
        error: error.message
      };
    } finally {
      // Always cleanup
      try {
        await this.cleanupTestTable();
      } catch (cleanupError) {
        console.warn('⚠️  Cleanup warning:', cleanupError.message);
      }
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  const tester = new SequenceResetTester();
  
  try {
    const result = await tester.runFullTest();
    
    if (result.success) {
      console.log('\n✅ Test completed successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ Test failed!');
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 Unexpected error:', error.message);
    process.exit(1);
  } finally {
    await tester.close();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = SequenceResetTester;
