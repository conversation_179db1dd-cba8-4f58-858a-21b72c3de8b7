<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.0.xsd">

    <changeSet id="reset-sequences-after-data-insert" author="system" runAlways="true">
        <comment>Reset PostgreSQL sequences after inserting data with explicit IDs</comment>
        
        <!-- Reset sequences for all tables with auto-increment primary keys -->
        <sql>
            DO $$
            DECLARE
                rec RECORD;
                max_id INTEGER;
                sequence_name TEXT;
            BEGIN
                -- Loop through all tables with auto-increment sequences
                FOR rec IN 
                    SELECT 
                        t.table_name,
                        c.column_name,
                        pg_get_serial_sequence(t.table_name, c.column_name) as seq_name
                    FROM information_schema.tables t
                    JOIN information_schema.columns c ON t.table_name = c.table_name
                    WHERE t.table_schema = 'public'
                      AND t.table_type = 'BASE TABLE'
                      AND c.column_default LIKE 'nextval%'
                      AND pg_get_serial_sequence(t.table_name, c.column_name) IS NOT NULL
                LOOP
                    -- Get the maximum ID from the table
                    EXECUTE format('SELECT COALESCE(MAX(%I), 0) FROM %I', rec.column_name, rec.table_name) INTO max_id;
                    
                    -- Reset the sequence to max_id + 1
                    EXECUTE format('SELECT setval(%L, %s, false)', rec.seq_name, max_id + 1);
                    
                    -- Log the operation (this will appear in Liquibase logs)
                    RAISE NOTICE 'Reset sequence % for table %.% to %', rec.seq_name, rec.table_name, rec.column_name, max_id + 1;
                END LOOP;
            END $$;
        </sql>
        
        <rollback>
            <comment>No rollback needed for sequence reset</comment>
        </rollback>
    </changeSet>

    <!-- Alternative changeset for specific tables if needed -->
    <changeSet id="reset-specific-sequences" author="system" runOnChange="false">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_name = 'sequence_reset_not_needed'
            </sqlCheck>
        </preConditions>
        
        <comment>Reset sequences for specific tables (use this as template)</comment>
        
        <!-- Example for activities table -->
        <sql>
            SELECT setval(
                pg_get_serial_sequence('activities', 'id'), 
                COALESCE((SELECT MAX(id) FROM activities), 0) + 1, 
                false
            );
        </sql>
        
        <!-- Example for currencies table -->
        <sql>
            SELECT setval(
                pg_get_serial_sequence('currencies', 'id'), 
                COALESCE((SELECT MAX(id) FROM currencies), 0) + 1, 
                false
            );
        </sql>
        
        <!-- Example for countries table -->
        <sql>
            SELECT setval(
                pg_get_serial_sequence('countries', 'id'), 
                COALESCE((SELECT MAX(id) FROM countries), 0) + 1, 
                false
            );
        </sql>
        
        <rollback>
            <comment>No rollback needed for sequence reset</comment>
        </rollback>
    </changeSet>

</databaseChangeLog>
