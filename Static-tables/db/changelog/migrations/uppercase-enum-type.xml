<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="HoussemMOUSSA (generated)" id="enum-1">
        <sql>
            CREATE TYPE enum_services_type_of_service_new AS ENUM (
                'MAINTENANCE',
                'INSURANCE',
                'REPLACEMENT_VEHICLE',
                'PECUNIARY_LOSS',
                'FINANCIAL_LOSS',
                'ADMINISTRATIVE_FEES'
            );

            ALTER TABLE services
            ALTER COLUMN type_of_service TYPE enum_services_type_of_service_new
                USING UPPER(type_of_service::text)::enum_services_type_of_service_new;

            DROP TYPE enum_services_type_of_service;

            ALTER TYPE enum_services_type_of_service_new RENAME TO enum_services_type_of_service;

        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="enum-2">
        <sql>
            CREATE TYPE enum_services_type_of_cover_new AS ENUM (
                'THIRD_PARTY',
                'MATERIAL'
            );

            ALTER TABLE services
            ALTER COLUMN type_of_cover TYPE enum_services_type_of_cover_new
                USING UPPER(type_of_cover::text)::enum_services_type_of_cover_new;

            DROP TYPE enum_services_type_of_cover;

            ALTER TYPE enum_services_type_of_cover_new RENAME TO enum_services_type_of_cover;
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="enum-3">
        <sql>
            ALTER TABLE services ALTER COLUMN status DROP DEFAULT;

            CREATE TYPE enum_services_status_new AS ENUM (
                'INITIAL',
                'ACTIVE',
                'EXPIRED'
            );

            ALTER TABLE services
            ALTER COLUMN status TYPE enum_services_status_new
                USING UPPER(status::text)::enum_services_status_new;

            DROP TYPE enum_services_status;

            ALTER TYPE enum_services_status_new RENAME TO enum_services_status;

            ALTER TABLE services ALTER COLUMN status SET DEFAULT 'INITIAL'::enum_services_status;
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="enum-4">
        <sql>
            CREATE TYPE enum_services_calculation_method_new AS ENUM (
                'PERCENTAGE',
                'FIXED_AMOUNT',
                'MATRIX'
            );

            ALTER TABLE services
            ALTER COLUMN calculation_method TYPE enum_services_calculation_method_new
                USING UPPER(calculation_method::text)::enum_services_calculation_method_new;

            DROP TYPE enum_services_calculation_method;

            ALTER TYPE enum_services_calculation_method_new RENAME TO enum_services_calculation_method;
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="enum-5">
        <sql>
            CREATE TYPE enum_services_billing_method_new AS ENUM (
                'SUBSCRIPTION',
                'FLAT'
            );

            ALTER TABLE services
            ALTER COLUMN billing_method TYPE enum_services_billing_method_new
                USING UPPER(billing_method::text)::enum_services_billing_method_new;

            DROP TYPE enum_services_billing_method;

            ALTER TYPE enum_services_billing_method_new RENAME TO enum_services_billing_method;
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="enum-6">
        <sql>
            CREATE TYPE enum_partners_calculation_method_new AS ENUM (
                'PERCENTAGE',
                'FIXED_AMOUNT'
            );

            ALTER TABLE partners
            ALTER COLUMN calculation_method TYPE enum_partners_calculation_method_new
                USING UPPER(calculation_method::text)::enum_partners_calculation_method_new;

            DROP TYPE enum_partners_calculation_method;

            ALTER TYPE enum_partners_calculation_method_new RENAME TO enum_partners_calculation_method;
        </sql>
    </changeSet>
</databaseChangeLog>
