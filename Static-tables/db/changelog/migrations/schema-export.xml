<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="HoussemMOUSSA (generated)" id="*************-219">
        <createTable tableName="service-services_packs">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="service-services_packs_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="service_reference" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="service_pack_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-221">
        <createTable tableName="activities">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="activities_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="associated_to" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="system_attribute" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-222">
        <createTable tableName="auto_catalogs">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="auto_catalogs_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="start_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="end_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="description" type="TEXT"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column defaultValueBoolean="false" name="system_attribute" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-223">
        <createTable tableName="currencies">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="currencies_pkey"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="symbol" type="VARCHAR(255)"/>
            <column name="language" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="decimal_number" type="INTEGER"/>
            <column name="unit" type="INTEGER"/>
            <column name="final_effectiveDate" type="date"/>
            <column name="intermediate_period_start_date" type="date"/>
            <column defaultValueBoolean="false" name="default_currency" type="BOOLEAN"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN"/>
            <column name="country_code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-224">
        <createTable tableName="equipments">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="equipments_pkey"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="category_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="country_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="market_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="system_attribute" type="BOOLEAN"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-225">
        <createTable tableName="markets">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="markets_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="system_attribute" type="BOOLEAN"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-226">
        <createTable tableName="milestones">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="milestones_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="rate" type="FLOAT8"/>
            <column name="start_date" type="date"/>
            <column name="end_date" type="date"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column defaultValueBoolean="false" name="system_attribute" type="BOOLEAN"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-227">
        <createTable tableName="operations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="operations_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="system_attribute" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-228">
        <createTable tableName="payment_method_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="payment_method_translations_pkey"/>
            </column>
            <column name="payment_method_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="requires_bank_account" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="manual_transaction" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="exchange_file" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="bank_card" type="BOOLEAN"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-229">
        <createTable tableName="payment_methods">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="payment_methods_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="requires_bank_account" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="manual_transaction" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="exchange_file" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="bank_card" type="BOOLEAN"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column defaultValueBoolean="true" name="system_attribute" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-230">
        <createTable tableName="products">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="products_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="system_attribute" type="BOOLEAN"/>
            <column name="start_date" type="date"/>
            <column name="end_date" type="date"/>
            <column name="activity_code" type="VARCHAR(255)"/>
            <column name="activity_associated_to" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-231">
        <createTable tableName="profiles">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="profiles_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="description" type="VARCHAR(255)"/>
            <column defaultValueBoolean="false" name="system_attribute" type="BOOLEAN"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-232">
        <createTable tableName="roles">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="roles_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="system_attribute" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="static_role_code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-233">
        <createTable tableName="taxes">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="taxes_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="start_date" type="date"/>
            <column name="end_date" type="date"/>
            <column name="country" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="description" type="VARCHAR(255)"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN"/>
            <column name="system_attribute" type="BOOLEAN"/>
            <column name="type" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-234">
        <createTable tableName="activity_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="activity_translations_pkey"/>
            </column>
            <column name="activity_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="activity_associated_to" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-235">
        <createTable tableName="allocation_categories">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="allocation_categories_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-236">
        <createTable tableName="allocation_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="allocation_translations_pkey"/>
            </column>
            <column name="allocation_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="allocation_category_code" type="VARCHAR(255)"/>
            <column name="operation_code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-237">
        <createTable tableName="allocations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="allocations_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="allocation_category_code" type="VARCHAR(255)"/>
            <column name="operation_code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-238">
        <createTable tableName="application_criteria_actors">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="application_criteria_actors_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="application_criteria_reference" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="actor_reference" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-239">
        <createTable tableName="static_roles">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="static_roles_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="is_exclusive" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="is_client" type="BOOLEAN"/>
            <column defaultValue="ACTEUR" name="associated_to" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-240">
        <createTable tableName="banks">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="banks_pkey"/>
            </column>
            <column name="bank_id" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="country_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="bank_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code_swift" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code_branch" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="city" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="postal_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="second_address" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-241">
        <createTable tableName="business_activities">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="business_activities_pkey"/>
            </column>
            <column name="activity_code" type="VARCHAR(255)"/>
            <column name="business_reference" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column defaultValue="" name="business_name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-242">
        <createTable tableName="businesses_activity_product_allocations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="businesses_activity_product_allocations_pkey"/>
            </column>
            <column name="activity_code" type="VARCHAR(255)"/>
            <column name="product_code" type="VARCHAR(255)"/>
            <column name="allocation_code" type="VARCHAR(255)"/>
            <column name="business_reference" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-243">
        <createTable tableName="businesses_activity_products">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="businesses_activity_products_pkey"/>
            </column>
            <column name="activity_code" type="VARCHAR(255)"/>
            <column name="product_code" type="VARCHAR(255)"/>
            <column name="business_reference" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-244">
        <createTable tableName="commercial_products">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="commercial_products_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="product_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="activity_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="default_duration" type="INTEGER"/>
            <column name="start_date" type="date"/>
            <column name="end_date" type="date"/>
            <column name="description" type="TEXT"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-245">
        <createTable tableName="countries">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="countries_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column defaultValue="true" name="country_tax_code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-246">
        <createTable tableName="country_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="country_translations_pkey"/>
            </column>
            <column name="country_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-247">
        <createTable tableName="currency_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="currency_translations_pkey"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="currency_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="symbol" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-248">
        <createTable tableName="delegation_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="delegation_translations_pkey"/>
            </column>
            <column name="delegation_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="short_label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-249">
        <createTable tableName="delegations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="delegations_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="short_label" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-250">
        <createTable tableName="financial_element_material">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="financial_element_material_pkey"/>
            </column>
            <column name="scale_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="market_code" type="VARCHAR(255)"/>
            <column name="designation" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-251">
        <createTable tableName="financial_element_material_equipment_mapping">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="financial_element_material_equipment_mapping_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="scale_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="equipment_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-252">
        <createTable tableName="financial_element_vehicle">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="financial_element_vehicle_pkey"/>
            </column>
            <column name="scale_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="maximum_mileage" type="BIGINT"/>
            <column name="minimum_mileage" type="BIGINT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-253">
        <createTable tableName="financial_element_vehicle_vehicle_mapping">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="financial_element_vehicle_vehicle_mapping_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="scale_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="vehicle_reference" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-254">
        <createTable tableName="legal_categories">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="legal_categories_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column defaultValueBoolean="false" name="system_attribute" type="BOOLEAN"/>
            <column name="country_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-255">
        <createTable tableName="legal_category_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="legal_category_translations_pkey"/>
            </column>
            <column name="legal_category_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-256">
        <createTable tableName="milestone_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="milestone_translations_pkey"/>
            </column>
            <column name="milestone_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-257">
        <createTable tableName="naf_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="naf_translations_pkey"/>
            </column>
            <column name="naf_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-258">
        <createTable tableName="nafs">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="nafs_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="system_attribute" type="BOOLEAN"/>
            <column name="country_code" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-259">
        <createTable tableName="naps">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="naps_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="system_attribute" type="BOOLEAN"/>
            <column name="country_code" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-260">
        <createTable tableName="nature_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="operation_line_nature_translations_pkey"/>
            </column>
            <column name="nature_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-261">
        <createTable tableName="natures">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="operation_line_natures_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column defaultValueBoolean="true" name="system_attribute" type="BOOLEAN"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-262">
        <createTable tableName="operation_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="operation_translations_pkey"/>
            </column>
            <column name="operation_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-263">
        <createTable tableName="phase_milestones">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="phase_milestones_pkey"/>
            </column>
            <column name="phase_code" type="VARCHAR(255)"/>
            <column name="milestone_code" type="VARCHAR(255)"/>
            <column name="associated_to" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-264">
        <createTable tableName="phase_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="phase_translations_pkey"/>
            </column>
            <column name="phase_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="phase_associated_to" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="entity" type="VARCHAR(255)"/>
            <column name="end_date" type="date"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="phase_associated_to_label" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-265">
        <createTable tableName="phases">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="phases_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="associated_to" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="associated_to_label" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-354">
        <addUniqueConstraint columnNames="associated_to, code" constraintName="unique_reference" tableName="phases"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-266">
        <createTable tableName="product_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="product_translations_pkey"/>
            </column>
            <column name="product_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-267">
        <createTable tableName="profile_action_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="profile_action_translations_pkey"/>
            </column>
            <column name="profile_action_code" type="VARCHAR(255)"/>
            <column name="description" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-268">
        <createTable tableName="profile_actions">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="profile_actions_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="description" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-269">
        <createTable tableName="profile_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="profile_translations_pkey"/>
            </column>
            <column name="profile_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-270">
        <createTable tableName="role_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="role_translations_pkey"/>
            </column>
            <column name="role_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-271">
        <createTable tableName="service_actors">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="service_actors_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="service_reference" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="actor_code" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-272">
        <createTable tableName="service_naps">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="service_naps_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="service_reference" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="nap_code" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-273">
        <createTable tableName="service_operations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="service_operations_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="service_reference" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="operation_nature_type_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-274">
        <createTable tableName="service_products">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="service_products_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="service_reference" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="product_code" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-275">
        <createTable tableName="single_autos">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="single_autos_pkey"/>
            </column>
            <column name="brand_code" type="VARCHAR(255)"/>
            <column name="brand_label" type="VARCHAR(255)"/>
            <column name="model_group_code" type="VARCHAR(255)"/>
            <column name="model_group_label" type="VARCHAR(255)"/>
            <column name="body_code" type="VARCHAR(255)"/>
            <column name="body_label" type="VARCHAR(255)"/>
            <column name="model_code" type="VARCHAR(255)"/>
            <column name="model_label" type="VARCHAR(255)"/>
            <column name="doors_number" type="VARCHAR(255)"/>
            <column name="version_code" type="VARCHAR(255)"/>
            <column name="version_label" type="VARCHAR(255)"/>
            <column name="variant_code" type="VARCHAR(255)"/>
            <column name="variant_label" type="VARCHAR(255)"/>
            <column name="color_code" type="VARCHAR(255)"/>
            <column name="color_label" type="VARCHAR(255)"/>
            <column name="interior_code" type="VARCHAR(255)"/>
            <column name="interior_label" type="VARCHAR(255)"/>
            <column name="marketing_flag" type="VARCHAR(255)"/>
            <column name="energy_code" type="VARCHAR(255)"/>
            <column name="energy_label" type="VARCHAR(255)"/>
            <column name="type_code" type="VARCHAR(255)"/>
            <column name="type_label" type="VARCHAR(255)"/>
            <column name="power" type="VARCHAR(255)"/>
            <column name="class_code" type="VARCHAR(255)"/>
            <column name="class_label" type="VARCHAR(255)"/>
            <column name="vds_vehicle" type="INTEGER"/>
            <column name="public_price_incl_tax" type="INTEGER"/>
            <column name="painting_payment_code" type="VARCHAR(255)"/>
            <column name="status" type="BOOLEAN"/>
            <column name="equipment" type="JSON"/>
            <column name="is_new" type="BOOLEAN"/>
            <column defaultValueComputed="now()" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="now()" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-276">
        <createTable tableName="static_role_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="static_role_translations_pkey"/>
            </column>
            <column name="static_role_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-277">
        <createTable tableName="tax_rate_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="tax_rate_translations_pkey"/>
            </column>
            <column name="tax_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="tax_rate_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="start_date" type="date"/>
            <column name="end_date" type="date"/>
            <column name="rate" type="FLOAT8"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-278">
        <createTable tableName="tax_rates">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="tax_rates_pkey"/>
            </column>
            <column name="tax_code" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="start_date" type="date"/>
            <column name="end_date" type="date"/>
            <column name="rate" type="FLOAT8"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="active" type="BOOLEAN"/>
            <column name="system_attribute" type="BOOLEAN"/>
            <column name="creationDate" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-279">
        <createTable tableName="tax_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="tax_translations_pkey"/>
            </column>
            <column name="tax_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" type="date"/>
            <column name="end_date" type="date"/>
            <column name="country" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-280">
        <createTable tableName="type_translations">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="line_nature_type_translations_pkey"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="type_code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-281">
        <createTable tableName="types">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="line_nature_types_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column defaultValueBoolean="false" name="system_attribute" type="BOOLEAN"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-282">
        <createTable tableName="users">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="users_pkey"/>
            </column>
            <column name="first_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="last_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="profile_code" type="VARCHAR(255)"/>
            <column name="phone" type="VARCHAR(255)"/>
            <column name="email" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="color" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="ACTIVE" name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="status_reason" type="VARCHAR(255)"/>
            <column name="status_updated_at" type="date"/>
            <column name="expiry_date" type="date"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="deletedAt" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-283">
        <createTable tableName="languages">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="languages_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="native_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-284">
        <createTable tableName="operation_nature_mapping">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="operation_line_mapping_pkey"/>
            </column>
            <column defaultValueBoolean="true" name="nature_status" type="BOOLEAN"/>
            <column name="operation_code" type="VARCHAR(255)"/>
            <column name="nature_code" type="VARCHAR(255)"/>
            <column name="operation_nature_code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-285">
        <createTable tableName="operation_nature_type_mapping">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="natures_types_mapping_pkey"/>
            </column>
            <column defaultValueBoolean="true" name="type_status" type="BOOLEAN"/>
            <column name="type_code" type="VARCHAR(255)"/>
            <column name="operation_nature_type_code" type="VARCHAR(255)"/>
            <column name="operation_nature_code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-286">
        <createTable tableName="product_type_mapping">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="product_type_mapping_pkey"/>
            </column>
            <column name="product_code" type="VARCHAR(255)"/>
            <column name="operation_nature_type_code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-287">
        <createTable tableName="profile_action_mapping">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="profile_action_mapping_pkey"/>
            </column>
            <column name="profile_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="profile_action_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-288">
        <createTable tableName="scale_commercial_product">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="scale_commercial_product_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="scale_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="commercial_product_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-289">
        <createTable tableName="scale_financial_product">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="scale_financial_product_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="scale_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="product_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-290">
        <createTable tableName="scale_line_type">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="scale_line_type_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="scale_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="operation_nature_type_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="product_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-291">
        <createTable tableName="scale_services">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="scale_services_pkey"/>
            </column>
            <column name="reference" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="scale_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="service_reference" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-292">
        <createTable tableName="third_parties">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="third_parties_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="actor_reference" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="role_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="scale_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-3220">
        <sql>
            CREATE TYPE ENUM_APPLICATION_CRITERIA_CHANNEL_OF_ACQUISITION AS ENUM ('DIRECT', 'INDIRECT', 'PARTNER');
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-3221">
        <sql>
            CREATE TYPE ENUM_APPLICATION_CRITERIA_CUSTOMER_TYPE AS ENUM ('CORPORATE', 'INDIVIDUAL', 'SOLE_PROPRIETORSHIP');
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-322">
        <createTable tableName="application_criteria">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="application_criteria_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="channel_of_acquisition" type="ENUM_APPLICATION_CRITERIA_CHANNEL_OF_ACQUISITION">
                <constraints nullable="false"/>
            </column>
            <column name="currency_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="customer_type" type="ENUM_APPLICATION_CRITERIA_CUSTOMER_TYPE">
                <constraints nullable="false"/>
            </column>
            <column name="financial_scoring" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="scale_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-4010">
        <sql>
            CREATE TYPE ENUM_SERVICES_INTENDED_FOR AS ENUM ('VH', 'MT');
            CREATE TYPE ENUM_SERVICES_TYPE_OF_SERVICE AS ENUM ('Maintenance', 'Insurance', 'Replacement_vehicle', 'Pecuniary_loss', 'Financial_loss', 'Administrative_fees');
            CREATE TYPE ENUM_SERVICES_TYPE_OF_COVER AS ENUM ('Third_party', 'Material');
            CREATE TYPE ENUM_SERVICES_BILLING_METHOD AS ENUM ('subscription', 'flat');
            CREATE TYPE ENUM_SERVICES_CALCULATION_METHOD AS ENUM ('percentage', 'fixed_amount', 'matrix');
            CREATE TYPE ENUM_SERVICES_BASIS_OF_CALCULATION AS ENUM ('RENTAL_BASE_EXCL_TAX', 'FINANCING_AMOUNT_EXCL_TAX', 'RENT_AMOUNT_EXCL_TAX', 'RENTAL_BASE_INCL_TAX', 'FINANCING_AMOUNT_INCL_TAX', 'RENT_AMOUNT_INCL_TAX');
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-4011">
        <sql>
            CREATE TYPE enum_services_status AS ENUM ('initial', 'active', 'expired');
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-4012">
        <sql>
            CREATE TYPE enum_services_packs_type AS ENUM ('COMPATIBLE', 'INCOMPATIBLE');

        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-401">
        <createTable tableName="services">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="services_pkey"/>
            </column>
            <column name="reference" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="intended_for" type="ENUM_SERVICES_INTENDED_FOR"/>
            <column name="label" type="VARCHAR(255)"/>
            <column name="type_of_service" type="ENUM_SERVICES_TYPE_OF_SERVICE"/>
            <column name="type_of_cover" type="ENUM_SERVICES_TYPE_OF_COVER"/>
            <column name="start_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="end_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="currency_code" type="VARCHAR(255)"/>
            <column defaultValue="initial" name="status" type="ENUM_SERVICES_STATUS"/>
            <column name="out_of_contract_termination" type="BOOLEAN"/>
            <column name="maximum_asset_price" type="FLOAT8"/>
            <column name="minimum_asset_price" type="FLOAT8"/>
            <column name="maximum_contract_duration" type="INTEGER"/>
            <column name="minimum_contract_duration" type="INTEGER"/>
            <column name="is_enterprise" type="BOOLEAN"/>
            <column name="is_enterprise_individuelle" type="BOOLEAN"/>
            <column name="is_enterprise_publique" type="BOOLEAN"/>
            <column name="is_particulier" type="BOOLEAN"/>
            <column name="is_registrable" type="BOOLEAN"/>
            <column name="is_unregistrable" type="BOOLEAN"/>
            <column name="billing_method" type="ENUM_SERVICES_BILLING_METHOD"/>
            <column name="calculation_method" type="ENUM_SERVICES_CALCULATION_METHOD"/>
            <column name="amount_excl_tax" type="FLOAT8"/>
            <column name="basis_of_calculation" type="ENUM_SERVICES_BASIS_OF_CALCULATION"/>
            <column name="tax_code" type="VARCHAR(255)"/>
            <column name="calculation_percentage" type="FLOAT8"/>
            <column name="tax_value" type="FLOAT8"/>
            <column name="partner_reference" type="UUID"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-402">
        <createTable tableName="services_packs">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="services_packs_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="invoice_grouping_code" type="VARCHAR(255)"/>
            <column name="grouping_code_regulation" type="VARCHAR(255)"/>
            <column name="type" type="ENUM_SERVICES_PACKS_TYPE"/>
            <column name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="reference" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-3990">
        <sql>
            CREATE TYPE ENUM_PARTNERS_MANDATE_TYPE AS ENUM ('OPAQUE', 'TRANSPARENT');
            CREATE TYPE ENUM_PARTNERS_REMITTANCE_METHOD AS ENUM ('ON_INVOICING', 'ON_PAYMENT_RECEIPT');
            CREATE TYPE ENUM_PARTNERS_CALCULATION_METHOD AS ENUM ('FIXED_AMOUNT', 'PERCENTAGE');
            CREATE TYPE ENUM_PARTNERS_BASIS_OF_CALCULATION AS ENUM ('RENTAL_BASE_EXCL_TAX', 'FINANCING_AMOUNT_EXCL_TAX', 'RENT_AMOUNT_EXCL_TAX', 'RENTAL_BASE_INCL_TAX', 'FINANCING_AMOUNT_INCL_TAX', 'RENT_AMOUNT_INCL_TAX');
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-399">
        <createTable tableName="partners">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="partners_pkey"/>
            </column>
            <column name="reference" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="actor_reference" type="VARCHAR(255)"/>
            <column name="operation_nature_type_code" type="VARCHAR(255)"/>
            <column name="currency_code" type="VARCHAR(255)"/>
            <column name="mandate_type" type="ENUM_PARTNERS_MANDATE_TYPE"/>
            <column name="external_reference" type="VARCHAR(255)"/>
            <column name="remittance_method" type="ENUM_PARTNERS_REMITTANCE_METHOD"/>
            <column name="calculation_method" type="ENUM_PARTNERS_CALCULATION_METHOD"/>
            <column name="amount_excl_tax" type="FLOAT8"/>
            <column name="basis_of_calculation" type="ENUM_PARTNERS_BASIS_OF_CALCULATION"/>
            <column name="tax_code" type="VARCHAR(255)"/>
            <column name="calculation_percentage" type="FLOAT8"/>
            <column name="tax_value" type="FLOAT8"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-4000">
        <sql>
            CREATE TYPE ENUM_SCALES_RATE_PERIOD AS ENUM ('ENUM_ANNEE', 'ENUM_SEMESTRE', 'ENUM_TRIMESTRE', 'ENUM_MOIS', 'ENUM_JOUR');
            CREATE TYPE ENUM_SCALES_ASSET_USAGE AS ENUM ('PROFESSIONAL', 'OWN');
            CREATE TYPE ENUM_SCALES_STATUS AS ENUM ('INI', 'OPERATED', 'SUSPENDED', 'EXPIRED');
            CREATE TYPE ENUM_SCALES_CONDITION AS ENUM ('NEW', 'OLD', 'REFURBISHED');
            CREATE TYPE ENUM_SCALES_NATURE AS ENUM ('VH', 'MT');
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-400">
        <createTable tableName="scales">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="scales_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="label" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" type="date"/>
            <column name="end_date" type="date"/>
            <column name="currency_code" type="VARCHAR(255)"/>
            <column name="market_code" type="VARCHAR(255)"/>
            <column name="country_code" type="VARCHAR(255)"/>
            <column name="description" type="TEXT"/>
            <column name="maximum_eligible_amount" type="INTEGER"/>
            <column name="minimum_eligible_amount" type="INTEGER"/>
            <column name="maximum_financing_duration" type="INTEGER"/>
            <column name="minimum_financing_duration" type="INTEGER"/>
            <column name="rate_period" type="ENUM_SCALES_RATE_PERIOD"/>
            <column name="maximum_residual_value" type="INTEGER"/>
            <column name="minimum_residual_value" type="INTEGER"/>
            <column name="minimum_personal_contribution" type="FLOAT8"/>
            <column name="maximum_personal_contribution" type="FLOAT8"/>
            <column name="maximum_security_deposit" type="FLOAT8"/>
            <column name="minimum_security_deposit" type="FLOAT8"/>
            <column name="asset_usage" type="ENUM_SCALES_ASSET_USAGE"/>
            <column defaultValue="INI" name="status" type="ENUM_SCALES_STATUS">
                <constraints nullable="false"/>
            </column>
            <column name="application_criteria_reference" type="UUID"/>
            <column name="condition" type="ENUM_SCALES_CONDITION"/>
            <column name="nature" type="ENUM_SCALES_NATURE"/>
            <column name="minimum_interest_rate" type="FLOAT8"/>
            <column name="nominal_interest_rate" type="FLOAT8"/>
            <column name="maximum_interest_rate" type="FLOAT8"/>
            <column name="has_personal_contribution" type="BOOLEAN"/>
            <column name="grace_period_duration" type="INTEGER"/>
            <column name="with_interest_payment" type="BOOLEAN"/>
            <column name="has_grace_period" type="BOOLEAN"/>
            <column name="has_security_deposit" type="BOOLEAN"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="createdAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updatedAt" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="reference" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
        </createTable>
    </changeSet>


    <changeSet author="HoussemMOUSSA (generated)" id="*************-294">
        <createView fullDefinition="false" viewName="lan_view">SELECT 'activities'::text AS collection,
    activities.code,
    activities.label,
    NULL::text AS entity
   FROM activities
UNION ALL
 SELECT 'products'::text AS collection,
    products.code,
    products.label,
    NULL::text AS entity
   FROM products
UNION ALL
 SELECT 'allocations'::text AS collection,
    allocations.code,
    allocations.label,
    NULL::text AS entity
   FROM allocations
UNION ALL
 SELECT 'phases'::text AS collection,
    phases.code,
    phases.label,
    phases.associated_to AS entity
   FROM phases
UNION ALL
 SELECT 'milestones'::text AS collection,
    milestones.code,
    milestones.label,
    NULL::text AS entity
   FROM milestones
UNION ALL
 SELECT 'legal_categories'::text AS collection,
    legal_categories.code,
    legal_categories.label,
    NULL::text AS entity
   FROM legal_categories
UNION ALL
 SELECT 'roles'::text AS collection,
    roles.code,
    roles.label,
    NULL::text AS entity
   FROM roles
UNION ALL
 SELECT 'delegations'::text AS collection,
    delegations.code,
    delegations.label,
    NULL::text AS entity
   FROM delegations
UNION ALL
 SELECT 'countries'::text AS collection,
    countries.code,
    countries.label,
    NULL::text AS entity
   FROM countries
UNION ALL
 SELECT 'taxes'::text AS collection,
    taxes.code,
    taxes.label,
    NULL::text AS entity
   FROM taxes
UNION ALL
 SELECT 'tax_rates'::text AS collection,
    tax_rates.code,
    tax_rates.label,
    NULL::text AS entity
   FROM tax_rates
UNION ALL
 SELECT 'currencies'::text AS collection,
    currencies.code,
    currencies.label,
    NULL::text AS entity
   FROM currencies;</createView>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-295">
        <addUniqueConstraint columnNames="associated_to, code" constraintName="unique_reference_activities" tableName="activities"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-296">
        <createView fullDefinition="false" viewName="view_static_tables">SELECT a.code,
    a.id,
    a.label,
    a.translated_label,
    a.language_code,
    'activity'::text AS entity,
    a."createdAt"
   FROM ( SELECT ac.code,
            ac.id,
            ac.label,
            act.label AS translated_label,
            act.language_code,
            ac."createdAt"
           FROM (activities ac
             JOIN activity_translations act ON (((ac.code)::text = (act.activity_code)::text)))) a
UNION
 SELECT b.code,
    b.id,
    b.label,
    b.translated_label,
    b.language_code,
    'allocation'::text AS entity,
    b."createdAt"
   FROM ( SELECT al.code,
            al.id,
            al.label,
            alt.label AS translated_label,
            alt.language_code,
            al."createdAt"
           FROM (allocations al
             JOIN allocation_translations alt ON (((al.code)::text = (alt.allocation_code)::text)))) b
UNION
 SELECT c.code,
    c.id,
    c.label,
    c.translated_label,
    c.language_code,
    'country'::text AS entity,
    c."createdAt"
   FROM ( SELECT co.code,
            co.id,
            co.label,
            cot.label AS translated_label,
            cot.language_code,
            co."createdAt"
           FROM (countries co
             JOIN country_translations cot ON (((co.code)::text = (cot.country_code)::text)))) c
UNION
 SELECT d.code,
    d.id,
    d.label,
    d.translated_label,
    d.language_code,
    'currency'::text AS entity,
    d."createdAt"
   FROM ( SELECT cu.code,
            cu.id,
            cu.label,
            cut.label AS translated_label,
            cut.language_code,
            cu."createdAt"
           FROM (currencies cu
             LEFT JOIN currency_translations cut ON (((cu.code)::text = (cut.currency_code)::text)))) d
UNION
 SELECT e.code,
    e.id,
    e.label,
    e.translated_label,
    e.language_code,
    'delegation'::text AS entity,
    e."createdAt"
   FROM ( SELECT de.code,
            de.id,
            de.label,
            det.label AS translated_label,
            det.language_code,
            de."createdAt"
           FROM (delegations de
             JOIN delegation_translations det ON (((de.code)::text = (det.delegation_code)::text)))) e
UNION
 SELECT f.code,
    f.id,
    f.label,
    f.translated_label,
    f.language_code,
    'legal_category'::text AS entity,
    f."createdAt"
   FROM ( SELECT le.code,
            le.id,
            le.label,
            let.label AS translated_label,
            let.language_code,
            le."createdAt"
           FROM (legal_categories le
             JOIN legal_category_translations let ON (((le.code)::text = (let.legal_category_code)::text)))) f
UNION
 SELECT g.code,
    g.id,
    g.label,
    g.translated_label,
    g.language_code,
    'milestone'::text AS entity,
    g."createdAt"
   FROM ( SELECT mi.code,
            mi.id,
            mi.label,
            mlt.label AS translated_label,
            mlt.language_code,
            mi."createdAt"
           FROM (milestones mi
             JOIN milestone_translations mlt ON (((mi.code)::text = (mlt.milestone_code)::text)))) g
UNION
 SELECT h.code,
    h.id,
    h.label,
    h.translated_label,
    h.language_code,
    'phase'::text AS entity,
    h."createdAt"
   FROM ( SELECT ph.code,
            ph.id,
            ph.label,
            pht.label AS translated_label,
            pht.language_code,
            ph."createdAt"
           FROM (phases ph
             JOIN phase_translations pht ON (((ph.code)::text = (pht.phase_code)::text)))) h
UNION
 SELECT i.code,
    i.id,
    i.label,
    i.translated_label,
    i.language_code,
    'operation'::text AS entity,
    i."createdAt"
   FROM ( SELECT op.code,
            op.id,
            op.label,
            opt.label AS translated_label,
            opt.language_code,
            op."createdAt"
           FROM (operations op
             JOIN operation_translations opt ON (((op.code)::text = (opt.operation_code)::text)))) i
UNION
 SELECT j.code,
    j.id,
    j.label,
    j.translated_label,
    j.language_code,
    'product'::text AS entity,
    j."createdAt"
   FROM ( SELECT pr.code,
            pr.id,
            pr.label,
            prt.label AS translated_label,
            prt.language_code,
            pr."createdAt"
           FROM (products pr
             JOIN product_translations prt ON (((pr.code)::text = (prt.product_code)::text)))) j
UNION
 SELECT k.code,
    k.id,
    k.label,
    k.translated_label,
    k.language_code,
    'role'::text AS entity,
    k."createdAt"
   FROM ( SELECT ro.code,
            ro.id,
            ro.label,
            rot.label AS translated_label,
            rot.language_code,
            ro."createdAt"
           FROM (roles ro
             JOIN role_translations rot ON (((ro.code)::text = (rot.role_code)::text)))) k
UNION
 SELECT l.code,
    l.id,
    l.label,
    l.translated_label,
    l.language_code,
    'tax'::text AS entity,
    l."createdAt"
   FROM ( SELECT ta.code,
            ta.id,
            ta.label,
            tat.label AS translated_label,
            tat.language_code,
            ta."createdAt"
           FROM (taxes ta
             JOIN tax_translations tat ON (((ta.code)::text = (tat.tax_code)::text)))) l
UNION
 SELECT m.code,
    m.id,
    m.label,
    m.translated_label,
    m.language_code,
    'tax_rate'::text AS entity,
    m."createdAt"
   FROM ( SELECT tar.code,
            tar.id,
            tar.label,
            tart.label AS translated_label,
            tart.language_code,
            tar."createdAt"
           FROM (tax_rates tar
             JOIN tax_rate_translations tart ON (((tar.code)::text = (tart.tax_rate_code)::text)))) m;</createView>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-298">
        <addForeignKeyConstraint baseColumnNames="brand_code" baseTableName="single_autos" constraintName="single_autos_brand_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="SET NULL" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="auto_catalogs" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="174952549g37066-300">
        <addForeignKeyConstraint baseColumnNames="activity_code" baseTableName="activity_translations" constraintName="activity_translations_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="activities" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-412">
        <addForeignKeyConstraint baseColumnNames="allocation_code" baseTableName="allocation_translations" constraintName="allocation_translations_allocation_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="allocations" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-332">
        <addForeignKeyConstraint baseColumnNames="country_code" baseTableName="country_translations" constraintName="country_translations_country_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="code" referencedTableName="countries" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="1749555137866-300">
        <addForeignKeyConstraint baseColumnNames="currency_code" baseTableName="currency_translations" constraintName="currency_translations_currency_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="code" referencedTableName="currencies" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-417">
        <addForeignKeyConstraint baseColumnNames="delegation_code" baseTableName="delegation_translations" constraintName="delegation_translations_delegation_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="code" referencedTableName="delegations" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-346">
        <addForeignKeyConstraint baseColumnNames="legal_category_code" baseTableName="legal_category_translations" constraintName="legal_category_translations_legal_category_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="legal_categories" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-347">
        <addForeignKeyConstraint baseColumnNames="milestone_code" baseTableName="milestone_translations" constraintName="milestone_translations_milestone_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="milestones" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-419">
        <addForeignKeyConstraint baseColumnNames="naf_code" baseTableName="naf_translations" constraintName="naf_translations_naf_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="nafs" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-420">
        <addForeignKeyConstraint baseColumnNames="nature_code" baseTableName="nature_translations" constraintName="operation_line_nature_translations_line_nature_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="code" referencedTableName="natures" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-353">
        <addForeignKeyConstraint baseColumnNames="operation_code" baseTableName="operation_translations" constraintName="operation_translations_operation_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="code" referencedTableName="operations" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-358">
        <addForeignKeyConstraint baseColumnNames="profile_code" baseTableName="profile_translations" constraintName="profile_translations_profile_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="profiles" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-355">
        <addForeignKeyConstraint baseColumnNames="product_code" baseTableName="product_translations" constraintName="product_translations_product_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="products" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-410">
        <addForeignKeyConstraint baseColumnNames="payment_method_code" baseTableName="payment_method_translations" constraintName="payment_method_translations_payment_method_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="payment_methods" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-421">
        <addForeignKeyConstraint baseColumnNames="phase_code,phase_associated_to" baseTableName="phase_translations" constraintName="fk_code_associated_to_phase" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code,associated_to" referencedTableName="phases" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-422">
        <addForeignKeyConstraint baseColumnNames="profile_action_code" baseTableName="profile_action_translations" constraintName="profile_action_translations_profile_action_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="profile_actions" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-359">
        <addForeignKeyConstraint baseColumnNames="role_code" baseTableName="role_translations" constraintName="role_translations_role_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="roles" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-369">
        <addForeignKeyConstraint baseColumnNames="static_role_code" baseTableName="static_role_translations" constraintName="static_role_translations_static_role_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="static_roles" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-374">
        <addForeignKeyConstraint baseColumnNames="tax_code" baseTableName="tax_translations" constraintName="tax_translations_tax_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="code" referencedTableName="taxes" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-423">
        <addForeignKeyConstraint baseColumnNames="tax_rate_code" baseTableName="tax_rate_translations" constraintName="tax_rate_translations_tax_rate_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="code" referencedTableName="tax_rates" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-424">
        <addForeignKeyConstraint baseColumnNames="type_code" baseTableName="type_translations" constraintName="type_translations_type_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="types" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-356">
        <addUniqueConstraint columnNames="profile_action_code" tableName="profile_action_translations" constraintName="profile_action_translations_profile_action_code_key"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-302">
        <addForeignKeyConstraint baseColumnNames="equipment_code" baseTableName="financial_element_material_equipment_mapping" constraintName="financial_element_material_equipment_mappin_equipment_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="equipments" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-303">
        <createIndex indexName="idx_equipments_market_category" tableName="equipments">
            <column name="market_code"/>
            <column name="category_code"/>
        </createIndex>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-304">
        <createIndex indexName="idx_equipments_market_code" tableName="equipments">
            <column name="market_code"/>
        </createIndex>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-308">
        <addUniqueConstraint columnNames="payment_method_code, language_code" constraintName="unique_payment_method_language" tableName="payment_method_translations"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-310">
        <addForeignKeyConstraint baseColumnNames="activity_code,activity_associated_to" baseTableName="products" constraintName="fk_code_associated_to_activity" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="NO ACTION" referencedColumnNames="code,associated_to" referencedTableName="activities" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-312">
        <addForeignKeyConstraint baseColumnNames="product_code" baseTableName="scale_financial_product" constraintName="scale_financial_product_product_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="products" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-313">
        <addForeignKeyConstraint baseColumnNames="product_code" baseTableName="scale_line_type" constraintName="scale_line_type_product_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="products" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-314">
        <addForeignKeyConstraint baseColumnNames="product_code" baseTableName="service_products" constraintName="service_products_product_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="products" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-316">
        <addForeignKeyConstraint baseColumnNames="profile_code" baseTableName="users" constraintName="users_profile_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="SET NULL" onUpdate="NO ACTION" referencedColumnNames="code" referencedTableName="profiles" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-318">
        <addForeignKeyConstraint baseColumnNames="role_code" baseTableName="third_parties" constraintName="third_parties_role_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="roles" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-323">
        <addUniqueConstraint columnNames="actor_reference, application_criteria_reference" constraintName="application_criteria_actors_actor_reference_application_criteri" tableName="application_criteria_actors"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-329">
        <addForeignKeyConstraint baseColumnNames="product_code" baseTableName="commercial_products" constraintName="commercial_products_product_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="products" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-330">
        <addForeignKeyConstraint baseColumnNames="commercial_product_code" baseTableName="scale_commercial_product" constraintName="scale_commercial_product_commercial_product_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="commercial_products" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-333">
        <addForeignKeyConstraint baseColumnNames="country_code" baseTableName="equipments" constraintName="equipments_country_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="countries" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-334">
        <addForeignKeyConstraint baseColumnNames="country_code" baseTableName="legal_categories" constraintName="legal_categories_country_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="countries" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-336">
        <addForeignKeyConstraint baseColumnNames="market_code" baseTableName="financial_element_material" constraintName="financial_element_material_market_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="markets" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-337">
        <addUniqueConstraint columnNames="scale_code" constraintName="financial_element_material_scale_code_key" tableName="financial_element_material"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-339">
        <addForeignKeyConstraint baseColumnNames="scale_code" baseTableName="financial_element_material_equipment_mapping" constraintName="financial_element_material_equipment_mapping_scale_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="scale_code" referencedTableName="financial_element_material" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-340">
        <addUniqueConstraint columnNames="scale_code, equipment_code" constraintName="financial_element_material_equipment_mapping_scale_equipment_ke" tableName="financial_element_material_equipment_mapping"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-341">
        <addUniqueConstraint columnNames="scale_code" constraintName="financial_element_vehicle_scale_code_key" tableName="financial_element_vehicle"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-342">
        <addForeignKeyConstraint baseColumnNames="scale_code" baseTableName="financial_element_vehicle_vehicle_mapping" constraintName="financial_element_vehicle_vehicle_mapping_scale_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="scale_code" referencedTableName="financial_element_vehicle" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-344">
        <addUniqueConstraint columnNames="scale_code, vehicle_reference" constraintName="financial_element_vehicle_vehicle_mapping_scale_vehicle_key" tableName="financial_element_vehicle_vehicle_mapping"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-350">
        <addForeignKeyConstraint baseColumnNames="nap_code" baseTableName="service_naps" constraintName="service_naps_nap_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="naps" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-352">
        <addForeignKeyConstraint baseColumnNames="nature_code" baseTableName="operation_nature_mapping" constraintName="operation_nature_mapping_nature_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="natures" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-361">
        <createIndex indexName="unique_service_actor" tableName="service_actors" unique="true">
            <column name="service_reference"/>
            <column name="actor_code"/>
        </createIndex>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-363">
        <createIndex indexName="unique_service_nap" tableName="service_naps" unique="true">
            <column name="service_reference"/>
            <column name="nap_code"/>
        </createIndex>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-365">
        <addUniqueConstraint columnNames="service_reference, operation_nature_type_code" constraintName="service_operations_service_reference_operation_nature_type_code" tableName="service_operations"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-367">
        <createIndex indexName="unique_service_product" tableName="service_products" unique="true">
            <column name="service_reference"/>
            <column name="product_code"/>
        </createIndex>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-370">
        <addForeignKeyConstraint baseColumnNames="tax_code" baseTableName="tax_rates" constraintName="fk_tax_code" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="taxes" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-373">
        <addUniqueConstraint columnNames="tax_code, rate" constraintName="unique_tax_code_rate_constraint" tableName="tax_rates"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-376">
        <addUniqueConstraint columnNames="email" constraintName="users_email_key" tableName="users"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-378">
        <addForeignKeyConstraint baseColumnNames="operation_code" baseTableName="operation_nature_mapping" constraintName="operation_nature_mapping_operation_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="operations" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-379">
        <addUniqueConstraint columnNames="operation_nature_code" constraintName="operation_nature_mapping_operation_nature_code_key" tableName="operation_nature_mapping"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-380">
        <addForeignKeyConstraint baseColumnNames="operation_nature_code" baseTableName="operation_nature_type_mapping" constraintName="operation_nature_type_mapping_operation_nature_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="operation_nature_code" referencedTableName="operation_nature_mapping" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-381">
        <addUniqueConstraint columnNames="operation_nature_type_code" constraintName="operation_nature_type_mapping_operation_nature_type_code_key" tableName="operation_nature_type_mapping"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-382">
        <addForeignKeyConstraint baseColumnNames="type_code" baseTableName="operation_nature_type_mapping" constraintName="operation_nature_type_mapping_type_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="types" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-383">
        <addForeignKeyConstraint baseColumnNames="operation_nature_type_code" baseTableName="product_type_mapping" constraintName="product_type_mapping_operation_nature_type_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="operation_nature_type_code" referencedTableName="operation_nature_type_mapping" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-384">
        <addForeignKeyConstraint baseColumnNames="operation_nature_type_code" baseTableName="scale_line_type" constraintName="scale_line_type_operation_nature_type_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="operation_nature_type_code" referencedTableName="operation_nature_type_mapping" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-385">
        <addForeignKeyConstraint baseColumnNames="operation_nature_type_code" baseTableName="service_operations" constraintName="service_operations_operation_nature_type_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="operation_nature_type_code" referencedTableName="operation_nature_type_mapping" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-386">
        <addForeignKeyConstraint baseColumnNames="product_code" baseTableName="product_type_mapping" constraintName="product_type_mapping_product_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="products" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-387">
        <addForeignKeyConstraint baseColumnNames="profile_action_code" baseTableName="profile_action_mapping" constraintName="profile_action_mapping_profile_action_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="profile_actions" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-388">
        <addForeignKeyConstraint baseColumnNames="profile_code" baseTableName="profile_action_mapping" constraintName="profile_action_mapping_profile_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="profiles" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-390">
        <addUniqueConstraint columnNames="scale_code, commercial_product_code" constraintName="scale_commercial_product_scale_code_commercial_product_code_key" tableName="scale_commercial_product"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-392">
        <addUniqueConstraint columnNames="scale_code, product_code" constraintName="scale_financial_product_scale_code_financial_product_code_key" tableName="scale_financial_product"/>
    </changeSet>

    <changeSet author="HoussemMOUSSA (generated)" id="*************-394">
        <addUniqueConstraint columnNames="scale_code, operation_nature_type_code, product_code" constraintName="scale_line_type_scale_code_line_type_code_key" tableName="scale_line_type"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-396">
        <addUniqueConstraint columnNames="scale_code, service_reference" constraintName="unique_scale_service" tableName="scale_services"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-397">
        <addUniqueConstraint columnNames="actor_reference, role_code, scale_code" constraintName="third_parties_actor_reference_role_code_scale_code_key" tableName="third_parties"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-403">
        <addForeignKeyConstraint baseColumnNames="currency_code" baseTableName="application_criteria" constraintName="application_criteria_currency_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="currencies" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-404">
        <addForeignKeyConstraint baseColumnNames="currency_code" baseTableName="partners" constraintName="partners_currency_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="code" referencedTableName="currencies" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-405">
        <addForeignKeyConstraint baseColumnNames="currency_code" baseTableName="scales" constraintName="scales_currency_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="currencies" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-406">
        <addForeignKeyConstraint baseColumnNames="currency_code" baseTableName="services" constraintName="services_currency_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="code" referencedTableName="currencies" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-407">
        <addForeignKeyConstraint baseColumnNames="category_code" baseTableName="equipments" constraintName="equipments_category_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="naps" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-408">
        <addForeignKeyConstraint baseColumnNames="market_code" baseTableName="equipments" constraintName="equipments_market_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="markets" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-409">
        <addForeignKeyConstraint baseColumnNames="market_code" baseTableName="scales" constraintName="scales_market_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="markets" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-411">
        <addForeignKeyConstraint baseColumnNames="static_role_code" baseTableName="roles" constraintName="roles_associated_to_static_roles_fk" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="static_roles" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-414">
        <addForeignKeyConstraint baseColumnNames="application_criteria_reference" baseTableName="scales" constraintName="fk_scales_application_criteria_reference" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="reference" referencedTableName="application_criteria" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-415">
        <addForeignKeyConstraint baseColumnNames="application_criteria_reference" baseTableName="application_criteria_actors" constraintName="application_criteria_actors_application_criteria_reference_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="reference" referencedTableName="application_criteria" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-416">
        <addForeignKeyConstraint baseColumnNames="country_code" baseTableName="scales" constraintName="scales_country_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="countries" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-418">
        <addForeignKeyConstraint baseColumnNames="vehicle_reference" baseTableName="financial_element_vehicle_vehicle_mapping" constraintName="financial_element_vehicle_vehicle_mappin_vehicle_reference_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="reference" referencedTableName="single_autos" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-425">
        <addForeignKeyConstraint baseColumnNames="operation_nature_type_code" baseTableName="partners" constraintName="partners_operation_nature_type_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="operation_nature_type_code" referencedTableName="operation_nature_type_mapping" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-430">
        <addForeignKeyConstraint baseColumnNames="scale_code" baseTableName="third_parties" constraintName="third_parties_scale_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="scales" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-431">
        <addForeignKeyConstraint baseColumnNames="partner_reference" baseTableName="services" constraintName="fk_services_partner_reference" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="reference" referencedTableName="partners" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-438">
        <addForeignKeyConstraint baseColumnNames="service_pack_code" baseTableName="service-services_packs" constraintName="service-services_packs_service_pack_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="services_packs" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-439">
        <addForeignKeyConstraint baseColumnNames="service_reference" baseTableName="service-services_packs" constraintName="service-services_packs_service_reference_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="reference" referencedTableName="services" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-440">
        <addForeignKeyConstraint baseColumnNames="scale_code" baseTableName="application_criteria" constraintName="fk_application_criteria_scale_code" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="scales" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-441">
        <addForeignKeyConstraint baseColumnNames="scale_code" baseTableName="financial_element_material" constraintName="financial_element_material_scale_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="scales" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-442">
        <addForeignKeyConstraint baseColumnNames="scale_code" baseTableName="financial_element_vehicle" constraintName="financial_element_vehicle_scale_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="scales" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-443">
        <addForeignKeyConstraint baseColumnNames="service_reference" baseTableName="service_actors" constraintName="service_actors_service_reference_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="reference" referencedTableName="services" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-444">
        <addForeignKeyConstraint baseColumnNames="service_reference" baseTableName="service_naps" constraintName="service_naps_service_reference_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="reference" referencedTableName="services" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-445">
        <addForeignKeyConstraint baseColumnNames="service_reference" baseTableName="service_operations" constraintName="service_operations_service_reference_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="reference" referencedTableName="services" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-446">
        <addForeignKeyConstraint baseColumnNames="service_reference" baseTableName="service_products" constraintName="service_products_service_reference_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="reference" referencedTableName="services" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-447">
        <addForeignKeyConstraint baseColumnNames="scale_code" baseTableName="scale_commercial_product" constraintName="scale_commercial_product_scale_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="scales" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-448">
        <addForeignKeyConstraint baseColumnNames="scale_code" baseTableName="scale_financial_product" constraintName="scale_financial_product_scale_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="scales" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-449">
        <addForeignKeyConstraint baseColumnNames="scale_code" baseTableName="scale_line_type" constraintName="scale_line_type_scale_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="scales" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-450">
        <addForeignKeyConstraint baseColumnNames="scale_code" baseTableName="scale_services" constraintName="scale_services_scale_code_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="code" referencedTableName="scales" validate="true"/>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="*************-451">
        <addForeignKeyConstraint baseColumnNames="service_reference" baseTableName="scale_services" constraintName="scale_services_service_reference_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="reference" referencedTableName="services" validate="true"/>
    </changeSet>
</databaseChangeLog>
