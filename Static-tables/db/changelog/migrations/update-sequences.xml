<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="AhmedKhiari" id="reset-all-sequences">
        <sql splitStatements="false">
            DO
            $$
    DECLARE
            rec RECORD;
    max_id
            BIGINT;
            BEGIN
            FOR rec IN
            SELECT ns.nspname  AS schema_name,
                   seq.relname AS sequence_name,
                   tab.relname AS table_name,
                   col.attname AS column_name
            FROM pg_class seq
                     JOIN pg_namespace ns ON seq.relnamespace = ns.oid
                     JOIN pg_depend dep ON seq.oid = dep.objid
                     JOIN pg_class tab ON dep.refobjid = tab.oid
                     JOIN pg_attribute col ON col.attrelid = tab.oid AND col.attnum = dep.refobjsubid
            WHERE seq.relkind = 'S'
              AND ns.nspname = 'public' LOOP
        -- Get max value, properly double-quoting the table name
        EXECUTE format('SELECT COALESCE(MAX(%I), 0) FROM %I."%s"',
            rec.column_name,
            rec.schema_name,
            rec.table_name
        )
            INTO max_id;

            -- Handle tables with data vs empty tables
            IF
            max_id > 0 THEN
            EXECUTE format('SELECT setval(%L, %s, true)',
                rec.schema_name || '.' || rec.sequence_name,
                max_id);

            RAISE
            NOTICE 'Reset sequence %.% for table %.% column % to %',
                rec.schema_name, rec.sequence_name,
                rec.schema_name, rec.table_name,
                rec.column_name, max_id;
            ELSE
            -- For empty tables, set sequence to start at 1
            EXECUTE format('SELECT setval(%L, 1, false)',
                rec.schema_name || '.' || rec.sequence_name);

            RAISE
            NOTICE 'Initialized sequence %.% for empty table %.%',
                rec.schema_name, rec.sequence_name,
                rec.schema_name, rec.table_name;
            END IF;
            END LOOP;
            END $$;
        </sql>
    </changeSet>
</databaseChangeLog>