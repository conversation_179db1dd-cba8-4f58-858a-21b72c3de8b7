# Static Tables Service - Express + Liquibase

A Node.js Express application for managing static tables with automatic database migrations using Liquibase.

## Key Features

- **Express.js** REST API framework
- **Liquibase** for automatic database migrations and seeders
- **PostgreSQL** database support
- **JWT** token authentication
- **ESLint** code linting
- **API Documentation** with Swagger
- **Automatic Migration** on application startup

## Quick Start

### Prerequisites
- Node.js (>= 8.3.0)
- PostgreSQL database
- Environment variables configured

### Installation

1. Install dependencies:
```bash
npm install
```

2. Configure environment variables:
```bash
# Copy and modify environment file
cp .env.example .env.local
```

3. Start the application:
```bash
npm run start-local
```

The application will automatically:
- Run pending database migrations
- Seed initial data
- Start the Express server

### Development

To start with auto-reload and documentation generation:
```bash
npm start
```

To generate API documentation:
```bash
npm run docs
```

## Database Management

This application uses **Liquibase** for database migrations and seeders instead of Sequelize CLI.

### Automatic Migrations

Migrations run automatically when the application starts. No manual intervention required.

### Manual Migration Commands

```bash
# Run pending migrations manually
npm run db:migrate

# Check migration status
npm run db:status

# Validate changelog files
npm run db:validate

# Rollback migrations
npm run db:rollback [count]
```

### Creating New Migrations

See [LIQUIBASE_MIGRATION.md](./LIQUIBASE_MIGRATION.md) for detailed instructions on:
- Creating new migrations
- Adding data seeders
- Best practices
- Troubleshooting

## Migration from Sequelize CLI

This application has been migrated from Sequelize CLI to Liquibase. See [LIQUIBASE_MIGRATION.md](./LIQUIBASE_MIGRATION.md) for:
- Migration details
- File conversion mapping
- Rollback procedures