/**
 * Abstract Base Controller
 * Defines the interface that all generic controllers must implement
 * This ensures consistent behavior across all controller implementations
 */
class AbstractBaseController {
  constructor(modelName, db) {
    if (this.constructor === AbstractBaseController) {
      throw new Error("Abstract class cannot be instantiated directly");
    }
    
    this.modelName = modelName;
    this.db = db;
  }

  /**
   * Abstract methods that must be implemented by concrete controllers
   */
  
  async search(req, res, next) {
    throw new Error("search method must be implemented by concrete controller");
  }

  async getOne(req, res, next) {
    throw new Error("getOne method must be implemented by concrete controller");
  }

  async create(req, res, next) {
    throw new Error("create method must be implemented by concrete controller");
  }

  async update(req, res, next) {
    throw new Error("update method must be implemented by concrete controller");
  }

  async remove(req, res, next) {
    throw new Error("remove method must be implemented by concrete controller");
  }

  /**
   * Common utility methods that can be shared across implementations
   */
  
  /**
   * Build where condition dynamically, handling active column filtering
   */
  buildWhereCondition(queryParams, hasActive = true) {
    const whereCondition = {};
    const excludedParams = ["offset", "limit", "sort_by", "order_by", "language"];
    
    Object.keys(queryParams).forEach((key) => {
      if (!excludedParams.includes(key)) {
        // Handle active column filtering
        if (key === "active") {
          if (hasActive) {
            whereCondition[key] = queryParams[key];
          }
          // If model doesn't have active column, ignore the filter silently
          // This prevents errors when frontend always sends active=true
        } else {
          whereCondition[key] = queryParams[key];
        }
      }
    });

    return whereCondition;
  }

  /**
   * Build translation where condition
   */
  buildTranslationWhereCondition(queryParams, language, defaultLanguage) {
    const translationWhereCondition = {};
    const excludedParams = ["offset", "limit", "sort_by", "order_by", "language"];
    
    Object.keys(queryParams).forEach((key) => {
      if (!excludedParams.includes(key)) {
        if (language !== defaultLanguage && key === "label") {
          translationWhereCondition[key] = queryParams[key];
        }
      }
    });

    return translationWhereCondition;
  }

  /**
   * Get sorting order for queries
   */
  getSortingOrder(sortBy, orderBy, language, defaultLanguage, TranslationModel) {
    if (TranslationModel && language !== defaultLanguage && sortBy === "label") {
      return [[TranslationModel, "label", orderBy]];
    }
    return [[sortBy, orderBy]];
  }
}

module.exports = AbstractBaseController;
