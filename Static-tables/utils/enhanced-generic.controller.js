const { Sequelize, Op } = require("sequelize");
const config = require("../config/app-config");
const { getModelConfig } = require("../config/model-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const { sendSyncMessage } = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");
const AbstractBaseController = require("./abstract-base.controller");

/**
 * Enhanced Generic Controller
 * Implements AbstractBaseController
 * Single Responsibility: Handle CRUD operations with relationships, translations, and validation
 * No execution logic - pure enhanced controller implementation
 */
class EnhancedGenericController extends AbstractBaseController {
  constructor(modelName, db) {
    super(modelName, db);

    this.config = getModelConfig(modelName);

    if (!this.config) {
      throw new Error(`Model configuration not found for: ${modelName}`);
    }

    this.Model = db[this.config.model];
    this.TranslationModel = this.config.translationModel ? db[this.config.translationModel] : null;
  }

  /**
   * Build include array for relationships
   */
  buildIncludes(language, translationWhereCondition = {}) {
    const includes = [];

    // Add translation include if needed
    if (this.TranslationModel && language !== config.language) {
      includes.push({
        model: this.TranslationModel,
        attributes: ["label", ...this.config.specificAttributes.filter(attr =>
          this.TranslationModel.rawAttributes && this.TranslationModel.rawAttributes[attr]
        )],
        where: {
          ...translationWhereCondition,
          language_code: language.toUpperCase()
        },
        required: false,
      });
    }

    // Add relationship includes
    if (this.config.relationships) {
      this.config.relationships.forEach(rel => {
        const relationshipInclude = {
          model: this.db[rel.model],
          attributes: rel.attributes || { exclude: ["createdAt", "updatedAt"] },
        };

        // Add alias if specified
        if (rel.as) {
          relationshipInclude.as = rel.as;
        }

        // Add where condition if specified
        if (rel.where) {
          relationshipInclude.where = rel.where;
        }

        // Add nested includes for relationship translations
        if (rel.includeTranslations && language !== config.language) {
          const translationModelName = rel.translationModel;
          if (translationModelName && this.db[translationModelName]) {
            relationshipInclude.include = [{
              model: this.db[translationModelName],
              attributes: ["label"],
              where: { language_code: language.toUpperCase() },
              required: false,
            }];
          }
        }

        includes.push(relationshipInclude);
      });
    }

    return includes;
  }

  /**
   * Build where condition dynamically, handling active column filtering
   */
  buildWhereCondition(queryParams) {
    const whereCondition = {};
    const excludedParams = ["offset", "limit", "sort_by", "order_by", "language"];

    Object.keys(queryParams).forEach((key) => {
      if (!excludedParams.includes(key)) {
        // Handle active column filtering
        if (key === "active") {
          if (this.config.hasActive) {
            whereCondition[key] = queryParams[key];
          }
          // If model doesn't have active column, ignore the filter silently
        } else {
          whereCondition[key] = queryParams[key];
        }
      }
    });

    return whereCondition;
  }

  /**
   * Build translation where condition
   */
  buildTranslationWhereCondition(queryParams, language) {
    const translationWhereCondition = {};
    const excludedParams = ["offset", "limit", "sort_by", "order_by", "language"];

    Object.keys(queryParams).forEach((key) => {
      if (!excludedParams.includes(key)) {
        if (language !== config.language && key === "label") {
          translationWhereCondition[key] = queryParams[key];
        }
      }
    });

    return translationWhereCondition;
  }

  /**
   * Get sorting order for queries
   */
  getSortingOrder(sortBy, orderBy, language) {
    if (this.TranslationModel && language !== config.language && sortBy === "label") {
      return [[this.TranslationModel, "label", orderBy]];
    }
    return [[sortBy, orderBy]];
  }

  /**
   * Search/List items with pagination, filtering, and relationships
   */
  async search(req, res, next) {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit) : config.limit;
      const offset = req.query.offset ? parseInt(req.query.offset) * limit : config.offset * limit;
      const sortBy = req.query.sort_by || config.SortBy;
      const orderBy = req.query.order_by || config.OrderBy;
      const language = req.query.language || config.defaultReturnedLanguage;

      const whereCondition = this.buildWhereCondition(req.query);
      const translationWhereCondition = this.buildTranslationWhereCondition(req.query, language);
      const includes = this.buildIncludes(language, translationWhereCondition);

      let items, total_count;

      [items, total_count] = await Promise.all([
        this.Model.findAll({
          order: this.getSortingOrder(sortBy, orderBy, language),
          offset,
          limit,
          where: whereCondition,
          include: includes,
        }),
        this.Model.count({
          where: whereCondition,
          include: includes.filter(inc => inc.required !== false) // Only count required includes
        }),
      ]);

      // Map response using model-specific mapping function
      const mappedItems = items.map(item =>
        this.config.responseMapping(item, language, config)
      );

      res.send({ data: mappedItems, total_count });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get single item by ID with relationships
   */
  async getOne(req, res, next) {
    try {
      const language = req.query.language || config.language;
      const includes = this.buildIncludes(language);

      const item = await this.Model.findOne({
        where: { id: req.params.id },
        include: includes,
      });

      if (!item) {
        throw new NotFoundError(`${this.config.entityName} not found`, this.config.entityName);
      }

      // Map response using model-specific mapping function
      const mappedItem = this.config.responseMapping(item, language, config);

      res.send({ data: mappedItem });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create new item with relationship validation
   */
  async create(req, res, next) {
    try {
      const { code, label, language = config.defaultReturnedLanguage } = req.body;
      const isDefaultLanguage = language.toUpperCase() === config.language;

      // Check if item already exists
      const existingItem = await this.Model.findOne({ where: { code } });
      if (existingItem) {
        throw new ConflictError(`${this.config.entityName} already exists`, this.config.entityName);
      }

      // Validate relationships if specified
      if (this.config.relationshipValidations) {
        for (const validation of this.config.relationshipValidations) {
          const relatedModel = this.db[validation.model];
          const relatedItem = await relatedModel.findOne({
            where: { [validation.field]: req.body[validation.bodyField] }
          });

          if (!relatedItem) {
            throw new NotFoundError(`${validation.entityName} doesn't exist`, validation.entityName);
          }
        }
      }

      // Create main item
      const newItem = await this.Model.create(req.body);

      // Create translation if needed
      if (this.TranslationModel && newItem) {
        await this.TranslationModel.create({
          [this.config.translationKey]: newItem.code,
          label: !isDefaultLanguage ? label : "",
          language_code: !isDefaultLanguage ? language.toUpperCase() : "",
        });
      }

      // Send sync message
      await sendSyncMessage(
        OperationType.POST,
        this.config.entityName,
        this.config.tableName,
        newItem
      );

      res.status(201).send({ data: newItem });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update existing item with relationship validation
   */
  async update(req, res, next) {
    try {
      const id = req.params.id;
      const { code, label, language = config.defaultReturnedLanguage } = req.body;
      const isDefaultLanguage = language.toUpperCase() === config.language;

      // Find existing item
      const oldItem = await this.Model.findOne({ where: { id } });
      if (!oldItem) {
        throw new NotFoundError(`${this.config.entityName} not found`, this.config.entityName);
      }

      // Check for code conflicts
      const existingItem = await this.Model.findOne({ where: { code } });
      if (existingItem && existingItem.id !== parseInt(id)) {
        throw new ConflictError(`${this.config.entityName} with matching code already exists`, this.config.entityName);
      }

      // Check system attribute restrictions
      if (this.config.hasSystemAttribute && oldItem.code !== code && oldItem.system_attribute) {
        throw new ConflictError(`${this.config.entityName} cannot be modified`, this.config.entityName);
      }

      // Validate relationships if specified
      if (this.config.relationshipValidations) {
        for (const validation of this.config.relationshipValidations) {
          if (req.body[validation.bodyField]) {
            const relatedModel = this.db[validation.model];
            const relatedItem = await relatedModel.findOne({
              where: { [validation.field]: req.body[validation.bodyField] }
            });

            if (!relatedItem) {
              throw new NotFoundError(`${validation.entityName} doesn't exist`, validation.entityName);
            }
          }
        }
      }

      // Update main item
      await this.Model.update(req.body, { where: { id } });

      // Update translation if exists
      if (this.TranslationModel) {
        const existingTranslation = await this.TranslationModel.findOne({
          where: { [this.config.translationKey]: oldItem.code },
        });

        if (existingTranslation) {
          await this.TranslationModel.update(
            {
              [this.config.translationKey]: code,
              label: !isDefaultLanguage ? label : existingTranslation.label,
            },
            { where: { [this.config.translationKey]: oldItem.code } }
          );
        }
      }

      // Get updated item
      const updatedItem = await this.Model.findOne({ where: { id } });

      // Send sync message
      await sendSyncMessage(
        OperationType.PUT,
        this.config.entityName,
        this.config.tableName,
        updatedItem
      );

      res.send({ data: updatedItem });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete item
   */
  async remove(req, res, next) {
    try {
      const id = req.params.id;
      const item = await this.Model.findOne({ where: { id } });

      if (!item) {
        throw new NotFoundError(`${this.config.entityName} not found`, this.config.entityName);
      }

      // Check system attribute restrictions
      if (this.config.hasSystemAttribute && item.system_attribute) {
        throw new ConflictError("Cannot delete system attribute", this.config.entityName);
      }

      // Delete item
      await this.Model.destroy({ where: { id } });

      // Send sync message
      await sendSyncMessage(
        OperationType.DELETE,
        this.config.entityName,
        this.config.tableName,
        item
      );

      res.send({
        data: {
          message: `Resource with ID ${id} has been deleted successfully`,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = EnhancedGenericController;
