const { Sequelize, Op } = require("sequelize");
const config = require("../config/app-config");
const { getModelConfig } = require("../config/model-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const { sendSyncMessage } = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");
const AbstractBaseController = require("./abstract-base.controller");

/**
 * Basic Generic Controller
 * Implements AbstractBaseController
 * Single Responsibility: Handle basic CRUD operations with translations and active column filtering
 * No execution logic - pure controller implementation
 */
class BasicGenericController extends AbstractBaseController {
  constructor(modelName, db) {
    super(modelName, db);

    this.config = getModelConfig(modelName);

    if (!this.config) {
      throw new Error(`Model configuration not found for: ${modelName}`);
    }

    this.Model = db[this.config.model];
    this.TranslationModel = this.config.translationModel ? db[this.config.translationModel] : null;
  }

  /**
   * Build where condition dynamically, handling active column filtering
   */
  buildWhereCondition(queryParams, isTranslation = false) {
    const whereCondition = {};
    const excludedParams = ["offset", "limit", "sort_by", "order_by", "language"];

    Object.keys(queryParams).forEach((key) => {
      if (!excludedParams.includes(key)) {
        // Handle active column filtering
        if (key === "active") {
          if (this.config.hasActive) {
            whereCondition[key] = queryParams[key];
          }
          // If model doesn't have active column, ignore the filter silently
          // This prevents errors when frontend always sends active=true
        } else {
          whereCondition[key] = queryParams[key];
        }
      }
    });

    return whereCondition;
  }

  /**
   * Build translation where condition
   */
  buildTranslationWhereCondition(queryParams, language) {
    const translationWhereCondition = {};
    const excludedParams = ["offset", "limit", "sort_by", "order_by", "language"];

    Object.keys(queryParams).forEach((key) => {
      if (!excludedParams.includes(key)) {
        if (language !== config.language && key === "label") {
          translationWhereCondition[key] = queryParams[key];
        }
      }
    });

    return translationWhereCondition;
  }

  /**
   * Get sorting order for queries
   */
  getSortingOrder(sortBy, orderBy, language) {
    if (this.TranslationModel && language !== config.language && sortBy === "label") {
      return [[this.TranslationModel, "label", orderBy]];
    }
    return [[sortBy, orderBy]];
  }

  /**
   * Search/List items with pagination and filtering
   */
  async search(req, res, next) {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit) : config.limit;
      const offset = req.query.offset ? parseInt(req.query.offset) : config.offset
      const sortBy = req.query.sort_by || config.SortBy;
      const orderBy = req.query.order_by || config.OrderBy;
      const language = req.query.language || config.defaultReturnedLanguage;

      const whereCondition = this.buildWhereCondition(req.query);
      const translationWhereCondition = this.buildTranslationWhereCondition(req.query, language);

      let items, total_count;

      if (language.toUpperCase() === config.language || !this.TranslationModel) {
        // Query main model only
        [items, total_count] = await Promise.all([
          this.Model.findAll({
            order: [[sortBy, orderBy]],
            offset,
            limit,
            where: whereCondition,
          }),
          this.Model.count({ where: whereCondition }),
        ]);
      } else {
        // Query with translations
        [items, total_count] = await Promise.all([
          this.Model.findAll({
            order: this.getSortingOrder(sortBy, orderBy, language),
            offset,
            limit,
            where: whereCondition,
            include: [
              {
                model: this.TranslationModel,
                where: {
                  ...translationWhereCondition,
                  language_code: language.toUpperCase()
                },
                attributes: ["label", ...this.config.specificAttributes.filter(attr =>
                  this.TranslationModel.rawAttributes && this.TranslationModel.rawAttributes[attr]
                )],
                required: false,
              },
            ],
          }),
          this.Model.count({
            where: whereCondition,
            include: this.TranslationModel ? {
              model: this.TranslationModel,
              where: {
                ...translationWhereCondition,
                language_code: language.toUpperCase()
              },
            } : undefined,
          }),
        ]);
      }

      // Map response using model-specific mapping function
      const mappedItems = items.map(item =>
        this.config.responseMapping(item, language, config)
      );

      res.send({ data: mappedItems, total_count });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get single item by ID
   */
  async getOne(req, res, next) {
    try {
      const language = req.query.language || config.language;
      const item = await this.Model.findOne({
        where: { id: req.params.id },
      });

      if (!item) {
        throw new NotFoundError(`${this.config.entityName} not found`, this.config.entityName);
      }

      // First result with default language
      const firstResult = this.config.responseMapping(item, config.language, config);

      if (language.toUpperCase() !== config.language && this.TranslationModel) {
        const translation = await this.TranslationModel.findOne({
          attributes: ["label", ...this.config.specificAttributes.filter(attr =>
            this.TranslationModel.rawAttributes && this.TranslationModel.rawAttributes[attr]
          )],
          where: {
            [this.config.translationKey]: item.code,
            language_code: language.toUpperCase(),
          },
          include: [
            {
              attributes: ["id", ...(this.config.hasActive ? ["active"] : []), ...(this.config.hasSystemAttribute ? ["system_attribute"] : [])],
              model: this.Model,
              on: {
                [`$${this.modelName}.code$`]: {
                  [Op.col]: `${this.config.translationModel.toLowerCase()}.${this.config.translationKey}`,
                },
              },
            },
          ],
          raw: true,
        });

        if (translation) {
          const translatedResult = this.config.responseMapping({
            ...item.dataValues,
            [`${this.modelName}_translations`]: [translation]
          }, language, config);

          return res.send({ data: translatedResult });
        }
      }

      res.send({ data: firstResult });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create new item
   */
  async create(req, res, next) {
    try {
      const { code, label, language = config.defaultReturnedLanguage } = req.body;
      const isDefaultLanguage = language.toUpperCase() === config.language;

      // Check if item already exists
      const existingItem = await this.Model.findOne({ where: { code } });
      if (existingItem) {
        throw new ConflictError(`${this.config.entityName} already exists`, this.config.entityName);
      }

      // Create main item
      const newItem = await this.Model.create(req.body);

      // Create translation if needed
      if (this.TranslationModel && newItem) {
        await this.TranslationModel.create({
          [this.config.translationKey]: newItem.code,
          label: !isDefaultLanguage ? label : "",
          language_code: !isDefaultLanguage ? language.toUpperCase() : "",
        });
      }

      // Send sync message
      await sendSyncMessage(
        OperationType.POST,
        this.config.entityName,
        this.config.tableName,
        newItem
      );

      res.status(201).send({ data: newItem });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update existing item
   */
  async update(req, res, next) {
    try {
      const id = req.params.id;
      const { code, label, language = config.defaultReturnedLanguage } = req.body;
      const isDefaultLanguage = language.toUpperCase() === config.language;

      // Find existing item
      const oldItem = await this.Model.findOne({ where: { id } });
      if (!oldItem) {
        throw new NotFoundError(`${this.config.entityName} not found`, this.config.entityName);
      }

      // Check for code conflicts
      const existingItem = await this.Model.findOne({ where: { code } });
      if (existingItem && existingItem.id !== parseInt(id)) {
        throw new ConflictError(`${this.config.entityName} with matching code already exists`, this.config.entityName);
      }

      // Check system attribute restrictions
      if (this.config.hasSystemAttribute && oldItem.code !== code && oldItem.system_attribute) {
        throw new ConflictError(`${this.config.entityName} cannot be modified`, this.config.entityName);
      }

      // Update main item
      await existingItem.update(req.body);

      // Update translation if exists
      if (this.TranslationModel) {
        const existingTranslation = await this.TranslationModel.findOne({
          where: { [this.config.translationKey]: oldItem.code },
        });

        if (existingTranslation) {
          await existingTranslation.update({
            [this.config.translationKey]: code,
            label: !isDefaultLanguage ? label : existingTranslation.label,
          });
        }
      }

      // Get updated item
      const updatedItem = await this.Model.findOne({ where: { id } });

      // Send sync message
      await sendSyncMessage(
        OperationType.PUT,
        this.config.entityName,
        this.config.tableName,
        updatedItem
      );

      res.send({ data: updatedItem });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete item
   */
  async remove(req, res, next) {
    try {
      const id = req.params.id;
      const item = await this.Model.findOne({ where: { id } });

      if (!item) {
        throw new NotFoundError(`${this.config.entityName} not found`, this.config.entityName);
      }

      // Check system attribute restrictions
      if (this.config.hasSystemAttribute && item.system_attribute) {
        throw new ConflictError("Cannot delete system attribute", this.config.entityName);
      }

      // Delete item
      await this.Model.destroy({ where: { id } });

      // Send sync message
      await sendSyncMessage(
        OperationType.DELETE,
        this.config.entityName,
        this.config.tableName,
        item
      );

      res.send({
        data: {
          message: `Resource with ID ${id} has been deleted successfully`,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = BasicGenericController;
