"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    return queryInterface.bulkDelete(
      "activity_translations",
      {
        activity_code: {
          [Sequelize.Op.in]: ["CBI", "Emprunt", "Ready"],
        },
      },
      {}
    );
  },

  async down(queryInterface, Sequelize) {
    return queryInterface.bulkInsert(
      "activity_translations",
      [
        {
          language_code: "FR",
          activity_code: "CBI",
          activity_associated_to: "DOSSIER",
          label: "Crédit bail immobilié",
        },
        {
          language_code: "FR",
          activity_code: "Emprunt",
          activity_associated_to: "DOSSIER",
          label: "Refinancement",
        },
        {
          language_code: "FR",
          activity_code: "Ready",
          activity_associated_to: "DOSSIER",
          label: "Crédit classique",
        },
      ],
      {}
    );
  },
};
