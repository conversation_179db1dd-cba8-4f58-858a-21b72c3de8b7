/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface) => {
    const tax_rate_translations =  [
      {
        "tax_rate_code": "VATDE",
        "tax_code": "VATDE",
        "label": "allemand VAT",
        "rate": 3,
        "start_date": "2020-11-24",
        "end_date": "2020-12-24",
        "language_code": "FR"
      },
      {
        "tax_rate_code": "TVADE",
        "tax_code": "TVADE",
        "label": "allemand Standard VAT",
        "rate": 19,
        "start_date": "2020-11-24",
        "end_date": "2020-12-24",
        "language_code": "FR"
      },
      {
        "tax_rate_code": "TVADE1",
        "tax_code": "TVADE1",
        "label": "allemand VAT 1",
        "rate": 10,
        "start_date": "2020-11-24",
        "end_date": "2020-12-24",
        "language_code": "FR"
      },
      {
        "tax_rate_code": "VATDE1",
        "tax_code": "VATDE1",
        "label": "allemand Reduced VAT",
        "rate": 16,
        "start_date": "2020-11-24",
        "end_date": "2020-12-24",
        "language_code": "FR"
      },
      {
        "tax_rate_code": "TVALUBE1",
        "tax_code": "TVALUBE1",
        "label": "TVA réduite belge 1",
        "rate": 16,
        "start_date": "2020-11-24",
        "end_date": "2020-12-24",
        "language_code": "FR"
      },
      {
        "tax_rate_code": "TVALUBE",
        "tax_code": "TVALUBE",
        "label": "TVA réduite belge",
        "rate": 14,
        "start_date": "2020-11-24",
        "end_date": "2020-12-24",
        "language_code": "FR"
      },
    ];

    for (const tax_rate of tax_rate_translations) {
      // Check if tax with this tax_rate_code exists
      const existingTax = await queryInterface.rawSelect(
          "tax_rate_translations",
          {
            where: {
              tax_rate_code: tax_rate.tax_rate_code,
              language_code: tax_rate.language_code
            }
          },
          ["id"]
      );

      if (existingTax) {
        // If exists, update everything except code
        await queryInterface.bulkUpdate(
            "tax_rate_translations",
            {
              tax_code: tax_rate.tax_code,
              label: tax_rate.label,
              rate: tax_rate.rate,
              start_date: tax_rate.start_date,
              end_date: tax_rate.end_date,
            },
            {
              tax_rate_code: tax_rate.tax_rate_code,
              language_code: tax_rate.language_code
            }  // Where clause - matches by code
        );
      } else {
        // If doesn't exist, create new record
        await queryInterface.bulkInsert("tax_rate_translations", [tax_rate]);
      }
    }
  },

  down: async (queryInterface) => {
    try {
      await queryInterface.bulkDelete("tax_rate_translations",  null, {});
    } catch (error) {
      console.error(`Error deleting tax_rates with empty code:`, error);
    }
  },
};
