/** @type {import('sequelize-cli').Migration} */
module.exports = {
    up: async (queryInterface, Sequelize) => {
        const role_translations = [
            {

                "role_code": "COLOC",
                "language_code": "FR",
                "label": "Colocataire (Facturé)",
            },
            {

                "role_code": "DEPT",
                "language_code": "FR",
                "label": "Département",
            },
            {

                "role_code": "FOURN",
                "language_code": "FR",
                "label": "Fournisseur",
            },
            {

                "role_code": "GARANT",
                "language_code": "FR",
                "label": "<PERSON>aran<PERSON>",
            },
            {

                "role_code": "CLIENT",
                "language_code": "FR",
                "label": "Client locataire (Facturé)",
            },
            {

                "role_code": "PARTEN",
                "language_code": "FR",
                "label": "Partenaire",
            },
            {

                "role_code": "ASSUR",
                "language_code": "FR",
                "label": "Assureur",
            },
            {

                "role_code": "CLITREP",
                "language_code": "FR",
                "label": "Agence de recouvrement",
            },
            {

                "role_code": "ACHET",
                "language_code": "FR",
                "label": "Acheteur",
            },
            {

                "role_code": "PRETEUR",
                "language_code": "FR",
                "label": "Préteur",
            },
            {

                "role_code": "EXTERNE",
                "language_code": "FR",
                "label": "Prestataire externe",
            },
            {

                "role_code": "AGENCE",
                "language_code": "FR",
                "label": "Agence partenaire",
            },
            {

                "role_code": "BAILS",
                "language_code": "FR",
                "label": "Bailleur Refinanceur",
            },
            {

                "role_code": "APPORT",
                "language_code": "FR",
                "label": "Apporteur",
            },
            {

                "role_code": "NOTAIRE",
                "language_code": "FR",
                "label": "Notaire",
            },
            {

                "role_code": "AVOCAT",
                "language_code": "FR",
                "label": "Avocat",
            },
            {

                "role_code": "HUISSIE",
                "language_code": "FR",
                "label": "Huissier",
            },
            {

                "role_code": "BROKER",
                "language_code": "FR",
                "label": "Broker",
            },
            {

                "role_code": "DELEG",
                "language_code": "FR",
                "label": "Délégataire",
            },
            {

                "role_code": "FCT",
                "language_code": "FR",
                "label": "FCT",
            },
            {

                "role_code": "INTERNE",
                "language_code": "FR",
                "label": "Interne",
            },
            {

                "role_code": "HOLDER",
                "language_code": "FR",
                "label": "Holder",
            },
            {

                "role_code": "OWNER",
                "language_code": "FR",
                "label": "Owner",
            },
            {

                "role_code": "NOTOR",
                "language_code": "FR",
                "label": "Notor",
            },
            {

                "role_code": "PROEXT",
                "language_code": "FR",
                "label": "Proprietaire externe",
            },
            {

                "role_code": "COURT",
                "language_code": "FR",
                "label": "Courtier",
            },
            {

                "role_code": "LOCFACT",
                "language_code": "FR",
                "label": "Siège social",
            },
            {

                "role_code": "CESSIO",
                "language_code": "FR",
                "label": "Cessionnaire",
            },
            {

                "role_code": "TIEENC",
                "language_code": "FR",
                "label": "Tiers Encaisseur",
            },
            {

                "role_code": "REGREC",
                "language_code": "FR",
                "label": "Regie des Recettes / Impots / Tresor Public",
            },
            {

                "role_code": "TIEPAY",
                "language_code": "FR",
                "label": "Tiers Payeur",
            },
            {

                "role_code": "ATTRIB",
                "language_code": "FR",
                "label": "Attributaire",
            },
            {

                "role_code": "TIEREP",
                "language_code": "FR",
                "label": "Tiers Repreneur",
            },
            {

                "role_code": "ADMJUD",
                "language_code": "FR",
                "label": "Administrateur Judiciaire",
            },
            {

                "role_code": "ASSCRE",
                "language_code": "FR",
                "label": "Assurance Credit",
            },
            {

                "role_code": "SSLOCAT",
                "language_code": "FR",
                "label": "Sous locataire (Facturé)",
            },
            {

                "role_code": "CREANC",
                "language_code": "FR",
                "label": "Créancier",
            },
            {

                "role_code": "ENVEL",
                "language_code": "FR",
                "label": "Enveloppe",
            },
            {
                "role_code": "LESSOR",
                "language_code": "FR",
                "label": "Bailleur",
            }
        ];


        for (const role of role_translations) {
            try {
                const existingRole = await queryInterface.rawSelect(
                    "role_translations",
                    {
                        where: {
                            role_code: role.role_code,
                            language_code: role.language_code
                        }
                    },
                    ["id"]
                );

                if (!existingRole) {
                    await queryInterface.bulkInsert("role_translations", [role]);
                }
            } catch (error) {
                console.error(`Error inserting role "${role.label}":`, error);
            }
        }
    },

    down: async (queryInterface, Sequelize) => {
        try {
            await queryInterface.bulkDelete("role_translations", null, {});
        } catch (error) {
            console.error(`Error deleting role_translations with empty code:`, error);
        }
    },
};
