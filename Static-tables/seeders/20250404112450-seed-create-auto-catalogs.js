"use strict";

module.exports = {
    async up(queryInterface) {
        const existingRecords = await queryInterface.sequelize.query(
            "SELECT code FROM auto_catalogs",
            { type: queryInterface.sequelize.QueryTypes.SELECT }
        );

        const newRecords = [
            {
                code: "01",
                label: "TOYOTA",
                start_date: new Date(),
                end_date: null,
                description:"",
                active: true
            },
            {
                code: "02",
                label: "BMW",
                start_date: new Date(),
                end_date: null,
                description: "",
                active: true
            },
            {
              code: "03",
              label: "AUDI",
              start_date: new Date(),
              end_date: null,
              description: "",
              active: true
          },
          {
              code: "04",
              label: "PEUGEOT",
              start_date: new Date(),
              end_date: null,
              description: "",
              active: true
          },
          {
            code: "05",
            label: "KIA",
            start_date: new Date(),
            end_date: null,
            description: "",
            active: true
        },
        {
            code: "06",
            label: "FORD",
            start_date: new Date(),
            end_date: null,
            description: "",
            active: true
        },
        {
          code: "07",
          label: "OPEL",
          start_date: new Date(),
          end_date: null,
          description: "",
          active: true
      },
      {
          code: "08",
          label: "GOLF",
          start_date: new Date(),
          end_date: null,
          description: "",
          active: true
      },
      {
        code: "09",
        label: "RANGE ROVER",
        start_date: new Date(),
        end_date: null,
        description: "",
        active: true
    },
    {
        code: "10",
        label: "MERCEDES",
        start_date: new Date(),
        end_date: null,
        description: "",
        active: true
    },
    {
      code: "11",
      label: "TESLA",
      start_date: new Date(),
      end_date: null,
      description:"",
      active: true
  },
  {
      code: "12",
      label: "NISSAN",
      start_date: new Date(),
      end_date: null,
      description:"",
      active: true
  },
  {
    code: "13",
    label: "HONDA",
    start_date: new Date(),
    end_date: null,
    description:"",
    active: true
},
{
    code: "14",
    label: "VOLKSWAGEN",
    start_date: new Date(),
    end_date: null,
    description:"",
    active: true
},
{
  code: "15",
  label: "HYUNDAI",
  start_date: new Date(),
  end_date: null,
  description:"",
  active: true
},
{
  code: "16",
  label: "FIAT",
  start_date: new Date(),
  end_date: null,
  description:"",
  active: true
},
{
  code: "17",
  label: "JEEP",
  start_date: new Date(),
  end_date: null,
  description:"",
  active: true
}
        ];

        const filteredRecords = newRecords.filter(
            (record) => !existingRecords.some((existing) => existing.code === record.code)
        );

        if (filteredRecords.length > 0) {
            await queryInterface.bulkInsert("auto_catalogs", filteredRecords);
        }
    },

    async down(queryInterface) {
        await queryInterface.bulkDelete("auto_catalog", null, {});
    }
};