"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up (queryInterface, Sequelize) {
        const allocation_translations =
            [
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "MORA",
                    "allocation_code": "MORAT",
                    "label": "<PERSON>ratoire",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "PREL",
                    "allocation_code": "PRELOY",
                    "label": "Loyer intercalaire",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "LOYE",
                    "allocation_code": "RLOCINT",
                    "label": "Loyer",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "PRES",
                    "allocation_code": "RMAINT",
                    "label": "Maintenance",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "RPDTFIN",
                    "label": "Intérêts de retard et frais de recouvrement",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "RINDMRE",
                    "label": "Frais de résiliation / Indemnités",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "RECO",
                    "allocation_code": "RRELOC",
                    "label": "Reconduction du loyer",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "PRES",
                    "allocation_code": "RVTASSU",
                    "label": "Facturation Assurance",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "PRES",
                    "allocation_code": "RFRDOSS",
                    "label": "Frais de dossier",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "GARA",
                    "allocation_code": "RGDOS",
                    "label": "Retenue de garantie sur dossier",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "GARA",
                    "allocation_code": "RGENC",
                    "label": "Retenue de garantie sur encours",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FREMAN",
                    "label": "Frais de gestion Remaniement du schéma financier",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FREJET",
                    "label": "Frais de rejet de prélevement",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "RADRESS",
                    "label": "Changement adresse",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "RRIB",
                    "label": "Changement RIB/IBAN",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "RMECH",
                    "label": "Modification date échéances",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "RMPLA",
                    "label": "Modif. plan de remboursement",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "RTRTC",
                    "label": "Transfert de titulaire",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "PRES",
                    "allocation_code": "ASSURC",
                    "label": "Facturation Assurance RC",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "PRES",
                    "allocation_code": "ASSUVIE",
                    "label": "Facturation Assurance Vie",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FDENOM",
                    "label": "Changement de dénomination sociale",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FCOPIE",
                    "label": "Copie de contrat te de toutes pièces",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FDUPF",
                    "label": "Duplicata de facture, d'échéancier, de RVF",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FDUPC",
                    "label": "Duplicata de certificat de cession",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "LOYE",
                    "allocation_code": "FECH",
                    "label": "Facturation à l'échéance",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FSIN",
                    "label": "Frais de gestion de sinistres",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FPAY",
                    "label": "Changement de mode de paiement",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FQUAN",
                    "label": "Changement de quantième",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FREAM",
                    "label": "Réaménagement de contrat",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FTRAN",
                    "label": "Transfert de contrat",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FRECH",
                    "label": "Recherches diverses",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FATT",
                    "label": "Attestation diverse",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FDEC",
                    "label": "Communication d'un décompte de solde anticipé",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FREPR",
                    "label": "Frais de représentation sur impayés",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FRET",
                    "label": "Pénalités de retard",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FRECO",
                    "label": "Frais de dossier de recouvrement",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FINDEM",
                    "label": "Indemnité forfaitaire",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FMEP",
                    "label": "Frais de mise en place des dossiers",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FGEST",
                    "label": "Frais de gestion annuels",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FSEPA",
                    "label": "Opérations sans mandats SEPA",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FDIV",
                    "label": "Frais d'acte de gestion SAV",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "RECO",
                    "allocation_code": "RELOC",
                    "label": "Relocation",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FREMISE",
                    "label": "Frais de remise en etat",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "FREES",
                    "allocation_code": "FDAUDI",
                    "label": "Frais d'audit",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "DEPK",
                    "allocation_code": "DEPK",
                    "label": "Dépassement kilométrique",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "PRES",
                    "allocation_code": "GESPAC",
                    "label": "Gestion de parc",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "PRES",
                    "allocation_code": "GESSENI",
                    "label": "Gestion des sinistres",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "PRES",
                    "allocation_code": "GESPAN",
                    "label": "Gestion des pannes",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCLI",
                    "allocation_category_code": "PRES",
                    "allocation_code": "GESFIN",
                    "label": "Gestion de fin de vie",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCHD",
                    "allocation_category_code": "ASSET",
                    "allocation_code": "DLOCINT",
                    "label": "Achat du bien",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCHD",
                    "allocation_category_code": "COMM",
                    "allocation_code": "DCOMAP",
                    "label": "Commission d'apporteur d'affaires",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCHD",
                    "allocation_category_code": "REVS",
                    "allocation_code": "RMAINT",
                    "label": "Reversement Maintenance",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCHD",
                    "allocation_category_code": "REVS",
                    "allocation_code": "RVTASSU",
                    "label": "Reversement Assurance",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCHD",
                    "allocation_category_code": "REVS",
                    "allocation_code": "REVLOY",
                    "label": "Reversement Loyer",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCHD",
                    "allocation_category_code": "GARA",
                    "allocation_code": "REMBDG",
                    "label": "Remboursement depot de garantie",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CMDEHB",
                    "allocation_category_code": "ACQU",
                    "allocation_code": "ABIE",
                    "label": "Bien principal",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CMDEHB",
                    "allocation_category_code": "ACQU",
                    "allocation_code": "ACCN",
                    "label": "Accessoire",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CMDEHB",
                    "allocation_category_code": "ACQU",
                    "allocation_code": "OPTI",
                    "label": "Option",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CMDEHB",
                    "allocation_category_code": "ACQU",
                    "allocation_code": "RMCL",
                    "label": "Remise fournisseur",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CMDEHB",
                    "allocation_category_code": "APPO",
                    "allocation_code": "APPO",
                    "label": "apport",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CMDEHB",
                    "allocation_category_code": "FRNA",
                    "allocation_code": "CGRI",
                    "label": "Frais carte grise",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CMDEHB",
                    "allocation_category_code": "FRNA",
                    "allocation_code": "FLIV",
                    "label": "Frais de livraison",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CMDEHB",
                    "allocation_category_code": "FRNA",
                    "allocation_code": "MALU",
                    "label": "Malus",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CMDEHB",
                    "allocation_category_code": "FRNA",
                    "allocation_code": "PACO",
                    "label": "Paiement acompte",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CMDEHB",
                    "allocation_category_code": "FRNA",
                    "allocation_code": "RACO",
                    "label": "Remboursement acompte",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCES",
                    "allocation_category_code": "CESS",
                    "allocation_code": "CANT",
                    "label": "Cession anticipee",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCES",
                    "allocation_category_code": "CESS",
                    "allocation_code": "CCTX",
                    "label": "Resiliation",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCES",
                    "allocation_category_code": "CESS",
                    "allocation_code": "CFIN",
                    "label": "Cession a terme",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCES",
                    "allocation_category_code": "CESS",
                    "allocation_code": "CSIN",
                    "label": "Cession sinistre",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCES",
                    "allocation_category_code": "CESS",
                    "allocation_code": "CTNL",
                    "label": "Cession ITNL",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCES",
                    "allocation_category_code": "CDPK",
                    "allocation_code": "DEPK",
                    "label": "Dépassement kilométrique",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCES",
                    "allocation_category_code": "INDM",
                    "allocation_code": "INDR",
                    "label": "Indemnite fin anticipe",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACCES",
                    "allocation_category_code": "PRES",
                    "allocation_code": "VR00",
                    "label": "Véhicule de  Remplacement",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACNPA",
                    "allocation_category_code": "ACQU",
                    "allocation_code": "ABIE",
                    "label": "Bien principal",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACNPA",
                    "allocation_category_code": "ACQU",
                    "allocation_code": "ACCN",
                    "label": "Accessoire",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACNPA",
                    "allocation_category_code": "ACQU",
                    "allocation_code": "OPTI",
                    "label": "Option",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACNPA",
                    "allocation_category_code": "ACQU",
                    "allocation_code": "RMCL",
                    "label": "Remise fournisseur",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACNPA",
                    "allocation_category_code": "APPO",
                    "allocation_code": "APPO",
                    "label": "apport",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACNPA",
                    "allocation_category_code": "FRNA",
                    "allocation_code": "CGRI",
                    "label": "Frais carte grise",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACNPA",
                    "allocation_category_code": "FRNA",
                    "allocation_code": "FLIV",
                    "label": "Frais de livraison",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACNPA",
                    "allocation_category_code": "FRNA",
                    "allocation_code": "MALU",
                    "label": "Malus",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACNPA",
                    "allocation_category_code": "FRNA",
                    "allocation_code": "PACO",
                    "label": "Paiement acompte",
                    "language_code": "FR"
                },
                {
                    "operation_code": "FACNPA",
                    "allocation_category_code": "FRNA",
                    "allocation_code": "RACO",
                    "label": "Remboursement acompte",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CONMEL",
                    "allocation_category_code": "MILO",
                    "allocation_code": "BLOC",
                    "label": "Base locative",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CONMEL",
                    "allocation_category_code": "MILO",
                    "allocation_code": "EBLC",
                    "label": "Ecart base locative courante",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CONMEL",
                    "allocation_category_code": "MILO",
                    "allocation_code": "IRDU",
                    "label": "interets restant dus",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CONMEL",
                    "allocation_category_code": "MILO",
                    "allocation_code": "LRDU",
                    "label": "Echeance restant dues",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CONMEL",
                    "allocation_category_code": "MILO",
                    "allocation_code": "VIB",
                    "label": "Immo fiscale",
                    "language_code": "FR"
                },
                {
                    "operation_code": "CONMEL",
                    "allocation_category_code": "MILO",
                    "allocation_code": "VIBF",
                    "label": "Vib financiere",
                    "language_code": "FR"
                },
                {
                    "operation_code": "ENCAIS",
                    "allocation_category_code": "NIMP",
                    "allocation_code": "NIMP",
                    "label": "Non impute",
                    "language_code": "FR"
                },
                {
                    "operation_code": "DECAIS",
                    "allocation_category_code": "FFOU",
                    "allocation_code": "COMM",
                    "label": "Commission apporteur",
                    "language_code": "FR"
                },
                {
                    "operation_code": "DECAIS",
                    "allocation_category_code": "NIMP",
                    "allocation_code": "NIMP",
                    "label": "Non impute",
                    "language_code": "FR"
                },
            ]

        for (const allocation of allocation_translations) {
            const existingActivity = await queryInterface.rawSelect(
                "allocation_translations",
                {
                    where: {
                        allocation_code: allocation.allocation_code,
                        language_code: allocation.language_code
                    }
                },["id"]
            );

            if (!existingActivity) {
                await queryInterface.bulkInsert("allocation_translations", [allocation]);
            }
        }
    },

    async down (queryInterface, Sequelize) {
        return queryInterface.bulkDelete("allocation_translations", null, {});
    }
};
