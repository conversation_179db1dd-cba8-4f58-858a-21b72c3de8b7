/** @type {import('sequelize-cli').Migration} */
module.exports = {
    up: async (queryInterface, Sequelize) => {
        // Check if each record exists before inserting
        const products =
            [
                {
                    "code": "CBICBI",
                    "label": "Real Estate Lease"
                },
                {
                    "code": "LESCBM",
                    "label": "Equipment Leasing"
                },
                {
                    "code": "LESLOA",
                    "label": "Lease with Purchase Option"
                },
                {
                    "code": "LESLLD",
                    "label": "Long-Term Lease"
                },
                {
                    "code": "LOCAUT",
                    "label": "Self-propelled"
                },
                {
                    "code": "CESCOM",
                    "label": "Full Transfer"
                },
                {
                    "code": "CESMAD",
                    "label": "Mandated Transfer"
                },
                {
                    "code": "CREAFF",
                    "label": "Assigned Credit"
                },
                {
                    "code": "CRENAF",
                    "label": "Non-assigned Credit"
                },
                {
                    "code": "MASFAC",
                    "label": "Master Facility"
                }
            ]

        for (const product of products) {
            const existingActivity = await queryInterface.rawSelect(
                "products",
                {
                    where: {
                        code: product.code
                    }
                },
                ["id"]
            );

            if (!existingActivity) {
                await queryInterface.bulkInsert("products", [product]);
            }
        }
    },

    down: async (queryInterface, Sequelize) => {
        return queryInterface.bulkDelete("products", null, {});
    },
};
