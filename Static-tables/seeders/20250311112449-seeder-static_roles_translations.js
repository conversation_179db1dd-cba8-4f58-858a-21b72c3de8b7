"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const roleTranslations = [
      { static_role_code: "ACHET", label: "Acheteur", language_code: "FR" },
      { static_role_code: "CLIENT", label: "Client", language_code: "FR" },
      { static_role_code: "PARTEN", label: "Partenaire", language_code: "FR" },
      { static_role_code: "APPORT", label: "Apporteur", language_code: "FR" },
      { static_role_code: "FOURN", label: "Fournisseur", language_code: "FR" },
      { static_role_code: "GARANT", label: "<PERSON>aran<PERSON>", language_code: "FR" },
      { static_role_code: "OWNER", label: "<PERSON><PERSON><PERSON><PERSON><PERSON>", language_code: "FR" },
      { static_role_code: "AVOCAT", label: "Avocat", language_code: "FR" },
      { static_role_code: "LESSOR", label: "Bailleur", language_code: "FR" },
      { static_role_code: "BROKER", label: "Courtier", language_code: "FR" },
      { static_role_code: "CREANC", label: "Créancier", language_code: "FR" },
      { static_role_code: "DELEG", label: "Délégataire", language_code: "FR" },
      { static_role_code: "ENVEL", label: "Enveloppe", language_code: "FR" },
      { static_role_code: "HOLDER", label: "Détenteur", language_code: "FR" },
      { static_role_code: "HUISSIE", label: "Huissier", language_code: "FR" },
      { static_role_code: "NOTAIRE", label: "Notaire", language_code: "FR" },
      { static_role_code: "NOTOR", label: "Notor", language_code: "FR" },
      { static_role_code: "PRETEUR", label: "Préteur", language_code: "FR" },
      { static_role_code: "RENT", label: "Loueur", language_code: "FR" },
    ];

    for (const translation of roleTranslations) {
      try {
        const existingTranslation = await queryInterface.rawSelect(
          "static_role_translations",
          {
            where: {
              static_role_code: translation.static_role_code,
              language_code: translation.language_code,
            },
          },
          ["id"]
        );

        if (!existingTranslation) {
          await queryInterface.bulkInsert("static_role_translations", [translation]);
        }
      } catch (error) {
        console.error(
          `Error inserting role translation with code ${translation.static_role_code}:`,
          error
        );
      }
    }
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete("static_role_translations", null, {});
  },
};
