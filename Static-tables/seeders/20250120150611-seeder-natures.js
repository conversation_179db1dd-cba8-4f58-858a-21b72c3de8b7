"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
    async up(queryInterface) {
        const naturesList = [
            { id: 1, code: "MORA", label: "Moratorium", system_attribute: true },
            { id: 2, code: "PREL", label: "Pre-rent", system_attribute: true },
            { id: 3, code: "LOYE", label: "Lease payment", system_attribute: true },
            { id: 4, code: "PRES", label: "Service", system_attribute: true },
            { id: 5, code: "FREES", label: "Fees", system_attribute: true },
            { id: 6, code: "RECO", label: "Lease renewal", system_attribute: true },
            { id: 7, code: "GARA", label: "Guarantee", system_attribute: true },
            { id: 8, code: "ASSET", label: "Asset", system_attribute: true },
            { id: 9, code: "COMM", label: "Commission", system_attribute: true },
            { id: 10, code: "REVS", label: "Reversal", system_attribute: true },
            { id: 11, code: "ACQU", label: "Acquisition", system_attribute: true },
            { id: 12, code: "APPO", label: "Contribution", system_attribute: true },
            { id: 13, code: "FRNA", label: "Franchise", system_attribute: true },
            { id: 14, code: "CESS", label: "Transfer", system_attribute: true },
            { id: 15, code: "CDPK", label: "Overdraft", system_attribute: true },
            { id: 16, code: "INDM", label: "Compensation", system_attribute: true },
            { id: 17, code: "MILO", label: "Leasing", system_attribute: true },
            { id: 18, code: "NIMP", label: "Not imputed", system_attribute: true },
            { id: 19, code: "FFOU", label: "Commission", system_attribute: true },
        ];
        
        for (const nature of naturesList) {
            try{
                const existingNature = await queryInterface.rawSelect(
                    "natures",
                    {
                        where: { code: nature.code },
                    },
                    ["id"]
                );
                if (!existingNature) await queryInterface.bulkInsert("natures", [nature]);
                    
            } catch (error) {
                console.error(
                    `Error inserting nature with id=${nature.id} :`,
                    error
                );
            }
        }
    },

    async down(queryInterface) {
        await queryInterface.bulkDelete("natures", null, {});
    },
};
