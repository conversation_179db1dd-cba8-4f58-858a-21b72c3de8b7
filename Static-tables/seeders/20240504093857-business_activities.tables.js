/** @type {import('sequelize-cli').Migration} */
module.exports = {
    up: async (queryInterface, Sequelize) => {
        return queryInterface.bulkInsert("business_activities",
            [
                {"activity_code": "CBI", "business_reference": "ACT_966106020", "business_name": "<PERSON><PERSON>"},
                {"activity_code": "CBM", "business_reference": "ACT_966106020", "business_name": "<PERSON><PERSON>"},
                {"activity_code": "LOA", "business_reference": "ACT_966106020", "business_name": "<PERSON><PERSON>"},
                {"activity_code": "LLD", "business_reference": "ACT_733567539", "business_name": "BMW"},
                {"activity_code": "LOCFIN", "business_reference": "ACT_733567539", "business_name": "BMW"},
                {"activity_code": "LOCFIN", "business_reference": "ACT_458962137", "business_name": "GMC"},
                {"activity_code": "LOCFIN", "business_reference": "ACT_458962137", "business_name": "GMC"},
                {"activity_code": "PRET", "business_reference": "ACT_458962137", "business_name": "GMC"},
                {"activity_code": "PRET", "business_reference": "ACT_917828791", "business_name": "DELL"},
                {"activity_code": "Emprunt", "business_reference": "ACT_917828791", "business_name": "DELL"}
            ]
            , {});
    },

    down: async (queryInterface, Sequelize) => {
        return queryInterface.bulkDelete("business_activities", null, {});
    }
};
