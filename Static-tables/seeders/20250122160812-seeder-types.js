"use strict";

/** @type {import("sequelize-cli").Migration} */
module.exports = {
    async up(queryInterface) {
        const naturesTypesList = [
            { code: "MORAT", label: "Moratorium", system_attribute: true },
            { code: "PRELOY", label: "Interim Rent", system_attribute: true },
            { code: "RLOCINT", label: "Rent", system_attribute: true },
            { code: "RMAINT", label: "Maintenance", system_attribute: true },
            { code: "RPDTFIN", label: "Late Fees and Collection Costs", system_attribute: true },
            { code: "RINDMRE", label: "Termination Fees / Penalties", system_attribute: true },
            { code: "RRELOC", label: "Rent Renewal", system_attribute: true },
            { code: "RVTASSU", label: "Insurance Billing", system_attribute: true },
            { code: "RFRDOSS", label: "File Fees", system_attribute: true },
            { code: "RGDOS", label: "File Guarantee Retention", system_attribute: true },
            { code: "RGENC", label: "Current Guarantee Retention", system_attribute: true },
            { code: "FREMAN", label: "Financial Scheme Management Fees", system_attribute: true },
            { code: "FREJET", label: "Direct Debit Rejection Fees", system_attribute: true },
            { code: "RADRESS", label: "Address Change", system_attribute: true },
            { code: "RRIB", label: "RIB/IBAN Change", system_attribute: true },
            { code: "RMECH", label: "Due Date Modification", system_attribute: true },
            { code: "RMPLA", label: "Repayment Plan Modification", system_attribute: true },
            { code: "RTRTC", label: "Holder Transfer", system_attribute: true },
            { code: "ASSURC", label: "RC Insurance Billing", system_attribute: true },
            { code: "ASSUVIE", label: "Life Insurance Billing", system_attribute: true },
            { code: "FDENOM", label: "Company Name Change", system_attribute: true },
            { code: "FCOPIE", label: "Contract and Document Copy", system_attribute: true },
            { code: "FDUPF", label: "Invoice, Schedule, RVF Duplicate", system_attribute: true },
            { code: "FDUPC", label: "Certificate of Assignment Duplicate", system_attribute: true },
            { code: "FECH", label: "Due Date Billing", system_attribute: true },
            { code: "FSIN", label: "Claims Management Fees", system_attribute: true },
            { code: "FPAY", label: "Payment Method Change", system_attribute: true },
            { code: "FQUAN", label: "Quantième Change", system_attribute: true },
            { code: "FREAM", label: "Contract Restructuring", system_attribute: true },
            { code: "FTRAN", label: "Contract Transfer", system_attribute: true },
            { code: "FRECH", label: "Various Searches", system_attribute: true },
            { code: "FATT", label: "Various Certificates", system_attribute: true },
            { code: "FDEC", label: "Early Settlement Statement", system_attribute: true },
            { code: "FREPR", label: "Representation Fees on Unpaid", system_attribute: true },
            { code: "FRET", label: "Late Penalties", system_attribute: true },
            { code: "FRECO", label: "Collection File Fees", system_attribute: true },
            { code: "FINDEM", label: "Flat-rate Compensation", system_attribute: true },
            { code: "FMEP", label: "File Setup Fees", system_attribute: true },
            { code: "FGEST", label: "Annual Management Fees", system_attribute: true },
            { code: "FSEPA", label: "Operations without SEPA Mandates", system_attribute: true },
            { code: "FDIV", label: "SAV Management Fees", system_attribute: true },
            { code: "RELOC", label: "Relocation", system_attribute: true },
            { code: "FREMISE", label: "Restoration Fees", system_attribute: true },
            { code: "FDAUDI", label: "Audit Fees", system_attribute: true },
            { code: "DEPK", label: "Mileage Overrun", system_attribute: true },
            { code: "LMADIS", label: "Rent Made Available", system_attribute: true },
            { code: "DLOCINT", label: "Asset Purchase", system_attribute: true },
            { code: "DCOMAP", label: "Broker Commission", system_attribute: true },
            { code: "REVLOY", label: "Rent Reimbursement", system_attribute: true },
            { code: "REMBDG", label: "Security Deposit Refund", system_attribute: true },
            { code: "ABIE", label: "Main Asset", system_attribute: true },
            { code: "ACCN", label: "Accessory", system_attribute: true },
            { code: "OPTI", label: "Option", system_attribute: true },
            { code: "RMCL", label: "Supplier Discount", system_attribute: true },
            { code: "APPO", label: "Contribution", system_attribute: true },
            { code: "CGRI", label: "Registration Fees", system_attribute: true },
            { code: "FLIV", label: "Delivery Fees", system_attribute: true },
            { code: "MALU", label: "Malus", system_attribute: true },
            { code: "PACO", label: "Down Payment", system_attribute: true },
            { code: "RACO", label: "Down Payment Refund", system_attribute: true },
            { code: "CANT", label: "Early Assignment", system_attribute: true },
            { code: "CCTX", label: "Termination", system_attribute: true },
            { code: "CFIN", label: "Term Assignment", system_attribute: true },
            { code: "CSIN", label: "Claim Assignment", system_attribute: true },
            { code: "CTNL", label: "ITNL Assignment", system_attribute: true },
            { code: "INDR", label: "Early Termination Compensation", system_attribute: true },
            { code: "VR00", label: "Replacement Vehicle", system_attribute: true },
            { code: "BLOC", label: "Rental Base", system_attribute: true },
            { code: "EBLC", label: "Current Rental Base Difference", system_attribute: true },
            { code: "IRDU", label: "Remaining Interest Due", system_attribute: true },
            { code: "LRDU", label: "Remaining Due Installment", system_attribute: true },
            { code: "VIB", label: "Fiscal Asset", system_attribute: true },
            { code: "VIBF", label: "Financial Vib", system_attribute: true },
            { code: "NIMP", label: "Not Allocated", system_attribute: true },
            { code: "COMM", label: "Broker Commission", system_attribute: true },
        ];

        for (const natureType of naturesTypesList) {
            try {
                const existingNatureType = await queryInterface.rawSelect(
                    "types",
                    {
                        where: { code: natureType.code },
                    },
                    ["id"]
                );
                if (!existingNatureType) await queryInterface.bulkInsert("types", [natureType]);
                
            } catch (error) {
                console.error(
                    `Error inserting type with code=${natureType.code} :`,
                    error
                );
            }
        }
    },

    async down(queryInterface) {
        await queryInterface.bulkDelete("types", null, {});
    },
};
