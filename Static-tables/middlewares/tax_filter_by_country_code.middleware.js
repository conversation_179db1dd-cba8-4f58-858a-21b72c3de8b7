const { Op } = require("sequelize");

const taxMiddleWare = (req, res, next) => {
  try {
    const filters = req.query;
    const andConditions = [];
    const orConditions = [];
    const processedKeys = new Set();

    // Map query parameters to database columns
    const filterMapping = {
      country: "country",
      active: "active",
      created_at: "createdAt",
      updated_at: "updatedAt",
    };

    // Iterate over the query parameters and build AND/OR conditions
    Object.keys(filters).forEach((key) => {
      const isOrCondition = key.startsWith("OR_");
      const operatorKey = isOrCondition ? key.replace("OR_", "") : key;
      const underscoreIndex = operatorKey.indexOf("_");

      if (underscoreIndex > 0) {
        const operator = operatorKey.substring(0, underscoreIndex);
        const column = operatorKey.substring(underscoreIndex + 1);

        if (filterMapping[column]) {
          const dbColumn = filterMapping[column];
          const value = filters[key];
          const condition = {};

          // Map operators to Sequelize operators
          switch (operator) {
            case "PMin":
              condition[dbColumn] = { [Op.gte]: parseFloat(value) };
              break;
            case "PMax":
              condition[dbColumn] = { [Op.lte]: parseFloat(value) };
              break;
            case "PEqual":
              condition[dbColumn] = { [Op.eq]: value };
              break;
            case "PNotEqual":
              condition[dbColumn] = { [Op.ne]: value };
              break;
            case "PLike":
              condition[dbColumn] = { [Op.like]: `%${value}%` };
              break;
            case "PIn":
              condition[dbColumn] = { [Op.in]: value.split(",") };
              break;
            case "PNotIn":
              condition[dbColumn] = { [Op.notIn]: value.split(",") };
              break;
            case "PDateMin":
              condition[dbColumn] = { [Op.gte]: new Date(value) };
              break;
            case "PDateMax":
              condition[dbColumn] = { [Op.lte]: new Date(value) };
              break;
            case "PDateEqual":
              condition[dbColumn] = { [Op.eq]: new Date(value) };
              break;
            default:
              break;
          }

          // Add the condition to the appropriate array
          if (Object.keys(condition).length > 0) {
            if (isOrCondition) {
              orConditions.push(condition);
            } else {
              andConditions.push(condition);
            }
            // Mark this key as processed
            processedKeys.add(key);
          }
        }
      }
    });

    // Remove processed keys from req.query to prevent double processing
    processedKeys.forEach((key) => {
      delete req.query[key];
    });

    // Combine AND and OR conditions
    const where = {};
    if (andConditions.length > 0) {
      where[Op.and] = andConditions;
    }
    if (orConditions.length > 0) {
      where[Op.or] = orConditions;
    }

    // Only attach filter conditions if we have any
    if (
      Object.keys(where).length > 0 ||
      Object.getOwnPropertySymbols(where).length > 0
    ) {
      req.filterConditions = where;
      console.log("Filter conditions:", req.filterConditions);
    }

    next();
  } catch (error) {
    next(error);
  }
};

module.exports = taxMiddleWare;