/**
 * Markets Controller
 */
const { MarketModel } = require("../db");
const NotFoundError = require("../error/exception/NotFound");
const {sendSyncMessage} = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");
const { marketController } = require("../utils/controller-factory");


async function update(req, res, next) {
    try {
        const code = req.params.code;
        const market = await MarketModel.findOne({
            where: { code },
        });

        if (!market) {
            throw new NotFoundError("Market not found", "market");
        }

        await market.update(req.body);

        const updatedMarket = await MarketModel.findOne({
            where: { code },
        });
        await sendSyncMessage(
            OperationType.PUT,
            "Market",
            "markets",
            updatedMarket
        );
        res.send({
            data: updatedMarket
        });
    } catch (error) {
        next(error);
    }
}

async function remove(req, res, next) {
    try {
        const code = req.params.code;
        const market = await MarketModel.findOne({
            where: { code },
        });

        if (!market) {
            throw new NotFoundError("Market not found", "market");
        }

        await market.destroy();

        await sendSyncMessage(
            OperationType.DELETE,
            "Market",
            "markets",
            market
        );
        res.send({
            data: {
                message: `Market with code ${code} has been deleted successfully`,
            },
        });
    } catch (error) {
        next(error);
    }
}

module.exports = {
    search: marketController.search,
    getOne: marketController.getOne,
    create: marketController.create,
    update,
    remove,
};
