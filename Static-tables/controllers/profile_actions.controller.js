const { Op } = require("sequelize");
const config = require("../config/app-config");
const { ProfileActionModel, ProfileActionTranslationsModel } = require("../db");

const renameKeys = (obj, keyMap) => {
  for (const [oldKey, newKey] of Object.entries(keyMap)) {
    if (obj.hasOwnProperty(oldKey)) {
      obj[newKey] = obj[oldKey];
      delete obj[oldKey];
    }
  }
  return obj;
};

const renameValues = (obj, valueMap) => {
  for (const [key, value] of Object.entries(valueMap)) {
      if (Object.values(obj).indexOf(key) > -1) {
          obj['sortBy'] = value;
      }
  }
  return obj;
};

async function search(req, res, next) {
  try {
    let whereCondition = {};
    const limit = req.query.limit ? req.query.limit : config.limit;
    const offset = req.query.offset
      ? req.query.offset 
      : config.offset ;
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;
    const language = req.query.language
      ? req.query.language.toUpperCase()
      : config.defaultReturnedLanguage;

    Object.keys(req.query).forEach((key) => {
      if (
        !["offset", "limit", "sort_by", "order_by", "language"].includes(key)
      ) {
        whereCondition[key] = req.query[key];
      }
    });

    let actions, total_count;

    const keyMap = {
      code: "profile_action_code",
    };

    [actions, total_count] = await Promise.all(
      language === config.language
        ? [
            (
              await ProfileActionModel.findAll({
                order: [[sortBy, orderBy]],
                limit,
                offset,
                where: whereCondition,
              })
            ).map((item) => {
              return {
                id: item.id,
                code: item.code,
                description: item.description,
              };
            }),
            ProfileActionModel.count({ where: whereCondition }),
          ]
        : [
            (
              await ProfileActionTranslationsModel.findAll({
                order: [[renameValues({sortBy}, keyMap).sortBy, orderBy]],
                limit,
                offset,
                where: renameKeys(
                  { ...whereCondition, language_code: language.toUpperCase() },
                  keyMap
                ),
                include: {
                  model: ProfileActionModel,
                  attributes: ["id", "description"],
                  on: {
                    "$profile_action.code$": {
                      [Op.col]: "profile_action_translations.profile_action_code",
                    },
                  },
                },
                raw: true,
              })
            ).map((item) => {
              return {
                id: item["profile_action.id"],
                code: item.profile_action_code,
                description: item.description,
              };
            }),
            ProfileActionTranslationsModel.count({
              where: renameKeys(
                { ...whereCondition, language_code: language.toUpperCase() },
                keyMap
              ),
            }),
          ]
    );

    res.send({ data: actions, total_count });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  search,
};
