const {
  ProfileModel,
  ProfileActionModel,
  ProfileActionMappingModel,
  ProfileTranslationsModel,
} = require("../db");
const { Op } = require("sequelize");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");

async function search(req, res, next) {
  try {
    let whereCondition = {};
    let translationWhereCondition = {};
    const limit = req.query.limit ? req.query.limit : config.limit;
    const offset = req.query.offset
      ? req.query.offset 
      : config.offset ;
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;
    const language = req.query.language
      ? req.query.language.toUpperCase()
      : config.defaultReturnedLanguage;

    Object.keys(req.query).forEach((key) => {
      if (
        !["offset", "limit", "sort_by", "order_by", "language"].includes(key)
      ) {
        if(language === config.language || !["label", "description"].includes(key)){
          whereCondition[key] = req.query[key];
        }else {
          translationWhereCondition[key] = req.query[key];
        }
      }
    });

    let profiles, total_count;

    [profiles, total_count] = await Promise.all(
      language === config.language
        ? [
            (
              await ProfileModel.findAll({
                order: [[sortBy, orderBy]],
                limit,
                offset,
                where: whereCondition,
                include: {
                  model: ProfileActionMappingModel,
                  include: {
                    model: ProfileActionModel,
                    attributes: ["id", "code", "description"],
                    on: {
                      "$profile_action_mappings->profile_action.code$": {
                        [Op.col]: "profile_action_mappings.profile_action_code",
                      },
                    },
                  },
                },
              })
            ).map((item) => {
              return {
                id: item.id,
                code: item.code,
                label: item.label || "",
                description: item.description || "",
                active: item.active,
                actions: item.profile_action_mappings?.map(
                  (item) => item.profile_action
                ),
                system_attribute: item.system_attribute,
              };
            }),
            ProfileModel.count({ where: whereCondition }),
          ]
        : [
            (
              await ProfileModel.findAll({
                order: ["label", "description"].includes(sortBy)
                  ? [[ProfileTranslationsModel, sortBy, orderBy]]
                  : [[sortBy, orderBy]],
                limit,
                offset,
                where: whereCondition,
                include: [
                  {
                    model: ProfileTranslationsModel,
                    attributes: ["label", "description"],
                    on: {
                      "$profile.code$": {
                        [Op.col]: "profile_translations.profile_code",
                      },
                    },
                  },
                  {
                    model: ProfileActionMappingModel,
                    include: {
                      model: ProfileActionModel,
                      attributes: ["id", "code", "description"],
                      on: {
                        "$profile_action_mappings->profile_action.code$": {
                          [Op.col]:
                            "profile_action_mappings.profile_action_code",
                        },
                      },
                    },
                  },
                ],
              })
            ).map((item) => {
              return {
                id: item.id,
                code: item.code,
                label: item.profile_translations[0]?.label || "",
                description: item.profile_translations[0]?.description || "",
                active: item.active,
                actions: item.profile_action_mappings?.map(
                  (item) => item.profile_action
                ),
                system_attribute: item.system_attribute,
              };
            }),
            ProfileModel.count({ where: whereCondition }),
          ]
    );

    res.send({ data: profiles, total_count });
  } catch (error) {
    next(error);
  }
}

async function create(req, res, next) {
  try {
    const {
      code,
      label,
      description,
      active,
      language = config.defaultReturnedLanguage,
    } = req.body;
    const isDefaultLanguage = language.toUpperCase() === config.language;

    const existingProfile = await ProfileModel.findOne({
      where: {
        code,
      },
    });

    if (existingProfile)
      throw new ConflictError(
        "Profile with matching code already exists",
        "Profiles"
      );

    const new_profile = await ProfileModel.create({
      code,
      label: isDefaultLanguage ? label : "",
      description: isDefaultLanguage ? description : "",
      active
    });

    if (new_profile) {
      await ProfileTranslationsModel.create({
        profile_code: new_profile.code,
        label: !isDefaultLanguage ? label : "",
        description: !isDefaultLanguage ? description : "",
        language_code: !isDefaultLanguage ? language.toUpperCase() : "",
      })
    }

    res.status(201).send({
      data: {
        id: new_profile.id,
        ...req.body
      },
    });
  } catch (error) {
    next(error);
  }
}

async function update(req, res, next) {
  try {
    const id = req.params.id;
    const { code } = req.body;

    const oldProfile = await ProfileModel.findOne({
      where: {
        id,
      },
    });

    if (!oldProfile) throw new NotFoundError("Profile not found", "Profiles");

    const existingProfile = await ProfileModel.findOne({
      where: {
        code,
      },
    });

    if (existingProfile && existingProfile.id !== oldProfile.id)
      throw new ConflictError(
        "Profile with matching code already exists",
        "Profile"
      );

    if (oldProfile.code !== code && oldProfile.system_attribute)
      throw new ConflictError("Profile code cannot be modified", "Profile");

    await oldProfile.update(req.body);

    res.send({
      data: { id, ...req.body },
    });
  } catch (error) {
    next(error);
  }
}

async function remove(req, res, next) {
  try {
    const id = req.params.id;

    const oldProfile = await ProfileModel.findOne({
      where: {
        id,
      },
    });

    if (!oldProfile) throw new NotFoundError("Profile not found", "Profile");

    if (oldProfile.system_attribute)
      throw new ConflictError("Profile cannot be removed", "Profile");

    await ProfileModel.destroy({
      where: {
        id,
      },
    });

    res.send({
      data: {
        message: `Resource with ID ${id} has been deleted successfully`,
      },
    });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  search,
  create,
  update,
  remove,
};
