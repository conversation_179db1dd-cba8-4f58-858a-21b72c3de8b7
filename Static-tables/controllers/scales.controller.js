const {
    ScaleModel,
    ScaleServiceModel,
    ScaleFinancialProductModel,
    ScaleCommercialProductModel,
    CommercialProductModel,
    ApplicationCriteriaModel,
    ApplicationCriteriaActorModel,
    FinancialElementVehicleVehicleMappingModel,
    FinancialElementMaterialModel,
    FinancialElementMaterialEquipmentMappingModel,
    FinancialElementVehicleModel,
    ThirdPartiesModel,
    ProductsModel,
    CurrencyModel,
    MarketModel,
    CountriesModel,
    EquipmentModel,
    SingleAutoModel,
    NapModel,
    AutoCatalogModel,
    ServiceModel,
    sequelize
} = require("../db");
const {Sequelize, Op} = require("sequelize");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const {v4: uuidv4} = require("uuid");
const {sendSyncMessage} = require('../producer/Producer');
const OperationType = require('../models/Stream/operationType');

async function createScale(req, res, next) {
    const payload = req.body;
    console.debug("Received payload:", payload); // Debug: Log the incoming payload

    // Validate date range
    if (payload.start_date && payload.end_date) {
        const startDate = new Date(payload.start_date);
        const endDate = new Date(payload.end_date);

        if (endDate < startDate) {
            return res.status(400).json({
                message: 'Validation error: End date must be equal to or greater than start date'
            });
        }
    }

    const transaction = await sequelize.transaction();
    console.debug("Transaction started"); // Debug: Log transaction start

    try {
        // Generate the reference
        const newScaleReference = uuidv4();
        // Create the scale
        const scaleData = {
            reference: newScaleReference,
            code: `SCA_${newScaleReference.split('-')[0]}`,
            label: payload.label,
            description: payload.description,
            condition: payload.condition,
            start_date: payload.start_date,
            end_date: payload.end_date,
            country_code: payload.country_code,
            market_code: payload.market_code,
            currency_code: payload.currency_code,
            maximum_interest_rate: payload.maximum_interest_rate,
            minimum_interest_rate: payload.minimum_interest_rate,
            nominal_interest_rate: payload.nominal_interest_rate,
            maximum_security_deposit: payload.maximum_security_deposit,
            minimum_security_deposit: payload.minimum_security_deposit,
            maximum_personal_contribution: payload.maximum_personal_contribution,
            minimum_personal_contribution: payload.minimum_personal_contribution,
            maximum_residual_value: payload.maximum_residual_value,
            minimum_residual_value: payload.minimum_residual_value,
            maximum_financing_duration: payload.maximum_financing_duration,
            minimum_financing_duration: payload.minimum_financing_duration,
            maximum_eligible_amount: payload.maximum_eligible_amount,
            minimum_eligible_amount: payload.minimum_eligible_amount,
            has_security_deposit: payload.has_security_deposit,
            has_grace_period: payload.has_grace_period,
            has_interest_payment: payload.has_interest_payment,
            has_spread_calculation: payload.has_spread_calculation,
            has_personal_contribution: payload.has_personal_contribution,
            grace_period_duration: payload.grace_period_duration,
            with_interest_payment: payload.with_interest_payment,
            financial_scoring: payload.financial_scoring,
            asset_usage: payload.asset_usage,
            rate_period: payload.rate_period,
            nature: payload.nature,
            status: "INI",
        };
        console.debug("Scale data to be created:", scaleData); // Debug: Log scale data

        var scale = await ScaleModel.create(scaleData, {transaction});
        console.debug("Scale created:", scale); // Debug: Log created scale

        var scaleStreamDto = {
            ...scale.get({plain: true}),
            designation: null,
            minimum_mileage: null,
            maximum_mileage: null,
        };

        // Handle different creation flows based on the nature field
        if (payload.nature === "MT") {
            console.debug("Handling material nature flow"); // Debug: Log nature flow
            const financialElementMaterial = await FinancialElementMaterialModel.create({
                scale_code: scale.code,
                designation: payload.material_designation || null,
            }, {transaction});
            scaleStreamDto.designation = payload.material_designation;
            await sendSyncMessage(OperationType.POST, 'Scale', 'Static-tables', scaleStreamDto);

            if (payload.assets && payload.assets.length > 0) {
                console.debug("Assigning equipment to material assets"); // Debug: Log asset assignment
                await Promise.all(
                    payload.assets.map(async (equipmentCode) => {
                        const newFinancialElementMaterialEquipmentMapping = await FinancialElementMaterialEquipmentMappingModel.create({
                            reference: uuidv4(),
                            scale_code: financialElementMaterial.scale_code,
                            equipment_code: equipmentCode,
                        }, {transaction});
                        console.debug("Equipment mapping created:", newFinancialElementMaterialEquipmentMapping); // Debug: Log equipment mapping
                        await sendSyncMessage(OperationType.POST, 'FinancialElementMaterialEquipmentMapping',
                            'Static-tables', newFinancialElementMaterialEquipmentMapping.get({plain: true}));
                    })
                );
            }
        } else if (payload.nature === "VH") {
            console.debug("Handling vehicle nature flow"); // Debug: Log nature flow
            const financialElementVehicle = await FinancialElementVehicleModel.create({
                scale_code: scale.code,
                minimum_mileage: payload.minimum_mileage || null,
                maximum_mileage: payload.maximum_mileage || null,
            }, {transaction});

            console.debug("Financial element vehicle created:", financialElementVehicle); // Debug: Log vehicle creation
            scaleStreamDto.minimum_mileage = payload.minimum_mileage;
            scaleStreamDto.maximum_mileage = payload.maximum_mileage;
            await sendSyncMessage(OperationType.POST, 'Scale', 'Static-tables', scaleStreamDto);

            if (payload.assets && payload.assets.length > 0) {
                console.debug("Assigning vehicles to vehicle assets"); // Debug: Log asset assignment
                await Promise.all(
                    payload.assets.map(async (vehicleReference) => {
                        const newFinancialElementVehicleVehicleMapping = await FinancialElementVehicleVehicleMappingModel.create({
                            reference: uuidv4(),
                            scale_code: financialElementVehicle.scale_code,
                            vehicle_reference: vehicleReference,
                        }, {transaction});
                        console.debug("Vehicle mapping created:", newFinancialElementVehicleVehicleMapping); // Debug: Log vehicle mapping
                        await sendSyncMessage(OperationType.POST, 'FinancialElementVehicleVehicleMapping',
                            'Static-tables', newFinancialElementVehicleVehicleMapping.get({plain: true}));
                    })
                );
            }
        }

        // Create application_criteria first
        const applicationCriteria = await ApplicationCriteriaModel.create({
            reference: uuidv4(),
            channel_of_acquisition: payload.channel_of_acquisition,
            currency_code: payload.currency_code,
            customer_type: payload.customer_type,
            financial_scoring: payload.financial_scoring,
            scale_code: scale.code,
        }, {transaction});

        console.debug("Application criteria created:", applicationCriteria); // Debug: Log application criteria

        await sendSyncMessage(OperationType.POST, 'ApplicationCriteria', 'Static-tables', applicationCriteria);

        // Create application_criteria_actors
        if (payload.businesses && payload.businesses.length > 0) {
            console.debug("Creating application criteria actors"); // Debug: Log actor creation
            const applicationCriteriaActors = await ApplicationCriteriaActorModel.bulkCreate(
                payload.businesses.map(actor_reference => ({
                    reference: uuidv4(),
                    application_criteria_reference: applicationCriteria.reference,
                    actor_reference: actor_reference
                })), {transaction}
            );

            console.debug("Application criteria actors created:", applicationCriteriaActors); // Debug: Log actors
            const messagePromises = applicationCriteriaActors.map(actor =>
                sendSyncMessage(OperationType.POST, 'ApplicationCriteriaActor', 'Static-tables', actor.get({plain: true}))
            );

            await Promise.all(messagePromises);
        }

        scale.application_criteria_reference = applicationCriteria.reference;
        await ScaleModel.update(
            {application_criteria_reference: applicationCriteria.reference},
            {where: {code: scale.code}, transaction}
        );

        console.debug("Scale updated with application criteria reference"); // Debug: Log scale update

        await sendSyncMessage(OperationType.PUT, 'Scale', 'Static-tables', scale);

        // Create related financial products
        if (payload.products && payload.products.length > 0) {
            console.debug("Creating related financial products"); // Debug: Log product creation
            await Promise.all(
                payload.products.map(async (productCode) => {
                    const newScaleFinancialProduct = await ScaleFinancialProductModel.create(
                        {
                            reference: uuidv4(),
                            scale_code: scale.code,
                            product_code: productCode,
                        },
                        {transaction}
                    );
                    console.debug("Financial product created:", newScaleFinancialProduct); // Debug: Log product
                    await sendSyncMessage(OperationType.POST, 'ScaleFinancialProduct', 'Static-tables',
                        newScaleFinancialProduct.get({plain: true}));
                })
            );
        }

        if (payload.third_parties && payload.third_parties.length > 0) {
            await Promise.all(
                payload.third_parties.map(async (thirdParty) => {
                    const newThirdPartiesModel = await ThirdPartiesModel.create({
                        reference: uuidv4(),
                        actor_reference: thirdParty.actor_reference,
                        role_code: thirdParty.role_code,
                        scale_code: scale.code,
                    }, {transaction});
                    await sendSyncMessage(OperationType.POST, 'ThirdParties',
                        'Static-tables', newThirdPartiesModel.get({plain: true}));
                })
            );
        }

        // Create related scale services
        if (payload.scale_service_list && payload.scale_service_list.length > 0) {
            await Promise.all(payload.scale_service_list.map(async (scale_service) => {
                const newScaleServiceModel = await ScaleServiceModel.create({
                    reference: uuidv4(),
                    scale_code: scale.code,
                     service_reference: scale_service,
                }, {transaction});
                await sendSyncMessage(OperationType.POST, 'ScaleService', 'Static-tables', newScaleServiceModel.get({plain: true}));
            }));
        }
        // console.log("createScaleLineType", ScaleLineTypeModel)
        // Commit the transaction
        await transaction.commit();
        return res.status(201).json({message: 'Scale created successfully', scale});
    } catch (error) {
        // Rollback the transaction in case of error
        await transaction.rollback();
        return res.status(500).json({message: 'Error creating scale', error: error.message});
    }
}


async function search(req, res, next) {
    try {
        const limit = req.query.limit ? req.query.limit : config.limit;
        const offset = req.query.offset
            ? req.query.offset * limit
            : config.offset * limit;
        const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
        const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;

        const where = req.filterConditions || {};

        Object.keys(req.query).forEach((key) => {
            if (!["offset", "limit", "sort_by", "order_by"].includes(key)) {
                where[key] = req.query[key];
            }
        });

        const {rows } = await ScaleModel.findAndCountAll({
            order: [[sortBy, orderBy]],
            offset,
            limit,
            where,
            include: [
                {
                    model: ProductsModel,
                    through: { attributes: [] },
                    attributes: { exclude: ["createdAt", "updatedAt"] },
                },
                {
                    model: FinancialElementVehicleModel,
                    attributes: { exclude: ["createdAt", "updatedAt"] },
                }
            ],
        });


        const total_count = rows.length;

        res.send({
            data: rows.map((scale) => ({
                ...scale.toJSON(),
                products: scale.products || [],
            })),
            total_count,
        });
    } catch (error) {
        next(error);
    }
}


async function getScaleByCodeSimple(req, res, next) {
    try {
        const code = req.params.code;
        const nap = await ScaleModel.findOne({
            where: {code}
        });

        if (!nap) {
            throw new NotFoundError("Scale not found", "Scale");
        }

        return res.send({data: nap});
    } catch (error) {
        next(error);
    }
}

async function updateStatus(req, res, next) {
    try {
        const code = req.params.code;
        const {status} = req.body;

        const existingScale = await ScaleModel.findOne({
            where: {
                code,
            },
        });

        if (!existingScale) throw new NotFoundError("Scale not found", "Scale");

        await existingScale.update({
            status,
        });

        const scale = existingScale;
        if (scale.nature === 'MT') {
            const financialElementMaterial = await FinancialElementMaterialModel.findOne({
                where: {
                    scale_code: code,
                },
            });
            await sendSyncMessage(OperationType.PUT, 'FinancialElementMaterial', 'Static-tables', {
                ...scale.get({plain: true}),
                ...financialElementMaterial.get({plain: true}),
                status
            });
        } else if (scale.nature === 'VH') {
            const financialElementVehicle = await FinancialElementVehicleModel.findOne({
                where: {
                    scale_code: code,
                },
            });
            await sendSyncMessage(OperationType.PUT, 'FinancialElementVehicle', 'Static-tables', {
                ...scale.get({plain: true}),
                ...financialElementVehicle.get({plain: true}),
                status
            });
        }

        res.send({data: {...existingScale, status}});
    } catch (error) {
        next(error);
    }
}

async function removeScaleEquipment(req, res, next) {
    try {
        const code = req.params.code;
        const equipmentCode = req.params.equipmentCode;

        const scale = await ScaleModel.findOne({
            where: {
                code,
            },
        });

        if (!scale) throw new NotFoundError("Scale not found", "Scale");


        let equipment;
        if (scale.nature === 'MT') {
            equipment = await FinancialElementMaterialEquipmentMappingModel.findOne({
                where: {
                    scale_code: code,
                    equipment_code: equipmentCode
                }
            });

            if (!equipment) throw new NotFoundError("Equipment not found", "Scale");

             // Count Equipment associated with this scale
        const equipmentCount = await FinancialElementMaterialEquipmentMappingModel.count({
            where: {
            scale_code: code
            }
        });

        if (equipmentCount <= 1) throw new ConflictError("Cannot remove the last Equipment relationship. A scale must have at least one equipment.", "Scale");


            await FinancialElementMaterialEquipmentMappingModel.destroy({
                where: {
                    scale_code: code,
                    equipment_code: equipmentCode
                }
            });
            await sendSyncMessage(OperationType.DELETE, 'FinancialElementMaterialEquipmentMapping',
                'Static-tables', equipment.get({ plain: true }));
        } else {
            equipment = await FinancialElementVehicleVehicleMappingModel.findOne({
                where: {
                    scale_code: code,
                    vehicle_reference: equipmentCode
                }
            });

            if (!equipment) throw new NotFoundError("Equipment not found", "Scale");

                  // Count Vehicle associated with this scale
        const VehicleCount = await FinancialElementVehicleVehicleMappingModel.count({
            where: {
            scale_code: code
            }
        });

        if (VehicleCount <= 1) throw new ConflictError("Cannot remove the last Vehicle relationship. A scale must have at least one Vehicle.", "Scale");



            await FinancialElementVehicleVehicleMappingModel.destroy({
                where: {
                    scale_code: code,
                    vehicle_reference: equipmentCode
                }
            });
            await sendSyncMessage(OperationType.POST, 'FinancialElementVehicleVehicleMapping',
                'Static-tables', equipment.get({ plain: true }));
        }

        res.send({
            data: {
                message: `Relation with CODE ${equipmentCode} has been deleted successfully`,
            },
        });
    } catch (error) {
        next(error);
    }
}

async function remove(req, res, next) {
    try {
        const code = req.params.code;

        const scale = await ScaleModel.findOne({
            where: {
                code,
            },
        });

        if (!scale) throw new NotFoundError("Scale not found", "Scale");

        await ScaleModel.destroy({
            where: {
                code,
            },
        });

        res.send({
            data: {
                message: `Resource with CODE ${code} has been deleted successfully`,
            },
        });
    } catch (error) {
        next(error);
    }
}

async function getOne(req, res, next) {
    try {
        const code = req.params.code;

        const scale = await ScaleModel.findOne({
            where: {code},
            include: [
                {
                    model: CurrencyModel
                },
                {
                    model: MarketModel
                },
                {
                    model: CountriesModel
                },
                {
                    model: ApplicationCriteriaModel,
                    as: "scale_application_criteria"
                },
                {
                    model: ScaleServiceModel,
                    attributes: { exclude: ["updatedAt", "createdAt"] },
                    include: [{
                        model: ServiceModel,
                        attributes: {
                            exclude: ["createdAt", "updatedAt"]
                        }
                    }]
                },
                {
                    model: FinancialElementVehicleModel,
                    attributes: { exclude: ["createdAt", "updatedAt"] },
                }
            ]
        });

        if (!scale) {
            throw new NotFoundError("Scale not found", "Scale");
        }

        const assets = scale.nature === 'MT' ?
        (
          await EquipmentModel.findAll({
            attributes: { exclude: ["updatedAt", "createdAt"] },
            include: [{
              model: FinancialElementMaterialModel,
             attributes: [],
              where: {scale_code: code},
              required: true,
              through: {attributes: []}
            },
            {
              as: "category",
              model: NapModel,
              attributes: { exclude: ["updatedAt", "createdAt"] },
            },]
          })

        ): (
          await SingleAutoModel.findAll({
            attributes: { exclude: ["updatedAt", "createdAt"] },
            include:[ {
              model: AutoCatalogModel,
              attributes: { exclude: ["updatedAt", "createdAt"] },
            },
            {
              model: FinancialElementVehicleModel,
             attributes: [],
              where: {scale_code: code},
              required: true,
              through: {attributes: []}
            }]
          })
        )

      const products = await ProductsModel.findAll({
          attributes: { exclude: ["updatedAt", "createdAt"] },
          include:
              {
                  model: ScaleModel,
                  attributes: [],
                  where: { code: code  },
                  through: {attributes: []},

              }
      });

      // Fetch all actors related to the scale using application_criteria_reference
      const businesses = await ApplicationCriteriaActorModel.findAll({
          where: { application_criteria_reference: scale.application_criteria_reference },
          attributes: { exclude: ["updatedAt", "createdAt"] }
      });
      const third_parties = await ThirdPartiesModel.findAll({
          where:{scale_code:scale.code},
          attributes: { exclude: ["updatedAt", "createdAt"] }
      });

      // Format the scale services with service details
      const formattedScaleServices = scale.scale_services.map(scaleService => {
        const serviceData = scaleService.service ? scaleService.service.get({ plain: true }) : null;

        return {
            id: scaleService.id,
            reference: scaleService.reference,
            scale_code: scaleService.scale_code,
            service_reference: scaleService.service_reference,
            service: serviceData
        };
    });

      res.send({
        data: {
          ...scale.toJSON(),
          products: products.map((e) => e.toJSON()),
          assets: assets.map(e => e.toJSON()),
          businesses: businesses.map((e) => e.toJSON()),
          third_parties: third_parties.map((e) => e.toJSON()),
          scale_services: formattedScaleServices,
        }
    });
    } catch (err) {
        next(err);
    }
}
async function removeThirdParty(req, res, next) {
    try {
        const code = req.params.code;
        const actor_reference = req.params.actor_reference;

        const scale = await ScaleModel.findOne({
            where: {
                code,
            },
        });

        if (!scale) throw new NotFoundError("Scale not found", "Scale");

        const thirdParty = await ThirdPartiesModel.findOne({
            where: {
                scale_code: code,
                actor_reference: actor_reference
            }
        });

        // Count third parties associated with this scale
        const thirdPartyCount = await ThirdPartiesModel.count({
            where: {
            scale_code: code
            }
        });

        if (thirdPartyCount <= 1) throw new ConflictError("Cannot remove the last third party relationship. A scale must have at least one third party.", "Scale");


        await ThirdPartiesModel.destroy({
            where: {
            scale_code: code,
            actor_reference: actor_reference
            },
        });
        await sendSyncMessage(OperationType.DELETE, 'ThirdParties',
            'Static-tables', thirdParty.get({ plain: true }));
        res.send({
            data: {
                message: `Relation with CODE ${actor_reference} has been deleted successfully`,
            },
        });
    } catch (error) {
        next(error);
    }
}

async function updateScale(req, res, next) {
    const code = req.params.code;
    const payload = req.body;
    console.debug("Received payload for update:", payload); // Debug: Log the incoming payload

    // Find the existing scale first for validation
    const existingScale = await ScaleModel.findOne({ where: { code } });
    if (!existingScale) throw new NotFoundError("Scale not found", "Scale");

    // Determine the start and end dates for validation
    const startDate = payload.start_date !== undefined ?
        (payload.start_date === "" ? null : payload.start_date) :
        existingScale.start_date;

    const endDate = payload.end_date !== undefined ?
        (payload.end_date === "" ? null : payload.end_date) :
        existingScale.end_date;

    // Validate date range if both dates are provided
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);

        if (end < start) {
            return res.status(400).json({
                message: 'Validation error: End date must be equal to or greater than start date'
            });
        }
    }

    const transaction = await sequelize.transaction();
    console.debug("Transaction started for update"); // Debug: Log transaction start

    try {
        // Find the existing scale
        const scale = await ScaleModel.findOne({ where: { code } });
        if (!scale) throw new NotFoundError("Scale not found", "Scale");

        // Get current or updated values for validation - handle empty strings and allow them

        const ratePeriod = payload.rate_period || scale.rate_period;
        const maxDuration = payload.maximum_financing_duration !== undefined ? payload.maximum_financing_duration : scale.maximum_financing_duration;


        // Update scale fields
        const updatedScaleData = {
            code: payload.code || `SCA_${scale.reference.split('-')[0]}`,
            reference: scale.reference,
            label: payload.label || scale.label,
            description: payload.description || scale.description,
            condition: payload.condition || scale.condition,
            start_date: payload.start_date !== undefined ? (payload.start_date === "" ? null : payload.start_date) : scale.start_date,
            end_date: payload.end_date !== undefined ? (payload.end_date === "" ? null : payload.end_date) : scale.end_date,
            country_code: payload.country_code || scale.country_code,
            market_code: payload.market_code || scale.market_code,
            currency_code: payload.currency_code || scale.currency_code,
            maximum_interest_rate: payload.maximum_interest_rate !== undefined ? payload.maximum_interest_rate : scale.maximum_interest_rate,
            minimum_interest_rate: payload.minimum_interest_rate !== undefined ? payload.minimum_interest_rate : scale.minimum_interest_rate,
            nominal_interest_rate: payload.nominal_interest_rate !== undefined ? payload.nominal_interest_rate : scale.nominal_interest_rate,
            maximum_security_deposit: payload.maximum_security_deposit !== undefined ? payload.maximum_security_deposit : scale.maximum_security_deposit,
            minimum_security_deposit: payload.minimum_security_deposit !== undefined ? payload.minimum_security_deposit : scale.minimum_security_deposit,
            maximum_personal_contribution: payload.maximum_personal_contribution !== undefined ? payload.maximum_personal_contribution : scale.maximum_personal_contribution,
            minimum_personal_contribution: payload.minimum_personal_contribution !== undefined ? payload.minimum_personal_contribution : scale.minimum_personal_contribution,
            maximum_residual_value: payload.maximum_residual_value !== undefined ? payload.maximum_residual_value : scale.maximum_residual_value,
            minimum_residual_value: payload.minimum_residual_value !== undefined ? payload.minimum_residual_value : scale.minimum_residual_value,
            maximum_financing_duration: maxDuration,
            minimum_financing_duration: payload.minimum_financing_duration !== undefined ? payload.minimum_financing_duration : scale.minimum_financing_duration,
            maximum_eligible_amount: payload.maximum_eligible_amount !== undefined ? payload.maximum_eligible_amount : scale.maximum_eligible_amount,
            minimum_eligible_amount: payload.minimum_eligible_amount !== undefined ? payload.minimum_eligible_amount : scale.minimum_eligible_amount,
            has_security_deposit: payload.has_security_deposit ?? scale.has_security_deposit,
            has_grace_period: payload.has_grace_period ?? scale.has_grace_period,
            has_interest_payment: payload.has_interest_payment ?? scale.has_interest_payment,
            has_spread_calculation: payload.has_spread_calculation ?? scale.has_spread_calculation,
            has_personal_contribution: payload.has_personal_contribution ?? scale.has_personal_contribution,
            grace_period_duration: payload.grace_period_duration !== undefined ? payload.grace_period_duration : scale.grace_period_duration,
            with_interest_payment: payload.with_interest_payment ?? scale.with_interest_payment,
            financial_scoring: payload.financial_scoring || scale.financial_scoring,
            asset_usage: payload.asset_usage || scale.asset_usage,
            rate_period: ratePeriod,
            nature: payload.nature || scale.nature,
        };

        await ScaleModel.update(updatedScaleData, { where: { code }, transaction });
        console.debug("Scale updated:", updatedScaleData);

        // Update related entities based on nature
        if (payload.nature === "MT") {
            const financialElementMaterial = await FinancialElementMaterialModel.findOne({ where: { scale_code: code } });
            if (financialElementMaterial) {
                await financialElementMaterial.update(
                    { designation: payload.material_designation || financialElementMaterial.designation },
                    { transaction }
                );
                console.debug("Material designation updated:", financialElementMaterial); // Debug: Log material update
            }
            // For material assets, compare with existing mappings and update
            if (payload.assets) {
                console.debug("Processing material assets for update"); // Debug: Log asset processing

                // Get existing equipment mappings
                const existingMappings = await FinancialElementMaterialEquipmentMappingModel.findAll({
                    where: { scale_code: code },
                    transaction
                });
                const existingEquipmentCodes = existingMappings.map(mapping => mapping.equipment_code);
                console.debug("Existing equipment codes:", existingEquipmentCodes);

                // Identify assets to add (in payload but not in database)
                const assetsToAdd = payload.assets.filter(code => !existingEquipmentCodes.includes(code));
                console.debug("Assets to add:", assetsToAdd);

                // Identify assets to remove (in database but not in payload)
                const assetsToRemove = existingEquipmentCodes.filter(code => !payload.assets.includes(code));
                console.debug("Assets to remove:", assetsToRemove);

                // Add new mappings
                if (assetsToAdd.length > 0) {
                    await Promise.all(
                        assetsToAdd.map(async (equipmentCode) => {
                            const newMapping = await FinancialElementMaterialEquipmentMappingModel.create({
                                reference: uuidv4(),
                                scale_code: code,
                                equipment_code: equipmentCode,
                            }, {transaction});
                            console.debug("New equipment mapping created:", newMapping);
                            await sendSyncMessage(OperationType.POST, 'FinancialElementMaterialEquipmentMapping',
                                'Static-tables', newMapping.get({plain: true}));
                        })
                    );
                }

                // Remove old mappings
                if (assetsToRemove.length > 0) {
                    for (const equipmentCode of assetsToRemove) {
                        const mappingToRemove = existingMappings.find(m => m.equipment_code === equipmentCode);
                        await FinancialElementMaterialEquipmentMappingModel.destroy({
                            where: {
                                scale_code: code,
                                equipment_code: equipmentCode
                            },
                            transaction
                        });
                        console.debug("Equipment mapping removed:", mappingToRemove);
                        await sendSyncMessage(OperationType.DELETE, 'FinancialElementMaterialEquipmentMapping',
                            'Static-tables', mappingToRemove.get({plain: true}));
                    }
                }
            }
        } else if (payload.nature === "VH") {
            const financialElementVehicle = await FinancialElementVehicleModel.findOne({ where: { scale_code: code } });
            if (financialElementVehicle) {
                await financialElementVehicle.update(
                    {
                        minimum_mileage: payload.minimum_mileage !== undefined ? payload.minimum_mileage : financialElementVehicle.minimum_mileage,
                        maximum_mileage: payload.maximum_mileage !== undefined ? payload.maximum_mileage : financialElementVehicle.maximum_mileage,
                    },
                    { transaction }
                );
                console.debug("Vehicle mileage updated:", financialElementVehicle); // Debug: Log vehicle update
            }
            // For vehicle assets, compare with existing mappings and update
            if (payload.assets) {
                console.debug("Processing vehicle assets for update"); // Debug: Log asset processing

                // Get existing vehicle mappings
                const existingMappings = await FinancialElementVehicleVehicleMappingModel.findAll({
                    where: { scale_code: code },
                    transaction
                });
                const existingVehicleReferences = existingMappings.map(mapping => mapping.vehicle_reference);
                console.debug("Existing vehicle references:", existingVehicleReferences);

                // Identify assets to add (in payload but not in database)
                const assetsToAdd = payload.assets.filter(ref => !existingVehicleReferences.includes(ref));
                console.debug("Vehicle assets to add:", assetsToAdd);

                // Identify assets to remove (in database but not in payload)
                const assetsToRemove = existingVehicleReferences.filter(ref => !payload.assets.includes(ref));
                console.debug("Vehicle assets to remove:", assetsToRemove);

                // Add new mappings
                if (assetsToAdd.length > 0) {
                    await Promise.all(
                        assetsToAdd.map(async (vehicleReference) => {
                            const newMapping = await FinancialElementVehicleVehicleMappingModel.create({
                                reference: uuidv4(),
                                scale_code: code,
                                vehicle_reference: vehicleReference,
                            }, {transaction});
                            console.debug("New vehicle mapping created:", newMapping);
                            await sendSyncMessage(OperationType.POST, 'FinancialElementVehicleVehicleMapping',
                                'Static-tables', newMapping.get({plain: true}));
                        })
                    );
                }

                // Remove old mappings
                if (assetsToRemove.length > 0) {
                    for (const vehicleReference of assetsToRemove) {
                        const mappingToRemove = existingMappings.find(m => m.vehicle_reference === vehicleReference);
                        await FinancialElementVehicleVehicleMappingModel.destroy({
                            where: {
                                scale_code: code,
                                vehicle_reference: vehicleReference
                            },
                            transaction
                        });
                        console.debug("Vehicle mapping removed:", mappingToRemove);
                        await sendSyncMessage(OperationType.DELETE, 'FinancialElementVehicleVehicleMapping',
                            'Static-tables', mappingToRemove.get({plain: true}));
                    }
                }
            }
        }

        // Update application criteria
        const applicationCriteria = await ApplicationCriteriaModel.findOne({ where: { scale_code: code } });
        if (applicationCriteria) {
            await applicationCriteria.update(
                {
                    channel_of_acquisition: payload.channel_of_acquisition || applicationCriteria.channel_of_acquisition,
                    currency_code: payload.currency_code || applicationCriteria.currency_code,
                    customer_type: payload.customer_type || applicationCriteria.customer_type,
                    financial_scoring: payload.financial_scoring || applicationCriteria.financial_scoring,
                },
                { transaction }
            );
            console.debug("Application criteria updated:", applicationCriteria); // Debug: Log application criteria update
            await sendSyncMessage(OperationType.PUT, 'ApplicationCriteria', 'Static-tables', applicationCriteria.get({plain: true}));
        }
// Update application criteria actors
if (payload.businesses) {
    console.debug("Processing businesses for update"); // Debug: Log business processing

    // Get existing application criteria actors
    const existingActors = await ApplicationCriteriaActorModel.findAll({
        where: { application_criteria_reference: applicationCriteria.reference },
        transaction
    });
    const existingActorReferences = existingActors.map(actor => actor.actor_reference);
    console.debug("Existing actor references:", existingActorReferences);

    // Identify actors to add (in payload but not in database)
    const actorsToAdd = payload.businesses.filter(reference => !existingActorReferences.includes(reference));
    console.debug("Actors to add:", actorsToAdd);

    // Identify actors to remove (in database but not in payload)
    const actorsToRemove = existingActorReferences.filter(reference => !payload.businesses.includes(reference));
    console.debug("Actors to remove:", actorsToRemove);

    // Add new actors
    if (actorsToAdd.length > 0) {
        const newActors = await ApplicationCriteriaActorModel.bulkCreate(
            actorsToAdd.map(actor_reference => ({
                reference: uuidv4(),
                application_criteria_reference: applicationCriteria.reference,
                actor_reference: actor_reference
            })),
            {transaction}
        );
        console.debug("New application criteria actors created:", newActors);

        const messagePromises = newActors.map(actor =>
            sendSyncMessage(OperationType.POST, 'ApplicationCriteriaActor', 'Static-tables', actor.get({plain: true}))
        );
        await Promise.all(messagePromises);
    }

    // Remove old actors
    if (actorsToRemove.length > 0) {
        for (const actorReference of actorsToRemove) {
            const actorToRemove = existingActors.find(actor => actor.actor_reference === actorReference);
            await ApplicationCriteriaActorModel.destroy({
                where: {
                    reference: actorToRemove.reference
                },
                transaction
            });
            console.debug("Application criteria actor removed:", actorToRemove);
            await sendSyncMessage(OperationType.DELETE, 'ApplicationCriteriaActor',
                'Static-tables', actorToRemove.get({plain: true}));
        }
    }
}

console.debug("Scale application criteria reference:", applicationCriteria.reference);
await sendSyncMessage(OperationType.PUT, 'Scale', 'Static-tables', updatedScaleData);

        // Update related financial products
        if (payload.products) {
            console.debug("Processing products for update"); // Debug: Log product processing

            // Get existing product mappings
            const existingMappings = await ScaleFinancialProductModel.findAll({
                where: { scale_code: code },
                transaction
            });
            const existingProductCodes = existingMappings.map(mapping => mapping.product_code);
            console.debug("Existing product codes:", existingProductCodes);

            // Identify products to add (in payload but not in database)
            const productsToAdd = payload.products.filter(code => !existingProductCodes.includes(code));
            console.debug("Products to add:", productsToAdd);

            // Identify products to remove (in database but not in payload)
            const productsToRemove = existingProductCodes.filter(code => !payload.products.includes(code));
            console.debug("Products to remove:", productsToRemove);

            // Add new mappings
            if (productsToAdd.length > 0) {
                await Promise.all(
                    productsToAdd.map(async (productCode) => {
                        const newMapping = await ScaleFinancialProductModel.create({
                            reference: uuidv4(),
                            scale_code: code,
                            product_code: productCode,
                        }, {transaction});
                        console.debug("New financial product mapping created:", newMapping);
                        await sendSyncMessage(OperationType.POST, 'ScaleFinancialProduct',
                            'Static-tables', newMapping.get({plain: true}));
                    })
                );
            }

            // Remove old mappings
            if (productsToRemove.length > 0) {
                for (const productCode of productsToRemove) {
                    const mappingToRemove = existingMappings.find(m => m.product_code === productCode);
                    await ScaleFinancialProductModel.destroy({
                        where: {
                            scale_code: code,
                            product_code: productCode
                        },
                        transaction
                    });
                    console.debug("Financial product mapping removed:", mappingToRemove);
                    await sendSyncMessage(OperationType.DELETE, 'ScaleFinancialProduct',
                        'Static-tables', mappingToRemove.get({plain: true}));
                }
            }
        }

        // Update third parties
        if (payload.third_parties) {
            console.debug("Processing third parties for update"); // Debug: Log third party processing

            // Get existing third party mappings
            const existingThirdParties = await ThirdPartiesModel.findAll({
                where: { scale_code: code },
                transaction
            });

            // Create maps for easier comparison
            const existingMap = new Map(existingThirdParties.map(tp =>
                [`${tp.actor_reference}-${tp.role_code}`, tp]));

            const payloadMap = new Map(payload.third_parties.map(tp =>
                [`${tp.actor_reference}-${tp.role_code}`, tp]));

            // Identify third parties to add (in payload but not in database)
            const thirdPartiesToAdd = payload.third_parties.filter(tp =>
                !existingMap.has(`${tp.actor_reference}-${tp.role_code}`));
            console.debug("Third parties to add:", thirdPartiesToAdd);

            // Identify third parties to remove (in database but not in payload)
            const thirdPartiesToRemove = existingThirdParties.filter(tp =>
                !payloadMap.has(`${tp.actor_reference}-${tp.role_code}`));
            console.debug("Third parties to remove:", thirdPartiesToRemove);

            // Add new third parties
            if (thirdPartiesToAdd.length > 0) {
                await Promise.all(
                    thirdPartiesToAdd.map(async (thirdParty) => {
                        const newThirdParty = await ThirdPartiesModel.create({
                            reference: uuidv4(),
                            actor_reference: thirdParty.actor_reference,
                            role_code: thirdParty.role_code,
                            scale_code: code,
                        }, {transaction});
                        console.debug("New third party created:", newThirdParty);
                        await sendSyncMessage(OperationType.POST, 'ThirdParties',
                            'Static-tables', newThirdParty.get({plain: true}));
                    })
                );
            }

            // Remove old third parties
            if (thirdPartiesToRemove.length > 0) {
                for (const thirdParty of thirdPartiesToRemove) {
                    await ThirdPartiesModel.destroy({
                        where: {
                            reference: thirdParty.reference
                        },
                        transaction
                    });
                    console.debug("Third party removed:", thirdParty);
                    await sendSyncMessage(OperationType.DELETE, 'ThirdParties',
                        'Static-tables', thirdParty.get({plain: true}));
                }
            }
        }

        // Update scale services
        if (payload.scale_service_list) {
            console.debug("Processing scale services for update");

            // Get existing service mappings
            const existingServices = await ScaleServiceModel.findAll({
                where: { scale_code: code },
                transaction
            });

            // Verify all service references exist before proceeding
            const allServices = await ServiceModel.findAll({
                where: {
                    reference: {
                        [Op.in]: payload.scale_service_list
                    }
                },
                attributes: ['reference']
            });

            const validServiceRefs = allServices.map(service => service.reference);

            // Filter out invalid service references
            const invalidServices = payload.scale_service_list.filter(
                ref => !validServiceRefs.includes(ref)
            );

            if (invalidServices.length > 0) {
                throw new NotFoundError(`Some services don't exist: ${invalidServices.join(', ')}`, 'Service');
            }

            // Create maps for easier comparison
            const existingServicesMap = new Map(existingServices.map(service =>
                [service.service_reference, service]
            ));

            const payloadServicesMap = new Map(payload.scale_service_list.map(service =>
                [service, service]
            ));

            // Identify services to add (in payload but not in database)
            const servicesToAdd = payload.scale_service_list.filter(service_ref =>
                !existingServicesMap.has(service_ref)
            );
            console.debug("Scale services to add:", servicesToAdd);

            // Identify services to remove (in database but not in payload)
            const servicesToRemove = existingServices.filter(service =>
                !payload.scale_service_list.includes(service.service_reference)
            );
            console.debug("Scale services to remove:", servicesToRemove);

            // Add new services
            if (servicesToAdd.length > 0) {
                await Promise.all(
                    servicesToAdd.map(async (service_reference) => {
                        const newScaleService = await ScaleServiceModel.create({
                            reference: uuidv4(),
                            scale_code: code,
                            service_reference: service_reference,
                        }, {transaction});

                        console.debug("New scale service created:", newScaleService);

                        await sendSyncMessage(
                            OperationType.POST,
                            'ScaleService',
                            'Static-tables',
                            newScaleService.get({plain: true})
                        );
                    })
                );
            }

            // Remove old services
            if (servicesToRemove.length > 0) {
                for (const service of servicesToRemove) {
                    await ScaleServiceModel.destroy({
                        where: { reference: service.reference },
                        transaction
                    });

                    console.debug("Scale service removed:", service);

                    await sendSyncMessage(
                        OperationType.DELETE,
                        'ScaleService',
                        'Static-tables',
                        service.get({plain: true})
                    );
                }
            }
        }

        // Commit the transaction
        await transaction.commit();
        console.debug("Transaction committed for update"); // Debug: Log transaction commit
        return res.status(200).json({ message: "Scale updated successfully" });
    } catch (error) {
        // Rollback the transaction in case of error
        await transaction.rollback();
        console.error("Error updating scale:", error);
        return res.status(500).json({ message: "Error updating scale", error: error.message });
    }
}

async function removeScaleService(req, res, next) {
    try {
        const scaleCode = req.params.code;
        const serviceReference = req.params.serviceReference;

        // Check if scale exists
        const scale = await ScaleModel.findOne({
            where: { code: scaleCode }
        });

        if (!scale) {
            throw new NotFoundError("Scale not found", "Scale");
        }

        // Find the specific scale service association
        const scaleService = await ScaleServiceModel.findOne({
            where: {
                scale_code: scaleCode,
                service_reference: serviceReference
            }
        });

        if (!scaleService) {
            throw new NotFoundError("Service association not found for this scale", "ScaleService");
        }



        // Delete the scale service association
        await ScaleServiceModel.destroy({
            where: {
                scale_code: scaleCode,
                service_reference: serviceReference
            }
        });

        // Send sync message for event streaming
        await sendSyncMessage(
            OperationType.DELETE,
            'ScaleService',
            'Static-tables',
            scaleService.get({ plain: true })
        );

        res.send({
            data: {
                message: `Service association with reference ${serviceReference} has been removed successfully from scale ${scaleCode}`,
            },
        });
    } catch (error) {
        next(error);
    }
}

module.exports = {
    createScale,
    search,
    getScaleByCodeSimple,
    updateStatus,
    removeScaleEquipment,
    remove,
    getOne,
    removeThirdParty,
    updateScale,
    removeScaleService
};