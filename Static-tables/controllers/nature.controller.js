const {
  OperationModel,
  NatureModel,
  NatureTranslationsModel,
  OperationNatureMappingModel,
  sequelize,
} = require("../db");
const NotFoundError = require("../error/exception/NotFound");
const config = require("../config/app-config");
const { QueryTypes } = require("sequelize");
const { sendSyncMessage } = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");

async function getNaturesByOperation(req, res, next) {
  try {
    const operationCode = req.params.operationCode;
    let whereCondition = {};
    let translationWhereCondition = {};
    let operationNatureCondition = { operation_code: operationCode };
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;
    const language = req.query.language
      ? req.query.language.toUpperCase()
      : config.defaultReturnedLanguage;
    const limit = req.query.limit ? req.query.limit : config.limit;
    const offset = req.query.offset
      ? req.query.offset * limit
      : config.offset * limit;

    Object.keys(req.query).forEach((key) => {
      if (
        !["offset", "limit", "sort_by", "order_by", "language"].includes(key)
      ) {
        if (key === "active") {
          operationNatureCondition["nature_status"] = req.query[key];
        } else if (language === config.language || key !== "label") {
          whereCondition[key] = req.query[key];
        } else {
          translationWhereCondition[key] = req.query[key];
        }
      }
    });

    const getSortingOrder = (sortBy, orderBy) => {
      if (language !== config.language && sortBy === "label") {
        return [[NatureTranslationsModel, "label", orderBy]];
      }
      if (sortBy === "active") {
        return [[OperationNatureMappingModel, "nature_status", orderBy]];
      }
      return [[sortBy, orderBy]];
    };

    let natures = (
      await NatureModel.findAll({
        order: getSortingOrder(sortBy, orderBy),
        limit,
        offset,
        where: whereCondition,
        include: [
          ...(language !== config.language
            ? [
                {
                  model: NatureTranslationsModel,
                  attributes: ["label"],
                  where: translationWhereCondition,
                },
              ]
            : []),
          {
            model: OperationNatureMappingModel,
            attributes: ["nature_status"],
            where: operationNatureCondition,
          },
        ],
        raw: true,
      })
    ).map((nature) => {
      return {
        id: nature.id,
        code: nature.code,
        label:
          language !== config.language && nature["nature_translations.label"]
            ? nature["nature_translations.label"]
            : nature.label,
        active: nature["operation_nature_mappings.nature_status"],
        system_attribute: nature.system_attribute,
      };
    });

    let total_count = await NatureModel.count({
      where: whereCondition,
      include: [
        {
          model: NatureTranslationsModel,
          where: translationWhereCondition,
        },
        {
          model: OperationNatureMappingModel,
          where: operationNatureCondition,
        },
      ],
    });

    return res.json({ data: natures, total_count });
  } catch (error) {
    next(error);
  }
}

async function update(req, res, next) {
  try {
    const id = req.params.id;
    const operationCode = req.params.operationCode;
    const { active } = req.body;

    const oldNature = await NatureModel.findOne({
      where: {
        id,
      },
    });

    if (!oldNature) throw new NotFoundError("Nature not found", "Natures");

    const operation = await OperationModel.findOne({
      where: {
        code: operationCode,
      },
    });

    if (!operation)
      throw new NotFoundError("Operation not found", "Operations");

    await OperationNatureMappingModel.update(
      {
        nature_status: active,
      },
      {
        where: {
          nature_code: oldNature.code,
          operation_code: operationCode,
        },
      }
    );
    const updatedOperationNatureMappingModel = OperationNatureMappingModel.findOne({
      where: {
        nature_code: oldNature.code,
        operation_code: operationCode,
      },
    });
    await sendSyncMessage(OperationType.PUT, 'Nature', 'Nature', updatedOperationNatureMappingModel);
    res.send({
      data: { ...req.body, id: parseInt(id) },
    });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  getNaturesByOperation,
  update,
};
