const {MilestoneModel, PhaseModel, Phase_milestoneModel} = require("../db");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const {sendSyncMessage} = require('../producer/Producer');
const OperationType = require('../models/Stream/operationType');
//GET::/api/v1/static-tables/phases-milestones
async function getPhaseMilestone(req, res, next) {
    try {
        let whereCondition = {};
        const limit = req.query.limit ? req.query.limit : config.limit;
        const offset = req.query.offset ? req.query.offset  : config.offset ;
        const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
        const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;

        // Build the where condition dynamically based on query parameters
        Object.keys(req.query).forEach(key => {
            if (key !== "offset" && key !== "limit" && key !== "sort_by" && key !== "order_by") {
                whereCondition[key] = req.query[key];
            }
        });

        const [milestones, total_count] = await Promise.all(
            [Phase_milestoneModel.findAll({
                order: [[sortBy, orderBy]],
                offset, limit,
                where: whereCondition
            }),
                Phase_milestoneModel.count({where: whereCondition}),]);

        const response = {
            data: milestones, total_count
        };
        // Sending the response
        res.send(response);
    } catch (error) {
        next(error);
    }
}


//GET::/api/v1/static-tables/phases/{phasecode}/milestones/{Milestonecode}
async function getMileStoneByMilestonecodeAndPhaseCode(req, res, next) {
    try {
        const {phasecode, Milestonecode} = req.params;
        const milestone = await MilestoneModel.findOne({
            where: {phase_code: phasecode, code: Milestonecode},
        });

        if (!milestone || milestone.length === 0) {
            throw new NotFoundError("MileStone not found for the given phase CODE and milestone CODE", "PhaseMilestone");
        } else {
            return res.send({
                data: milestone,
            });
        }

    } catch (error) {
        next(error);
    }
}

//POST::/api/v1/static-tables/phases-milestones

//body {milestone_code:””, phase_code:””, associated_to:”"}
async function createMileStonesForPhase(req, res, next) {
    try {
        const {milestone_code, phase_code, associated_to} = req.body;

        const existingPhase = await PhaseModel.findOne({where: {code: phase_code, associated_to: associated_to}});
        const existingMileStone = await MilestoneModel.findOne({
            where: {code: milestone_code}
        });

        if (!existingPhase) {
            throw new NotFoundError("No phase found", "PhaseMilestone")
        }
        if (!existingMileStone) {
            throw new NotFoundError("No Milestone found", "PhaseMilestone")
        }

        const milestonePhaseExist = await Phase_milestoneModel.findOne({
            where: {phase_code: phase_code, milestone_code: milestone_code, associated_to: associated_to}
        });

        if (milestonePhaseExist) {
            throw new ConflictError("Phase and Milestone already exist", "PhaseMilestone")
        }

        const createdPhaseMilestone = await Phase_milestoneModel.create({
            phase_code, milestone_code, associated_to
        });
        const updatedMilestone = {
            id: existingMileStone.id,
            code: existingMileStone.code,
            label: existingMileStone.label,
            rate: existingMileStone.rate,
            start_date: existingMileStone.start_date,
            end_date: existingMileStone.end_date,
            system_attribute: existingMileStone.system_attribute,
            active: existingMileStone.active,
            phaseId: existingPhase.id
        }
        await sendSyncMessage(OperationType.PUT, "Milestone", "milestones", updatedMilestone);
        res.status(201).json({data: createdPhaseMilestone});

    } catch (error) {
        next(error);
    }
}

//PUT::/api/v1/static-tables/phases-milestones/:id

//body {milestone_code:””, phase_code:””, associated_to:”"}
async function updatePhaseByPhaseCodeMileStoneCodeAndEntityCode(req, res, next) {
    try {
        const milestonePhase = await Phase_milestoneModel.findOne({
            where: {
                id: req.params.id,
            },
        });
        if (!milestonePhase) {
            throw new NotFoundError("Phase-Milestone not found for the given phase CODE and milestone CODE", "PhaseMilestone")
        }

        const {milestone_code, phase_code, associated_to} = req.body;
        const existingPhase = await PhaseModel.findOne({where: {code: phase_code, associated_to: associated_to}});
        const existingMileStone = await MilestoneModel.findOne({
            where: {code: milestone_code}
        });

        if (!existingPhase) {
            throw new NotFoundError("No phase found", "PhaseMilestone")
        }
        if (!existingMileStone) {
            throw new NotFoundError("No Milestone found", "PhaseMilestone")
        }

        const milestonePhaseExist = await Phase_milestoneModel.findOne({
            where: {phase_code: phase_code, milestone_code: milestone_code, associated_to: associated_to}
        });

        if (milestonePhaseExist) {
            throw new ConflictError("Phase and Milestone already exist", "PhaseMilestone")
        }

        const updatedPhaseMilestone = await milestonePhaseExist.update({
            phase_code, milestone_code, associated_to
        });
        const updatedMilestone = {
            id: existingMileStone.id,
            code: existingMileStone.code,
            label: existingMileStone.label,
            rate: existingMileStone.rate,
            start_date: existingMileStone.start_date,
            end_date: existingMileStone.end_date,
            system_attribute: existingMileStone.system_attribute,
            active: existingMileStone.active,
            phaseId: existingPhase.id
        }
        await sendSyncMessage(OperationType.PUT, "Milestone", "milestones", updatedMilestone);
        res.send({
            data: updatedPhaseMilestone[1][0],
        });
    } catch (error) {
        next(error);
    }
}

// DELETE::/api/v1/static-tables/phases-milestones/:id

async function deletePhaseByPhaseCodeMileStoneCodeAndEntityCode(req, res, next) {
    try {
        const numRowsDeleted = await Phase_milestoneModel.destroy({
            where: {
                id: req.params.id,
            },
        });

        if (numRowsDeleted === 0) {
            throw new NotFoundError("Phase-Milestone not found for the given phase CODE and milestone CODE", "PhaseMilestone")
        }

        res.send({
            data: {
                message: "Phase Milestone deleted successfully"
            },
        });
        const milestone = await MilestoneModel.findOne({
            where: {code: req.params.milestone_code},
        })
        const updatedMilestone = {
            id: milestone.id,
            code: milestone.code,
            label: milestone.label,
            rate: milestone.rate,
            start_date: milestone.start_date,
            end_date: milestone.end_date,
            system_attribute: milestone.system_attribute,
            active: milestone.active,
            phaseId: null
        }
        await sendSyncMessage(OperationType.PUT, "Milestone", "milestones", updatedMilestone);
    } catch (error) {
        next(error);
    }
}

module.exports = {

    getPhaseMilestone,
    getMileStoneByMilestonecodeAndPhaseCode,
    createMileStonesForPhase,
    updatePhaseByPhaseCodeMileStoneCodeAndEntityCode,
    deletePhaseByPhaseCodeMileStoneCodeAndEntityCode,
};
