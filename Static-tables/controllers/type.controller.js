const {
  TypeModel,
  TypeTranslationsModel,
  OperationNatureTypeMappingModel,
  OperationNatureMappingModel,
  OperationModel,
  NatureModel,
  OperationTranslationsModel,
  NatureTranslationsModel,
  ProductTypeMappingModel,
  ProductsModel,
  ProductTranslationsModel,
} = require("../db");
const { Op } = require("sequelize");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const config = require("../config/app-config");
const { sendSyncMessage } = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");

async function search(req, res, next) {
  try {
    const natureCode = req.params.natureCode;
    const operationCode = req.params.operationCode;
    let whereCondition = {};
    let translationWhereCondition = {};
    let operationNatureTypeCondition = {
      operation_nature_code: `${operationCode}_${natureCode}`,
    };
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;
    const language = req.query.language
      ? req.query.language.toUpperCase()
      : config.defaultReturnedLanguage;
    const limit = req.query.limit ? req.query.limit : config.limit;
    const offset = req.query.offset
      ? req.query.offset 
      : config.offset ;

    Object.keys(req.query).forEach((key) => {
      if (
        !["offset", "limit", "sort_by", "order_by", "language"].includes(key)
      ) {
        if (key === "active") {
          operationNatureTypeCondition["type_status"] = req.query[key];
        } else if (language === config.language || key !== "label" ) {
          whereCondition[key] = req.query[key];
        } else {
          translationWhereCondition[key] = req.query[key];
        }
      }
    });

    const getSortingOrder = (sortBy, orderBy) => {
      if (language !== config.language && sortBy === "label") {
        return [[TypeTranslationsModel, "label", orderBy]];
      }
      if (sortBy === "active") {
        return [[OperationNatureTypeMappingModel, "type_status", orderBy]];
      }
      return [[sortBy, orderBy]];
    };

    let natureTypes = (
      await TypeModel.findAll({
        order: getSortingOrder(sortBy, orderBy),
        limit,
        offset,
        where: whereCondition,
        include: [
          ...(language !== config.language
            ? [
                {
                  model: TypeTranslationsModel,
                  attributes: ["label"],
                  where: translationWhereCondition,
                },
              ]
            : []),
          {
            model: OperationNatureTypeMappingModel,
            attributes: ["type_status"],
            where: operationNatureTypeCondition,
          },
        ],
        raw: true,
      })
    ).map((type) => {
      return {
        id: type.id,
        code: type.code,
        label:
          language !== config.language && type["type_translations.label"]
            ? type["type_translations.label"]
            : type.label ||  type["type_translations.label"],
        active: type["operation_nature_type_mappings.type_status"],
        system_attribute: type.system_attribute,
      };
    });

    let total_count = await TypeModel.count({
      where: whereCondition,
      include: [
        {
          model: TypeTranslationsModel,
          where: translationWhereCondition,
        },
        {
          model: OperationNatureTypeMappingModel,
          where: operationNatureTypeCondition,
        },
      ],
    });

    return res.json({
      data: natureTypes,
      total_count,
    });
  } catch (error) {
    next(error);
  }
}

async function update(req, res, next) {
  try {
    const id = req.params.id;
    const operationCode = req.params.operationCode;
    const natureCode = req.params.natureCode;
    const {
      code,
      label,
      active,
      language = config.defaultReturnedLanguage,
    } = req.body;
    const isDefaultLanguage = language.toUpperCase() === config.language;

    const operationNatureMapping = await OperationNatureMappingModel.findOne({
      attributes: ["id"],
      where: {
        operation_code: operationCode,
        nature_code: natureCode,
      },
    });

    if (!operationNatureMapping)
      throw new NotFoundError(
        "Operation Nature Mapping not found",
        "OperationNatureMapping"
      );

    const oldType = await TypeModel.findOne({
      where: {
        id,
      },
    });

    if (!oldType) throw new NotFoundError("Nature Type not found", "Types");

    const existingType = await TypeModel.findOne({
      where: {
        code: code,
      },
    });

    if (existingType && existingType.id !== parseInt(id)) {
      throw new ConflictError(
        "Nature Type with matching code already exists",
        "Types"
      );
    }

    if (oldType.code !== code && oldType.system_attribute)
      throw new ConflictError("Type code cannot be modified", "Type");

    await oldType.update({
      code,
      label: isDefaultLanguage ? label : oldType.label,
    });
    const updatedType = await TypeModel.findOne({
      where: {
        id,
      },
    });
    await sendSyncMessage(
      OperationType.PUT,
      "LineType",
      "LineType",
      updatedType
    );

    const existingTranslatedType = await TypeTranslationsModel.findOne({
      where: {
        type_code: code,
      },
    });

    if (existingTranslatedType && !isDefaultLanguage)
      await TypeTranslationsModel.update(
        {
          label,
        },
        {
          where: {
            type_code: code,
          },
        }
      );

    await OperationNatureTypeMappingModel.update(
      {
        type_status: active,
        operation_nature_type_code: `${operationCode}_${natureCode}_${code}`
      },
      {
        where: { operation_nature_type_code:  `${operationCode}_${natureCode}_${oldType.code}` },
      }
    );
    const OperationNatureTypeMapping = {
        operation_nature_type_code: `${operationCode}_${natureCode}_${code}`,
        operation_nature_code: `${operationCode}_${natureCode}`,
        type_code: code,
        type_status: active,
    }
    await sendSyncMessage(
      OperationType.PUT,
      "OperationNatureTypeMapping",
      "OperationNatureTypeMapping",
      OperationNatureTypeMapping
    );
    res.send({
      data: {
        id: parseInt(id),
        ...req.body,
      },
    });
  } catch (error) {
    next(error);
  }
}

async function remove(req, res, next) {
  try {
    const id = req.params.id;

    const oldType = await TypeModel.findOne({
      where: {
        id,
      },
    });

    if (!oldType) throw new NotFoundError("Nature Type not found", "Types");

    if (oldType.system_attribute == true)
      throw new ConflictError("Nature Type cannot be removed", "Types");

    await TypeModel.destroy({
      where: {
        id: id,
      },
    });

    await sendSyncMessage(
      OperationType.DELETE,
      "LineType",
      "LineType",
      oldType
    );

    res.send({
      data: {
        message: `Resource with ID ${id} has been deleted successfully`,
      },
    });
  } catch (error) {
    next(error);
  }
}

async function create(req, res, next) {
  try {
    const operationCode = req.params.operationCode;
    const natureCode = req.params.natureCode;
    const {
      code,
      label,
      active,
      language = config.defaultReturnedLanguage,
    } = req.body;
    const isDefaultLanguage = language.toUpperCase() === config.language;

    const operationNatureCodes = await OperationNatureMappingModel.findAll({
      attributes: ["operation_nature_code"],
      where: {
        operation_nature_code: { [Op.like]: `%_${natureCode}` }, //type will be added to different types under different operations
      },
    });

    if (!operationNatureCodes)
      throw new NotFoundError(
        "No Operation Nature Mapping was found",
        "OperationNatureMapping"
      );

    const existingType = await TypeModel.findOne({
      where: {
        code,
      },
    });

    if (existingType) {
      throw new ConflictError(
        "A Nature Type with matching information already exists",
        "Types"
      );
    }

    const new_type = await TypeModel.create({
      code,
      label: isDefaultLanguage ? label : "",
    });

    await sendSyncMessage(OperationType.POST, "LineType", "LineType", new_type);

    if (new_type)
      await TypeTranslationsModel.create({
        type_code: new_type.code,
        label: !isDefaultLanguage ? label : "",
        language_code: !isDefaultLanguage ? language.toUpperCase() : "",
      });

    await Promise.all(
      operationNatureCodes.map(async (item) => {
        const newOperationNatureTypeMappingModel =
          await OperationNatureTypeMappingModel.create(
            {
              operation_nature_code: item.operation_nature_code,
              operation_nature_type_code: `${item.operation_nature_code}_${code}`,
              type_code: new_type.code,
              type_status:
                item.operation_nature_code === `${operationCode}_${natureCode}`
                  ? active
                  : false,
            },
            {
              returning: [],
            }
          );
        await sendSyncMessage(
          OperationType.POST,
          "OperationNatureTypeMapping",
          "OperationNatureTypeMapping",
          newOperationNatureTypeMappingModel
        );
      })
    );

    res.status(201).send({
      data: {
        id: new_type.id,
        ...req.body,
      },
    });
  } catch (err) {
    next(err);
  }
}

async function getTypesMapping(req, res, next) {
  try {
    const language = req.query.language
      ? req.query.language.toUpperCase()
      : config.defaultReturnedLanguage;

    let result =
      language === config.language
        ? await OperationNatureTypeMappingModel.findAll({
            attributes: ["operation_nature_type_code"],
            include: [
              {
                model: TypeModel,
                attributes: ["id", "code", "label"],
              },
              {
                model: OperationNatureMappingModel,
                as: 'operationNatureMapping',
                attributes: ["id", "operation_nature_code"],
                include: [
                  {
                    model: OperationModel,
                    attributes: ["id", "code", "label"],
                  },
                  {
                    model: NatureModel,
                    attributes: ["id", "code", "label"],
                  },
                ],
              },
            ],
            raw: true,
          })
        : await OperationNatureTypeMappingModel.findAll({
            attributes: ["operation_nature_type_code"],
            include: [
              {
                model: TypeModel,
                attributes: ["id", "code", "label"],
                include: {
                  model: TypeTranslationsModel,
                  attributes: ["label"],
                  where: { language_code: language },
                  required: false
                },
              },
              {
                model: OperationNatureMappingModel,
                as: 'operationNatureMapping',
                attributes: ["id", "operation_nature_code"],
                include: [
                  {
                    model: OperationModel,
                    attributes: ["id", "code", "label"],
                    include: {
                      model: OperationTranslationsModel,
                      attributes: ["label"],
                      where: { language_code: language },
                      required: false
                    },
                  },
                  {
                    model: NatureModel,
                    attributes: ["id", "code", "label"],
                    include: {
                      model: NatureTranslationsModel,
                      attributes: ["label"],
                      where: { language_code: language },
                      required: false
                    },
                  },
                ],
              },
            ],
            raw: true,
          });

    result = result.map((item) => ({
      operation_nature_type_code: item.operation_nature_type_code,
      operation: {
        id: item["operationNatureMapping.operation.id"],
        code: item["operationNatureMapping.operation.code"],
        label:
          language !== config.language &&
          item[
            "operationNatureMapping.operation.operation_translations.label"
          ]
            ? item[
                "operationNatureMapping.operation.operation_translations.label"
              ]
            : item["operationNatureMapping.operation.label"],
      },
      nature: {
        id: item["operationNatureMapping.nature.id"],
        code: item["operationNatureMapping.nature.code"],
        label:
          language !== config.language &&
          item["operationNatureMapping.nature.nature_translations.label"]
            ? item["operationNatureMapping.nature.nature_translations.label"]
            : item["operationNatureMapping.nature.label"],
      },
      type: {
        id: item["type.id"],
        code: item["type.code"],
        label:
          language !== config.language && item["type.type_translations.label"]
            ? item["type.type_translations.label"]
            : item["type.label"],
      },
    }));

    res.json({ data: result, total_count: result.length });
  } catch (err) {
    next(err);
  }
}

async function getScaleLineTypes(req, res, next) {
  try {
    const language = req.query.language
      ? req.query.language.toUpperCase()
      : config.defaultReturnedLanguage;

    const filterConditions = req.filterConditions; // Use the conditions from the middleware

    const result = await OperationNatureTypeMappingModel.findAll({
      attributes: ["operation_nature_type_code"],
      where: {
        operation_nature_code: {
          [Op.like]: "%PRES",
        },
      },
      include: [
        {
          model: TypeModel,
          attributes: ["label"],
          include:
            language !== config.language
              ? [
                  {
                    model: TypeTranslationsModel,
                    attributes: ["label"],
                    where: { language_code: "FR" },
                  },
                ]
              : [],
        },
        {
          model: ProductTypeMappingModel,
          attributes: ["product_code"],
          where: filterConditions, // Apply the filter conditions
          required: true,
          include: {
            model: ProductsModel,
            attributes: ["label"],
            include:
              language !== config.language
                ? [
                    {
                      model: ProductTranslationsModel,
                      attributes: ["label"],
                      where: { language_code: "FR" },
                    },
                  ]
                : [],
          },
        },
      ],
      raw: true,
    });

    const response = result.map((item) => {
      return {
        operation_nature_type_code: item.operation_nature_type_code,
        product_code: item["product_type_mappings.product_code"],
        type_label: item["type.type_translations.label"] || item["type.label"],
        product_label:
          item["product_type_mappings.product.product_translations.label"] ||
          item["product_type_mappings.product.label"],
      };
    });

    res.json({ data: response });
  } catch (err) {
    next(err);
  }
}

module.exports = {
  search,
  update,
  remove,
  create,
  getTypesMapping,
  getScaleLineTypes
};