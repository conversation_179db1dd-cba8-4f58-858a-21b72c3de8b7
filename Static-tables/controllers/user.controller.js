const { Op } = require("sequelize");
const { UserModel, ProfileModel, ProfileTranslationsModel } = require("../db");
const ConflictError = require("../error/exception/Conflict");
const NotFoundError = require("../error/exception/NotFound");
const config = require("../config/app-config");

const randomColor = () => {
  const colors = ["#fddc69", "#81e19c", "#7a99bb", "#25a048", "#1062fe"];
  const randomIndex = Math.floor(Math.random() * colors.length);
  return colors[randomIndex];
};

async function search(req, res, next) {
  try {
    let whereCondition = {};
    const limit = req.query.limit ? req.query.limit : config.limit;
    const offset = req.query.offset
      ? req.query.offset 
      : config.offset ;
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;
    const language = req.query.language
      ? req.query.language.toUpperCase()
      : config.defaultReturnedLanguage;

    Object.keys(req.query).forEach((key) => {
      if (
        !["offset", "limit", "sort_by", "order_by", "language"].includes(key)
      ) {
        whereCondition[key] = req.query[key];
      }
    });

    let users, total_count;

    [users, total_count] = await Promise.all([
      UserModel.findAll({
        limit,
        offset,
        orderBy,
        order: [[sortBy, orderBy]],
        where: whereCondition,
        include: {
          model: ProfileModel,
          on: {
            "$profile.code$": { [Op.col]: "user.profile_code" },
          },
          include:
            language !== config.language
              ? [
                  {
                    model: ProfileTranslationsModel,
                    attributes: ["label"],
                    on: {
                      "$profile.code$": {
                        [Op.col]: "profile->profile_translations.profile_code",
                      },
                    },
                  },
                ]
              : [],
        },
        raw: true,
      }),
      UserModel.count({ where: whereCondition }),
    ]);

    users = users.map((user) => {
      return {
        id: user.id,
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email,
        phone: user.phone,
        status: user.status,
        status_reason: user.status_reason,
        status_updated_at: user.status_updated_at,
        expiry_date: user.expiry_date,
        color: user.color,
        profile: {
          id: user["profile.id"],
          code: user["profile.code"],
          label:
            user["profile.profile_translations.label"] || user["profile.label"],
          description: user["profile.description"],
          active: user["profile.active"],
          system_attribute: user["profile.system_attribute"],
        },
      };
    });

    res.send({ data: users, total_count });
  } catch (error) {
    next(error);
  }
}

async function create(req, res, next) {
  try {
    const { email, phone, ...rest } = req.body;

    const existingUser = await UserModel.findOne({
      where: {
        email,
      },
    });

    if (existingUser)
      throw new ConflictError(
        "User with matching email already exists",
        "Users"
      );

    const color = randomColor();

    const expiry_date = new Date();
    expiry_date.setDate(expiry_date.getDate() + 7);
    const formattedExpireDate = expiry_date.toISOString().split("T")[0];

    const newUser = await UserModel.create({
      email,
      ...rest,
      phone: phone || null,
      color,
      status_reason: "ChangePassword",
      expiry_date: formattedExpireDate,
    });

    res.status(201).send({
      data: {
        id: newUser.id,
        first_name: newUser.first_name,
        last_name: newUser.last_name,
        email: newUser.email,
        phone: newUser.phone,
        profile_code: newUser.profile_code,
        status: newUser.status,
        status_reason: newUser.status_reason,
        status_updated_at: newUser.status_updated_at,
        expiry_date: newUser.expiry_date,
        color: newUser.color,
      }
    });
    
  } catch (error) {
    next(error);
  }
}

async function update(req, res, next) {
  try {
    const id = req.params.id;
    const existingUser = await UserModel.findOne({
      where: {
        id,
      },
    });

    if (!existingUser) throw new NotFoundError("User not found", "Users");

    const usedEmail = await UserModel.findOne({
      where: {
        email: req.body.email,
      },
    });

    if (usedEmail && usedEmail.id !== parseInt(id))
      throw new ConflictError("User with same email was found", "Users");

    await existingUser.update({
      ...req.body,
    });

    res.send({
      data: { id, ...req.body },
    });
  } catch (error) {
    next(error);
  }
}

async function updateStatus(req, res, next) {
  try {
    const id = req.params.id;
    const { status } = req.body;

    const existingUser = await UserModel.findOne({
      where: {
        id,
      },
    });

    if (!existingUser) throw new NotFoundError("User not found", "Users");

    const currentDate = new Date().toISOString().split("T")[0];

    await existingUser.update({
      status,
      status_updated_at: currentDate,
    });

    res.send({
      data: {
        id: existingUser.id,
        first_name: existingUser.first_name,
        last_name: existingUser.last_name,
        email: existingUser.email,
        phone: existingUser.phone,
        profile_code: existingUser.profile_code,
        status,
        status_reason: existingUser.status_reason,
        status_updated_at: currentDate,
        expiry_date: existingUser.expiry_date,
        color: existingUser.color,
      }
    });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  search,
  create,
  update,
  updateStatus,
};
