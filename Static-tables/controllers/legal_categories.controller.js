/**
 * Legal Categories Controller
 * Uses AbstractControllerFactory which automatically routes to EnhancedGenericController
 * due to country relationships configuration
 */
const { LegalCategoryModel, LegalCategoryTranslationsModel } = require("../db");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const { sendSyncMessage } = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");
const { legalCategoryController } = require("../utils/controller-factory");

async function update(req, res, next) {
  try {
    const code = req.params.code;
    const { label, language = config.defaultReturnedLanguage, country_code } = req.body;
    const isDefaultLanguage = language.toUpperCase() === config.language;

    const legalCategory = await LegalCategoryModel.findOne({
      where: {
        code,
      },
    });

    if (!legalCategory) {
      throw new NotFoundError("LegalCategory not found", "LegalCategory");
    }

    await LegalCategoryModel.update(
        {
          label: isDefaultLanguage ? label : legalCategory.label,
          country_code
        },
        {
          where: {
            code,
          },
        }
    );

    const existingTranslatedLegalCategory =
        await LegalCategoryTranslationsModel.findOne({
          where: {
            legal_category_code: code,
          },
        });

    if (existingTranslatedLegalCategory)
      await LegalCategoryTranslationsModel.update(
          {
            label: !isDefaultLanguage
                ? label
                : existingTranslatedLegalCategory.label,
          },
          {
            where: {
              legal_category_code: code,
            },
          }
      );

    await sendSyncMessage(OperationType.PUT, "LegalCategory", "Static-tables", {
      code,
      ...req.body,
    });
    res.send({
      data: { ...req.body, code: code },
    });
  } catch (error) {
    next(error);
  }
}

async function remove(req, res, next) {
  try {
    const code = req.params.code;
    const legalCategory = await LegalCategoryModel.findOne({
      where: {
        code,
      },
    });
    if (!legalCategory) {
      throw new NotFoundError("LegalCategory not found", "LegalCategory");
    }

    if (legalCategory.system_attribute) {
      throw new ConflictError(
          "Cannot remove system attribute",
          "LegalCategory"
      );
    }

    await LegalCategoryModel.destroy({
      where: {
        code,
      },
    });
    await sendSyncMessage(
        OperationType.DELETE,
        "LegalCategory",
        "Static-tables",
        { code }
    );
    res.send({
      data: {
        message: `Resource with code ${code} has been deleted successfully`,
      },
    });
  } catch (error) {
    next(error);
  }
}

// Custom upload method preserved
async function upload(req, res, next) {
  try {
    const { LegalCategoryModel, LegalCategoryTranslationsModel } = require("../db");
    const { items } = req.body;

    const createdItems = await LegalCategoryModel.bulkCreate(items, {
      validate: true,
      returning: true,
    });

    const translations = createdItems.map((item) => ({
      legal_category_code: item.code,
      label: item.label,
      language_code: "FR",
    }));

    await LegalCategoryTranslationsModel.bulkCreate(translations, {
      validate: true,
    });

    res.send({
      data: {
        message: `Data has been created successfully`,
      },
    });
  } catch (err) {
    next(err);
  }
}

module.exports = {
  // Standard CRUD methods using enhanced generic controller
  search: legalCategoryController.search,
  getOne: legalCategoryController.getOne,
  create: legalCategoryController.create,

  // Custom upload method preserved
  update,
  remove,
  upload,
};
