const { PaymentMethodModel, PaymentMethodTranslationsModel} = require("../db");

const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const {Op} = require("sequelize");
const OperationType = require('../models/Stream/operationType');
const { sendSyncMessage } = require('../producer/Producer');

const renameKeys = (obj, keyMap) => {
    for (const [oldKey, newKey] of Object.entries(keyMap)) {
        if (obj.hasOwnProperty(oldKey)) {
            obj[newKey] = obj[oldKey];
            delete obj[oldKey];
        }
    }
    return obj;
};

async function search(req, res, next) {
    try {
        let whereCondition = {};
        const limit = parseInt(req.query.limit) || config.limit;
        const offset = (parseInt(req.query.offset) || config.offset) ;
        let sortBy = req.query.sort_by || config.SortBy;
        const orderBy = req.query.order_by || config.OrderBy;
        const language = req.query.language ? req.query.language.toUpperCase() : config.language.toUpperCase();

        // Build the where condition dynamically based on query parameters
        Object.keys(req.query).forEach(key => {
            if (!["offset", "limit", "sort_by", "order_by", "language"].includes(key)) {
                whereCondition[key] = req.query[key];
            }
        });

        const keyMap = {
            code: "payment_method_code"
        };

        // Rename keys for translation queries
        const translatedWhere = renameKeys({ ...whereCondition, language_code: language }, keyMap);

        let paymentMethods, total_count;

        if (language === config.language.toUpperCase()) {
            paymentMethods = await PaymentMethodModel.findAll({
                where: whereCondition,
                order: [[sortBy, orderBy]],
                limit,
                offset
            });
            total_count = await PaymentMethodModel.count({ where: whereCondition });
            paymentMethods = paymentMethods.map(pm => ({
                id: pm.id,
                code: pm.code,
                label: pm.label,
                language: config.language,
                requires_bank_account: pm.requires_bank_account,
                manual_transaction: pm.manual_transaction,
                exchange_file: pm.exchange_file,
                bank_card: pm.bank_card,
                system_attribute: pm.system_attribute,
                active: pm.active
            }));
        } else {
            const translationExists = await PaymentMethodTranslationsModel.count({
                where: translatedWhere
            });

            if (translationExists === 0) {
                paymentMethods = await PaymentMethodModel.findAll({
                    where: whereCondition,
                    order: [[sortBy, orderBy]],
                    limit,
                    offset
                });
                total_count = await PaymentMethodModel.count({ where: whereCondition });
                paymentMethods = paymentMethods.map(pm => ({
                    id: pm.id,
                    code: pm.code,
                    label: pm.label,
                    language: config.language,
                    requires_bank_account: pm.requires_bank_account,
                    manual_transaction: pm.manual_transaction,
                    exchange_file: pm.exchange_file,
                    bank_card: pm.bank_card,
                    system_attribute: pm.system_attribute,
                    active: pm.active
                }));
            } else {
                paymentMethods = await PaymentMethodTranslationsModel.findAll({
                    where: translatedWhere,
                    include: [{
                        attributes: ["id", "active", "code", "label", "requires_bank_account", "manual_transaction", "exchange_file", "bank_card", "system_attribute"],
                        model: PaymentMethodModel,
                        as: "payment_method",
                        on: {
                            "$payment_method.code$": { [Op.col]: "payment_method_translations.payment_method_code" }
                        }
                    }],
                    order: [[sortBy, orderBy]],
                    limit,
                    offset,
                    raw: true
                });
                total_count = await PaymentMethodTranslationsModel.count({ where: translatedWhere });
                paymentMethods = paymentMethods.map(pm => ({
                    id: pm["payment_method.id"],
                    code: pm.payment_method_code,
                    label: pm.label,
                    language: language,
                    requires_bank_account: pm.requires_bank_account,
                    manual_transaction: pm.manual_transaction,
                    exchange_file: pm.exchange_file,
                    bank_card: pm.bank_card,
                    system_attribute: pm["payment_method.system_attribute"],
                    active: pm["payment_method.active"]
                }));
            }
        }

        res.send({ data: paymentMethods, total_count });

    } catch (error) {
        next(error);
    }
}

async function getOne(req, res, next) {
    try {
        const language = req.query.language ? req.query.language.toUpperCase() : config.language.toUpperCase();
        const paymentMethod = await PaymentMethodModel.findOne({
            where: { id: req.params.id }
        });

        if (!paymentMethod) {
            throw new NotFoundError(`Payment method with ID ${req.params.id} not found`);
        }

        let result = {
            id: paymentMethod.id,
            code: paymentMethod.code,
            label: paymentMethod.label,
            language: config.language,
            requires_bank_account: paymentMethod.requires_bank_account,
            manual_transaction: paymentMethod.manual_transaction,
            exchange_file: paymentMethod.exchange_file,
            bank_card: paymentMethod.bank_card,
            system_attribute: paymentMethod.system_attribute,
            active: paymentMethod.active
        };

        if (language !== config.language.toUpperCase()) {
            const translation = await PaymentMethodTranslationsModel.findOne({
                where: {
                    payment_method_code: paymentMethod.code,
                    language_code: language
                }
            });
            if (translation) {
                result.label = translation.label;
                result.language = language;
                result.requires_bank_account = translation.requires_bank_account;
                result.manual_transaction = translation.manual_transaction;
                result.exchange_file = translation.exchange_file;
                result.bank_card = translation.bank_card;
            }
        }

        res.send({ data: result });
    } catch (error) {
        next(error);
    }
}

async function create(req, res, next) {
    try {
        const  code  = req.body.code;
        const existingPaymentMethod = await PaymentMethodModel.findOne({ where: { code } });
        if (existingPaymentMethod) {
            throw new ConflictError(`Payment method with code ${code} already exists`);
        }
        const newPaymentMethod = await PaymentMethodModel.create(req.body);
        await sendSyncMessage(OperationType.POST, "PaymentMethod", "PaymentMethod", newPaymentMethod);
        res.status(201).send({ data: newPaymentMethod });
    } catch (error) {
        next(error);
    }
}

async function update(req, res, next) {
    try {
        const id = req.params.id;

        const oldPaymentMethod = await PaymentMethodModel.findOne({ where: { id } });
        if (!oldPaymentMethod) {
            throw new NotFoundError(`Payment method with ID ${id} not found`);
        }
        const { code,system_attribute, ...rest } = req.body;
        await oldPaymentMethod.update(rest);
        const updatedPaymentMethod = await PaymentMethodModel.findOne({ where: { id } });
        await sendSyncMessage(OperationType.PUT, "PaymentMethod", "PaymentMethod", updatedPaymentMethod);
        res.send({ data: updatedPaymentMethod });
    } catch (error) {
        next(error);
    }
}

async function remove(req, res, next) {
    try {
        const id = req.params.id;
        const paymentMethod = await PaymentMethodModel.findOne({ where: { id } });
        if (!paymentMethod) {
            throw new NotFoundError(`Payment method with ID ${id} not found`);
        }
        if(paymentMethod.system_attribute) {
            throw new ConflictError("Cannot delete system attribute", "paymentMethod");
        }

        await PaymentMethodModel.destroy({ where: { id } });
        await sendSyncMessage(OperationType.DELETE, "PaymentMethod", "PaymentMethod", paymentMethod);
        res.send({ data: { message: `Resource with ID ${id} has been deleted successfully` } });
    } catch (error) {
        next(error);
    }
}

module.exports = {
    search,
    getOne,
    create,
    update,
    remove,
};
