/**
 * Currency Controller
 */
const { currencyController } = require("../utils/controller-factory");
const { CurrencyModel } = require("../db");
const ConflictError = require("../error/exception/Conflict");
const NotFoundError = require("../error/exception/NotFound");
const OperationType = require("../models/Stream/operationType");
const {sendSyncMessage} = require("../producer/Producer");


async function create(req, res, next) {
    try {
        const { code, default_currency } = req.body;

        // Convert empty string dates to null
        if (req.body.final_effectiveDate === "") {
            req.body.final_effectiveDate = null;
        }
        if (req.body.intermediate_period_start_date === "") {
            req.body.intermediate_period_start_date = null;
        }

        const existingCurrency = await CurrencyModel.findOne({
            where: {
                code
            },
        });
        if (existingCurrency) {
            throw new ConflictError("Currency already exists", "Currency");
        }

        // Handle default currency logic
        if (default_currency === true) {
            // Find and update existing default currency
            await existingCurrency.update(
                { default_currency: false }
            );
        }

        const new_currency = await CurrencyModel.create(req.body);
        await sendSyncMessage(OperationType.POST, "Currency", "Currency", new_currency);
        res.status(201).send({
            data: new_currency
        });
    } catch (error) {
        next(error);
    }
}

async function update(req, res, next) {
    try {
        const id = req.params.id;
        const { code, default_currency } = req.body;

        // Convert empty string dates to null
        if (req.body.final_effectiveDate === "") {
            req.body.final_effectiveDate = null;
        }
        if (req.body.intermediate_period_start_date === "") {
            req.body.intermediate_period_start_date = null;
        }

        const oldCurrency = await CurrencyModel.findOne({
            where: {
                id,
            },
        });
        if (!oldCurrency || oldCurrency.length === 0) {
            throw new NotFoundError("Currency not found", "Currency");
        }

        const existingCurrency = await CurrencyModel.findOne({
            where: {
                code: code
            },
        });
        if (existingCurrency && existingCurrency.id !== parseInt(id)) {
            throw new ConflictError("Currency with matching code already exists", "Currency");
        }

        // Handle default currency logic
        if (default_currency === true && !oldCurrency.default_currency) {
            // Find and update existing default currency
            await existingCurrency.update(
                { default_currency: false }
            );
        }

        await existingCurrency.update(req.body);
        const currency = await CurrencyModel.findOne({
            where: {
                id,
            },
        });
        await sendSyncMessage(OperationType.PUT, "Currency", "Currency", currency);
        res.send({
            data: {...req.body, id: parseInt(id)}
        });
    } catch (error) {
        next(error);
    }
}


module.exports = {
    // Custom create and update method with specific filtering and sorting logic
    create,
    update,

    // Standard CRUD methods using enhanced generic controller
    search: currencyController.search,
    getOne: currencyController.getOne,
    remove: currencyController.remove,
};
