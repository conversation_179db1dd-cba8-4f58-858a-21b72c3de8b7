/**
 * Equipment Controller
 * Uses AbstractControllerFactory which automatically routes to EnhancedGenericController
 * due to NAP, Country, and Market relationships configuration
 */

const { equipmentController } = require("../utils/controller-factory");
const { EquipmentModel, CountriesModel, MarketModel, NapModel } = require("../db");
const NotFoundError = require("../error/exception/NotFound");
const OperationType = require("../models/Stream/operationType");
const {sendSyncMessage} = require("../producer/Producer");

// Custom upload method preserved
async function create(req, res, next) {
  try {
    const {country_code, market_code, category_code} = req.body

    const country = await CountriesModel.findOne({
      where: {
        code: country_code,
      }
    });
    if (!country) throw new NotFoundError("Country doesn't exist", "Country");

    const market = await MarketModel.findOne({
      where: {
        code: market_code,
      }
    });
    if (!market) throw new NotFoundError("Market doesn't exist", "Market");

    const category = await NapModel.findOne({
      where: {
        code: category_code,
      }
    });
    if (!category) throw new NotFoundError("Category doesn't exist", "category");

    const newEquipment = await EquipmentModel.create(req.body);
    await sendSyncMessage(OperationType.POST, "Equipment", "Static-tables", newEquipment);
    res.status(201).send({
      data: {
        id: newEquipment.id,
        ...req.body,
      },
    });
  } catch (err) {
    next(err);
  }
}

async function update(req, res, next) {
  try {
    const id = req.params.id;
    const {country_code, market_code, category_code} = req.body

    const country = await CountriesModel.findOne({
      code: country_code,
    });
    if (!country) throw new NotFoundError("Country doesn't exist", "Country");

    const market = await MarketModel.findOne({
      code: market_code,
    });
    if (!market) throw new NotFoundError("Market doesn't exist", "Market");

    const category = await NapModel.findOne({
      code: category_code,
    });
    if (!category) throw new NotFoundError("Category doesn't exist", "category");

    const oldEquipment = await EquipmentModel.findOne({
      where: {
        id,
      },
    });

    if (!oldEquipment) {
      throw new NotFoundError("Equipment not found", "Equipment");
    }

    await oldEquipment.update(req.body);
    await sendSyncMessage(OperationType.PUT, "Equipment", "Static-tables",
        await EquipmentModel.findOne(
            { where: { id } }
        )
    );
    res.send({
      data: { id: parseInt(id), ...req.body },
    });
  } catch (error) {
    next(error);
  }
}

async function upload(req, res, next) {
  try {
    const { EquipmentModel } = require("../db");
    const {items} = req.body

    await EquipmentModel.bulkCreate(items, { validate: true })

    res.send({
      data: {
        message: `Data has been created successfully`,
      },
    });

  } catch (err) {
    next(err);
  }
}

module.exports = {
  // Custom create and update method with specific filtering and sorting logic
  create,
  update,

  // Standard CRUD methods using enhanced generic controller
  search: equipmentController.search,
  getOne: equipmentController.getOne,
  remove: equipmentController.remove,

  // Custom upload method preserved
  upload,
};
