/**
 * AutoCatalog Controller
 */

const { autoCatalogController } = require("../utils/controller-factory");

const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const { sendSyncMessage } = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");
// Import dependencies for custom methods
const { AutoCatalogModel, SingleAutoModel } = require("../db");
const config = require("../config/app-config");

async function createAutoCatalog(req, res, next) {
    try {
        const { code } = req.body;
        const existing = await AutoCatalogModel.findOne({ where: { code } });
        if (existing) {
            throw new ConflictError("AutoCatalog with matching code already exists", "AutoCatalog");
        }

        const createData = { ...req.body };
        if (createData.start_date === "") {
            createData.start_date = null;
        }
        if (createData.end_date === "") {
            createData.end_date = null;
        }

        const newItem = await AutoCatalogModel.create(createData);
        res.status(201).send({ data: newItem });
    } catch (error) {
        next(error);
    }
}

async function updateAutoCatalog(req, res, next) {
    try {
        const code = req.params.code;
        const item = await AutoCatalogModel.findOne({ where: { code } });
        if (!item) {
            throw new NotFoundError("AutoCatalog not found", "AutoCatalog");
        }

        // Convert empty strings in start_date and end_date to null
        const updatedData = { ...req.body };
        if (updatedData.start_date === "") {
            updatedData.start_date = null;
        }
        if (updatedData.end_date === "") {
            updatedData.end_date = null;
        }

        await item.update(updatedData);
        await sendSyncMessage(OperationType.PUT, "AutoCatalog", "AutoCatalog", item);
        res.send({ data: item });
    } catch (error) {
        next(error);
    }
}

async function getAutoCatalogsWithAutos(req, res, next) {
    try{
        const language = req.query.language
        ? req.query.language.toUpperCase()
        : config.defaultReturnedLanguage;

        // const market_code = req.query.market_code
        // const country_code = req.query.country_code

        // if(!market_code || !country_code ){
        //     return res.send({ data: [] });
        // }

        const result = await AutoCatalogModel.findAll({
            attributes: ["id","code", "label"],
            include:[
                {
                    model: SingleAutoModel,
                    as: "single_autos",
                    attributes: ["id",["reference", "code"], ["brand_label", "label"]],
                }
            ]
        })

        return res.send({data: result})

    }catch(err) {
        next(err);
    }
}

async function getAutoCatalogByCode(req, res, next) {
    try {
        const code = req.params.code;
        const item = await AutoCatalogModel.findOne({ where: { code } });
        if (!item) {
            throw new NotFoundError("AutoCatalog not found", "AutoCatalog");
        }
        res.send({ data: item });
    } catch (error) {
        next(error);
    }
}

module.exports = {
    // Standard CRUD methods using generic controller
    search: autoCatalogController.search,
    getOne: autoCatalogController.getOne,
    create: autoCatalogController.create,
    update: autoCatalogController.update,
    remove: autoCatalogController.remove,

    // Aliases for backward compatibility
    getAllAutoCatalogs: autoCatalogController.search,
    deleteAutoCatalog: autoCatalogController.remove,

    // Custom method preserved
    createAutoCatalog,
    updateAutoCatalog,
    getAutoCatalogsWithAutos,
    getAutoCatalogByCode
};