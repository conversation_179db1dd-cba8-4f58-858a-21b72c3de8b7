const {
    BankModel,
    CountriesModel
} = require("../db");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const {sendSyncMessage} = require('../producer/Producer');
const OperationType = require('../models/Stream/operationType');
const {v4: uuidv4} = require("uuid");
const axios = require('axios');
const ibantools = require('ibantools');

async function search(req, res, next) {
    try {
        let whereCondition = {};
        const limit = req.query.limit ? req.query.limit : config.limit;
        const offset = req.query.offset
            ? req.query.offset * limit
            : config.offset * limit;
        const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
        const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;

        Object.keys(req.query).forEach((key) => {
            if (!["offset", "limit", "sort_by", "order_by"].includes(key)) {
                whereCondition[key] = req.query[key];
            }
        });

        let banks = await BankModel.findAll({
            order: [[sortBy, orderBy]],
            limit,
            offset,
            where: whereCondition,
        });

        let total_count = await BankModel.count({where: whereCondition});

        res.send({data: banks, total_count});
    } catch (error) {
        next(error);
    }
}

async function create(req, res, next) {
    try {
        const {bank_id} = req.body;

        const existingBank = await BankModel.findOne({
            where: {
                bank_id,
            },
        });

        if (existingBank)
            throw new ConflictError("Bank with matching id already exists", "Banks");
        req.body.reference = uuidv4();
        const newBank = await BankModel.create({
            ...req.body,
        });

        // Send a message to the stream
        await sendSyncMessage(
            OperationType.POST,
            "BankBranch",
            "BankBranch",
            newBank
        );

        res.status(201).send({
            data: req.body,
        });
    } catch (error) {
        next(error);
    }
}

async function update(req, res, next) {
    try {
        const bank_id = req.params.bankId;

        const oldBank = await BankModel.findOne({
            where: {
                bank_id,
            },
        });

        if (!oldBank) throw new NotFoundError("Bank not found", "Banks");

        await oldBank.update(req.body);

        // Send a message to the stream
        const updatedBank = oldBank;
        await sendSyncMessage(
            OperationType.PUT,
            "BankBranch",
            "BankBranch",
            updatedBank
        );

        res.send({
            data: {bank_id, ...req.body},
        });
    } catch (error) {
        next(error);
    }
}

async function remove(req, res, next) {
    try {
        const bank_id = req.params.bankId;

        const oldBank = await BankModel.findOne({
            where: {
                bank_id,
            },
        });

        if (!oldBank) throw new NotFoundError("Bank not found", "Banks");

        await BankModel.destroy({
            where: {
                bank_id,
            },
        });

        // Send a message to the stream
        await sendSyncMessage(
            OperationType.DELETE,
            "BankBranch",
            "BankBranch",
            oldBank
        );

        res.send({
            data: {
                message: `Resource with ID ${oldBank.id} has been deleted successfully`,
            },
        });
    } catch (error) {
        next(error);
    }
}

/**
 * Get bank branch information by IBAN
 * @param {Object} req - Request object with IBAN in query params
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 */
async function getBankBranchByInterbankCode(req, res, next) {
    try {
        const {iban} = req.params;

        // Validate IBAN first using ibantools
        if (!ibantools.isValidIBAN(iban)) {
            throw new NotFoundError("Invalid IBAN", "IBAN_VALIDATION");
        }

        // Extract IBAN components using ibantools
        const ibanDetails = ibantools.extractIBAN(iban);

        if (!ibanDetails || !ibanDetails.valid) {
            throw new NotFoundError("Could not parse IBAN details", "IBAN_STRUCTURE");
        }

        const countryCode = ibanDetails.countryCode;
        const bankCode = ibanDetails.bankIdentifier;
        // Some countries don't have branch codes in the ibantools result
        // Use branchIdentifier if available, otherwise default to empty string
        const branchCode = ibanDetails.branchIdentifier || '';

        // Check required IBAN components
        if (!countryCode || !bankCode) {
            throw new NotFoundError("IBAN doesn't have required components", "IBAN_STRUCTURE");
        }

        // Find bank branch in local database first
        const bankBranch = await BankModel.findOne({
            where: {
                country_code: countryCode,
                bank_id: bankCode,
                code_branch: branchCode || '0000' // Fallback if branch code is empty
            }
        });

        // Return local data if found
        if (bankBranch) {
            const bankBranchData = bankBranch.get({plain: true});

            const countryTranslation = await CountriesModel.findOne({
                where: {
                    code: bankBranchData.country_code,
                }
            });

            bankBranchData.country = countryTranslation ? countryTranslation.get('label') : bankBranchData.country_code;
            const {createdAt, updatedAt, ...bankData} = bankBranch.get({plain: true});

            return res.status(200).send({
                data: bankData
            });
        }

        // Not found locally, fetch from API
        const branchDto = await fetchBankBranchFromApi(iban);

        // Check API response
        if (!branchDto.data || !branchDto.data.bank) {
            throw new NotFoundError("Cannot find bank branch information", "BANK_BRANCH");
        }

        // Create bank branch DTO
        const bankBranchStreamDto = createBankBranchStreamDto(
            countryCode,
            bankCode,
            branchCode || '0000', // Fallback if branch code is empty
            branchDto
        );

        // Save to database
        await saveBankBranchToStaticTables(bankBranchStreamDto);

        // Format the response to match the database structure
        // This ensures consistency between API and local data responses
        const bankBranchResponse = {
            id: null, // Will be assigned when saved to database
            reference: bankBranchStreamDto.reference,
            bank_id: bankBranchStreamDto.bank_id,
            country_code: bankBranchStreamDto.country_code,
            bank_name: bankBranchStreamDto.bank_name,
            code_swift: bankBranchStreamDto.code_swift,
            code_branch: bankBranchStreamDto.code_branch,
            city: bankBranchStreamDto.city,
            postal_code: bankBranchStreamDto.postal_code,
            address: bankBranchStreamDto.address,
            second_address: null, // Include all fields from the model
            country: branchDto.data.countryName // Include translated country name
        };

        return res.status(200).send({
            data: bankBranchResponse
        });
    } catch (error) {
        console.error(`Error fetching bank branch information: ${error.message}`);
        next(error);
    }
}

/**
 * Fetch bank branch information from external API
 * @param {string} iban - IBAN string
 * @returns {Promise<Object>} - API response object
 */
async function fetchBankBranchFromApi(iban) {
    try {
        // Get API key from env or config
        const apiKey = process.env.IBANAPI_KEY || config.ibanApiKey;
        const apiUrl = `https://api.ibanapi.com/v1/validate/${iban}?api_key=${apiKey}`;

        const response = await axios.get(apiUrl, {
            headers: {
                'Accept': 'application/json'
            }
        });

        return response.data;
    } catch (error) {
        console.error(`Error calling IBAN API: ${error.message}`);
        throw new NotFoundError(`Failed to fetch bank information: ${error.message}`);
    }
}

/**
 * Create bank branch data transfer object
 * @param {string} countryCode - Country code from IBAN
 * @param {string} bankCode - Bank code from IBAN
 * @param {string} branchCode - Branch code from IBAN
 * @param {Object} response - API response object (matches IbanApiResponse.java structure)
 * @returns {Object} - Bank branch DTO
 */
function createBankBranchStreamDto(countryCode, bankCode, branchCode, response) {
    // Validate that the response has the expected structure
    if (!response || !response.data || !response.data.bank) {
        console.warn('API response missing expected structure for bank data');
        throw new NotFoundError("Invalid API response structure", "BANK_BRANCH");
    }

    // Map from IbanApiResponse structure to BankBranchStreamDto
    return {
        bank_id: bankCode,
        country_code: countryCode,
        bank_name: response.data.bank.bankName,
        code_swift: response.data.bank.bic,
        code_branch: branchCode,
        city: response.data.bank.city,
        postal_code: response.data.bank.zip,
        address: response.data.bank.address,
        reference: uuidv4()
    };
}

/**
 * Save bank branch information to local database
 * @param {Object} dto - Bank branch data transfer object
 * @returns {Promise<boolean>} - Success status
 */
async function saveBankBranchToStaticTables(dto) {
    try {
        // Check if bank branch already exists
        const existingBank = await BankModel.findOne({
            where: {
                bank_id: dto.bank_id,
                country_code: dto.country_code,
                code_branch: dto.code_branch
            }
        });

        if (existingBank) {
            return true;
        }

        // Create new bank branch record
        const newBank = await BankModel.create(dto);

        // Send stream message about the new bank
        await sendSyncMessage(
            OperationType.POST,
            "BankBranch",
            "BankBranch",
            newBank
        );

        return true;
    } catch (error) {
        console.error(`Failed to save bank branch information: ${error.message}`);
        throw new Error(`Failed to save bank branch information: ${error.message}`);
    }
}

module.exports = {
    search,
    create,
    update,
    remove,
    getBankBranchByInterbankCode
};
