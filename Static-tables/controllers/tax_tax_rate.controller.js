const {TaxrateModel, TaxModel} = require("../db");
const moment = require("moment");
const { Op } = require("sequelize");
const NotFoundError = require("../error/exception/NotFound");
const OperationType = require('../models/Stream/operationType');
const {sendSyncMessage} = require('../producer/Producer');
const { v4: uuidv4 } = require('uuid');

//GET::/api/v1/static-tables/taxes/{taxecode}/tax_rates
async function getTax_rateByTaxCode(req, res, next) {
    try {
        const {taxecode} = req.params;
        const {active} = req.query; 
        const currentDate = moment().format('YYYY-MM-DD');
        
        let whereClause = {
            tax_code: taxecode,
        };

        // If active is true, only return currently active rates
        if (active === 'true') {
            whereClause.active = true;
            whereClause = {
                ...whereClause,
                [Op.and]: [
                    {
                        [Op.or]: [
                            { start_date: null },
                            { start_date: { [Op.lte]: currentDate } }
                        ]
                    },
                    {
                        [Op.or]: [
                            { end_date: null },
                            { end_date: { [Op.gte]: currentDate } }
                        ]
                    }
                ]
            };
        }

        const tax_rates = await TaxrateModel.findAll({
            where: whereClause,
            attributes: [
                "tax_code",
                "code",
                "rate",
                "active",
                "start_date",
                "end_date",
                "creationDate",
                "reference",
            ],
        });

        const response = {
            data: tax_rates || [],
            total_count: tax_rates.length,
        };

        // Sending the response
        res.send(response);
    } catch (error) {
        next(error);
    }
}

//GET::/api/v1/static-tables/taxes/{taxecode}/tax_rates/{Taxratecode}
async function getTaxRateByTaxRateCodeAndTaxCode(req, res, next) {
    try {
        const {taxecode, Taxratecode} = req.params;
        const tax_rate = await TaxrateModel.findOne({
            where: {tax_code: taxecode, code: Taxratecode},
        });

        if (!tax_rate || tax_rate.length === 0) {
            throw new NotFoundError("Tax Rate not found", "Tax Rate");
        } else {
            return res.send({
                data: {
                    tax_rates: tax_rate,
                },
            });
        }
    } catch (error) {
        next(error);
    }
}

//POST::/api/v1/static-tables/taxes/{taxecode}/tax_rates
//body {"rates": [{"active": true, "rate": 1, "end_date": "2025-01-29", "start_date": "2025-02-07"}]}
async function createTaxRatesForTax(req, res, next) {
    try {
        const taxecode = req.params.taxecode;
        const {rates} = req.body;

        if (!rates || rates.length === 0) {
            return res.status(400).json({
                error: "No rates provided"
            });
        }

        const existingTax = await TaxModel.findOne({where: {code: taxecode}});

        if (!existingTax) {
            throw new NotFoundError("No tax found", "Tax");
        }

        const processedTaxRates = [];

        for (const rate of rates) {
            // Convert empty strings to null for start_date and end_date
            const startDate = !rate.start_date || rate.start_date === "" ? null : rate.start_date;
            const endDate = !rate.end_date || rate.end_date === "" ? null : rate.end_date;
            const creationDate = !rate.creationDate
                ? moment().toDate()
                : moment(rate.creationDate, "YYYY-MM-DD").toDate();

            let processedRate;
            let existingTaxRate = null;

            // Check if tax rate exists by reference (only if reference is provided)
            if (rate.reference && rate.reference.trim() !== '') {
                existingTaxRate = await TaxrateModel.findOne({
                    where: { reference: rate.reference }
                });
            }

            if (existingTaxRate) {
                // Update existing tax rate
                await existingTaxRate.update({
                    tax_code: taxecode,
                    code: rate.code,
                    start_date: startDate,
                    end_date: endDate,
                    label: `${taxecode}-${rate.rate}`,
                    rate: rate.rate,
                    active: rate.active,
                    creationDate: creationDate,
                });
                
                processedRate = existingTaxRate;
                await sendSyncMessage(OperationType.PUT, "TaxRate", "TaxRate", processedRate);
            } else {
                // Create new tax rate
                const newReference = uuidv4();
                                
                if (!newReference) {
                    throw new Error('Failed to generate UUID reference');
                }
                
                const uniqueCode = rate.code || `${taxecode}-${newReference.split('-')[0]}`;
                
                const taxRateData = {
                    reference: newReference,
                    tax_code: taxecode,
                    code: uniqueCode,
                    start_date: startDate,
                    end_date: endDate,
                    label: `${taxecode}-${rate.rate}`,
                    rate: rate.rate,
                    active: rate.active !== undefined ? rate.active : false,
                    creationDate: creationDate,
                };

                // Validate that reference is not null before creating
                if (!taxRateData.reference) {
                    throw new Error('Reference cannot be null when creating tax rate');
                }
                
                processedRate = await TaxrateModel.create(taxRateData);
                
                await sendSyncMessage(OperationType.POST, "TaxRate", "TaxRate", processedRate);
            }

            processedTaxRates.push(processedRate);
        }

        res.status(201).json({
            data: { 
                processed: processedTaxRates,
                message: `Processed ${processedTaxRates.length} tax rates for tax code: ${taxecode}`
            }
        });
    } catch (error) {
        next(error);
    }
}

// DELETE::/api/v1/static-tables/taxes/{taxecode}/tax_rates/{Taxratecode}
async function deleteTaxByTAxCodeAndTAx_rateCode(req, res, next) {
    try {
        const taxecode = req.params.taxecode;
        const Taxratecode = req.params.Taxratecode;

        const numRowsDeleted = await TaxrateModel.destroy({
            where: {tax_code: taxecode, code: Taxratecode},
        });
        await sendSyncMessage(OperationType.DELETE, "TaxRate", "TaxRate", {tax_code: taxecode, code: Taxratecode});

        if (numRowsDeleted === 0) {
            throw new NotFoundError(
                "Tax not found for the given tax CODE and tax_rate CODE",
                "Tax"
            );
        }

        res.send({
            data: {
                message: `Resource with tax code: ${taxecode} and tax_rate code: ${Taxratecode} has been deleted successfully`,
            },
        });
    } catch (error) {
        next(error);
    }
}

// DELETE::/api/v1/static-tables/taxes/{taxecode}/tax_rates
async function deleteAllTaxRatesByTaxCode(req, res, next) {
    try {
        const {taxecode} = req.params;

        const numRowsDeleted = await TaxrateModel.destroy({
            where: {tax_code: taxecode},
        });
        await sendSyncMessage(OperationType.DELETE, "TaxRate", "TaxRate", {tax_code: taxecode});

        if (numRowsDeleted === 0) {
            throw new NotFoundError(
                "No tax rates found for the given tax code",
                "Tax Rate"
            );
        }

        res.send({
            data: {
                message: `All tax rates with tax code: ${taxecode} have been deleted successfully`,
                count: numRowsDeleted,
            },
        });
    } catch (error) {
        next(error);
    }
}

module.exports = {
    getTax_rateByTaxCode,
    getTaxRateByTaxRateCodeAndTaxCode,
    createTaxRatesForTax,
    deleteTaxByTAxCodeAndTAx_rateCode,
    deleteAllTaxRatesByTaxCode,
};
