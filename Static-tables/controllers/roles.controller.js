const { RoleModel, RoleTranslationsModel, StaticRoleModel, StaticRoleTranslationsModel } = require("../db");
const { Op } = require("sequelize");
const config = require("../config/app-config");
const { roleController } = require("../utils/controller-factory");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const OperationType = require("../models/Stream/operationType");
const {sendSyncMessage} = require("../producer/Producer");


// Custom search method with specific filtering and sorting logic for roles
async function search(req, res, next) {
  try {
    let whereCondition = {};
    let translationWhereCondition = {};
    let staticRoleWhereCondition = {};
    const limit = req.query.limit ? req.query.limit : config.limit;
    const offset = req.query.offset
      ? req.query.offset 
      : config.offset ;
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;
    const language = req.query.language
      ? req.query.language.toUpperCase()
      : config.defaultReturnedLanguage;

    // Custom filtering logic for roles
    Object.keys(req.query).forEach((key) => {
      if (
        !["offset", "limit", "sort_by", "order_by", "language"].includes(key)
      ) {
        if (key === "associated_to") {
          staticRoleWhereCondition[key] = {
            [Op.substring]: req.query[key]
          };
        } else if (
          language === config.language ||
          key !== "label"
        ) {
          whereCondition[key] = req.query[key];
        } else {
          translationWhereCondition[key] = req.query[key];
        }
      }
    });

    // Custom sorting logic for roles
    const getSortingOrder = (sortBy, orderBy) => {
      if (language !== config.language && sortBy === "label") {
        return [[RoleTranslationsModel, "label", orderBy]];
      }
      if (sortBy === "role" && language !== config.language) {
        return [[StaticRoleModel, StaticRoleTranslationsModel, "static_role_code", orderBy]];
      }
      if (sortBy === "role" && language === config.language) {
        return [[StaticRoleModel, "code", orderBy]];
      }
      return [[sortBy, orderBy]];
    };

    let roles, total_count;

    [roles, total_count] = await Promise.all(
      language === config.language
        ? [
            RoleModel.findAll({
              order: getSortingOrder(sortBy, orderBy),
              offset,
              limit,
              where: whereCondition,
              include: {
                model: StaticRoleModel,
                where: staticRoleWhereCondition,
              },
            }),
            RoleModel.count({ where: whereCondition }),
          ]
        : [
            await RoleModel.findAll({
              order: getSortingOrder(sortBy, orderBy),
              offset,
              limit,
              where: whereCondition,
              include: [
                {
                  model: RoleTranslationsModel,
                  attributes: ["label"],
                  where: translationWhereCondition,
                },
                {
                  model: StaticRoleModel,
                  where: staticRoleWhereCondition,
                  include: {
                    model: StaticRoleTranslationsModel,
                    attributes: ["label"],
                  },
                },
              ],
            }),
            RoleModel.count({
              where: whereCondition,
              include: [
                {
                  model: RoleTranslationsModel,
                  where: translationWhereCondition,
                },
                {
                  model: StaticRoleModel,
                  where: staticRoleWhereCondition,
                },
              ],
            }),
          ]
    );

    roles = roles.map((item) => {
      return {
        id: item.id,
        code: item.code,
        label: item.role_translations?.[0]?.label || item.label,
        active: item.active,
        system_attribute: item.system_attribute,
        static_role: {
          id: item.static_role?.id,
          code: item.static_role?.code,
          label:
            item.static_role?.static_role_translations?.[0]?.label ||
            item.static_role?.label,
          is_exclusive: item.static_role?.is_exclusive,
          is_client: item.static_role?.is_client,
          associated_to: item.static_role?.associated_to,
        },
      };
    });

    res.send({ data: roles, total_count });
  } catch (error) {
    next(error);
  }
}

async function create(req, res, next) {
  try {
    const {
      code,
      label,
      active,
      static_role_code,
      language = config.defaultReturnedLanguage,
    } = req.body;
    const isDefaultLanguage = language.toUpperCase() === config.language;

    const existingRole = await RoleModel.findOne({
      where: {
        code,
      },
    });

    if (existingRole) {
      throw new ConflictError("Role already exists", "Role");
    }

    const staticRole = await StaticRoleModel.findOne({
      where: {
        code: static_role_code
      }
    })

    if(!staticRole){
      throw new ConflictError("Static Role does not exist", "Static Role");

    }

    const new_role = await RoleModel.create({
      code,
      active,
      static_role_code,
      label: isDefaultLanguage ? label : "",
    });

    new_role.label = label;
    await sendSyncMessage(OperationType.POST, "Role", "Role", new_role);

    if (new_role)
      await RoleTranslationsModel.create({
        role_code: new_role.code,
        label: !isDefaultLanguage ? label : "",
        language_code: !isDefaultLanguage ? language.toUpperCase() : "",
      });

    res.status(201).send({
      id: new_role.id,
      ...req.body,
    });
  } catch (error) {
    next(error);
  }
}

async function update(req, res, next) {
  try {
    const id = req.params.id;
    const { code, label, static_role_code,language = config.defaultReturnedLanguage } = req.body;
    const isDefaultLanguage = language.toUpperCase() === config.language;

    const oldRole = await RoleModel.findOne({
      where: {
        id,
      },
    });

    if (!oldRole) {
      throw new NotFoundError("Role not found", "Role");
    }
    const existingRole = await RoleModel.findOne({
      where: {
        code: code,
      },
    });

    if (existingRole && existingRole.id !== parseInt(id)) {
      throw new ConflictError("Role with matching code already exists", "Role");
    }

    if (oldRole.code !== code && oldRole.system_attribute) {
      throw new ConflictError("Role cannot be modified", "Role");
    }

    const staticRole = await StaticRoleModel.findOne({
      where: {
        code: static_role_code
      }
    })

    if(!staticRole){
      throw new ConflictError("Static Role does not exist", "Static Role");

    }

    await oldRole.update(req.body);

    await sendSyncMessage(OperationType.PUT, "Role", "Role", {
      ...req.body,
      id,
    });

    const existingTranslatedRole = await RoleTranslationsModel.findOne({
      where: {
        role_code: oldRole.code,
      },
    });

    if (existingTranslatedRole)
      await existingTranslatedRole.update({
        role_code: code,
        label: !isDefaultLanguage ? label : existingTranslatedRole.label,
      });

    res.send({
      data: { id: parseInt(id), ...req.body },
    });
  } catch (error) {
    next(error);
  }
}


//get static roles from static-role table
async function getStaticRoles(req, res, next) {
  try {
    let whereCondition = {};
    let translationWhereCondition = {};
    const limit = req.query.limit ? req.query.limit : config.limit;
    const offset = req.query.offset
      ? req.query.offset 
      : config.offset ;
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;
    const language = req.query.language
      ? req.query.language.toUpperCase()
      : config.defaultReturnedLanguage;

    Object.keys(req.query).forEach((key) => {
      if (
        !["offset", "limit", "sort_by", "order_by", "language", "active"].includes(key)
      ) {
        if(key === "associated_to"){
          whereCondition[key] = {
            [Op.substring]: req.query[key]
          };
        } else if (language === config.language || key !== "label") {
            whereCondition[key] = req.query[key];
        } else {
          translationWhereCondition[key] = req.query[key];
        }
      }
    });

    const getSortingOrder = (sortBy, orderBy) => {
      if (sortBy === "label") {
        return [[StaticRoleTranslationsModel, "label", orderBy]];
      }
      return [[sortBy, orderBy]];
    };

    let roles, total_count;

    [roles, total_count] = await Promise.all(
      language === config.language
        ? [
            StaticRoleModel.findAll({
              order: [[sortBy, orderBy]],
              offset,
              limit,
              where: whereCondition,
            }),
            StaticRoleModel.count({ where: whereCondition }),
          ]
        : [
            await StaticRoleModel.findAll({
              order: getSortingOrder(sortBy, orderBy),
              offset,
              limit,
              where: whereCondition,
              include: [
                {
                  model: StaticRoleTranslationsModel,
                  where: translationWhereCondition,
                },
              ],
              raw: true,
            }),
            StaticRoleModel.count({
              where: whereCondition,
              include: {
                model: StaticRoleTranslationsModel,
                whereCondition: translationWhereCondition,
              },
            }),
          ]
    );

    roles = roles.map((item) => {
      return {
        id: item.id,
        code: item.code,
        label: item["static_role_translations.label"] || item.label,
        is_exclusive: item.is_exclusive,
        is_client: item.is_client,
        associated_to: item.associated_to,
      };
    });
    res.send({ data: roles, total_count });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  // Custom search method with specific filtering and sorting logic
  search,
  create,
  update,

  // Standard CRUD methods using generic controller
  getOne: roleController.getOne,
  remove: roleController.remove,

  // Custom static roles method
  getStaticRoles,
};
