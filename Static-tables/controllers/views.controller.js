const { ViewModel } = require("../db");
const config = require("../config/app-config");

async function getAllViews(req, res, next) {
    try {
        let whereCondition = {};
        const limit = req.query.limit ? req.query.limit : config.limit;
        const offset = req.query.offset ? req.query.offset*limit : config.offset*limit;
        //const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
        // const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;

        console.log("default",config.offset,config.limit,config.SortBy,config.OrderBy)
        // Build the where condition dynamically based on query parameters
        Object.keys(req.query).forEach(key => {
            if (key !== "offset" && key !== "limit" && key !== "sort_by" && key !== "order_by") {
                whereCondition[key] = req.query[key];
            }
        });
        const { count, rows: views } = await ViewModel.findAndCountAll({
            attributes: ["collection", "code", "label"], // Select only necessary columns
            offset,
            limit,
            where: whereCondition
        });

        if (views.length === 0) {
            // No views found
            return res.status(404).json({
                error: "Views not found",
            });
        }

        res.json({ data: views, total_count: count });

    } catch (error) {
        next(error)
    }
}


module.exports = {
    getAllViews,
};
