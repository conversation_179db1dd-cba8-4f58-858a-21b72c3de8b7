/**
 * NAF Controller
 */
const { NafModel, NafTranslationsModel } = require("../db");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const {sendSyncMessage} = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");
const { nafController } = require("../utils/controller-factory");


async function update(req, res, next) {
  try {
    const code = req.params.code;
    const { label, language = config.defaultReturnedLanguage, country_code } = req.body;
    const isDefaultLanguage = language.toUpperCase() === config.language;

    const oldNaf = await NafModel.findOne({
      where: { code },
    });

    if (!oldNaf) {
      throw new NotFoundError("Naf not found", "Naf");
    }

    await oldNaf.update(
        {
          label: isDefaultLanguage ? label : oldNaf.label,
          country_code
        }
    );

    const existingTranslatedNaf = await NafTranslationsModel.findOne({
      where: {
        naf_code: code,
      },
    });

    if (existingTranslatedNaf)
      await existingTranslatedNaf.update(
          {
            label: !isDefaultLanguage ? label : existingTranslatedNaf.label,
          }
      );

    const updatedNaf = await NafModel.findOne({
      where: { code },
    });
    await sendSyncMessage(
        OperationType.PUT,
        "Naf",
        "nafs",
        updatedNaf
    );
    res.send({
      data: updatedNaf,
    });
  } catch (error) {
    next(error);
  }
}


async function remove(req, res, next) {
  try {
    const code = req.params.code;

    const naf = await NafModel.findOne({
      where: { code },
    });

    if (!naf) {
      throw new NotFoundError("Naf not found", "Naf");
    }

    if (naf.system_attribute) {
      throw new ConflictError("Cannot remove system attribute", "Naf");
    }
    await naf.destroy();

    await sendSyncMessage(
        OperationType.DELETE,
        "Naf",
        "nafs",
        naf
    );
    res.send({
      data: {
        message: `Naf with code ${code} has been removed successfully`,
      },
    });
  } catch (error) {
    next(error);
  }
}

// Custom upload method preserved

async function upload(req, res, next) {
  try {
    const { NafModel, NafTranslationsModel } = require("../db");
    const { items } = req.body;

    const createdItems = await NafModel.bulkCreate(items, {
      validate: true,
      returning: true,
    });

    const translations = createdItems.map((item) => ({
      naf_code: item.code,
      label: item.label,
      language_code: "FR",
    }));

    await NafTranslationsModel.bulkCreate(translations, { validate: true });

    res.send({
      data: {
        message: `Data has been created successfully`,
      },
    });
  } catch (err) {
    next(err);
  }
}

module.exports = {
  // Standard CRUD methods using enhanced generic controller
  search: nafController.search,
  getOne: nafController.getOne,
  create: nafController.create,

  // Custom upload method preserved
  update,
  remove,
  upload,
};
