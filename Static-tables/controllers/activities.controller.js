/**
 * Activities Controller
 */

const { activityController } = require("../utils/controller-factory");

// Import dependencies for custom methods
const {
  ActivityModel,
  ProductsModel,
  ProductTranslationsModel,
} = require("../db");
const { ActivityTranslation } = require("../db");
const { Sequelize, Op } = require("sequelize");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const OperationType = require("../models/Stream/operationType");
const { sendSyncMessage } = require("../producer/Producer");

// Custom method: Get activities with their products
async function create(req, res, next) {
  try {
    const { code, associated_to } = req.body;
    const existingActivity = await ActivityModel.findOne({
      where: {
        code,
        associated_to,
      },
    });
    if (existingActivity) {
      throw new ConflictError(
          "Activity with matching code already exists",
          "activities"
      );
    }
    const new_activity = await ActivityModel.create(req.body);
    await sendSyncMessage(
        OperationType.POST,
        "Activity",
        "Activity",
        new_activity
    );
    res.status(201).send({
      data: new_activity,
    });
  } catch (err) {
    next(err);
  }
}

async function update(req, res, next) {
  try {
    const id = req.params.id;

    const { code, active, associated_to } = req.body;
    const oldActivity = await ActivityModel.findOne({
      where: {
        id,
      },
    });
    if (!oldActivity || oldActivity.length === 0) {
      throw new NotFoundError("Activity not found", "activity");
    }
    const existingActivity = await ActivityModel.findOne({
      where: {
        code: code,
        associated_to: associated_to,
      },
    });
    if (existingActivity && existingActivity.id !== parseInt(id)) {
      throw new ConflictError(
          "Activity with matching code already exists",
          "activity"
      );
    }
    await oldActivity.update({
      active,
    });
    const updatedActivity = await ActivityModel.findOne({
      where: {
        id,
      },
    });
    await sendSyncMessage(OperationType.PUT, "Activity", "Activity", updatedActivity);
    res.send({
      data: { ...req.body, id: parseInt(id) },
    });
  } catch (error) {
    next(error);
  }
}

async function getActivitiesAndProducts(req, res, next) {
  try {
    let whereCondition = {
      associated_to: "DOSSIER",
      active: true,
    };
    const limit = req.query.limit ? req.query.limit : config.limit;
    const offset = req.query.offset
      ? req.query.offset * limit
      : config.offset * limit;
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;
    language = req.query.language
      ? req.query.language
      : config.defaultReturnedLanguage;

    Object.keys(req.query).forEach((key) => {
      if (
        key !== "offset" &&
        key !== "limit" &&
        key !== "sort_by" &&
        key !== "order_by" &&
        key !== "language"
      ) {
        whereCondition[key] = req.query[key];
      }
    });

    let result, total_count;

    [result, total_count] = await Promise.all(
      language.toUpperCase() === config.language
        ? [
            ActivityModel.findAll({
              where: whereCondition,
              order: [[sortBy, orderBy]],
              offset,
              limit,
              include: {
                model: ProductsModel,
                where: {
                  active: true,
                },
              },
            }),
            ActivityModel.count({ where: whereCondition }),
          ]
        : //FIXME - filter with label for translation
          [
            ActivityModel.findAll({
              where: whereCondition,
              order: [[sortBy, orderBy]],
              offset,
              limit,
              include: [
                {
                  model: ActivityTranslation,
                  attributes: ["label"],
                  on: {
                    "$activity.code$": {
                      [Op.eq]: Sequelize.col(
                        "activity_translations.activity_code"
                      ),
                    },
                  },
                },
                {
                  model: ProductsModel,
                  where: {
                    active: true,
                  },
                  include: {
                    model: ProductTranslationsModel,
                    attributes: ["label"],
                    on: {
                      "$products.code$": {
                        [Op.eq]: Sequelize.col(
                          "products->product_translations.product_code"
                        ),
                      },
                    },
                  },
                },
              ],
            }),
            ActivityModel.count({ where: whereCondition }),
          ]
    );

    if (language.toUpperCase() !== config.language) {
      result = result.map((activity) => {
        return {
          id: activity.id,
          code: activity.code,
          label: activity.activity_translations?.[0]?.label || activity.label,
          associated_to: activity.associated_to,
          active: true,
          system_attribute: activity.system_attribute,
          products: activity.products.map((product) => ({
            id: product.id,
            code: product.code,
            label: product.product_translations?.[0]?.label || product.label,
            activity_id: product.activity_id,
            start_date: product.start_date,
            end_date: product.end_date,
            active: product.active,
            system_attribute: product.system_attribute,
          })),
        };
      });
    }

    res.send({
      data: result,
      total_count,
    });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  // Custom create and update method with specific filtering and sorting logic
  create,
  update,

  // Standard CRUD methods using generic controller
  search: activityController.search,
  getOne: activityController.getOne,
  remove: activityController.remove,

  // Custom method preserved
  getActivitiesAndProducts,
};
