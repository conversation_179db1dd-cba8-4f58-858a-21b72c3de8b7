const {
  ServiceModel,
  ServiceOperationModel,
  ServiceProductModel,
  ServiceNapModel,
  ServiceActorModel,
  PartnerModel,
  TaxModel,
  OperationModel,
  ProductsModel,
  NapModel,
  CurrencyModel,
  OperationNatureMappingModel,
  OperationNatureTypeMappingModel,
  NatureModel,
  TypeModel,
  ServiceServicesPackModel,
  sequelize,
} = require("../db");
const { Sequelize, Op, where } = require("sequelize");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const { v4: uuidv4 } = require("uuid");
const { sendSyncMessage } = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");

async function createService(req, res, next) {
  const payload = req.body;
  console.debug("Received payload:", payload); // Debug: Log the incoming payload

  const transaction = await sequelize.transaction();
  console.debug("Transaction started"); // Debug: Log transaction start
  try {
    // Create partner first (if actor_reference is provided)
    let partnerReference = null;

    if (payload.actor_reference) {
      // Create the partner record
      const partnerData = {
        reference: uuidv4(),
        actor_reference: payload.actor_reference,
        operation_nature_type_code: payload.operation_nature_type_code,
        currency_code: payload.partner_currency_code,
        mandate_type: payload.mandate_type,
        external_reference: payload.external_reference,
        remittance_method: payload.remittance_method,
        calculation_method: payload.partner_calculation_method,
        amount_excl_tax: payload.partner_amount_excl_tax,
        basis_of_calculation: payload.partner_basis_of_calculation,
        tax_code: payload.partner_tax_code,
        calculation_percentage: payload.partner_percentage_calculation,
        tax_value: payload.partner_tax_value,
      };

      // Pass the transaction but don't tell Sequelize to auto-set other fields
      const partner = await PartnerModel.create(partnerData, {
        transaction,
        fields: Object.keys(partnerData), // Explicitly specify which fields to set
      });
      partnerReference = partner.reference;
        await sendSyncMessage(
            OperationType.POST,
            "Partner",
            "Static-tables",
            partner);
    }

    // Now create service with the partner reference
    // Validate that minimum values are not greater than maximum values
    if (payload.minimum_asset_price && payload.maximum_asset_price &&
      parseFloat(payload.minimum_asset_price) > parseFloat(payload.maximum_asset_price)) {
      throw new ConflictError("Minimum asset price cannot be greater than maximum asset price", "Service");
    }

    if (payload.minimum_contract_duration && payload.maximum_contract_duration &&
      parseInt(payload.minimum_contract_duration) > parseInt(payload.maximum_contract_duration)) {
      throw new ConflictError("Minimum contract duration cannot be greater than maximum contract duration", "Service");
    }

    // Generate the reference
    const newServiceReference = uuidv4();

    const serviceData = {
      reference: newServiceReference,
      code: `SRV_${newServiceReference.split('-')[0]}`,
      intended_for: payload.intended_for,
      label: payload.label,
      type_of_service: payload.type_of_service,
      type_of_cover: payload.type_of_cover,
      start_date: payload.start_date === "" ? null : payload.start_date,
      end_date: payload.end_date === "" ? null : payload.end_date,
      currency_code: payload.currency_code,
      status: payload.status,
      out_of_contract_termination: payload.out_of_contract_termination,
      maximum_asset_price: payload.maximum_asset_price,
      minimum_asset_price: payload.minimum_asset_price,
      maximum_contract_duration: payload.maximum_contract_duration,
      minimum_contract_duration: payload.minimum_contract_duration,
      is_enterprise: payload.is_enterprise,
      is_enterprise_individuelle: payload.is_enterprise_individuelle,
      is_enterprise_publique: payload.is_enterprise_publique,
      is_particulier: payload.is_particulier,
      is_registrable: payload.is_registrable,
      is_unregistrable: payload.is_unregistrable,
      billing_method: payload.billing_method,
      calculation_method: payload.calculation_method,
      amount_excl_tax: payload.amount_excl_tax,
      basis_of_calculation: payload.basis_of_calculation,
      tax_code: payload.tax_code,
      calculation_percentage: payload.calculation_percentage,
      tax_value: payload.tax_value,
      partner_reference: partnerReference,
    };

    // Create the service record
    const service = await ServiceModel.create(serviceData, { transaction });
    const serviceReference = service.reference;
    await sendSyncMessage(
        OperationType.POST,
        "Service",
        "Static-tables",
        service);

    // Replace the operations section with operation_nature_types
    if (payload.operation_nature_types && payload.operation_nature_types.length > 0) {
      const uniqueOperationNatureTypes = [...new Set(payload.operation_nature_types)];

      // Validate that all operation nature type codes exist
      const existingOperationNatureTypes = await OperationNatureTypeMappingModel.findAll({
        where: {
          operation_nature_type_code: { [Op.in]: uniqueOperationNatureTypes }
        },
        attributes: ['operation_nature_type_code'],
        raw: true,
        transaction
      });

      const existingCodes = existingOperationNatureTypes.map(ont => ont.operation_nature_type_code);
      const invalidCodes = uniqueOperationNatureTypes.filter(code => !existingCodes.includes(code));

      if (invalidCodes.length > 0) {
        throw new ConflictError(
          `Invalid operation nature type codes: ${invalidCodes.join(', ')}. These codes do not exist.`,
          "Service"
        );
      }

      await Promise.all(
        uniqueOperationNatureTypes.map(async (operationNatureTypeCode) => {
          const [association, created] = await ServiceOperationModel.findOrCreate({
            where: {
              service_reference: serviceReference,
              operation_nature_type_code: operationNatureTypeCode,
            },
            defaults: {
              reference: uuidv4(),
            },
            transaction,
          });
          await sendSyncMessage(
            OperationType.POST,
            "ServiceOperation",
            "Static-tables",
            association
          );
        })
      );
    }

    // Associate products with the service
    if (payload.products && payload.products.length > 0) {
      // Remove duplicates from the products array
      const uniqueProducts = [
        ...new Set(payload.products),
      ];

      await Promise.all(
        uniqueProducts.map(async (productCode) => {
          try {
            // Use findOrCreate for an atomic operation that handles race conditions
            const [association, created] =
              await ServiceProductModel.findOrCreate({
                where: {
                  service_reference: serviceReference,
                  product_code: productCode,
                },
                defaults: {
                  reference: uuidv4(),
                },
                transaction,
              });
            await sendSyncMessage(
                OperationType.POST,
                "ServiceProduct",
                "Static-tables",
                association);
          } catch (err) {
            // Log the error but don't throw
            console.error(
              `Error handling product ${productCode}:`,
              err.message
            );
            // We'll continue processing other products
          }
        })
      );
    }

    // Associate NAPs with the service
    if (payload.naps && payload.naps.length > 0) {
      // Remove duplicates from the naps array
      const uniqueNaps = [...new Set(payload.naps)];

      await Promise.all(
        uniqueNaps.map(async (napCode) => {
          try {
            // Use findOrCreate for an atomic operation that handles race conditions
            const [association, created] = await ServiceNapModel.findOrCreate({
              where: {
                service_reference: serviceReference,
                nap_code: napCode,
              },
              defaults: {
                reference: uuidv4(),
              },
              transaction,
            });
            await sendSyncMessage(
                OperationType.POST,
                "ServiceNap",
                "Static-tables",
                association);
          } catch (err) {
            // Log the error but don't throw
            console.error(`Error handling NAP ${napCode}:`, err.message);
            // We'll continue processing other NAPs
          }
        })
      );
    }

    // Associate actors with the service
    if (payload.actors && payload.actors.length > 0) {
      // Remove duplicates from the actors array
      const uniqueActors = [...new Set(payload.actors)];

      await Promise.all(
        uniqueActors.map(async (actor) => {
          try {
            // Use findOrCreate for an atomic operation that handles race conditions
            const [association, created] = await ServiceActorModel.findOrCreate(
              {
                where: {
                  service_reference: serviceReference,
                  actor_code: actor,
                },
                defaults: {
                  reference: uuidv4(),
                },
                transaction,
              }
            );
            await sendSyncMessage(
                OperationType.POST,
                "ServiceActor",
                "Static-tables",
                association);
          } catch (err) {
            console.error(`Error handling actor ${actor}:`, err.message);
          }
        })
      );
    }

    // Commit the transaction
    await transaction.commit();
    res.status(201).send({
      data: service,
      message: "Service created successfully",
    });
  } catch (error) {
    // Rollback the transaction in case of error
    await transaction.rollback();
    next(error);
  }
}

async function getOne(req, res, next) {
  try {
    const serviceReference = req.params.reference;

    // Main service query with necessary includes
    const service = await ServiceModel.findOne({
      where: { reference: serviceReference },
      include: [
        {
          model: ProductsModel,
          through: { attributes: [] },
          attributes: { exclude: ["createdAt", "updatedAt"] },
        },
        {
          model: NapModel,
          through: { attributes: [] },
          attributes: { exclude: ["createdAt", "updatedAt"] },
        },
        {
          model: PartnerModel,
          required: false,
          attributes: { exclude: ["createdAt", "updatedAt"] },
          include: [
            {
              model: OperationNatureTypeMappingModel,
              as: 'operationNatureTypeDetails',
              required: false,
              include: [
                {
                  model: OperationNatureMappingModel,
                  as: 'operationNatureMapping',
                  required: false,
                  include: [
                    {
                      model: NatureModel,
                      attributes: ['code', 'label'],
                      required: false
                    },
                    {
                      model: OperationModel,
                      attributes: ['code', 'label'],
                      required: false
                    }
                  ]
                },
                {
                  model: TypeModel,
                  attributes: ['code', 'label'],
                  required: false
                }
              ]
            }
          ]
        },
        {
          model: CurrencyModel,
          attributes: { exclude: ["createdAt", "updatedAt"] },
        },
        {
          model: TaxModel,
          attributes: { exclude: ["createdAt", "updatedAt"] },
        },
      ],
    });

    if (!service) {
      throw new NotFoundError("Service not found", "Service");
    }

    const serviceJSON = service.toJSON();

    let partner = null;

    if (serviceJSON.Partner) {
      partner = { ...serviceJSON.Partner };

      // Add operation nature code and type code from the included operationNatureTypeDetails
      if (partner.operationNatureTypeDetails) {
        const mapping = partner.operationNatureTypeDetails;
        partner.operation_nature_code = mapping.operation_nature_code;
        partner.type_code = mapping.type_code;

        // Clean up the nested object
        delete partner.operationNatureTypeDetails;
      }

    } else if (serviceJSON.partner_reference) {
      // If partner_reference exists but Partner object is null, try direct lookup
      try {
        const directPartnerLookup = await PartnerModel.findOne({
          where: { reference: serviceJSON.partner_reference },
          include: [
            {
              model: OperationNatureTypeMappingModel,
              as: 'operationNatureTypeDetails',
              required: false,
              include: [
                {
                  model: OperationNatureMappingModel,
                  as: 'operationNatureMapping',
                  required: false,
                  include: [
                    {
                      model: NatureModel,
                      attributes: ['code', 'label'],
                      required: false
                    },
                    {
                      model: OperationModel,
                      attributes: ['code', 'label'],
                      required: false
                    }
                  ]
                },
                {
                  model: TypeModel,
                  attributes: ['code', 'label'],
                  required: false
                }
              ]
            }
          ]
        });

        if (directPartnerLookup) {
          partner = directPartnerLookup.toJSON();

          // Add operation nature code and type code from the included operationNatureTypeDetails
          if (partner.operationNatureTypeDetails) {
            const mapping = partner.operationNatureTypeDetails;
            partner.operation_nature_code = mapping.operation_nature_code;
            partner.type_code = mapping.type_code;

            // Clean up the nested object
            delete partner.operationNatureTypeDetails;
          }

        }
      } catch (directLookupError) {

      }
    }
    // Remove the original Partner property
    delete serviceJSON.Partner;

    // Get service operations with their mapping details using raw queries
    const serviceOperations = await ServiceOperationModel.findAll({
      where: { service_reference: serviceReference },
      attributes: { exclude: ["updatedAt", "createdAt"] },
    });

    // Transform each service operation to get the full operation nature type details
    const enhancedOperationNatureTypes = await Promise.all(
      serviceOperations.map(async (serviceOp) => {
        const serviceOpData = serviceOp.toJSON();

        // Get the operation nature type mapping details
        const ontMapping = await OperationNatureTypeMappingModel.findOne({
          where: { operation_nature_type_code: serviceOpData.operation_nature_type_code },
          attributes: ['operation_nature_type_code', 'type_status', 'operation_nature_code', 'type_code']
        });

        if (!ontMapping) {
          return {
            ...serviceOpData,
            operation_code: null,
            operation_label: null,
            nature_code: null,
            nature_label: null,
            type_code: null,
            type_label: null,
            operation_nature_type_code: serviceOpData.operation_nature_type_code,
            operation_nature_type_mapping: {
              operation_nature_type_code: serviceOpData.operation_nature_type_code,
              type_status: null,
              operation_nature_code: null,
              type_code: null,
              operationNatureMapping: {
                operation: {},
                nature: {}
              },
              type: {}
            },
            operation_nature_type_details: []
          };
        }

        const ontMappingData = ontMapping.toJSON();

        // Get operation nature mapping details
        const operationNatureMapping = await OperationNatureMappingModel.findOne({
          where: { operation_nature_code: ontMappingData.operation_nature_code },
          attributes: ['operation_nature_code', 'nature_status', 'operation_code', 'nature_code']
        });

        let operationCode = null, operationLabel = null, natureCode = null, natureLabel = null;

        if (operationNatureMapping) {
          const onMappingData = operationNatureMapping.toJSON();
          operationCode = onMappingData.operation_code;
          natureCode = onMappingData.nature_code;

          // Get operation label
          if (operationCode) {
            const operation = await OperationModel.findOne({
              where: { code: operationCode },
              attributes: ['code', 'label']
            });
            operationLabel = operation ? operation.label : null;
          }

          // Get nature label
          if (natureCode) {
            const nature = await NatureModel.findOne({
              where: { code: natureCode },
              attributes: ['code', 'label']
            });
            natureLabel = nature ? nature.label : null;
          }
        }

        // Get type label
        const typeCode = ontMappingData.type_code;
        let typeLabel = null;
        if (typeCode) {
          const type = await TypeModel.findOne({
            where: { code: typeCode },
            attributes: ['code', 'label']
          });
          typeLabel = type ? type.label : null;
        }

        return {
          ...serviceOpData,
          operation_code: operationCode,
          operation_label: operationLabel,
          nature_code: natureCode,
          nature_label: natureLabel,
          type_code: typeCode,
          type_label: typeLabel,
          operation_nature_type_code: ontMappingData.operation_nature_type_code,
          operation_nature_type_mapping: {
            operation_nature_type_code: ontMappingData.operation_nature_type_code,
            type_status: ontMappingData.type_status,
            operation_nature_code: ontMappingData.operation_nature_code,
            type_code: ontMappingData.type_code,
            operationNatureMapping: {
              operation: operationCode && operationLabel ? { code: operationCode, label: operationLabel } : {},
              nature: natureCode && natureLabel ? { code: natureCode, label: natureLabel } : {}
            },
            type: typeCode && typeLabel ? { code: typeCode, label: typeLabel } : {}
          },
          operation_nature_type_details: []
        };
      })
    );

    const actors = await ServiceActorModel.findAll({
      where: { service_reference: serviceReference },
      attributes: { exclude: ["updatedAt", "createdAt"] },
    });

    res.send({
      data: {
        ...serviceJSON,
        partner: partner,
        operation_nature_types: enhancedOperationNatureTypes,
        actors: actors.map((e) => e.toJSON()),
      },
      message: "Service retrieved successfully",
    });
  } catch (err) {
    console.error("Error in getOne:", err);
    next(err);
  }
}

async function search(req, res, next) {
  try {
    const limit = req.query.limit ? req.query.limit : config.limit;
    const offset = req.query.offset
      ? req.query.offset 
      : config.offset ;
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;

    const where = req.filterConditions || {};

    Object.keys(req.query).forEach((key) => {
      if (!["offset", "limit", "sort_by", "order_by"].includes(key)) {
        where[key] = req.query[key];
      }
    });

    // Main query for services
    const { rows, count } = await ServiceModel.findAndCountAll({
      order: [[sortBy, orderBy]],
      offset,
      limit,
      where,
      include: [
        {
          model: PartnerModel,
          required: false,
          attributes: { exclude: ["createdAt", "updatedAt"] },
          include: [
            {
              model: OperationNatureTypeMappingModel,
              as: 'operationNatureTypeDetails',
              required: false,
              include: [
                {
                  model: OperationNatureMappingModel,
                  as: 'operationNatureMapping',
                  required: false,
                  include: [
                    {
                      model: NatureModel,
                      attributes: ['code', 'label'],
                      required: false
                    },
                    {
                      model: OperationModel,
                      attributes: ['code', 'label'],
                      required: false
                    }
                  ]
                },
                {
                  model: TypeModel,
                  attributes: ['code', 'label'],
                  required: false
                }
              ]
            }
          ]
        },
        {
          model: CurrencyModel,
          attributes: { exclude: ["createdAt", "updatedAt"] },
          required: false
        },
        {
          model: TaxModel,
          attributes: { exclude: ["createdAt", "updatedAt"] },
          required: false
        },
      ],
    });

    // For each service, fetch its complete associations
    const detailedServices = await Promise.all(
      rows.map(async (service) => {
        const serviceJSON = service.toJSON();
        const serviceReference = service.reference;

        let partner = null;

        if (serviceJSON.Partner) {
          partner = { ...serviceJSON.Partner };

          // Add operation nature code and type code from the included operationNatureTypeDetails
          if (partner.operationNatureTypeDetails) {
            const mapping = partner.operationNatureTypeDetails;
            partner.operation_nature_code = mapping.operation_nature_code;
            partner.type_code = mapping.type_code;

            // Clean up the nested object
            delete partner.operationNatureTypeDetails;
          }
        } else if (serviceJSON.partner_reference) {
          // If partner_reference exists but Partner object is null, try direct lookup
          try {
            const directPartnerLookup = await PartnerModel.findOne({
              where: { reference: serviceJSON.partner_reference },
              include: [
                {
                  model: OperationNatureTypeMappingModel,
                  as: 'operationNatureTypeDetails',
                  required: false,
                  include: [
                    {
                      model: OperationNatureMappingModel,
                      as: 'operationNatureMapping',
                      required: false,
                      include: [
                        {
                          model: NatureModel,
                          attributes: ['code', 'label'],
                          required: false
                        },
                        {
                          model: OperationModel,
                          attributes: ['code', 'label'],
                          required: false
                        }
                      ]
                    },
                    {
                      model: TypeModel,
                      attributes: ['code', 'label'],
                      required: false
                    }
                  ]
                }
              ]
            });

            if (directPartnerLookup) {
              partner = directPartnerLookup.toJSON();

              // Add operation nature code and type code from the included operationNatureTypeDetails
              if (partner.operationNatureTypeDetails) {
                const mapping = partner.operationNatureTypeDetails;
                partner.operation_nature_code = mapping.operation_nature_code;
                partner.type_code = mapping.type_code;

                // Clean up the nested object
                delete partner.operationNatureTypeDetails;
              }
            }
          } catch (directLookupError) {
            console.log('Could not fetch partner details for service:', serviceReference, directLookupError.message);
          }
        }

        // Remove the original Partner property
        delete serviceJSON.Partner;

        // Get products for this service
        const products = await ProductsModel.findAll({
          attributes: { exclude: ["updatedAt", "createdAt"] },
          include: [
            {
              model: ServiceModel,
              where: { reference: serviceReference },
              attributes: [],
              through: { attributes: [] },
            },
          ],
        });

        // Get NAPs for this service
        const naps = await NapModel.findAll({
          attributes: { exclude: ["updatedAt", "createdAt"] },
          include: [
            {
              model: ServiceModel,
              where: { reference: serviceReference },
              attributes: [],
              through: { attributes: [] },
            },
          ],
        });

        // Get service operations with their mapping details using the same logic as getOne
        const serviceOperations = await ServiceOperationModel.findAll({
          where: { service_reference: serviceReference },
          attributes: { exclude: ["updatedAt", "createdAt"] },
        });

        // Transform each service operation to get the full operation nature type details
        const enhancedOperationNatureTypes = await Promise.all(
          serviceOperations.map(async (serviceOp) => {
            const serviceOpData = serviceOp.toJSON();

            // Get the operation nature type mapping details
            const ontMapping = await OperationNatureTypeMappingModel.findOne({
              where: { operation_nature_type_code: serviceOpData.operation_nature_type_code },
              attributes: ['operation_nature_type_code', 'type_status', 'operation_nature_code', 'type_code']
            });

            if (!ontMapping) {
              return {
                ...serviceOpData,
                operation_code: null,
                operation_label: null,
                nature_code: null,
                nature_label: null,
                type_code: null,
                type_label: null,
                operation_nature_type_code: serviceOpData.operation_nature_type_code,
                operation_nature_type_mapping: {
                  operation_nature_type_code: serviceOpData.operation_nature_type_code,
                  type_status: null,
                  operation_nature_code: null,
                  type_code: null,
                  operationNatureMapping: {
                    operation: {},
                    nature: {}
                  },
                  type: {}
                },
                operation_nature_type_details: []
              };
            }

            const ontMappingData = ontMapping.toJSON();

            // Get operation nature mapping details
            const operationNatureMapping = await OperationNatureMappingModel.findOne({
              where: { operation_nature_code: ontMappingData.operation_nature_code },
              attributes: ['operation_nature_code', 'nature_status', 'operation_code', 'nature_code']
            });

            let operationCode = null, operationLabel = null, natureCode = null, natureLabel = null;

            if (operationNatureMapping) {
              const onMappingData = operationNatureMapping.toJSON();
              operationCode = onMappingData.operation_code;
              natureCode = onMappingData.nature_code;

              // Get operation label
              if (operationCode) {
                const operation = await OperationModel.findOne({
                  where: { code: operationCode },
                  attributes: ['code', 'label']
                });
                operationLabel = operation ? operation.label : null;
              }

              // Get nature label
              if (natureCode) {
                const nature = await NatureModel.findOne({
                  where: { code: natureCode },
                  attributes: ['code', 'label']
                });
                natureLabel = nature ? nature.label : null;
              }
            }

            // Get type label
            const typeCode = ontMappingData.type_code;
            let typeLabel = null;
            if (typeCode) {
              const type = await TypeModel.findOne({
                where: { code: typeCode },
                attributes: ['code', 'label']
              });
              typeLabel = type ? type.label : null;
            }

            return {
              ...serviceOpData,
              operation_code: operationCode,
              operation_label: operationLabel,
              nature_code: natureCode,
              nature_label: natureLabel,
              type_code: typeCode,
              type_label: typeLabel,
              operation_nature_type_code: ontMappingData.operation_nature_type_code,
              operation_nature_type_mapping: {
                operation_nature_type_code: ontMappingData.operation_nature_type_code,
                type_status: ontMappingData.type_status,
                operation_nature_code: ontMappingData.operation_nature_code,
                type_code: ontMappingData.type_code,
                operationNatureMapping: {
                  operation: operationCode && operationLabel ? { code: operationCode, label: operationLabel } : {},
                  nature: natureCode && natureLabel ? { code: natureCode, label: natureLabel } : {}
                },
                type: typeCode && typeLabel ? { code: typeCode, label: typeLabel } : {}
              },
              operation_nature_type_details: []
            };
          })
        );

        // Get actors for this service
        const actors = await ServiceActorModel.findAll({
          where: { service_reference: serviceReference },
          attributes: { exclude: ["updatedAt", "createdAt"] },
        });

        // Return the service with all its associations
        return {
          ...serviceJSON,
          partner: partner,
          products: products.map((e) => e.toJSON()),
          naps: naps.map((e) => e.toJSON()),
          operation_nature_types: enhancedOperationNatureTypes,
          actors: actors.map((e) => e.toJSON()),
        };
      })
    );

    res.send({
      data: detailedServices,
      total_count: count,
    });
  } catch (error) {
    next(error);
  }
}

async function updateService(req, res, next) {
  const reference = req.params.reference;
  const payload = req.body;
  console.debug("Received payload for update:", payload); // Debug: Log the incoming payload

  const transaction = await sequelize.transaction();
  console.debug("Transaction started for update"); // Debug: Log transaction start
  try {
    // Check if service exists
    const existingService = await ServiceModel.findOne({
      where: { reference: reference },
    });

    if (!existingService)
      throw new NotFoundError("Service not found", "Service");

    // Update service data
    // Validate that minimum values are not greater than maximum values
    if (payload.minimum_asset_price && payload.maximum_asset_price &&
      parseFloat(payload.minimum_asset_price) > parseFloat(payload.maximum_asset_price)) {
      throw new ConflictError("Minimum asset price cannot be greater than maximum asset price", "Service");
    }

    if (payload.minimum_contract_duration && payload.maximum_contract_duration &&
      parseInt(payload.minimum_contract_duration) > parseInt(payload.maximum_contract_duration)) {
      throw new ConflictError("Minimum contract duration cannot be greater than maximum contract duration", "Service");
    }

    const serviceData = {
      intended_for: payload.intended_for,
      code: `SRV_${existingService.reference.split('-')[0]}`,
      label: payload.label,
      type_of_service: payload.type_of_service,
      type_of_cover: payload.type_of_cover,
      start_date: payload.start_date === "" ? null : payload.start_date,
      end_date: payload.end_date === "" ? null : payload.end_date,
      currency_code: payload.currency_code,
      status: payload.status,
      out_of_contract_termination: payload.out_of_contract_termination,
      maximum_asset_price: payload.maximum_asset_price,
      minimum_asset_price: payload.minimum_asset_price,
      maximum_contract_duration: payload.maximum_contract_duration,
      minimum_contract_duration: payload.minimum_contract_duration,
      is_enterprise: payload.is_enterprise,
      is_enterprise_individuelle: payload.is_enterprise_individuelle,
      is_enterprise_publique: payload.is_enterprise_publique,
      is_particulier: payload.is_particulier,
      is_registrable: payload.is_registrable,
      is_unregistrable: payload.is_unregistrable,
      billing_method: payload.billing_method,
      calculation_method: payload.calculation_method,
      amount_excl_tax: payload.amount_excl_tax,
      basis_of_calculation: payload.basis_of_calculation,
      tax_code: payload.tax_code,
      calculation_percentage: payload.calculation_percentage,
      tax_value: payload.tax_value,
    };

    // Update the service record
    await ServiceModel.update(serviceData, {
      where: { reference: reference },
      transaction,
    });

    // Update or create partner if provided
    if (payload.partner_reference || payload.actor_reference) {
      const partnerData = {
         actor_reference: payload.actor_reference,
        operation_nature_type_code: payload.operation_nature_type_code,
        currency_code: payload.partner_currency_code,
        mandate_type: payload.mandate_type,
        external_reference: payload.external_reference,
        remittance_method: payload.remittance_method,
        calculation_method: payload.partner_calculation_method,
        amount_excl_tax: payload.partner_amount_excl_tax,
        basis_of_calculation: payload.partner_basis_of_calculation,
        tax_code: payload.partner_tax_code,
        calculation_percentage: payload.partner_percentage_calculation,
        tax_value: payload.partner_tax_value,
      };

      if (existingService.partner_reference) {
        // Update existing partner
        const existingPartner = await PartnerModel.findOne({
          where: { reference: existingService.partner_reference },
          transaction,
        });
        await existingPartner.update(partnerData, { transaction });
        await sendSyncMessage(
            OperationType.PUT,
            "Partner",
            "Static-tables",
            existingPartner
        );
      } else if (payload.actor_reference) {
        // Create new partner
        const newPartner = await PartnerModel.create(
          {
            reference: uuidv4(),
            ...partnerData,
          },
          { transaction }
        );
        await sendSyncMessage(
            OperationType.POST,
            "Partner",
            "Static-tables",
            newPartner
        );
        // Associate the service with the new partner
        await existingService.update(
          { partner_reference: newPartner.reference },
          { transaction }
        );
      }
    }

    // Update operation nature types associations
    if (payload.operation_nature_types) {
      // Validate that all operation nature type codes exist before updating associations
      const existingOperationNatureTypesInDb = await OperationNatureTypeMappingModel.findAll({
        where: {
          operation_nature_type_code: { [Op.in]: payload.operation_nature_types }
        },
        attributes: ['operation_nature_type_code'],
        raw: true,
        transaction
      });

      const existingCodes = existingOperationNatureTypesInDb.map(ont => ont.operation_nature_type_code);
      const invalidCodes = payload.operation_nature_types.filter(code => !existingCodes.includes(code));

      if (invalidCodes.length > 0) {
        throw new ConflictError(
          `Invalid operation nature type codes: ${invalidCodes.join(', ')}. These codes do not exist.`,
          "Service"
        );
      }

      // Get existing operation nature types for the service
      const existingOperationNatureTypes = await ServiceOperationModel.findAll({
        where: { service_reference: existingService.reference },
        attributes: ["operation_nature_type_code"],
        raw: true,
        transaction
      });

      const existingOpNatureTypeCodes = existingOperationNatureTypes.map((op) => op.operation_nature_type_code);
      const newOpNatureTypeCodes = payload.operation_nature_types;

      // Find operation nature types to remove (exist in DB but not in payload)
      const operationNatureTypesToRemove = existingOpNatureTypeCodes.filter(
        (code) => !newOpNatureTypeCodes.includes(code)
      );

      // Find operation nature types to add (exist in payload but not in DB)
      const operationNatureTypesToAdd = newOpNatureTypeCodes.filter(
        (code) => !existingOpNatureTypeCodes.includes(code)
      );

      // Remove operation nature types that are no longer needed
      if (operationNatureTypesToRemove.length > 0) {
        await ServiceOperationModel.destroy({
          where: {
            service_reference: existingService.reference,
            operation_nature_type_code: { [Op.in]: operationNatureTypesToRemove },
          },
          transaction,
        });
        for (const code of operationNatureTypesToRemove) {
          await sendSyncMessage(
            OperationType.DELETE,
            "ServiceOperation",
            "Static-tables",
            { service_reference: existingService.reference, operation_nature_type_code: code }
          );
        }
      }

      // Add new operation nature types
      if (operationNatureTypesToAdd.length > 0) {
        await Promise.all(
          operationNatureTypesToAdd.map(async (operationNatureTypeCode) => {
            try {
              const [association, created] =
                await ServiceOperationModel.findOrCreate({
                  where: {
                    service_reference: existingService.reference,
                    operation_nature_type_code: operationNatureTypeCode,
                  },
                  defaults: {
                    reference: uuidv4(),
                  },
                  transaction,
                });
              await sendSyncMessage(
                OperationType.POST,
                "ServiceOperation",
                "Static-tables",
                association
                );
            } catch (err) {
              console.error(
                `Error handling operation nature type ${operationNatureTypeCode}:`,
                err.message
              );
            }
          })
        );
      }
    }

    // Update products associations
    if (payload.products) {
      // Get existing products for the service
      const existingProducts = await ServiceProductModel.findAll({
        where: { service_reference: existingService.reference },
        attributes: ["product_code"],
        raw: true,
      });

      const existingProductCodes = existingProducts.map(
        (product) => product.product_code
      );
      const newProductCodes = payload.products;

      // Find products to remove (exist in DB but not in payload)
      const productsToRemove = existingProductCodes.filter(
        (code) => !newProductCodes.includes(code)
      );

      // Find products to add (exist in payload but not in DB)
      const productsToAdd = newProductCodes.filter(
        (code) => !existingProductCodes.includes(code)
      );

      // Remove products that are no longer needed
      if (productsToRemove.length > 0) {
        await ServiceProductModel.destroy({
          where: {
            service_reference: existingService.reference,
            product_code: { [Op.in]: productsToRemove },
          },
          transaction,
        });
        for (const code of productsToRemove) {
          await sendSyncMessage(
            OperationType.DELETE,
            "ServiceProduct",
            "Static-tables",
            { service_reference: existingService.reference, product_code: code }
          );
        }
      }

      // Add new products
      if (productsToAdd.length > 0) {
        await Promise.all(
          productsToAdd.map(async (productCode) => {
            try {
              const [association, created] =
                await ServiceProductModel.findOrCreate({
                  where: {
                    service_reference: existingService.reference,
                    product_code: productCode,
                  },
                  defaults: {
                    reference: uuidv4(),
                  },
                  transaction,
                });
              await sendSyncMessage(
                OperationType.POST,
                "ServiceProduct",
                "Static-tables",
                association
                );
            } catch (err) {
              console.error(
                `Error handling product ${productCode}:`,
                err.message
              );
            }
          })
        );
      }
    }

    // Update NAP associations
    if (payload.naps) {
      // Get existing NAPs for the service
      const existingNaps = await ServiceNapModel.findAll({
        where: { service_reference: existingService.reference },
        attributes: ["nap_code"],
        raw: true,
      });

      const existingNapCodes = existingNaps.map((nap) => nap.nap_code);
      const newNapCodes = payload.naps;

      // Find NAPs to remove (exist in DB but not in payload)
      const napsToRemove = existingNapCodes.filter(
        (code) => !newNapCodes.includes(code)
      );

      // Find NAPs to add (exist in payload but not in DB)
      const napsToAdd = newNapCodes.filter(
        (code) => !existingNapCodes.includes(code)
      );

      // Remove NAPs that are no longer needed
      if (napsToRemove.length > 0) {
        await ServiceNapModel.destroy({
          where: {
            service_reference: existingService.reference,
            nap_code: { [Op.in]: napsToRemove },
          },
          transaction,
        });
        for (const code of napsToRemove) {
          await sendSyncMessage(
            OperationType.DELETE,
            "ServiceNap",
            "Static-tables",
            { service_reference: existingService.reference, nap_code: code }
          );
        }
      }

      // Add new NAPs
      if (napsToAdd.length > 0) {
        await Promise.all(
          napsToAdd.map(async (napCode) => {
            try {
              const [association, created] = await ServiceNapModel.findOrCreate(
                {
                  where: {
                    service_reference: existingService.reference,
                    nap_code: napCode,
                  },
                  defaults: {
                    reference: uuidv4(),
                  },
                  transaction,
                }
              );
                await sendSyncMessage(
                    OperationType.POST,
                    "ServiceNap",
                    "Static-tables",
                    association
                );
            } catch (err) {
              console.error(`Error handling NAP ${napCode}:`, err.message);
            }
          })
        );
      }
    }

    // Update actor associations
    if (payload.actors) {
      // Get existing actors for the service
      const existingActors = await ServiceActorModel.findAll({
        where: { service_reference: existingService.reference },
        attributes: ["actor_code"],
        raw: true,
      });

      const existingActorCodes = existingActors.map(
        (actor) => actor.actor_code
      );
      const newActorCodes = payload.actors;

      // Find actors to remove (exist in DB but not in payload)
      const actorsToRemove = existingActorCodes.filter(
        (code) => !newActorCodes.includes(code)
      );

      // Find actors to add (exist in payload but not in DB)
      const actorsToAdd = newActorCodes.filter(
        (code) => !existingActorCodes.includes(code)
      );

      // Remove actors that are no longer needed
      if (actorsToRemove.length > 0) {
        await ServiceActorModel.destroy({
          where: {
            service_reference: existingService.reference,
            actor_code: { [Op.in]: actorsToRemove },
          },
          transaction,
        });
        for (const code of actorsToRemove) {
          await sendSyncMessage(
            OperationType.DELETE,
            "ServiceActor",
            "Static-tables",
            { service_reference: existingService.reference, actor_code: code }
          );
        }
      }

      // Add new actors
      if (actorsToAdd.length > 0) {
        await Promise.all(
          actorsToAdd.map(async (actorCode) => {
            try {
              const [association, created] =
                await ServiceActorModel.findOrCreate({
                  where: {
                    service_reference: existingService.reference,
                    actor_code: actorCode,
                  },
                  defaults: {
                    reference: uuidv4(),
                  },
                  transaction,
                });
                await sendSyncMessage(
                    OperationType.POST,
                    "ServiceActor",
                    "Static-tables",
                    association
                );
            } catch (err) {
              console.error(`Error handling actor ${actorCode}:`, err.message);
            }
          })
        );
      }
    }



    // Commit the transaction
    await transaction.commit();
    // Retrieve the updated service with all its associations
    const updatedService = await ServiceModel.findOne({
      where: {
        reference,
      },
    });
    // Send sync message for event streaming
    await sendSyncMessage(
        OperationType.PUT,
        "Service",
        "Static-tables",
        updatedService
    );
    res.status(200).send({
      data: updatedService,
      message: "Service updated successfully",
    });
  } catch (error) {
    // Rollback the transaction in case of error
    await transaction.rollback();
    next(error);
  }
}

async function remove(req, res, next) {
  const reference = req.params.reference;
  const transaction = await sequelize.transaction();

  try {
    const service = await ServiceModel.findOne({
      where: { reference },
      transaction
    });

    if (!service) {
      throw new NotFoundError("Service not found", "Service");
    }

    // Find all services packs associated with this service
    const servicePackAssociations = await ServiceServicesPackModel.findAll({
      where: { service_reference: reference },
      attributes: ["service_pack_code"],
      raw: true,
      transaction
    });

    // Get unique service pack codes
    const servicePackCodes = [...new Set(servicePackAssociations.map(assoc => assoc.service_pack_code))];

    // Remove all associations with service packs
    await ServiceServicesPackModel.destroy({
      where: { service_reference: reference },
      transaction
    });

    // Send sync message for each affected service pack
    await Promise.all(
      servicePackCodes.map(async (packCode) => {
        await sendSyncMessage({
          entityName: "ServicesPack",
          entityReference: packCode,
          operationType: OperationType.PUT,
        });
      })
    );

    // Now remove the service itself
    await ServiceModel.destroy({
      where: { reference },
      transaction
    });

    await transaction.commit();

    // Send sync message for the service itself
    await sendSyncMessage(
        OperationType.DELETE,
        "Service",
        "Static-tables",
        service
    );

    res.send({
      data: {
        message: `Service with reference ${reference} has been deleted successfully`,
      },
    });
  } catch (error) {
    await transaction.rollback();
    next(error);
  }
}

module.exports = {
  createService,
  getOne,
  search,
  updateService,
  remove,
};
