/**
 * Taxes Controller
 * Uses unified ControllerFactory which automatically routes to EnhancedGenericController
 * due to country relationships configuration
 * Custom remove method preserved for tax rate deletion logic
 */

const { taxController } = require("../utils/controller-factory");
const config = require("../config/app-config");
// Import dependencies for custom methods
const { TaxModel, TaxrateModel,TaxTranslationsModel } = require("../db");
const { Op } = require("sequelize");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const { sendSyncMessage } = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");


async function search(req, res, next) {
  try {
    // Start with filter conditions from middleware, fallback to empty object
    let whereCondition = req.filterConditions || {};
    let translationWhereCondition = {};
    const limit = req.query.limit ? req.query.limit : config.limit;
    const offset = req.query.offset
      ? req.query.offset * limit
      : config.offset * limit;
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;

    const language = req.query.language
      ? req.query.language
      : config.defaultReturnedLanguage;

    // Process remaining query parameters (those not handled by middleware)
    Object.keys(req.query).forEach((key) => {
      if (
        !["offset", "limit", "sort_by", "order_by", "language"].includes(key)
      ) {
        if (language === config.language || key !== "label") {
          // Merge with existing whereCondition from middleware
          if (whereCondition[Op.and]) {
            // If we already have AND conditions from middleware, add to them
            whereCondition[Op.and].push({ [key]: req.query[key] });
          } else if (Object.keys(whereCondition).length > 0) {
            // If we have other conditions, convert to AND structure
            const existingConditions = { ...whereCondition };
            whereCondition = {
              [Op.and]: [existingConditions, { [key]: req.query[key] }]
            };
          } else {
            // No existing conditions, add directly
            whereCondition[key] = req.query[key];
          }
        } else {
          translationWhereCondition[key] = req.query[key];
        }
      }
    });

    const getSortingOrder = (sortBy, orderBy) => {
      if (language !== config.language && sortBy === "label") {
        return [[TaxTranslationsModel, "label", orderBy]];
      }
      return [[sortBy, orderBy]];
    };

    let taxes = [];
    let total_count = 0;
    const requestedLimit = parseInt(limit);

    // FIX: Always count distinct tax IDs, even when including translations
    if (language.toUpperCase() === config.language) {
      total_count = await TaxModel.count({ where: whereCondition });
    } else {
      // Count distinct tax IDs when using translations
      total_count = await TaxModel.count({
        where: whereCondition,
        include: {
          model: TaxTranslationsModel,
          where: translationWhereCondition,
        },
        distinct: true, // This is the key fix - count distinct tax records
      });
    }

    // If large limit is requested, fetch records in batches
    if (requestedLimit > 1000) {
      const batchSize = 1000;
      let currentOffset = 0;
      let hasMore = true;

      // Fetch all records in batches
      while (hasMore && taxes.length < total_count && taxes.length < requestedLimit) {
        let batch;
        if (language.toUpperCase() === config.language) {
          batch = await TaxModel.findAll({
            order: [[sortBy, orderBy]],
            offset: currentOffset,
            limit: batchSize,
            where: whereCondition,
          });
        } else {
          batch = await TaxModel.findAll({
            order: getSortingOrder(sortBy, orderBy),
            offset: currentOffset,
            limit: batchSize,
            where: whereCondition,
            include: [
              {
                model: TaxTranslationsModel,
                where: translationWhereCondition,
                attributes: ["label"],
              },
            ],
          });
        }

        if (batch.length === 0) {
          hasMore = false;
        } else {
          taxes = taxes.concat(batch);
          currentOffset += batch.length;
        }
      }
    } else {
      // Use existing approach for normal queries
      if (language.toUpperCase() === config.language) {
        taxes = await TaxModel.findAll({
          order: [[sortBy, orderBy]],
          offset,
          limit: requestedLimit,
          where: whereCondition,
        });
      } else {
        taxes = await TaxModel.findAll({
          order: getSortingOrder(sortBy, orderBy),
          offset,
          limit: requestedLimit,
          where: whereCondition,
          include: [
            {
              model: TaxTranslationsModel,
              where: translationWhereCondition,
              attributes: ["label"],
            },
          ],
        });
      }
    }

    // Process the results
    taxes = taxes.map((item) => {
      return {
        id: item.id,
        code: item.code,
        label: item.tax_translations?.[0]?.label || item.label,
        start_date: item.start_date,
        end_date: item.end_date,
        country: item.country,
        active: item.active
      };
    });

    res.send({ data: taxes, total_count });
  } catch (error) {
    next(error);
  }
}

async function update(req, res, next) {
    try {
        const id = req.params.id;
        const code = req.body.code;
        // Convert empty strings to null for start_date and end_date
        const startDate = !req.body.start_date ? null : req.body.start_date;
        const endDate = !req.body.end_date ? null : req.body.end_date;
        const oldTax = await TaxModel.findOne({
            where: {
                id,
            },
        });
        if (!oldTax || oldTax.length === 0) {
            throw new NotFoundError("Tax not found", "Tax");
        }
        const existingTax = await TaxModel.findOne({
            where: {
                code: code
            },
        });
        if(existingTax && existingTax.id !== parseInt(id)) {
            throw new ConflictError("Tax with matching code already exists", "Tax");
        }
        await TaxModel.update(
            {
                code: code,
                label: req.body.label,
                start_date: startDate,
                end_date: endDate,
                country: req.body.country,
                description: req.body.description,
                type: req.body.type,
                active: req.body.active
            },
            {
                where: {
                    id,
                },
            }
        );
        const updatedTax = await TaxModel.findOne({
            where: {
                id,
            },
        });
        await sendSyncMessage(OperationType.PUT, "Tax", "Tax", updatedTax);
        res.send({
            data: {...req.body, id: parseInt(id)}
        });
    } catch (error) {
        next(error);
    }
}


async function patch(req, res, next) {
    try {
        const id = req.params.id;
        const code = req.body.code;
        // Convert empty strings to null for start_date and end_date
        const startDate = !req.body.start_date ? null : req.body.start_date;
        const endDate = !req.body.end_date ? null : req.body.end_date;
        const oldTax = await TaxModel.findOne({
            where: {
                id,
            },
        });
        if (!oldTax || oldTax.length === 0) {
            throw new NotFoundError("Tax not found", "Tax");
        }
        const existingTax = await TaxModel.findOne({
            where: {
                code: code
            },
        });
        if(existingTax && existingTax.id !== parseInt(id)) {
            throw new ConflictError("Tax with matching code already exists", "Tax");
        }
        await TaxModel.update(
            {
                code: code,
                label: req.body.label,
                start_date: startDate,
                end_date: endDate,
                country: req.body.country,
                description: req.body.description,
                type: req.body.type,
                active: req.body.active
            },
            {
                where: {
                    id,
                },
            }
        );
        const updatedTax = await TaxModel.findOne({
            where: {
                id,
            },
        });
        await sendSyncMessage(OperationType.PUT, "Tax", "Tax", updatedTax);
        res.send({
            data: {...req.body, id: parseInt(id)}
        });
    } catch (error) {
        next(error);
    }
}

async function remove(req, res, next) {
    try {
        const code = req.params.code;
        const tax = await TaxModel.findOne({
            where: {
                code: code,
            },
        });
        if (!tax || tax.length === 0) {
            throw new NotFoundError("Tax not found", "Tax");
        }

        // First delete tax rates if they exist
        const taxRates = await TaxrateModel.destroy({
            where: { tax_code: code },
        });

        // Then delete the tax
        await TaxModel.destroy({
            where: {
                code: code,
            },
        });

        const message = taxRates > 0
            ? `Resource with code ${code} and ${taxRates} associated tax rates have been deleted successfully`
            : `Resource with code ${code} has been deleted successfully`;

        res.send({
            data: {
                message,
                count: taxRates
            },
        });
    } catch (error) {
        next(error);
    }
}


module.exports = {
    // Standard CRUD methods using enhanced generic controller
    search,
    getOne: taxController.getOne,
    create: taxController.create,
    // Custom remove method with tax rate deletion logic preserved
    update,
    patch,
    remove,
};
