const { ProductsModel, AllocationModel,Business_Activity_Product_AllocationsModel ,Business_ActivitiesModel} = require("../db");
const config = require("../config/app-config");
// GET /api/v1/
async function search(req, res) {
    try {
        let whereCondition = {};
        const limit = parseInt(req.query.limit) || config.limit;
        const offset = parseInt(req.query.offset) ? parseInt(req.query.offset)  : config.offset ;
        const sortBy = req.query.sort_by || config.SortBy;
        const orderBy = req.query.order_by || config.OrderBy;

        Object.keys(req.query).forEach(key => {
            if (key !== "offset" && key !== "limit" && key !== "sort_by" && key !== "order_by") {
                whereCondition[key] = req.query[key];
            }
        });

        let [activityproductallocations, total_count] = await Promise.all([
            Business_Activity_Product_AllocationsModel.findAll({
                order: [[sortBy, orderBy]],
                offset,
                limit,
                where: whereCondition
            }),
            Business_Activity_Product_AllocationsModel.count({ where: whereCondition }),
        ]);

        if (!activityproductallocations || activityproductallocations.length === 0) {
            return res.status(404).send({
                error: "Activityproductallocations not found ",
            });
        }

        // Group detailed products by product code
        let groupedData = [];
        await Promise.all(activityproductallocations.map(async allocation => {
            let businessActivity = await Business_ActivitiesModel.findOne({
                where: { business_reference: allocation.business_reference },
                attributes: ["business_name"]
            });

            let detailedAllocation = await AllocationModel.findOne({
                where: { code: allocation.allocation_code },
                attributes: ["code", "label","allocation_category_code","operation_code"]
            });

            let existingEntry = groupedData.find(entry => entry.business_reference === allocation.business_reference && entry.activity_code === allocation.activity_code && entry.product_code === allocation.product_code);

            if (existingEntry) {
                existingEntry.allocations.push(detailedAllocation);
            } else {
                const businessName = businessActivity ? businessActivity.business_name : "" ;
                groupedData.push({
                    business_reference: allocation.business_reference,
                    business_name: businessName ,
                    activity_code: allocation.activity_code,
                    product_code: allocation.product_code,
                    allocations: [detailedAllocation]
                });
            }
        }));

        res.send({ data:groupedData, total_count });

    } catch (error) {
        console.error(error);

        const responseBody = {
            error: {
                message: "Internal Server Error",
            },
        };
        if (process.env.DEBUG == "true") {
            responseBody.error.debug = {
                detail: error.message || "Internal Server Error",
                source: error,
            };
        }

        res.status(500).json(responseBody);
    }
}

// GET /api/v1/id
async function getOne(req, res) {
    // TODO - Add validation
    try {
        const id = req.params.id;
        const activityproductallocations = await Business_Activity_Product_AllocationsModel.findAll({
            where: {
                id,
            },
        });
        if (activityproductallocations.length === 0) {
            // No activityproductallocations found for the given id
            return res.status(404).send({
                error: "Activities not found for the given id ",
            });
        }

        res.send({
            data: activityproductallocations,
            total_count: activityproductallocations.length,
        });
    } catch (error) {
        console.error(error); // Log the full error for debugging

        // Craft a user-friendly error response for production
        const responseBody = {
            error: {
                // message: " Internal Server Error",
                // Optionally include a generic user-facing message:
                // userMessage: 'An unexpected error occurred. Please try again later.',
            },
        };

        // If in development or debug mode, consider adding more details:
        if (process.env.DEBUG=="true") {
            responseBody.error.debug = {
                detail: error.message || " Internal Server Error", // Provide a fallback detail
                source: error,
            };
        }

        res.status(500).json(responseBody);
    }
}

//GET::/api/v1/businesses/{reference}/activities/{code}/products/{code}/allocations
async function getAllocationsByActivityAndProduct(req, res) {
    try {
        const activityCode = req.params.code;
        const productCode = req.params.productCode;
        const reference = req.params.reference;
        const allocations = await Business_Activity_Product_AllocationsModel.findAll({ 
            where: { business_reference: reference, activity_code: activityCode, product_code: productCode } 
        });
        
        // Array to store existing allocations
        let existingAllocations = [];
        
        // Loop through the allocations to find existing allocations in AllocationModel
        for (const allocation of allocations) {
            const code = allocation.allocation_code;
            const existingAllocation = await AllocationModel.findOne({
                where: { code: code }
            });
            if (existingAllocation) {
                existingAllocations.push(existingAllocation); // Push into the array
            }
        }
        
        const response = {
            data: {
                business_reference: reference,
                activity_code: activityCode,
                product_code: productCode,
                allocation: existingAllocations
            },
            total_count: existingAllocations.length,
        };
        res.send(response);
       
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
}


// GET /api/v1/businesses/{reference}/activities/{code}/products/{code}/allocations/{code}
async function getAllocationByActivityAndProduct(req, res) {
    try {
        const activityCode = req.params.code;
        const productCode = req.params.productCode;
        const allocationCode = req.params.allocationCode;
        const reference = req.params.reference;

        const allocation = await Business_Activity_Product_AllocationsModel.findOne({ 
            where: { business_reference: reference, product_code: productCode, activity_code: activityCode, allocation_code: allocationCode } 
        });

        if (!allocation) {
            return res.status(404).json({ error: "Element not found for the given activity CODE, product CODE, and allocation CODE" });
        }

        const existingAllocation = await AllocationModel.findOne({
            where: { code: allocationCode }
        });

        if (!existingAllocation) {
            return res.status(404).send({
                error: "Allocation not found for the given code",
            });
        }

        const response = {
            data: {
                activity_code: allocation.activity_code,
                product_code: allocation.product_code,
                allocation: existingAllocation
            },
            total_count: 1, // Since you're querying for a specific allocation
        };

        res.send(response);

    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
}


// POST /api/v1/activities/{code}/products/allocations
async function createAllocationForProduct(req, res) {
    try {
        const activityCode = req.params.code;
        const productCode = req.params.productCode;
        const allocations = req.body.allocations;
        const reference  = req.params.reference;
        console.log("req pram",req.params)
        // Check if allocations exist
        if (!allocations || allocations.length === 0) {
            return res
                .status(404)
                .json({ error: "No allocations found for the given activity product code" });
        }

        // Create activity_product_allocation for each allocation
        const createdActivityProductAllocations = [];
        const notFoundAllocations = [];
        const existingAllocations = [];
        for (const allocation of allocations) {
            // Check if allocation exists
            const existingAllocation = await AllocationModel.findOne({
                where: { code: allocation.code },
            });
            if (existingAllocation) {
                // Check if activity product allocation already exists
                const existingActivityProductAllocation = await Business_Activity_Product_AllocationsModel.findOne({
                    where: {
                        activity_code: activityCode,
                        product_code: productCode,
                        allocation_code: allocation.code,
                        business_reference:reference
                    }
                });
                if (!existingActivityProductAllocation) {
                    // Create activity product allocation only if it doesn't exist
                    const activity_product_allocation = await Business_Activity_Product_AllocationsModel.create({
                        activity_code: activityCode,
                        product_code: productCode,
                        allocation_code: allocation.code,
                        business_reference:reference
                    });
                    createdActivityProductAllocations.push(activity_product_allocation)
                } else {
                    // Activity product allocation already exists
                    existingAllocations.push(allocation.code);
                }
            } else {
                // Record allocations that do not exist
                notFoundAllocations.push(allocation.code);
            }
        }

        // If all allocations were not found, return error
        if (notFoundAllocations.length === allocations.length) {
            return res.status(404).json({ error: `No allocations found` });
        }

        // If some allocations were not found, return error message
        let responseBody = {};
        if (notFoundAllocations.length > 0) {
            responseBody["error"] = `Allocations not found in allocation table: ${notFoundAllocations.join(", ")}`;
        }
        if (existingAllocations.length > 0) {
            responseBody["error"] = responseBody["error"]
                ? `${responseBody["error"]} | Allocations already exist: ${existingAllocations.join(", ")}`
                : `Allocations already exist: ${existingAllocations.join(", ")}`;
        }
       
        res.status(201).json({ data: createdActivityProductAllocations, ...responseBody });
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
}
// DELETE /api/v1/static-tables/activities/{code}/products/{code}/allocations/{code}
async function deleteAllocationByActivityCodeAndProductCodeAndAllocationCode(req, res) {
    try {
        const activityCode = req.params.code;
        const productCode = req.params.productCode;
        const allocationCode = req.params.allocationCode;
        
        const numRowsDeleted = await Business_Activity_Product_AllocationsModel.destroy({
            where: { activity_code: activityCode, product_code: productCode , allocation_code: allocationCode },
        });

        console.log("numRowsDeleted", numRowsDeleted);

        if (numRowsDeleted === 0) {
            return res.status(404).json({ error: "Product not found for the given activity CODE and product CODE" });
        }

      
        res.send({
            data: {
                message: `Resource with activity code: ${activityCode}, productcode: ${productCode } and allocation code: ${allocationCode} has been deleted successfully`,
            },
        });
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
}



module.exports = {
    search,
    getOne,
    getAllocationsByActivityAndProduct,
    getAllocationByActivityAndProduct,
    createAllocationForProduct,
    deleteAllocationByActivityCodeAndProductCodeAndAllocationCode
    
};
