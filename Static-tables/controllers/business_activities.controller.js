const { ActivityModel, Business_ActivitiesModel } = require("../db");
const config = require("../config/app-config");
// GET /api/v1/
async function search(req, res) {
    try {
        let whereCondition = {};
        const limit = req.query.limit ? req.query.limit : config.limit;
        const offset = req.query.offset ? req.query.offset*limit : config.offset*limit;
        const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
        const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy
        // Build the where condition dynamically based on query parameters
        Object.keys(req.query).forEach(key => {
            if (key !== "offset" && key !== "limit" && key !== "sort_by" && key !== "order_by") {
                whereCondition[key] = req.query[key];
            }
        });

        let [businessActivities, total_count] = await Promise.all([
            Business_ActivitiesModel.findAll({
                order: [[sortBy, orderBy]],
                offset,
                limit,
                where: whereCondition
            }),
            Business_ActivitiesModel.count({ where: whereCondition }),
        ]);

        if (!businessActivities || businessActivities.length === 0) {
            return res.status(404).send({ error: "Business activities not found" });
        }

        const businessReferences = Array.from(new Set(businessActivities.map((activity) => activity.business_reference)));

        const detailedActivities = await fetchDetailedActivities(businessReferences);

        res.send({ data: detailedActivities, total_count });
    } catch (error) {
        console.error(error);
        handleError(res, error);
    }
}


async function fetchDetailedActivities(businessReferences) {
    const activities = await Business_ActivitiesModel.findAll({
        where: {
            business_reference: businessReferences,
        }
    });

    const detailedActivityCodes = activities.map((activity) => activity.activity_code);

    // Fetch detailed activities in parallel
    const detailedActivitiesPromise = ActivityModel.findAll({
        where: {
            code: detailedActivityCodes,
        },
    });

    const detailedActivities = await detailedActivitiesPromise;

    const activitiesByReference = businessReferences.map((reference) => {
        const businessActivities = activities.filter((act) => act.business_reference === reference);
        let businessName = "";
        
        for (const activity of businessActivities) {
            if (activity.business_name !== null) {
                businessName = activity.business_name;
                break;
            }
        }

        return {
            business_reference: reference,
            business_name: businessName,
            activities: detailedActivities.filter((activity) =>
                businessActivities.some((act) => act.activity_code === activity.code)
            ),
        };
    });

    return activitiesByReference;
}


function handleError(res, error) {
    const responseBody = {
        error: {
            message: "Internal Server Error",
        },
    };
    if (process.env.DEBUG === "true") {
        responseBody.error.debug = {
            detail: error.message || "Internal Server Error",
            source: error,
        };
    }
    res.status(500).json(responseBody);
}



// GET /api/v1/id
async function getOne(req, res) {
    // TODO - Add validation
    try {
        const id = req.params.id;
        const business_Activities = await Business_ActivitiesModel.findAll({
            where: {
                id,
            },
        });

        if (!business_Activities || business_Activities.length === 0) {
            // No business_Activities found for the given id
            return res.status(404).send({
                error: "Business_Activities not found for the given id ",
            });
        }

        res.send({
            data: business_Activities,
            total_count: business_Activities.length,
        });
    } catch (error) {
        console.error(error); // Log the full error for debugging

        // Craft a user-friendly error response for production
        const responseBody = {
            error: {
                // message: " Internal Server Error",
                // Optionally include a generic user-facing message:
                // userMessage: 'An unexpected error occurred. Please try again later.',
            },
        };

        // If in development or debug mode, consider adding more details:
        if (process.env.DEBUG=="true") {
            responseBody.error.debug = {
                detail: error.message || " Internal Server Error", // Provide a fallback detail
                source: error,
            };
        }

        res.status(500).json(responseBody);
    }
}
//GET::/api/v1/static-tables/business/{reference}/activities
async function getActivitiesByBusinessReference(req, res) {
    try {
        const { reference } = req.params;
       

        // Find activities associated with the business reference
        const activities = await Business_ActivitiesModel.findAll({
            where: { business_reference: reference },
        });

        // Array to store existing activities
        let existingActivities = [];

        // Loop through the activities to find existing activities in ActivityModel
        for (const activity of activities) {
            const  code  = activity.activity_code; // Assuming 'code' is the identifier for activities
            const existingActivity = await ActivityModel.findOne({
                where: { code: code }
            });
            if (existingActivity) {
                existingActivities.push(existingActivity);
            }
        }

        const response = {
            data: {
                business_reference: reference,
                activities: existingActivities,
            },
            total_count: existingActivities.length,
        };
        console.log("res", response);

        // Sending the response
        res.send(response);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
}


// GET::/api/v1/static-tables/business/{reference}/activities/{code}
async function getActivityByActivityCodeAndBusinessReference(req, res) {
    try {
        const { reference,code} = req.params;
        console.log("req.prams", req.params);
        const activity = await Business_ActivitiesModel.findOne({
            where: { business_reference: reference, activity_code: code },
        });
        console.log("activity", activity);
        if (!activity) {
            return res
                .status(404)
                .json({
                    error:
            "Activity not found for the given activity CODE and activity CODE",
                });
        }
        else{
            const existing_activity = await ActivityModel.findAll({
                where: {
                    code:code
                },
                
            })
            res.send({ data: { business_reference: reference,activity:existing_activity } });
        }
        
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
}

// POST::/api/v1/static-tables/business/{reference}/activities
async function createActivityForBusiness(req, res) {
    try {
        const reference = req.params.reference;
        const { activities, business_name } = req.body;

        if (!activities || activities.length === 0) {
            return res.status(400).json({ error: "No activities found in the request" });
        }

        
        const createdBusinessActivities = [];
        const notFoundActivities = [];
        const existingActivities = [];

        // Fetch all activity codes from the database in one query
        const activityCodes = activities.map(activity => activity.code);
        const existingActivitiesData = await ActivityModel.findAll({ 
            where: { code: activityCodes } 
        });
        const existingActivityCodes = existingActivitiesData.map(activity => activity.code);

        for (const activity of activities) {
            if (existingActivityCodes.includes(activity.code)) {
                const existingBusinessActivity = await Business_ActivitiesModel.findOne({
                    where: {
                        business_reference: reference,
                        activity_code: activity.code
                    }
                });

                if (!existingBusinessActivity) {
                    const businessActivity = await Business_ActivitiesModel.create({
                        business_reference: reference,
                        activity_code: activity.code,
                        business_name: business_name
                    });
                    createdBusinessActivities.push(businessActivity);
                } else {
                    existingActivities.push(activity.code);
                }
            } else {
                notFoundActivities.push(activity.code);
            }
        }

        const responseBody = {};

        if (notFoundActivities.length > 0) {
            responseBody["error"] = `Activities not found: ${notFoundActivities.join(", ")}`;
        }

        if (existingActivities.length > 0) {
            responseBody["error"] = responseBody["error"]
                ? `${responseBody["error"]} | Activities already exist: ${existingActivities.join(", ")}`
                : `Activities already exist: ${existingActivities.join(", ")}`;
        }

        if (notFoundActivities.length === activities.length) {
            return res.status(400).json(responseBody);
        }

        res.status(201).json({ data: createdBusinessActivities, ...responseBody });
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
}


// DELETE::/api/v1/static-tables/business/{reference}/activities/{code}
async function deleteActivityByActivityCodeAndBusinessReference(req, res) { 
    try {
        const activityCode = req.params.code;
        const reference = req.params.reference;

        const numRowsDeleted = await Business_ActivitiesModel.destroy({
            where: { activity_code: activityCode, business_reference: reference },
        });

        console.log("numRowsDeleted", numRowsDeleted);

        if (numRowsDeleted === 0) {
            return res
                .status(404)
                .json({
                    error:
            "Activity not found for the given activity CODE and activity CODE",
                });
        }

        res.send({
            data: {
                message: `Resource with activity code: ${activityCode} and reference: ${reference} has been deleted successfully`,
            },
        });
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
}

module.exports = {
    search,
    getOne,
    getActivitiesByBusinessReference,
    getActivityByActivityCodeAndBusinessReference,
    createActivityForBusiness,
    deleteActivityByActivityCodeAndBusinessReference,
};
