/**
 * Products Controller
 * This controller has complex type mapping logic with ProductTypeMappingModel
 * and should be kept as-is due to its complex business logic
 */

const {
    ProductsModel,
    ProductTranslationsModel,
    ActivityModel,
    ActivityTranslation,
    ProductTypeMappingModel,
    sequelize,
} = require("../db");
const {Op, QueryTypes} = require("sequelize");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const OperationType = require("../models/Stream/operationType");
const {sendSyncMessage} = require("../producer/Producer");

async function search(req, res, next) {
    try {
        let whereCondition = {};
        let translationWhereCondition = {};
        const limit = req.query.limit ? req.query.limit : config.limit;
        const offset = req.query.offset
            ? req.query.offset * limit
            : config.offset * limit;
        const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
        const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;
        const language = req.query.language
            ? req.query.language.toUpperCase()
            : config.defaultReturnedLanguage;

        Object.keys(req.query).forEach((key) => {
            if (
                !["offset", "limit", "sort_by", "order_by", "language"].includes(key)
            ) {
                if (
                    language === config.language || key !== "label"
                ) {
                    whereCondition[key] = req.query[key];
                } else {
                    translationWhereCondition[key] = req.query[key];
                }
            }
        });

        const getSortingOrder = (sortBy, orderBy) => {
            if (language !== config.language && sortBy === "label") {
                return [[ProductTranslationsModel, "label", orderBy]];
            }
            if (sortBy === "activity" && language !== config.language) {
                return [[ActivityModel, ActivityTranslation, "activity_code", orderBy]];
            }
            if (sortBy === "activity" && language === config.language) {
                return [[ActivityModel, "code", orderBy]];
            }
            return [[sortBy, orderBy]];
        };

        let result, total_count;

        [result, total_count] = await Promise.all(
            language === config.language
                ? [
                    ProductsModel.findAll({
                        order: getSortingOrder(sortBy, orderBy),
                        limit,
                        offset,
                        where: whereCondition,
                        include: {
                            model: ActivityModel,
                            where: {associated_to: "DOSSIER"},
                        },
                    }),
                    ProductsModel.count({
                        where: whereCondition,
                        include: {
                            model: ActivityModel,
                            required: true,
                        },
                    }),
                ]
                : [
                    ProductsModel.findAll({
                        order: getSortingOrder(sortBy, orderBy),
                        limit,
                        offset,
                        where: whereCondition,
                        include: [
                            {
                                model: ProductTranslationsModel,
                                where: translationWhereCondition,
                                attributes: ["label"],
                            },
                            {
                                model: ActivityModel,
                                where: {associated_to: "DOSSIER"},
                                include: {
                                    model: ActivityTranslation,
                                    attributes: ["label"],
                                },
                            },
                        ],
                    }),
                    ProductsModel.count({
                        where: whereCondition,
                        include: [
                            {
                                model: ProductTranslationsModel,
                                where: translationWhereCondition,
                            },
                            {
                                model: ActivityModel,
                                required: true,
                            },
                        ],
                    }),
                ]
        );

        result = result.map((item) => {
            return {
                id: item.id,
                code: item.code,
                label: item.product_translations?.[0]?.label || item.label,
                start_date: item.start_date,
                end_date: item.end_date,
                system_attribute: item.system_attribute,
                active: item.active,
                activity: {
                    id: item.activity.id,
                    code: item.activity.code,
                    label:
                        item.activity.activity_translations?.[0]?.label ||
                        item.activity.label,
                    active: item.activity.active,
                },
            };
        });

        res.send({data: result, total_count});
    } catch (error) {
        next(error);
    }
}

async function getOne(req, res, next) {
    try {
        const language = req.query.language ? req.query.language : config.language;
        const product = await ProductsModel.findOne({
            where: {
                id: req.params.id,
            },
        });
        if (!product || product.length === 0) {
            throw new NotFoundError("Product not found", "Product");
        }
        const first_result = [product].map((product) => {
            return {
                id: product.id,
                code: product.code,
                label: product.label,
                language_code: config.language,
            };
        });

        if (language.toUpperCase() !== config.language) {
            const product_translations = await ProductTranslationsModel.findOne({
                as: "product_translations",
                attributes: ["label", "language_code"],
                where: {
                    product_code: product.code,
                    language_code: language.toUpperCase(),
                },
                include: [
                    {
                        attributes: ["id"],
                        model: ProductsModel,
                        as: "product",
                        on: {
                            "$product.code$": {
                                [Op.col]: "product_translations.product_code",
                            },
                        },
                    },
                ],
                raw: true,
            });

            if (!product_translations) {
                return res.send({
                    data: first_result[0],
                });
            }

            const result = [product_translations].map((product) => {
                return {
                    id: product["product.id"],
                    code: product.code,
                    label: product.label,
                    language_code: product.language_code,
                };
            });

            return res.send({
                data: result[0],
            });
        }
        return res.send({
            data: first_result[0],
        });
    } catch (error) {
        next(error);
    }
}

async function create(req, res, next) {
    try {
        const {
            code,
            label,
            activity_code,
            start_date,
            end_date,
            active,
            types,
            language = config.defaultReturnedLanguage,
        } = req.body;
        const isDefaultLanguage = language.toUpperCase() === config.language;

        const existingProduct = await ProductsModel.findOne({
            where: {
                code,
            },
        });

        if (existingProduct) {
            throw new ConflictError("Product already exists", "Product");
        }

        const activity = await ActivityModel.findOne({
            where: {
                code: activity_code,
            }
        });

        if (!activity) throw new NotFoundError("Activity don't exist", "Activity");

        const newProduct = await ProductsModel.create({
            code,
            label: label,
            activity_code,
            activity_associated_to: "DOSSIER",
            start_date: start_date ? start_date : null,
            end_date: end_date ? end_date : null,
            active,
        });

        await sendSyncMessage(
            OperationType.POST,
            "Product",
            "Static-tables",
            newProduct
        );

        if (newProduct)
            await ProductTranslationsModel.create({
                product_code: newProduct.code,
                label: !isDefaultLanguage ? label : "",
                language_code: !isDefaultLanguage ? language.toUpperCase() : "",
            });

        await Promise.all(
            types.map(async (item) => {
                const createdProductTypeMappingModel = await ProductTypeMappingModel.create({
                    product_code: newProduct.code,
                    operation_nature_type_code: item,
                })
                await sendSyncMessage(OperationType.POST, "ProductLineTypeMapping", "Static-tables", createdProductTypeMappingModel)
            })
        );
        res.status(201).send({
            data: {
                id: newProduct.id,
                code: newProduct.code,
                label,
                start_date: newProduct.start_date,
                end_date: newProduct.end_date,
                active: newProduct.active,
                activity_code: newProduct.activity_code,
            },
        });
    } catch (error) {
        next(error);
    }
}

async function update(req, res, next) {
    try {
        const id = req.params.id;
        const {
            code,
            label,
            activity_code,
            start_date,
            end_date,
            active,
            types,
            language = config.defaultReturnedLanguage,
        } = req.body;
        const isDefaultLanguage = language.toUpperCase() === config.language;

        const oldProduct = await ProductsModel.findOne({
            where: {
                id,
            },
        });

        if (!oldProduct) {
            throw new NotFoundError("Product not found", "Product");
        }

        const existingProduct = await ProductsModel.findOne({
            where: {
                code: code,
            },
        });

        if (existingProduct && existingProduct.id !== parseInt(id)) {
            throw new ConflictError(
                "Product with matching code already exists",
                "Product"
            );
        }

        if (oldProduct.code !== code && oldProduct.system_attribute)
            throw new ConflictError("Product code cannot be modified", "Product");

        await oldProduct.update({
            code,
            label: isDefaultLanguage ? label : oldProduct.label,
            activity_code,
            start_date: start_date ? start_date : null,
            end_date: end_date ? end_date : null,
            active,
        });
        const updatedProduct = {
            id: parseInt(id),
            code,
            label,
            activity_code,
            start_date,
            end_date,
            active,
        };
        await sendSyncMessage(
            OperationType.PUT,
            "Product",
            "Static-tables",
            updatedProduct
        );

        const existingTranslatedProduct = await ProductTranslationsModel.findOne({
            where: {
                product_code: oldProduct.code,
            },
        });

        if (existingTranslatedProduct && !isDefaultLanguage)
            await ProductTranslationsModel.update(
                {
                    product_code: code,
                    label,
                },
                {
                    where: {
                        product_code: oldProduct.code,
                    },
                }
            );

        if (types) {
            const oldTypes = (
                await ProductTypeMappingModel.findAll({
                    attributes: ["operation_nature_type_code"],
                    where: {
                        product_code: code,
                    },
                })
            ).map((item) => item.operation_nature_type_code);

            const typesToRemove = oldTypes.filter(
                (item) => !types.includes(item.toString())
            );
            const typesToAdd = types.filter((item) => !oldTypes.includes(item));

            await ProductTypeMappingModel.destroy({
                where: {
                    operation_nature_type_code: typesToRemove,
                    product_code: code,
                },
            });
            for (const item of typesToRemove) {
                await sendSyncMessage(
                    OperationType.DELETE,
                    "ProductLineTypeMapping",
                    "Static-tables",
                    {operation_nature_type_code: item, product_code: code}
                );
            }
            await Promise.all(
                typesToAdd.map(async (item) => {
                    const newProductTypeMappingModel =
                        await ProductTypeMappingModel.create({
                            product_code: code,
                            operation_nature_type_code: item,
                        });
                    await sendSyncMessage(
                        OperationType.PUT,
                        "ProductLineTypeMapping",
                        "Static-tables",
                        newProductTypeMappingModel
                    );
                })
            );
        }

        res.send({
            data: {
                id: parseInt(id),
                code,
                label,
                start_date,
                end_date,
                active,
                activity_code,
            },
        });
    } catch (error) {
        next(error);
    }
}

async function remove(req, res, next) {
    try {
        const id = req.params.id;

        const product = await ProductsModel.findOne({
            where: {
                id: req.params.id,
            },
        });

        if (!product) {
            throw new NotFoundError("Product not found", "Product");
        }

        if (product.system_attribute === true)
            throw new ConflictError("Product cannot be removed", "Product");

        await ProductsModel.destroy({
            where: {
                id: id,
            },
        });

        await sendSyncMessage(
            OperationType.DELETE,
            "Product",
            "Static-tables",
            product
        );

        res.send({
            data: {
                message: `Resource with ID ${id} has been deleted successfully`,
            },
        });
    } catch (error) {
        next(error);
    }
}

async function getProductTypes(req, res, next) {
    try {
        const code = req.params.code;

        const product = await ProductsModel.findOne({
            where: {
                code,
            },
        });
        if (!product) {
            throw new NotFoundError("Product not found", "Product");
        }

        const relationIds = (
            await ProductTypeMappingModel.findAll({
                attributes: ["operation_nature_type_code"],
                where: {
                    product_code: code,
                },
            })
        ).map((item) => item.operation_nature_type_code);

        if (!relationIds.length) {
            return res.send({data: [], total_count: 0});
        }

        const rows = await sequelize.query(
            `select operation_nature_type_mapping.operation_nature_type_code,
                    type.id         as type_id,
                    type.code       as type_code,
                    type.label      as type_label,
                    nature.id       as nature_id,
                    nature.code     as nature_code,
                    nature.label    as nature_label,
                    operation.id    as operation_id,
                    operation.code  as operation_code,
                    operation.label as operation_label
             from operation_nature_type_mapping
                      join types as type
                           on type.code = operation_nature_type_mapping.type_code
                      join operation_nature_mapping
                           on operation_nature_mapping.operation_nature_code =
                              operation_nature_type_mapping.operation_nature_code
                      join natures as nature
                           on nature.code = operation_nature_mapping.nature_code
                      join operations as operation
                           on operation.code = operation_nature_mapping.operation_code
             where operation_nature_type_mapping.operation_nature_type_code IN (:relationIds)
            `,
            {
                replacements: {relationIds},
                type: QueryTypes.SELECT,
            }
        );
        const formattedData = rows.map((item) => ({
            operation_nature_type_code: item.operation_nature_type_code,
            operation: {
                id: item.operation_id,
                code: item.operation_code,
                label: item.operation_label,
            },
            nature: {
                id: item.nature_id,
                code: item.nature_code,
                label: item.nature_label,
            },
            type: {
                id: item.type_id,
                code: item.type_code,
                label: item.type_label,
            },
        }));

        res.send({data: formattedData, total_count: formattedData.length});
    } catch (error) {
        next(error);
    }
}

module.exports = {
    search,
    getOne,
    create,
    update,
    remove,
    getProductTypes,
};
