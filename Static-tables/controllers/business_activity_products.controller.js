const { ProductsModel, Business_Activity_ProductsModel,Business_ActivitiesModel } = require("../db");
const config = require("../config/app-config");
// GET /api/v1/
async function search(req, res) {
    try {
        let whereCondition = {};
        const limit = req.query.limit ? req.query.limit : config.limit;
        const offset = req.query.offset ? req.query.offset  : config.offset ;
        const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
        const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;
        // Build the where condition dynamically based on query parameters
        Object.keys(req.query).forEach(key => {
            if (key !== "offset" && key !== "limit" && key !== "sort_by" && key !== "order_by") {
                whereCondition[key] = req.query[key];
            }
        });

        let groupedActivityProducts = await Business_Activity_ProductsModel.findAll({
            attributes: ["business_reference", "activity_code"],
            group: ["business_reference", "activity_code"],
            offset,
            limit,
            where: whereCondition
        });

        if (!groupedActivityProducts || groupedActivityProducts.length === 0) {
            // No activity found 
            return res.status(404).send({
                error: "Activity products not found ",
            });
        }

        // Fetch products for each group with detailed information
        let data = await Promise.all(groupedActivityProducts.map(async group => {
            // Retrieve business_name for the business_reference
            let businessActivity = await Business_ActivitiesModel.findOne({
                where: { business_reference: group.business_reference },
                attributes: ["business_name"]
            });

            let products = await Business_Activity_ProductsModel.findAll({
                where: {
                    business_reference: group.business_reference,
                    activity_code: group.activity_code,
                    ...whereCondition
                },
                attributes: ["product_code"] // Adjust attributes as needed
            });

            // Fetch detailed product information using ProductModel
            let detailedProducts = await Promise.all(products.map(async product => {
                let detailedProduct = await ProductsModel.findOne({
                    where: { code: product.product_code },
                    attributes: ["code", "label"] // Adjust attributes as needed
                });
                
                return detailedProduct;
            }));
         
            // Check if businessActivity.business_name is not null or undefined
            let businessName = businessActivity ? businessActivity.business_name : "" ; 
            return {
                business_reference: group.business_reference,
                business_name: businessName, // Include business_name
                activity_code: group.activity_code,
                products: detailedProducts
            };
        }));

        res.send({ data });

    } catch (error) {
        console.error(error);

        const responseBody = {
            error: {
                message: "Internal Server Error",
            },
        };
        if (process.env.DEBUG == "true") {
            responseBody.error.debug = {
                detail: error.message || "Internal Server Error",
                source: error,
            };
        }

        res.status(500).json(responseBody);
    }
}


// GET /api/v1/id
async function getOne(req, res) {
    // TODO - Add validation
    try {
        const id = req.params.id;
        const activityproducts = await Business_Activity_ProductsModel.findAll({
            where: {
                id,
            },
        });

        if (!activityproducts || activityproducts.length === 0) {
            // No activityproducts found for the given id
            return res.status(404).send({
                error: "Activities not found for the given id ",
            });
        }

        res.send({
            data: activityproducts,
            total_count: activityproducts.length,
        });
    } catch (error) {
        console.error(error); // Log the full error for debugging

        // Craft a user-friendly error response for production
        const responseBody = {
            error: {
                // message: " Internal Server Error",
                // Optionally include a generic user-facing message:
                // userMessage: 'An unexpected error occurred. Please try again later.',
            },
        };

        // If in development or debug mode, consider adding more details:
        if (process.env.DEBUG=="true") {
            responseBody.error.debug = {
                detail: error.message || " Internal Server Error", // Provide a fallback detail
                source: error,
            };
        }

        res.status(500).json(responseBody);
    }
}
//GET::/api/v1/static-tables/businesses/{reference}/activities/{code}/products
async function getProductsByActivityCode(req, res) {
    try {
        const { reference,code } = req.params;
        const products = await Business_Activity_ProductsModel.findAll({
            where: { business_reference: reference,activity_code: code },
        });
        // Array to store existing products
        let existingProducts = [];

        // Loop through the products to find existing products in ProductModel
        for (const product of products) {
            const  code  = product.product_code; // Assuming 'code' is the identifier for products
            const existingProduct = await ProductsModel.findOne({
                where: { code: code }
            });
            if (existingProduct) {
                existingProducts.push(existingProduct);
            }
        }
        const response = {
            data: {
                business_reference: reference,
                activity_code: code,
                products: existingProducts,
            },
            total_count: existingProducts.length,
        };

        // Sending the response
        res.send(response);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
}

// GET /api/v1/activities/{code}/products/{productcode}
async function getProductByActivityCodeAndProductCode(req, res) {
    try {
        const { code, productCode } = req.params;
        console.log("req.prams", req.params);
        const product = await Business_Activity_ProductsModel.findOne({
            where: { product_code: productCode, activity_code: code },
        });
        if (!product) {
            return res
                .status(404)
                .json({
                    error:
            "Product not found for the given product CODE and activity CODE",
                });
        }
        else{
            const existing_product = await ProductsModel.findAll({
                where: {
                    code:productCode 
                },
                
            })
            res.send({ data: { activity_code: code,product:existing_product } });
        }
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
}
// POST /api/v1/activities/{code}/products
async function createProductForActivity(req, res) {
    try {
        console.log("req",req.params)
        const code= req.params.code;
        const reference  = req.params.reference;
        const products = req.body.products;
        // Check if products exist
        if (!products || products.length === 0) {
            return res
                .status(404)
                .json({ error: "No products found for the given activity code" });
        }

        // Create activity products for each product
        const createdActivityProducts = [];
        const notFoundProducts = [];
        const existingActivityProducts = [];
        for (const product of products) {
            // Check if product exists
            const existingProduct = await ProductsModel.findOne({
                where: { code: product.code },
            });
            if (existingProduct) {
                // Check if activity product already exists
                const existingActivityProduct = await Business_Activity_ProductsModel.findOne({
                    where: {
                        activity_code: code,
                        product_code: product.code,
                        business_reference:reference
                    }
                });
                if (!existingActivityProduct) {
                    // Create activity product only if it doesn't exist
                    const activity_product = await Business_Activity_ProductsModel.create({
                        activity_code: code,
                        product_code: product.code,
                        business_reference:reference
                    });
                    createdActivityProducts.push(activity_product);
                } else {
                    // Activity product already exists
                    existingActivityProducts.push(product.code);
                }
            } else {
                // Record products that do not exist
                notFoundProducts.push(product.code);
            }
        }

        // Construct response body
        const responseBody = {};
        if (notFoundProducts.length > 0) {
            responseBody["error"] = `Products not found: ${notFoundProducts.join(", ")}`;
        }
        if (existingActivityProducts.length > 0) {
            responseBody["error"] = responseBody["error"]
                ? `${responseBody["error"]} | Products already exist in activity: ${existingActivityProducts.join(", ")}`
                : `Products already exist in activity: ${existingActivityProducts.join(", ")}`;
        }
      
        // If all products were not found, return error
        if (notFoundProducts.length === products.length) {
            return res.status(404).json(responseBody);
        }

        res.status(201).json({ data: createdActivityProducts, ...responseBody });
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
}
// DELETE /api/v1/activities/{code}/products/{productCode}
async function deleteProductByActivityCodeAndProductCode(req, res) {
    try {
        const activityCode = req.params.code;
        const productCode = req.params.productCode;

        const numRowsDeleted = await Business_Activity_ProductsModel.destroy({
            where: { activity_code: activityCode, product_code: productCode },
        });

        console.log("numRowsDeleted", numRowsDeleted);

        if (numRowsDeleted === 0) {
            return res
                .status(404)
                .json({
                    error:
            "Product not found for the given activity CODE and product CODE",
                });
        }

        res.send({
            data: {
                message: `Resource with activity code: ${activityCode}and productcode: ${productCode} has been deleted successfully`,
            },
        });
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
}

module.exports = {
    search,
    getOne,
    getProductsByActivityCode,
    getProductByActivityCodeAndProductCode,
    createProductForActivity,
    deleteProductByActivityCodeAndProductCode,
};
