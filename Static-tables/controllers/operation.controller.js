/**
 * Operation Controller
 * Enhanced with generic CRUD methods + specialized mapping methods
 */

const { operationController } = require("../utils/controller-factory");

// Import dependencies for custom methods
const {
  OperationModel,
  OperationTranslationsModel,
  NatureModel,
  TypeModel,
  sequelize,
  OperationNatureMappingModel,
  OperationNatureTypeMappingModel,
} = require("../db");
const { Op, QueryTypes } = require("sequelize");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");

async function search(req, res, next) {
  try {
    let whereCondition = {};
    let translationWhereCondition = {};
    const limit = req.query.limit ? req.query.limit : config.limit;
    const offset = req.query.offset
      ? req.query.offset 
      : config.offset ;
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;
    const language = req.query.language
      ? req.query.language.toUpperCase()
      : config.defaultReturnedLanguage;

    Object.keys(req.query).forEach((key) => {
      if (
        !["offset", "limit", "sort_by", "order_by", "language"].includes(key)
      ) {
        if (language === config.language || key !== "label") {
          whereCondition[key] = req.query[key];
        } else {
          translationWhereCondition[key] = req.query[key];
        }
      }
    });

    const getSortingOrder = (sortBy, orderBy) => {
      if (language !== config.language && sortBy === "label") {
        return [[OperationTranslationsModel, "label", orderBy]];
      }
      return [[sortBy, orderBy]];
    };

    let operations = (
      await OperationModel.findAll({
        order: getSortingOrder(sortBy, orderBy),
        offset,
        limit,
        where: whereCondition,
        include: [
          ...(language !== config.language
            ? [
                {
                  model: OperationTranslationsModel,
                  attributes: ["label"],
                  where: translationWhereCondition,
                },
              ]
            : []),
          {
            model: NatureModel,
            through: { attributes: [] },
          },
        ],
      })
    ).map((operation) => {
      return {
        id: operation.id,
        code: operation.code,
        label:
          language !== config.language && operation.operation_translations
            ? operation.operation_translations[0].label
            : operation.label,
        active: operation.active,
        system_attribute: operation.system_attribute,
        natures_count: operation.natures?.length || 0,
      };
    });

    let total_count = await OperationModel.count({
      where: whereCondition,
      include: {
        model: OperationTranslationsModel,
        where: translationWhereCondition,
      },
    });

    let nature_total_count = await NatureModel.count();
    let types_total_count = await TypeModel.count();

    res.send({
      data: operations,
      total_count,
      nature_total_count,
      types_total_count,
    });
  } catch (error) {
    next(error);
  }
}

// Custom search method with nature counts preserved

async function getOperationsMapping(req, res, next) {
  try {
    const language = req.query.language
      ? req.query.language
      : config.defaultReturnedLanguage;

    const rows = await sequelize.query(
      `
            select
                operation.id, operation.code, operation.active,
                nature.id AS nature_id, nature.code AS nature_code,
                operation_nature_mapping.nature_status AS nature_status, operation_nature_mapping.operation_nature_code AS operation_nature_code,
                operation_nature_type_mapping.type_status  AS type_status, operation_nature_type_mapping.operation_nature_type_code as operation_nature_type_code,
                type.id AS type_id, type.code AS type_code,
                ${
                  language.toUpperCase() === config.defaultReturnedLanguage
                    ? `
                    operation_translations.label as label,
                    nature_translations.label as nature_label,
                    type_translations.label as type_label
                    `
                    : "operation.label as label , nature.label as nature_label, type.label as type_label"
                }
                    FROM operations AS operation
                    JOIN operation_nature_mapping
                    ON operation_nature_mapping.operation_code = operation.code
                    JOIN natures AS nature
                    ON nature.code = operation_nature_mapping.nature_code
                    JOIN operation_nature_type_mapping
                    ON operation_nature_type_mapping.operation_nature_code = operation_nature_mapping.operation_nature_code
                    JOIN types AS type ON type.code = operation_nature_type_mapping.type_code
              ${
                language.toUpperCase() === config.defaultReturnedLanguage
                  ? `
                      JOIN operation_translations ON operation_translations.operation_code = operation.code
                      JOIN type_translations ON type_translations.type_code = type.code
                      JOIN nature_translations ON nature_translations.nature_code = nature.code
                    `
                  : ""
              }
            WHERE type_status = true
            AND nature_status = true
            AND operation.active = true
            ORDER BY operation.label ASC
            `,
      { type: QueryTypes.SELECT }
    );

    const result = [];

    rows.forEach((row) => {
      let operation = result.find((op) => op.id === row.id);
      if (!operation) {
        operation = {
          id: row.id,
          code: row.code,
          label: row.label,
          natures: [],
        };
        result.push(operation);
      }

      let nature = operation.natures.find((nat) => nat.id === row.nature_id);
      if (!nature) {
        nature = {
          id: row.nature_id,
          code: row.nature_code,
          label: row.nature_label,
          operation_nature_code: row.operation_nature_code,
          types: [],
        };
        operation.natures.push(nature);
      }

      nature.types.push({
        id: row.type_id,
        code: row.type_code,
        label: row.type_label,
        operation_nature_type_code: row.operation_nature_type_code,
      });

      operation.natures.sort((a, b) => a.label.localeCompare(b.label));
      nature.types.sort((a, b) => a.label.localeCompare(b.label));
    });

    res.send({ data: result, total_count: result.length });
  } catch (error) {
    next(error);
  }
}

async function getOperationsByNature(req, res, next) {
  try {
    const natureCode = req.params.natureCode || "PRES";
    const language = req.query.language
      ? req.query.language.toUpperCase()
      : config.defaultReturnedLanguage;

    // Find all operation-nature mappings with the specified nature code
    const mappings = await OperationNatureMappingModel.findAll({
      where: {
        nature_code: natureCode,
      },
      attributes: ["operation_code", "operation_nature_code"],
      raw: true,
    });

    // Extract operation codes from mappings and create a lookup for operation_nature_code
    const operationCodes = mappings.map((mapping) => mapping.operation_code);
    const operationNatureCodeMap = mappings.reduce((acc, mapping) => {
      acc[mapping.operation_code] = mapping.operation_nature_code;
      return acc;
    }, {});

    // Get the actual operations
    const operations = await OperationModel.findAll({
      where: {
        code: {
          [Op.in]: operationCodes,
        },
        active: true,
      },
      include: [
        ...(language !== config.language
          ? [
              {
                model: OperationTranslationsModel,
                attributes: ["label"],
                where: { language_code: language },
              },
            ]
          : []),
      ],
      order: [["label", "ASC"]],
    });

    // Format the response
    const result = operations.map((operation) => {
      return {
        id: operation.id,
        code: operation.code,
        label:
          language !== config.language && operation.operation_translations
            ? operation.operation_translations[0].label
            : operation.label,
        active: operation.active,
        operation_nature_code: operationNatureCodeMap[operation.code]
      };
    });

    res.send({
      data: result,
      total_count: result.length,
    });
  } catch (error) {
    next(error);
  }
}


async function getOperationNatureTypeCodes(req, res, next) {
  try {
    const { operationNatureCode } = req.params;
    
    if (!operationNatureCode) {
      throw new NotFoundError("Operation nature code is required", "OperationNatureMapping");
    }

    // Find the operation nature mapping to get the operation code
    const operationNatureMapping = await OperationNatureMappingModel.findOne({
      where: {
        operation_nature_code: operationNatureCode,
        nature_status: true
      },
      attributes: ['operation_code']
    });

    const operationCode = operationNatureMapping ? operationNatureMapping.operation_code : null;

    // Find all operation nature type mappings with the given operation_nature_code
    const typeCodeMappings = await OperationNatureTypeMappingModel.findAll({
      where: {
        operation_nature_code: operationNatureCode,
        type_status: true
      },
      attributes: ['operation_nature_type_code', 'type_code'],
      include: [
        {
          model: TypeModel,
          attributes: ['id', 'code', 'label'],
        }
      ]
    });

    if (!typeCodeMappings || typeCodeMappings.length === 0) {
      return res.send({
        data: [],
        total_count: 0
      });
    }

    // Format the response
    const result = typeCodeMappings.map(mapping => {
      return {
        operation_code: operationCode,
        operation_nature_code: operationNatureCode,
        operation_nature_type_code: mapping.operation_nature_type_code,
        type_code: mapping.type_code,
        type: mapping.type ? {
          id: mapping.type.id,
          code: mapping.type.code,
          label: mapping.type.label
        } : null
      };
    });

    res.send({
      data: result,
      total_count: result.length
    });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  // Custom search method with specific filtering and sorting logic
  search,

  // Standard CRUD methods using enhanced generic controller
  getOne: operationController.getOne,
  create: operationController.create,
  update: operationController.update,
  remove: operationController.remove,

  // Specialized mapping methods preserved
  getOperationsMapping,
  getOperationsByNature,
  getOperationNatureTypeCodes
};
