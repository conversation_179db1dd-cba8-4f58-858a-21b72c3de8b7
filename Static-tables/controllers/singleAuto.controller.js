const { SingleAutoModel, AutoCatalogModel } = require("../db");
const config = require("../config/app-config");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const { v4: uuidv4 } = require('uuid'); // Import the UUID generation function
const { sendSyncMessage } = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");

async function createSingleAuto(req, res, next) {
  try {
    const { model_code } = req.body;
    const existing = await SingleAutoModel.findOne({ where: { model_code } });
    if (existing) {
      throw new ConflictError(
        "SingleAuto with matching model_code already exists",
        "SingleAuto"
      );
    }
    const newItem = await SingleAutoModel.create(req.body);
    res.status(201).send({ data: newItem });
  } catch (error) {
    next(error);
  }
}

async function getAllSingleAutos(req, res, next) {
  try {
    const limit = req.query.limit ? req.query.limit : config.limit;
        const offset = req.query.offset
          ? req.query.offset 
          : config.offset ;
        const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
        const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;

        const where = req.filterConditions || {}; 

    Object.keys(req.query).forEach((key) => {
      if (!["offset", "limit", "sort_by", "order_by"].includes(key)) {
        where[key] = req.query[key];
      }
    });
    const { count: total_count, rows } = await SingleAutoModel.findAndCountAll({
      order: [[sortBy, orderBy]],
      offset,
      limit,
      where,
    });
    res.send({ data: rows, total_count });
  } catch (error) {
    next(error);
  }
}

async function getSingleAutoById(req, res, next) {
  try {
    const id = req.params.id;
    const item = await SingleAutoModel.findByPk(id);
    if (!item) {
      throw new NotFoundError("SingleAuto not found", "SingleAuto");
    }
    res.send({ data: item });
  } catch (error) {
    next(error);
  }
}

async function updateSingleAuto(req, res, next) {
  try {
    const id = req.params.id;
    const item = await SingleAutoModel.findByPk(id);
    if (!item) {
      throw new NotFoundError("SingleAuto not found", "SingleAuto");
    }

    const updatedData = { ...req.body };
    await item.update(updatedData);
    res.send({ data: item });
  } catch (error) {
    next(error);
  }
}

async function deleteSingleAuto(req, res, next) {
  try {
    const id = req.params.id;
    const item = await SingleAutoModel.findByPk(id);
    if (!item) {
      throw new NotFoundError("SingleAuto not found", "SingleAuto");
    }
    await item.destroy();
    res.send({
      data: {
        message: `SingleAuto with id ${id} has been deleted successfully`,
      },
    });
  } catch (error) {
    next(error);
  }
}

async function getSingleAutosByBrandCode(req, res, next) {
  try {
    const { brand_code } = req.params;
    const limit = req.query.limit ? req.query.limit : config.limit;
    const offset = req.query.offset ? req.query.offset : config.offset;
    const sortBy = req.query.sort_by ? req.query.sort_by : config.SortBy;
    const orderBy = req.query.order_by ? req.query.order_by : config.OrderBy;

    // Check if the brand_code exists in AutoCatalogModel
    const autoCatalog = await AutoCatalogModel.findOne({
      where: { code: brand_code },
    });
    if (!autoCatalog) {
      throw new NotFoundError("Brand code not found in AutoCatalog", "AutoCatalog");
    }

    // Use filters from middleware
    const where = req.filterConditions || {};
    where.brand_code = brand_code; // Ensure brand_code is always included in the filter
  
    // Fetch filtered data
    const { count: total_count, rows: singleAutos } = await SingleAutoModel.findAndCountAll({
      where,
      order: [[sortBy, orderBy]],
      offset,
      limit,
    });

    const combinedData = singleAutos.map((auto) => ({
      brand: {
        code: auto.brand_code,
        label: auto.brand_label,
      },
      model_group: {
        code: auto.model_group_code,
        label: auto.model_group_label,
      },
      body: {
        code: auto.body_code,
        label: auto.body_label,
      },
      model: {
        code: auto.model_code,
        label: auto.model_label,
      },
      doors_number: auto.doors_number,
      version: {
        code: auto.version_code,
        label: auto.version_label,
      },
      variant: {
        code: auto.variant_code,
        label: auto.variant_label,
      },
      color: {
        code: auto.color_code,
        label: auto.color_label,
      },
      interior: {
        code: auto.interior_code || null,
        label: auto.interior_label || null,
      },
      marketing_flag: auto.marketing_flag,
      energy: {code:auto.energy_code, label: auto.energy_label},
      type: { code: auto.type_code, label: auto.type_label },
      power: auto.power,
      class_auto: {code:auto.class_code, label:auto.class_label},
      public_price_incl_tax: auto.public_price_incl_tax,
      status: auto.status,
      equipment: auto.equipment,
      is_new: auto.is_new,
      vds_vehicle: auto.vds_vehicle,
      painting_payment_code: auto.painting_payment_code,
    }));

    res.send({ data: combinedData, total_count });
  } catch (error) {
    next(error);
  }
}

async function uploadAutos(req, res, next) {
  try {
    const {items} = req.body

    const itemsWithReference = items.map((item) => ({
      ...item,
      reference: uuidv4(),
    }));

    await SingleAutoModel.bulkCreate(itemsWithReference, { validate: true })
    await sendSyncMessage(
      OperationType.POST,
      "Vehicle",
      "Static-tables",
      itemsWithReference
    );
    res.send({
      data: {
        message: `Data has been created successfully`,
      },
    });

  } catch (err) {
    next(err);
  }
}

module.exports = {
  createSingleAuto,
  getAllSingleAutos,
  getSingleAutoById,
  updateSingleAuto,
  deleteSingleAuto,
  getSingleAutosByBrandCode,
  uploadAutos
};
