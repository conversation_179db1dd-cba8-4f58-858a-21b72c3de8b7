const express = require("express");
const bodyParser = require("body-parser");
const ActivityRouter = require("./routes/activity.route");
const ProductsRouter = require("./routes/product.route");
const CountriesRouter = require("./routes/country.route");
const CurrencyRouter = require("./routes/currency.route");
const DelegationsRouter = require("./routes/delegation.route");
const MilestonesRouter = require("./routes/milestone.route");
const legalCategoriesRouter = require("./routes/legalcategory.route");
const PhasesRouter = require("./routes/phase.route");
const RolesRouter = require("./routes/role.route");
const TaxRateRouter = require("./routes/tax_rates.route");
const TaxRouter = require("./routes/tax.route");
const AllocationsRouter = require("./routes/allocation.route");
const ViewRouter = require("./routes/view.route");
const OperationsRouter = require("./routes/operation.route");
const Allocation_CategoriesRouter = require("./routes/allocation_category.route");
const Business_Activiy_ProductsRouter = require("./routes/business_activity_products.route");
const Business_Activiy_Product_allocationsRouter = require("./routes/business_activiy_product_allocations.route");
const Business_ActivitiesRouter = require("./routes/business_activities.route");
const Tax_Tax_RatesRouter = require("./routes/tax_tax_rates.route");
const Phases_Milestones_RatesRouter = require("./routes/phases_milestones.route");
const GobalViewModelRouter = require("./routes/global_view.route");
const PaymentMethodsRouter = require("./routes/paymentmethod.route");
const ProfileRouter = require("./routes/profile.route");
const TypeRouter = require("./routes/type.route");
const ProfileActionRouter = require("./routes/profile_action.route");
const UserRouter = require("./routes/user.route");
const LanguageRouter = require("./routes/language.route");
const NapsRouter = require("./routes/nap.route");
const AutoCatalogRouter = require("./routes/autoCatalog.route");
const SingleAutoRouter = require("./routes/singleAuto.route");
const MarketRouter = require("./routes/market.route");
const EquipmentRouter = require("./routes/equipment.route");
const NafsRouter = require("./routes/naf.route");
const BanksRouter = require("./routes/bank.route");
const ScalesRouter = require("./routes/scale.route");
const ServicesRouter = require("./routes/service.route");
const ServicesPackRouter = require("./routes/services_pack.route");

const path = require("path");
const app = express();
// const cacheMiddleware = require("./middlewares/cache.middleware")
const ErrorHandlerModule = require("./error/handler/errorHandler");
const swaggerUi = require("swagger-ui-express");
const swaggerDocument = require("./swagger.json");
// cache middleware
// app.use(cacheMiddleware);

require("dotenv").config({
  path: `.env.${process.env.NODE_ENV}`, // Loads .env.local, .env.development, etc.
});

const { httpPort } = require("./config/app-config");

require("./microservice/register");
const LiquibaseConfig = require('./config/liquibase-config');

// let's have documentation at the root
app.use(express.static("doc"));

// parse application/x-www-form-urlencoded
app.use(bodyParser.urlencoded({ extended: false }));

// parse application/json
app.use(bodyParser.json());

// enable CORS
/*app.use(function(req, res, next) {
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, token");
    next();
});*/

app.get("/docs", function (req, res) {
  res.sendFile(path.join(__dirname + "/doc/index.html"));
});

/* Uncomment below lines if JWT authentication is to be used */
// it will be good to move below two imports to top of the file
//const AuthRouter = require("./routes/auth.route");
//const jwtMiddleware = require("./middlewares/jwt.middleware");

// router rules for auth
//app.use("/v1/auth", AuthRouter);

// NOTE - we are purposely adding jwt middleware after auth
// this is to skip token checks for above auth routes

//TODO
//app.use(jwtMiddleware);

//activities api routes
app.use("/api/v1/static-tables/activities", ActivityRouter);
app.use("/api/v1/static-tables/products", ProductsRouter);
app.use("/api/v1/static-tables/countries", CountriesRouter);
app.use("/api/v1/static-tables/currencies", CurrencyRouter);
app.use("/api/v1/static-tables/delegations", DelegationsRouter);
app.use("/api/v1/static-tables/milestones", MilestonesRouter);
app.use("/api/v1/static-tables/legal-categories", legalCategoriesRouter);
app.use("/api/v1/static-tables/phases", PhasesRouter);
app.use("/api/v1/static-tables/roles", RolesRouter);
app.use("/api/v1/static-tables/tax-rates", TaxRateRouter);
app.use("/api/v1/static-tables/taxes", TaxRouter);
app.use("/api/v1/static-tables/allocations", AllocationsRouter);
app.use("/api/v1/static-tables/operations", OperationsRouter);
app.use(
  "/api/v1/static-tables/allocation-categories",
  Allocation_CategoriesRouter
);
app.use("/api/v1/static-tables", Business_Activiy_ProductsRouter);
app.use("/api/v1/static-tables", Business_Activiy_Product_allocationsRouter);
app.use("/api/v1/static-tables", Business_ActivitiesRouter);
app.use("/api/v1/static-tables", Tax_Tax_RatesRouter);
app.use("/api/v1/static-tables", Phases_Milestones_RatesRouter);
app.use("/api/v1/static-tables/views", ViewRouter);
app.use("/api/v1/static-tables/collection", GobalViewModelRouter);
app.use("/api/v1/static-tables/payment-methods", PaymentMethodsRouter);
app.use("/api/v1/static-tables/profiles", ProfileRouter);
app.use("/api/v1/static-tables/types", TypeRouter);
app.use("/api/v1/static-tables/profile-actions", ProfileActionRouter);
app.use("/api/v1/static-tables/users", UserRouter);
app.use("/api/v1/static-tables/languages", LanguageRouter);
app.use("/api/v1/static-tables/naps", NapsRouter);
app.use("/api/v1/static-tables/auto-catalogs", AutoCatalogRouter);
app.use("/api/v1/static-tables/single-autos", SingleAutoRouter);
app.use("/api/v1/static-tables/markets", MarketRouter);
app.use("/api/v1/static-tables/equipments", EquipmentRouter);
app.use("/api/v1/static-tables/nafs", NafsRouter);
app.use("/api/v1/static-tables/banks", BanksRouter);
app.use("/api/v1/static-tables/scales", ScalesRouter);
app.use("/api/v1/static-tables/services", ServicesRouter);
app.use("/api/v1/static-tables/services-grouping", ServicesPackRouter);
// custom error handlers
// this will also catch async errors since we are usign express-async-errors
// eslint-disable-next-line no-unused-vars
app.use(ErrorHandlerModule.errorHandler);

// Initialize Liquibase and start server
async function startServer() {
  try {
    const liquibaseConfig = new LiquibaseConfig();
    await liquibaseConfig.runMigrations();

    app.listen(httpPort, () => {
      console.log(`✅ Static Tables app listening on port ${httpPort}!`);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

var options = {
  swaggerOptions: {
    url: "/v3/api-docs",
  },
};
app.get("/v3/api-docs", (req, res) => res.json(swaggerDocument));
app.use(
  "/swagger-ui.html",
  swaggerUi.serveFiles(null, options),
  swaggerUi.setup(null, options)
);
