const BaseJoi = require("joi");
const Extension = require("joi-date-extensions");
const Joi = BaseJoi.extend(Extension);

module.exports = {
    create: {
        body: Joi.object().keys({
            code: Joi.string().min(2).max(255).required().label("code"),
            label: Joi.string().min(2).max(255).required().label("label"),
            symbol: Joi.string().min(1).max(255).required().label("symbol"),
            language: Joi.string().min(2).max(255).allow(null, "").label("language"),
            decimal_number: Joi.number().integer().allow(null).label("decimal_number"),
            unit: Joi.number().integer().allow(null).label("Unit"),
            final_effectiveDate: Joi.date().allow(null, "").label("final_effectiveDate"),
            intermediate_period_start_date: Joi.date().allow(null, "").label("intermediate_period_start_date"),
            default_currency: Joi.boolean().default(false).label("default_currency"),
            active: Joi.boolean().default(true).label("active")
        }).options({abortEarly: false}),
    }
};