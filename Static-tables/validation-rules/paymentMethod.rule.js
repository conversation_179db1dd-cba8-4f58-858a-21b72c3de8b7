const BaseJoi = require("joi");
const Extension = require("joi-date-extensions");
const Joi = BaseJoi.extend(Extension);

module.exports = {
    create: {
        body: Joi.object().keys({
            code: Joi.string().min(2).max(255).required().label("code"),
            label: Joi.string().min(2).max(255).required().label("label"),
            requires_bank_account: Joi.boolean(),
            manual_transaction: Joi.boolean(),
            exchange_file: Joi.boolean(),
            bank_card: Joi.boolean(),
            active: Joi.boolean().allow(null).label("active"),
        }).options({abortEarly : false}),
    }
};
