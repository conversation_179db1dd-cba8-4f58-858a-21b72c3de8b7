const BaseJoi = require("joi");
const Extension = require("joi-date-extensions");
const Joi = BaseJoi.extend(Extension);

const equipmentSchema = Joi.object().keys({
  market_code: Joi.string().min(2).max(255).required().label("market_code"),
  category_code: Joi.string().min(2).max(255).required().label("category_code"),
  country_code: Joi.string().min(2).max(255).required().label("country_code"),
  label: Joi.string().min(2).max(255).required().label("label"),
  code: Joi.string().min(2).max(255).required().label("code"),
  active: Joi.boolean().required().optional("active"),
  system_attribute: Joi.boolean().optional().label("system_attribute"),
});

module.exports = {
  create: {
    body: equipmentSchema.options({ abortEarly: false }),
  },
  upload: {
    body: Joi.object().keys({
      items: Joi.array().items(equipmentSchema).required().label("items"),
    }).options({ abortEarly: false }),
  },
};
