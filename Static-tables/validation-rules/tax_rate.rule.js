const BaseJoi = require("joi");
const Extension = require("joi-date-extensions");
const Joi = BaseJoi.extend(Extension);

module.exports = {
    create: {
        params: Joi.object().keys({
            tax_code: Joi.string().min(2).max(255).required().label("tax_code"),
        }),
        body: Joi.object().keys({
            code: Joi.string().min(2).max(255).required().label("code"),
            label: Joi.string().min(2).max(255).required().label("label"),
            tax_code: Joi.string().min(2).max(255).label("tax_code"),
            rate: Joi.number().precision(2).min(0).max(255).required().label("rate"),
            start_date: Joi.date().iso().allow(null, "").label("start_date"),
            end_date: Joi.date().iso().allow(null, "").label("end_date"),
            creationDate: Joi.string().allow(null, "").label("creationDate"),
        }).options({abortEarly : false}),
    },
    createTable: {
        params: Joi.object().keys({
            tax_code: Joi.string().min(2).max(255).required().label("tax_code"),
        }),
        body: Joi.object().keys({
            rates: Joi.array().items(
                Joi.object().keys({
                    reference: Joi.string().uuid().optional().label("reference"),
                    code: Joi.string().min(2).max(255).optional().label("code"),
                    active: Joi.boolean().required().label("active"),
                    rate: Joi.number().precision(2).min(0).max(255).required().label("rate"),
                    start_date: Joi.date().iso().allow(null, "").label("start_date"),
                    end_date: Joi.date().iso().allow(null, "").label("end_date"),
                    creationDate: Joi.string().allow(null, "").label("creationDate"),
                })
            ).required().label("rates"),
        }).options({ abortEarly: false }),
    }
};