const BaseJoi = require("joi");
const Extension = require("joi-date-extensions");
const Joi = BaseJoi.extend(Extension);

module.exports = {
    create: {
        body: Joi.object().keys({
            products: Joi.array().items(
                Joi.object().keys({
                    code: Joi.string().min(2).max(255).required().label("product_code"),
                })
            ).required(),
        }),
    }
};
