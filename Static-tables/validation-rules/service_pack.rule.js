const BaseJoi = require("joi");
const Extension = require("joi-date-extensions");
const Joi = BaseJoi.extend(Extension);

module.exports = {
    create: {
        body: Joi.object().keys({
            code: Joi.string().allow(null, '').optional().label('code'),
            label: Joi.string().allow('').required().label('label'),
            invoice_grouping_code: Joi.string().allow(null, '').label('invoice_grouping_code').when('type', {
                    is: 'COMPATIBLE',
                    then: Joi.required(),
                    otherwise: Joi.optional()
                }).label('services'),
            grouping_code_regulation: Joi.string().allow(null, '').label('grouping_code_regulation').when('type', {
                    is: 'COMPATIBLE',
                    then: Joi.required(),
                    otherwise: Joi.optional()
                }).label('services'),
            type: Joi.string().optional().default('COMPATIBLE').valid('COMPATIBLE', 'INCOMPATIBLE').label('type'),                   
            services: Joi.array().items(Joi.string().allow(null, '')).optional().label('services')
        }).options({ abortEarly: false }),
    },
};