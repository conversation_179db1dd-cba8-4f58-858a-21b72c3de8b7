const BaseJoi = require("joi");
const Extension = require("joi-date-extensions");
const Joi = BaseJoi.extend(Extension);

module.exports = {
    create: {
        body: Joi.object().keys({
            code: Joi.string().min(2).max(255).required().label("code"),
            label: Joi.string().min(2).max(255).required().label("label"),
            active: Joi.boolean().required().label("active"),
            start_date: Joi.date().iso().allow(null, "").label("start_date"),
            end_date: Joi.date().iso().allow(null, "").label("end_date"),
            activity_code: Joi.string().required().label("activity_code"),
            types: Joi.array().items(Joi.string()).label("types"),
            system_attribute: Joi.boolean().optional().label("system_attribute"),
            language: Joi.string().min(2).max(255).optional().label("language"),
        }).options({abortEarly : false}),
    },
};
