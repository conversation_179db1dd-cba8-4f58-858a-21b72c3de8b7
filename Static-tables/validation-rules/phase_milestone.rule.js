const BaseJoi = require("joi");
const Extension = require("joi-date-extensions");
const Joi = BaseJoi.extend(Extension);

module.exports = {
    create: {
        
        body: Joi.object().keys({
            phase_code: Joi.string().min(2).max(255).required().label("phase_code"),
            milestone_code: Joi.string().min(2).max(255).required().label("milestone_code"),
            associated_to: Joi.string().min(2).max(255).required().label("associated_to"),            
        }),
    }
};
