const BaseJoi = require("joi");
const Extension = require("joi-date-extensions");
const Joi = BaseJoi.extend(Extension);

module.exports = {
    create: {
        body: Joi.object().keys({
            intended_for: Joi.string().optional().label('intended_for'),
            label: Joi.string().required().label('label'),
            type_of_service: Joi.string().required().label('type_of_service'),
            type_of_cover: Joi.string().optional().label('type_of_cover'),
            start_date: Joi.date().iso().optional().allow(null, '').label("start_date"),
            end_date: Joi.date().iso().optional().allow(null, '').label("end_date"),
            currency_code: Joi.string().required().label('currency_code'),
            status: Joi.string().required().label('status'),
            service_reference: Joi.string().uuid().optional().label('service_reference'),
            out_of_contract_termination: Joi.boolean().optional().label('out_of_contract_termination'),
            maximum_asset_price: Joi.number().optional().label('maximum_asset_price'),
            minimum_asset_price: Joi.number().optional().label('minimum_asset_price'),
            maximum_contract_duration: Joi.number().optional().label('maximum_contract_duration'),
            minimum_contract_duration: Joi.number().optional().label('minimum_contract_duration'),
            is_enterprise: Joi.boolean().optional().label('is_enterprise'),
            is_enterprise_individuelle: Joi.boolean().optional().label('is_enterprise_individuelle'),
            is_enterprise_publique: Joi.boolean().optional().label('is_enterprise_publique'),
            is_particulier: Joi.boolean().optional().label('is_particulier'),
            is_registrable: Joi.boolean().optional().label('is_registrable'),
            is_unregistrable: Joi.boolean().optional().label('is_unregistrable'),
            billing_method: Joi.string().optional().label('billing_method'),
            calculation_method: Joi.string().optional().label('calculation_method'),
            amount_excl_tax: Joi.number().optional().label('amount_excl_tax'),
            basis_of_calculation: Joi.string().optional().label('basis_of_calculation'),
            tax_code: Joi.string().optional().label('tax_code'),
            calculation_percentage: Joi.number().optional().label('calculation_percentage'),
            tax_value: Joi.number().optional().label('tax_value'),
            
            // Partner-related fields
            actor_reference: Joi.string().optional().label('actor_reference'),
            operation_nature_type_code: Joi.string().optional().label('operation_nature_type_code'),
            partner_currency_code: Joi.string().optional().label('partner_currency_code'),
            mandate_type: Joi.string().optional().label('mandate_type'),
            external_reference: Joi.string().optional().label('external_reference'),
            remittance_method: Joi.string().optional().label('remittance_method'),
            partner_calculation_method: Joi.string().optional().label('partner_calculation_method'),
            partner_amount_excl_tax: Joi.number().optional().label('partner_amount_excl_tax'),
            partner_basis_of_calculation: Joi.string().optional().label('partner_basis_of_calculation'),
            partner_tax_code: Joi.string().optional().label('partner_tax_code'),
            partner_percentage_calculation: Joi.number().optional().label('partner_percentage_calculation'),
            partner_tax_value: Joi.number().optional().label('partner_tax_value'),
            
            // Association arrays
            operation_nature_types: Joi.array().items(Joi.string()).optional().label('operation_nature_types'),
            products: Joi.array().items(Joi.string()).optional().label('products'),
            naps: Joi.array().items(Joi.string()).optional().label('naps'),
            actors: Joi.array().items(Joi.string()).optional().label('actors'),
        }).options({ abortEarly: false }),
    },
};