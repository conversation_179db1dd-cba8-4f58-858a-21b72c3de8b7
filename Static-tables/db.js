const Sequelize = require("sequelize");
const ActivityModel =require("./models/activity.model")
const ProductsModel=require("./models/product.model.js")
const CountriesModel =require("./models/country.model.js")
const CurrencyModel= require("./models/currency.model.js")
const DelegationModel= require("./models/delegation.model.js")
const MilestoneModel=require("./models/milestone.model.js")
const LegalCategoryModel=require("./models/legalcategory.model.js")
const PhaseModel=require("./models/phase.model.js")
const RoleModel=require("./models/role.model.js")
const TaxrateModel=require("./models/tax_rate.model.js")
const TaxModel=require("./models/tax.model.js")
const AllocationModel=require("./models/allocation.mode.js")
const ViewModel=require("./models/view.model.js")
const OperationModel=require("./models/operation.model.js")
const Allocation_CategoryModel=require("./models/allocation_category.mode.js")
const Business_Activity_ProductsModel=require("./models/business_activity_products.model.js")
const Business_Activity_Product_AllocationsModel=require("./models/business_activity_product_allocations.model.js")
const Business_ActivitiesModel=require("./models/business_activities.model.js")
const Phase_milestoneModel=require("./models/phase_milestone.model.js")
const ActivityTranslationModel=require("./models/activitytranslation.model.js")
const AllocationTranslationsModel=require("./models/allocation.translations.js")
const ProductTranslationsModel=require("./models/product.translations.js")
const CountryTranslationsModel=require("./models/country.translations.js")
const CurrencyTranslationsModel=require("./models/currency.translations.js")
const DelegationTranslationsModel=require("./models/delegation.translations.js")
const MilestoneTranslationsModel=require("./models/milestone.translations.js")
const LegalCategoryTranslationsModel=require("./models/legal_category.translations.js")
const PhaseTranslationsModel=require("./models/phase.translations.js")
const RoleTranslationsModel=require("./models/role.translations.js")
const TaxrateTranslationsModel=require("./models/tax_rate.translations.js")
const TaxTranslationsModel=require("./models/tax.translations.js")
const OperationTranslationsModel=require("./models/operation.translations.js")
const GlobalViewModel=require("./models/global_view.model.js")
const PaymentMethodModel=require("./models/payment_method.model.js") 
const PaymentMethodTranslationsModel=require("./models/payment_method.translations.js")
const config = require("./config/app-config.js");
const NatureModel =require("./models/nature.model.js")
const NatureTranslationsModel =require("./models/nature_translations.js")
const TypeModel =require("./models/type.model.js")
const TypeTranslationsModel =require("./models/type_translations.model.js");
const OperationNatureTypeMappingModel =require("./models/operation_nature_type_mapping.model.js");
const OperationNatureMappingModel =require("./models/operation_nature_mapping.model.js");
const ProductTypeMappingModel= require("./models/product_type_mapping.model.js");
const ProfileModel =require("./models/profile.model.js");
const ProfileTranslationsModel =require("./models/profile.translation.js");
const ProfileActionModel =require("./models/profile_action.model.js");
const UserModel = require("./models/user.model.js");
const ProfileActionMappingModel = require("./models/profile_action_mapping.model.js");
const ProfileActionTranslationsModel = require("./models/profile_action.translations.js");
const LanguageModel = require("./models/language.model.js");
const StaticRoleModel = require("./models/static_role.model.js");
const StaticRoleTranslationsModel = require("./models/static_role.translation.js");
const NapModel = require("./models/nap.model.js");
const AutoCatalogModel = require("./models/autoCatalog.model");
const SingleAutoModel = require("./models/singleAuto.model");
const MarketModel = require("./models/market.model");
const EquipmentModel = require("./models/equipment.model.js");
const NafModel = require("./models/naf.model.js");
const NafTranslationsModel = require("./models/naf.translations.js");
const BankModel = require("./models/bank.model.js");
const ScaleModel = require("./models/scale.model.js");
const ScaleServiceModel = require("./models/scale_service.model.js");
const ScaleFinancialProductModel = require("./models/scale_financial_product.model.js");
const ScaleCommercialProductModel = require("./models/scale_commercial_product.model.js");
const CommercialProductModel= require("./models/commercial_product.model.js");
const ApplicationCriteriaModel = require("./models/application_criteria.model.js");
const ApplicationCriteriaActorModel = require("./models/application_criteria_actors.model.js");
const FinancialElementVehicleVehicleMappingModel = require("./models/financial_element_vehicle_vehicle_mapping.model.js");
const FinancialElementMaterialModel = require("./models/financial_element_material.model.js");
const FinancialElementMaterialEquipmentMappingModel = require("./models/financial_element_material_equipment_mapping.model.js");
const FinancialElementVehicleModel = require("./models/financial_element_vehicle.model.js");
const thirdPartiesModel = require("./models/third_parties.model.js");
const ServiceModel = require("./models/service.model.js");
const ServiceOperationModel = require("./models/service_operation.model.js");
const ServiceProductModel = require("./models/service_product.model.js");
const ServiceNapModel = require("./models/service_nap.model.js");
const ServiceActorModel = require("./models/service_actor.model.js");
const PartnerModel = require("./models/partner.model.js");
const ServicesPackModel = require("./models/services_pack.model.js")
const ServiceServicesPackModel = require("./models/service_services_pack.model.js");


// initialize database connection
const sequelize = new Sequelize(
    config.db.name, // database name
    config.db.user, // user
    config.db.password, // password
    {
        host: config.db.host,
        port: config.db.port,
        dialect: config.db.dialect,
        timezone: "+00:00" // set time zone to UTC
    }
);

// check database connection
sequelize
    .authenticate()
    .then(() => {
        // eslint-disable-next-line
        console.log("Connection has been established successfully.");
    })
    .catch(err => {
        // eslint-disable-next-line
        console.error("Unable to connect to the database:", err);
    });

const Countries=CountriesModel(sequelize,Sequelize)
const Activity =ActivityModel(sequelize,Sequelize)
const ActivityTranslation = ActivityTranslationModel(sequelize,Sequelize)
const Products =ProductsModel(sequelize,Sequelize)
const Currency=CurrencyModel(sequelize,sequelize)
const Delegation=DelegationModel(sequelize,sequelize)
const Milestone=MilestoneModel(sequelize,Sequelize)
const LegalCategory=LegalCategoryModel(sequelize,Sequelize)
const phase=PhaseModel(sequelize,sequelize)
const Role=RoleModel(sequelize,Sequelize)
const taxrate=TaxrateModel(sequelize,sequelize)
const tax=TaxModel(sequelize,sequelize)
const allocation=AllocationModel(sequelize,sequelize)
const view=ViewModel(sequelize,sequelize)
const Operation=OperationModel(sequelize,sequelize)
const allocation_category=Allocation_CategoryModel(sequelize,sequelize)
const business_activity_products=Business_Activity_ProductsModel(sequelize,sequelize)
const business_activity_product_allocations=Business_Activity_Product_AllocationsModel(sequelize,sequelize)
const business_activities=Business_ActivitiesModel(sequelize,sequelize)
const phase_milestone=Phase_milestoneModel(sequelize,sequelize)
const AllocationTranslations=AllocationTranslationsModel(sequelize,Sequelize)
const ProductTranslations=ProductTranslationsModel(sequelize,Sequelize)
const CountryTranslations=CountryTranslationsModel(sequelize,Sequelize)
const CurrencyTranslations=CurrencyTranslationsModel(sequelize,Sequelize)
const DelegationTranslations=DelegationTranslationsModel(sequelize,Sequelize)
const MilestoneTranslations=MilestoneTranslationsModel(sequelize,Sequelize)
const LegalCategoryTranslations=LegalCategoryTranslationsModel(sequelize,Sequelize)
const PhaseTranslations=PhaseTranslationsModel(sequelize,Sequelize)
const RoleTranslations=RoleTranslationsModel(sequelize,Sequelize)
const TaxrateTranslations=TaxrateTranslationsModel(sequelize,Sequelize)
const TaxTranslations=TaxTranslationsModel(sequelize,Sequelize)
const OperationTranslations=OperationTranslationsModel(sequelize,Sequelize)
const GlobalView=GlobalViewModel(sequelize,Sequelize)
const PaymentMethod=PaymentMethodModel(sequelize,Sequelize)
const PaymentMethodTranslations=PaymentMethodTranslationsModel(sequelize,Sequelize)
const Nature=NatureModel(sequelize,Sequelize)
const NatureTranslations=NatureTranslationsModel(sequelize,Sequelize)
const Type=TypeModel(sequelize,Sequelize)
const TypeTranslations=TypeTranslationsModel(sequelize,Sequelize)
const OperationNatureTypeMapping=OperationNatureTypeMappingModel(sequelize,Sequelize)
const OperationNatureMapping=OperationNatureMappingModel(sequelize,Sequelize)
const Profile=ProfileModel(sequelize,Sequelize)
const ProfileTranslations=ProfileTranslationsModel(sequelize,Sequelize)
const ProductTypeMapping=ProductTypeMappingModel(sequelize,Sequelize)
const ProfileAction=ProfileActionModel(sequelize,Sequelize)
const User=UserModel(sequelize,Sequelize)
const ProfileActionMapping=ProfileActionMappingModel(sequelize,Sequelize)
const ProfileActionTranslations=ProfileActionTranslationsModel(sequelize,Sequelize)
const Language = LanguageModel(sequelize, Sequelize);
const StaticRole = StaticRoleModel(sequelize, Sequelize);
const StaticRoleTranslations = StaticRoleTranslationsModel(sequelize, Sequelize);
const Nap = NapModel(sequelize, Sequelize);
const AutoCatalog = AutoCatalogModel(sequelize, Sequelize);
const SingleAuto = SingleAutoModel(sequelize, Sequelize);
const Market = MarketModel(sequelize, Sequelize);
const Equipment = EquipmentModel(sequelize, Sequelize);
const Naf = NafModel(sequelize, Sequelize);
const NafTranslations = NafTranslationsModel(sequelize, Sequelize);
const Scale = ScaleModel(sequelize, Sequelize);
const ScaleService = ScaleServiceModel(sequelize, Sequelize);
const ScaleFinancialProduct = ScaleFinancialProductModel(sequelize, Sequelize);
const ScaleCommercialProduct = ScaleCommercialProductModel(sequelize, Sequelize);
const CommercialProduct = CommercialProductModel(sequelize, Sequelize);
const ApplicationCriteria = ApplicationCriteriaModel(sequelize, Sequelize);
const ApplicationCriteriaActor = ApplicationCriteriaActorModel(sequelize, Sequelize);
const FinancialElementVehicleVehicleMapping = FinancialElementVehicleVehicleMappingModel(sequelize, Sequelize);
const FinancialElementMaterial = FinancialElementMaterialModel(sequelize, Sequelize);
const FinancialElementMaterialEquipmentMapping = FinancialElementMaterialEquipmentMappingModel(sequelize, Sequelize);
const FinancialElementVehicle = FinancialElementVehicleModel(sequelize, Sequelize);
const thirdParties = thirdPartiesModel(sequelize, Sequelize);
const Bank = BankModel(sequelize, Sequelize);
const Service = ServiceModel(sequelize, Sequelize);
const ServiceOperation = ServiceOperationModel(sequelize, Sequelize);
const ServiceProduct = ServiceProductModel(sequelize, Sequelize);
const ServiceNap = ServiceNapModel(sequelize, Sequelize);
const ServiceActor = ServiceActorModel(sequelize, Sequelize);
const Partner = PartnerModel(sequelize, Sequelize);
const ServicesPack = ServicesPackModel(sequelize, Sequelize);
const ServiceServicesPack = ServiceServicesPackModel(sequelize, Sequelize);

Activity.hasMany(ActivityTranslation, { foreignKey: "activity_code", sourceKey: "code" });
ActivityTranslation.belongsTo(Activity, { foreignKey: "activity_code", targetKey: "code" });

Products.hasMany(ProductTranslations, { foreignKey: "product_code", sourceKey: "code"  });
ProductTranslations.belongsTo(Products, { foreignKey: "product_code", targetKey: "code"  });

Countries.hasMany(CountryTranslations, { foreignKey: "country_code", sourceKey: "code" });
CountryTranslations.belongsTo(Countries, { foreignKey: "country_code", targetKey: "code" });

Currency.hasMany(CurrencyTranslations, { foreignKey: "currency_code", sourceKey: "code" });
CurrencyTranslations.belongsTo(Currency, { foreignKey: "currency_code", targetKey: "code" });

Delegation.hasMany(DelegationTranslations, { foreignKey: "delegation_code", sourceKey: "code" });
DelegationTranslations.belongsTo(Delegation, { foreignKey: "delegation_code", targetKey: "code" });

Milestone.hasMany(MilestoneTranslations, { foreignKey: "milestone_code", sourceKey: "code" });
MilestoneTranslations.belongsTo(Milestone, { foreignKey: "milestone_code", targetKey: "code" });

LegalCategory.hasMany(LegalCategoryTranslations, { foreignKey: "legal_category_code", sourceKey: "code" });
LegalCategoryTranslations.belongsTo(LegalCategory, { foreignKey: "legal_category_code", targetKey: "code" });

phase.hasMany(PhaseTranslations, { foreignKey: "phase_code", sourceKey: "code" });
PhaseTranslations.belongsTo(phase, { foreignKey: "phase_code", targetKey: "code" });

Role.hasMany(RoleTranslations, { foreignKey: "role_code", sourceKey: 'code' });
RoleTranslations.belongsTo(Role, { foreignKey: "role_code", targetKey: 'code' });

taxrate.hasMany(TaxrateTranslations, { foreignKey: "tax_rate_code", sourceKey: "code" });
TaxrateTranslations.belongsTo(taxrate, { foreignKey: "tax_rate_code", targetKey: "code" });

tax.hasMany(TaxTranslations, { foreignKey: "tax_code", sourceKey: "code" });
TaxTranslations.belongsTo(tax, { foreignKey: "tax_code", targetKey: "code" });

allocation.hasMany(AllocationTranslations, { foreignKey: "allocation_code", sourceKey: "code" });
AllocationTranslations.belongsTo(allocation, { foreignKey: "allocation_code", targetKey: "code" });

Operation.hasMany(OperationTranslations, { foreignKey: "operation_code", sourceKey: "code" });
OperationTranslations.belongsTo(Operation, { foreignKey: "operation_code", targetKey: "code" });

Nature.hasMany(NatureTranslations, { foreignKey: "nature_code", sourceKey: "code" });
NatureTranslations.belongsTo(Nature, { foreignKey: "nature_code", targetKey: "code" });

Type.hasMany(TypeTranslations, { foreignKey: "type_code", sourceKey: "code"  });
TypeTranslations.belongsTo(Type, { foreignKey: "type_code", targetKey: "code"  });

PaymentMethod.hasMany(PaymentMethodTranslations, {
    foreignKey: "payment_method_code",
    as: "translations",
    onDelete: "CASCADE"
});
PaymentMethodTranslations.belongsTo(PaymentMethod, {
    foreignKey: "payment_method_code",
    as: "payment_method"
});

Operation.belongsToMany(Nature, {
    through: { model: OperationNatureMapping},  
    foreignKey: "operation_code",
    otherKey: "nature_code",
    sourceKey: "code",
    targetKey: "code" 
});
Nature.belongsToMany(Operation, {
    through: { model: OperationNatureMapping}, 
    foreignKey: "nature_code",
    otherKey: "operation_code",
    sourceKey: "code", 
    targetKey: "code" 
});

OperationNatureMapping.belongsTo(Nature, { foreignKey: "nature_code", targetKey: "code" });
Nature.hasMany(OperationNatureMapping, { foreignKey: "nature_code", sourceKey: "code" });

OperationNatureMapping.belongsTo(Operation, { foreignKey: "operation_code", targetKey: "code" });
Operation.hasMany(OperationNatureMapping, { foreignKey: "operation_code", sourceKey: "code" });

Type.hasMany(OperationNatureTypeMapping, { foreignKey: "type_code", sourceKey: "code" });
OperationNatureTypeMapping.belongsTo(Type, { foreignKey: "type_code", targetKey: "code"});

OperationNatureTypeMapping.belongsTo(OperationNatureMapping, { 
    foreignKey: "operation_nature_code",
    targetKey: "operation_nature_code",
      as: 'operationNatureMapping'
});
OperationNatureMapping.hasMany(OperationNatureTypeMapping, { 
    foreignKey: "operation_nature_code",
    sourceKey: "operation_nature_code",
     as: 'typeMapping'
});

Type.belongsToMany(OperationNatureMapping, {
    through: {model: OperationNatureTypeMapping},
    sourceKey: "code", 
    foreignKey: "type_code", 
    targetKey: "operation_nature_code",
    as: 'operationNatureMappings'
});

OperationNatureMapping.belongsToMany(Type, {
    through: {model: OperationNatureTypeMapping},
    sourceKey: "operation_nature_code", 
    foreignKey: "operation_nature_code", 
    targetKey: "code",
    as: 'typeDetails'
});

Activity.hasMany(Products, { foreignKey: "activity_code", sourceKey: 'code'  })
Products.belongsTo(Activity , { foreignKey: "activity_code", targetKey: 'code'  })

Products.belongsToMany(OperationNatureTypeMapping, {
    through: ProductTypeMapping,
    foreignKey: "product_code",
});
  
OperationNatureTypeMapping.belongsToMany(Products, {
    through: ProductTypeMapping,
    foreignKey: "operation_nature_type_code", 
});

tax.belongsTo(Countries, { foreignKey: "country", targetKey: "code", as: "countryDetails" });
Countries.hasMany(tax, { foreignKey: "country", sourceKey: "code", as: "taxes" });

Profile.hasMany(ProfileTranslations, { foreignKey: "profile_code" });
ProfileTranslations.belongsTo(Profile, { foreignKey: "profile_code" });

Profile.hasMany(User, { foreignKey: "profile_code" });
User.belongsTo(Profile, { foreignKey: "profile_code" })

Profile.belongsToMany(ProfileAction, {
    through: ProfileActionMapping,
    foreignKey: "profile_code",
    otherKey: "profile_action_code",
});

ProfileAction.belongsToMany(Profile, {
    through: ProfileActionMapping,
    foreignKey: "profile_action_code",
    otherKey: "profile_code",
});

Profile.hasMany(ProfileActionMapping, { foreignKey: 'profile_code', sourceKey: 'code' });
ProfileActionMapping.belongsTo(Profile, { foreignKey: 'profile_code', targetKey: 'code' });

ProfileAction.hasMany(ProfileActionMapping, { foreignKey: 'profile_action_code' });
ProfileActionMapping.belongsTo(ProfileAction, { foreignKey: 'profile_action_code' });

ProfileAction.hasMany(ProfileActionTranslations, { foreignKey: "profile_action_code" });
ProfileActionTranslations.belongsTo(ProfileAction, { foreignKey: "profile_action_code" });

StaticRole.hasMany(StaticRoleTranslations, { foreignKey: "static_role_code", sourceKey: 'code' });
StaticRoleTranslations.belongsTo(StaticRole, { foreignKey: "static_role_code", targetKey: 'code' });

StaticRole.hasMany(Role, { foreignKey: "static_role_code", sourceKey: 'code' });
Role.belongsTo(StaticRole, { foreignKey: "static_role_code", targetKey: 'code' });

Equipment.belongsTo(Nap, { foreignKey: "category_code", targetKey: "code", as: "category" });
Nap.hasMany(Equipment, { foreignKey: "category_code", sourceKey: "code",as: "equipments" });

Equipment.belongsTo(Countries, { foreignKey: "country_code", targetKey: "code" });
Countries.hasMany(Equipment, { foreignKey: "country_code", sourceKey: "code" });

Equipment.belongsTo(Market, { foreignKey: "market_code", targetKey: "code" });
Market.hasMany(Equipment, { foreignKey: "market_code", sourceKey: "code" });

Naf.hasMany(NafTranslations, { foreignKey: "naf_code", sourceKey: 'code' });
NafTranslations.belongsTo(Naf, { foreignKey: "naf_code", targetKey: 'code' });

Naf.belongsTo(Countries, { foreignKey: "country_code", targetKey: 'code' });
Countries.hasMany(Naf, { foreignKey: "country_code", sourceKey: 'code' });

LegalCategory.belongsTo(Countries, { foreignKey: "country_code", targetKey: 'code' });
Countries.hasMany(LegalCategory, { foreignKey: "country_code", sourceKey: 'code' });

OperationNatureTypeMapping.hasMany(ProductTypeMapping, { foreignKey: "operation_nature_type_code", sourceKey: 'operation_nature_type_code' });
ProductTypeMapping.belongsTo(OperationNatureTypeMapping, { foreignKey: "operation_nature_type_code", targetKey: 'operation_nature_type_code' });

Products.hasMany(ProductTypeMapping, { foreignKey: "product_code", sourceKey: 'code' });
ProductTypeMapping.belongsTo(Products, { foreignKey: "product_code", targetKey: 'code' });

Currency.hasMany(Scale, { foreignKey: "currency_code", sourceKey: 'code' });
Scale.belongsTo(Currency, { foreignKey: "currency_code", targetKey: 'code' });

Market.hasMany(Scale, { foreignKey: "market_code", sourceKey: 'code' });
Scale.belongsTo(Market, { foreignKey: "market_code", targetKey: 'code' });
 
Countries.hasMany(Scale, { foreignKey: "country_code", sourceKey: 'code' });
Scale.belongsTo(Countries, { foreignKey: "country_code", targetKey: 'code' });

SingleAuto.belongsTo(AutoCatalog, { foreignKey: "brand_code", targetKey: "code" });
AutoCatalog.hasMany(SingleAuto, { foreignKey: "brand_code", sourceKey: "code"});

// Define relationships with Scale
Scale.hasMany(ScaleService, { foreignKey: "scale_code", sourceKey: "code" });
ScaleService.belongsTo(Scale, { foreignKey: "scale_code", targetKey: "code" });

// Define relationships with Scale
Scale.hasMany(ScaleService, { foreignKey: "scale_code", sourceKey: "code" });
ScaleService.belongsTo(Scale, { foreignKey: "scale_code", targetKey: "code" });

// Define relationships with Service
Service.hasMany(ScaleService, { foreignKey: "service_reference", sourceKey: "reference" });
ScaleService.belongsTo(Service, { foreignKey: "service_reference", targetKey: "reference" });

Scale.hasMany(ScaleFinancialProduct, { foreignKey: "scale_code", sourceKey: "code" });
ScaleFinancialProduct.belongsTo(Scale, { foreignKey: "scale_code", targetKey: "code" });

Scale.hasMany(ScaleCommercialProduct, { foreignKey: "scale_code", sourceKey: "code" });
ScaleCommercialProduct.belongsTo(Scale, { foreignKey: "scale_code", targetKey: "code" });

Scale.hasMany(CommercialProduct, { foreignKey: "scale_code", sourceKey: "code" });
CommercialProduct.belongsTo(Scale, { foreignKey: "scale_code", targetKey: "code" });

Scale.hasOne(ApplicationCriteria, { foreignKey: "scale_code", sourceKey: "code", as: "scale_application_criteria" });
ApplicationCriteria.belongsTo(Scale, { foreignKey: "scale_code", targetKey: "code" });

Scale.hasMany(thirdParties, { foreignKey: "scale_code", sourceKey: "code" });
thirdParties.belongsTo(Scale, { foreignKey: "scale_code", targetKey: "code" });

Scale.hasMany(FinancialElementVehicleVehicleMapping, { foreignKey: "scale_code", sourceKey: "code" });
FinancialElementVehicleVehicleMapping.belongsTo(Scale, { foreignKey: "scale_code", targetKey: "code" });

Scale.hasMany(FinancialElementMaterial, { foreignKey: "scale_code", sourceKey: "code" });
FinancialElementMaterial.belongsTo(Scale, { foreignKey: "scale_code", targetKey: "code" });

Scale.hasMany(FinancialElementMaterialEquipmentMapping, { foreignKey: "scale_code", sourceKey: "code" });
FinancialElementMaterialEquipmentMapping.belongsTo(Scale, { foreignKey: "scale_code", targetKey: "code" });

Scale.hasMany(FinancialElementVehicle, { foreignKey: "scale_code", sourceKey: "code" });
FinancialElementVehicle.belongsTo(Scale, { foreignKey: "scale_code", targetKey: "code" });

Equipment.belongsToMany(FinancialElementMaterial, {
    through: { model: FinancialElementMaterialEquipmentMapping},  
    foreignKey: "equipment_code",
    otherKey: "scale_code",
    sourceKey: "code",
    targetKey: "scale_code"
});

Scale.hasMany(FinancialElementVehicleVehicleMapping, { foreignKey: "scale_code", sourceKey: "code" });
FinancialElementVehicleVehicleMapping.belongsTo(Scale, { foreignKey: "scale_code", targetKey: "code" });
 
SingleAuto.belongsToMany(FinancialElementVehicle, {
    through: { model: FinancialElementVehicleVehicleMapping},  
    foreignKey: "vehicle_reference",
    otherKey: "scale_code",
    sourceKey: "reference",
    targetKey: "scale_code"
});
 
Products.belongsToMany(Scale, {
    through: { model: ScaleFinancialProduct},  
    foreignKey: "product_code",
    otherKey: "scale_code",
    sourceKey: "code",
    targetKey: "code"
});
Scale.belongsToMany(Products, {
    through: ScaleFinancialProduct,
    foreignKey: "scale_code",
    otherKey: "product_code",
    sourceKey: "code",
    targetKey: "code"
});

Products.belongsToMany(Scale, {
    through: ScaleFinancialProduct,
    foreignKey: "product_code",
    otherKey: "scale_code",
    sourceKey: "code",
    targetKey: "code"
});

// Add new Service-OperationNatureTypeMapping relationship

Service.belongsToMany(OperationNatureTypeMapping, { 
  through: ServiceOperation, 
  foreignKey: 'service_reference',
  otherKey: 'operation_nature_type_code',
  sourceKey: 'reference',
  targetKey: 'operation_nature_type_code'
});
OperationNatureTypeMapping.belongsToMany(Service, { 
  through: ServiceOperation, 
  foreignKey: 'operation_nature_type_code',
  otherKey: 'service_reference',
  sourceKey: 'operation_nature_type_code',
  targetKey: 'reference'
});

// Add direct relationships for ServiceOperation

ServiceOperation.belongsTo(Service, {
  foreignKey: 'service_reference',
  targetKey: 'reference'
});
Service.hasMany(ServiceOperation, {
  foreignKey: 'service_reference',
  sourceKey: 'reference'
});

ServiceOperation.belongsTo(OperationNatureTypeMapping, {
  foreignKey: 'operation_nature_type_code',
  targetKey: 'operation_nature_type_code'
});
OperationNatureTypeMapping.hasMany(ServiceOperation, {
  foreignKey: 'operation_nature_type_code',
  sourceKey: 'operation_nature_type_code'
})

Service.belongsToMany(Products, { 
  through: ServiceProduct, 
  foreignKey: 'service_reference',
  otherKey: 'product_code',
  sourceKey: 'reference',
  targetKey: 'code'
});
Products.belongsToMany(Service, { 
  through: ServiceProduct, 
  foreignKey: 'product_code',
  otherKey: 'service_reference',
  sourceKey: 'code',
  targetKey: 'reference'
});

Service.belongsToMany(Nap, { 
  through: ServiceNap, 
  foreignKey: 'service_reference',
  otherKey: 'nap_code',
  sourceKey: 'reference',
  targetKey: 'code'
});
Nap.belongsToMany(Service, { 
  through: ServiceNap, 
  foreignKey: 'nap_code',
  otherKey: 'service_reference',
  sourceKey: 'code',
  targetKey: 'reference'
});

Service.hasMany(ServiceActor, { 
  foreignKey: 'service_reference', 
  sourceKey: 'reference' 
});
ServiceActor.belongsTo(Service, { 
  foreignKey: 'service_reference', 
  targetKey: 'reference' 
});

Currency.hasMany(Service, { 
  foreignKey: 'currency_code',
  sourceKey: 'code'
});
Service.belongsTo(Currency, { 
  foreignKey: 'currency_code',
  targetKey: 'code'
});

Service.belongsTo(Partner, { 
  foreignKey: 'partner_reference', 
  targetKey: 'reference' 
});
Partner.hasMany(Service, { 
  foreignKey: 'partner_reference', 
  sourceKey: 'reference' 
});

tax.hasMany(Service, { 
  foreignKey: 'tax_code', 
  sourceKey: 'code' 
});
Service.belongsTo(tax, { 
  foreignKey: 'tax_code', 
  targetKey: 'code' 
});

// Define relationship between Service and ServicesPack
Service.belongsToMany(ServicesPack, { 
    through: ServiceServicesPack, 
    foreignKey: 'service_reference',
    otherKey: 'service_pack_code',
    sourceKey: 'reference',
    targetKey: 'code'
  });
  
  ServicesPack.belongsToMany(Service, { 
    through: ServiceServicesPack, 
    foreignKey: 'service_pack_code',
    otherKey: 'service_reference',
    sourceKey: 'code',
    targetKey: 'reference'
  });


OperationNatureTypeMapping.hasMany(Partner, {
  foreignKey: 'operation_nature_type_code',
  sourceKey: 'operation_nature_type_code',
  as: 'partners'
});

Partner.belongsTo(OperationNatureTypeMapping, {
  foreignKey: 'operation_nature_type_code',
  targetKey: 'operation_nature_type_code',
  as: 'operationNatureTypeDetails'
});

module.exports = {
    "ActivityModel":Activity,
    "ActivityTranslation": ActivityTranslation,
    "ProductsModel":Products,
    "CurrencyModel":Currency,
    "CountriesModel":Countries,
    "DelegationModel":Delegation,
    "MilestoneModel":Milestone,
    "LegalCategoryModel":LegalCategory,
    "PhaseModel":phase,
    "RoleModel":Role,
    "TaxrateModel":taxrate,
    "TaxModel":tax,
    "AllocationModel":allocation,
    "ViewModel":view,
    "OperationModel":Operation,
    "Allocation_CategoryModel":allocation_category,
    "Business_Activity_ProductsModel":business_activity_products,
    "Business_Activity_Product_AllocationsModel":business_activity_product_allocations,
    "Business_ActivitiesModel":business_activities,
    "Phase_milestoneModel":phase_milestone,
    "AllocationTranslationsModel":AllocationTranslations,
    "ProductTranslationsModel":ProductTranslations,
    "CountryTranslationsModel":CountryTranslations,
    "CurrencyTranslationsModel":CurrencyTranslations,
    "DelegationTranslationsModel":DelegationTranslations,
    "MilestoneTranslationsModel":MilestoneTranslations,
    "LegalCategoryTranslationsModel":LegalCategoryTranslations,
    "PhaseTranslationsModel":PhaseTranslations,
    "RoleTranslationsModel":RoleTranslations,
    "TaxrateTranslationsModel":TaxrateTranslations,
    "TaxTranslationsModel":TaxTranslations,
    "OperationTranslationsModel":OperationTranslations,
    "GlobalViewModel":GlobalView,
    "PaymentMethodModel":PaymentMethod,
    "PaymentMethodTranslationsModel":PaymentMethodTranslations,
    "NatureModel":Nature,
    "NatureTranslationsModel":NatureTranslations,
    "TypeModel":Type,
    "TypeTranslationsModel":TypeTranslations,
    "OperationNatureTypeMappingModel":OperationNatureTypeMapping,
    "OperationNatureMappingModel":OperationNatureMapping,
    "ProductTypeMappingModel": ProductTypeMapping,
    "ProfileModel":Profile,
    "ProfileTranslationsModel":ProfileTranslations,
    "ProfileActionModel":ProfileAction,
    "UserModel":User,
    "ProfileActionMappingModel": ProfileActionMapping,
    "ProfileActionTranslationsModel": ProfileActionTranslations,
    "LanguageModel": Language,
    "StaticRoleModel": StaticRole,
    "StaticRoleTranslationsModel": StaticRoleTranslations,
    "NapModel": Nap,
    "BankModel": Bank,
    "AutoCatalogModel": AutoCatalog,
    "SingleAutoModel": SingleAuto,
    "MarketModel": Market,
    "EquipmentModel": Equipment,
    "NafModel": Naf,
    "NafTranslationsModel": NafTranslations,
    "ScaleModel": Scale,
    "ScaleServiceModel": ScaleService,
    "ScaleFinancialProductModel": ScaleFinancialProduct,
    "ScaleCommercialProductModel": ScaleCommercialProduct,
    "CommercialProductModel": CommercialProduct,
    "ApplicationCriteriaModel": ApplicationCriteria,
    "ApplicationCriteriaActorModel": ApplicationCriteriaActor,
    "FinancialElementVehicleVehicleMappingModel": FinancialElementVehicleVehicleMapping,
    "FinancialElementMaterialModel": FinancialElementMaterial,
    "FinancialElementMaterialEquipmentMappingModel": FinancialElementMaterialEquipmentMapping,
    "FinancialElementVehicleModel": FinancialElementVehicle,
    "ThirdPartiesModel": thirdParties,
    "ServiceModel": Service,
    "ServiceOperationModel": ServiceOperation,
    "ServiceProductModel": ServiceProduct,
    "ServiceNapModel": ServiceNap,
    "ServiceActorModel": ServiceActor,
    "PartnerModel": Partner,
    "OperationNatureTypeMappingModel": OperationNatureTypeMapping,
    "OperationNatureMappingModel": OperationNatureMapping,
    "ServicesPackModel": ServicesPack,
    "ServiceServicesPackModel": ServiceServicesPack,
    sequelize
};