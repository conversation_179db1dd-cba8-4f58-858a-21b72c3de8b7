FROM public.ecr.aws/docker/library/node:18.20.4-alpine

# Install system dependencies including Java for Liquibase
RUN apk add --no-cache bash curl openjdk11-jre wget

# Install Infisical
RUN curl -1sLf \
'https://dl.cloudsmith.io/public/infisical/infisical-cli/setup.alpine.sh' | bash \
&& apk add infisical

# Install Liquibase
ENV LIQUIBASE_VERSION=4.25.1
RUN wget -O liquibase.tar.gz "https://github.com/liquibase/liquibase/releases/download/v${LIQUIBASE_VERSION}/liquibase-${LIQUIBASE_VERSION}.tar.gz" \
    && mkdir -p /opt/liquibase \
    && tar -xzf liquibase.tar.gz -C /opt/liquibase \
    && rm liquibase.tar.gz \
    && chmod +x /opt/liquibase/liquibase \
    && ln -s /opt/liquibase/liquibase /usr/local/bin/liquibase

# Set environment variables for Liquibase
ENV LIQUIBASE_PATH=/usr/local/bin/liquibase
ENV PATH="/opt/liquibase:${PATH}"

WORKDIR /var/www

COPY package.json .
RUN npm install

COPY . .

# Set execute permission for the script
RUN chmod +x infisical.sh

EXPOSE 8807

CMD ["npm", "run", "start-local"]
