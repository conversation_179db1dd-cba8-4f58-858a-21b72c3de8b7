{"name": "express-sequelize", "version": "1.0.0", "description": "Sketelon structure for Node, Express, MySQL and Sequelize", "main": "index.js", "engines": {"node": ">=8.3.0"}, "scripts": {"start": "npm run docs && nodemon index.js", "start-local": "node index.js", "lint": "eslint --ignore-path .gitignore .", "lint-fix": "eslint --ignore-path .gitignore --fix .", "test": "echo \"Error: no test specified\" && exit 1", "docs": "apidoc -i ./ -e node_modules/ -t ./node_modules/apidoc-contenttype-plugin/template/ --parse-parsers apicontenttype=./node_modules/apidoc-contenttype-plugin/api_content_type.js", "db:migrate": "node scripts/liquibase-migrate.js", "db:rollback": "node scripts/liquibase-rollback.js", "db:status": "node scripts/liquibase-status.js", "db:validate": "node scripts/liquibase-validate.js"}, "author": "DATA-TRICKS", "license": "GPL-3.0", "dependencies": {"axios": "^1.9.0", "body-parser": "^1.18.3", "dotenv": "^16.4.5", "eureka-js-client": "^4.5.0", "express": "^4.16.4", "express-async-errors": "^3.1.1", "ibantools": "^4.5.1", "ioredis": "^5.4.1", "joi": "^14.3.0", "joi-date-extensions": "^1.2.0", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "moment": "^2.30.1", "mysql2": "^1.6.4", "node-liquibase": "^4.3.3", "pg": "^8.11.3", "sequelize": "^6.35.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"eslint": "^9.20.1", "eslint-plugin-node": "^11.1.0", "nodemon": "^3.0.3"}}