const { DataTypes } = require("sequelize");

module.exports = (Sequelize, type) => {
    return Sequelize.define(
        "delegation_translations",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            delegation_code: {
                type: type.STRING,
                allowNull: false,
                references: {
                    model: "delegations",
                    key: "code"
                }
            },
            label: {
                type: DataTypes.STRING,
            },
            language_code: {
                type: DataTypes.STRING,
                allowNull: false
            },
        },
        {
            timestamps: true,
        },
    );

}
