module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "legal_category",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: DataTypes.STRING,
        unique: true,
      },
      label: {
        type: DataTypes.STRING,
      },
      system_attribute: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      country_code: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
          model: "countries",
          key: "code",
        },
        onDelete: "Cascade",
        onUpdate: "Cascade",
      },
    },
    {
      timestamps: true
    }
  );
};
