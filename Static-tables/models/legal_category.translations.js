module.exports = (Sequelize, DataTypes) => {
    return Sequelize.define(
        "legal_category_translations",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            legal_category_code: {
                type: DataTypes.STRING,
                allowNull: false,
                references: {
                    model: "legal_categories",
                    key: "code"
                },
                onDelete: "CASCADE",
                onUpdate: "CASCADE"
            },
            label: {
                type: DataTypes.STRING,
            },
            language_code: {
                type: DataTypes.STRING,
                allowNull: false
            },
        },
        {
            timestamps: true,
        },
    );

}
