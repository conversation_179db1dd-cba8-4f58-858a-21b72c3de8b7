const { DataTypes } = require("sequelize");

module.exports = (Sequelize, type) => {
    return Sequelize.define(
        "view_static_tables",
        {
            id: {
                type: DataTypes.INTEGER,
            },
            code: {
                type: type.STRING,
                primaryKey: true,
            },
            label: {
                type: DataTypes.STRING,
            },
            translated_label: {
                type: DataTypes.STRING,
            },
            language_code: {
                type: DataTypes.STRING,
            },
            entity: {
                type: DataTypes.STRING,
            },
            createdAt: {
                type: DataTypes.DATE,
            }
        },
        {
            timestamps: true
        },
    );
}
