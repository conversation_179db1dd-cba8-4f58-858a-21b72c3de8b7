module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "profile",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: DataTypes.STRING,
        unique: true,
      },
      label: DataTypes.STRING,
      description: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      system_attribute: {
        type: DataTypes.BOOLEAN,
        default: false,
      },
      active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
    },
    {
      timestamps: true,
      createdAt: true,
      updatedAt: true,
      fields: {
        createdAt: {
          update: false
        }
      }
    }
  );
};
