module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
      "service-services_pack",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        reference: {
          type: DataTypes.UUID,
          unique: true,
          allowNull: false,
        },
        service_reference: {
          type: DataTypes.STRING,
          allowNull: false,
          references: {
            model: "services",
            key: "reference",
          },
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
        },

        service_pack_code: {
          type: DataTypes.STRING,
          allowNull: false,
          references: {
            model: "services_packs",
            key: "code",
          },
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
        },
      },
      {
        timestamps: true
      }
    );
  };
