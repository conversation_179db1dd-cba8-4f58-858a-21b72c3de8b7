module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "service_actor",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      reference: {
        type: DataTypes.UUID,
        unique: true,
        allowNull: false,
      },
      service_reference: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
          model: "services",
          key: "reference",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      actor_code: {
        type: DataTypes.STRING,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
    },
    {
      timestamps: true,
      indexes: [
        {
          unique: true,
          fields: ["service_reference", "actor_code"],
        },
      ],
    }
  );
};
