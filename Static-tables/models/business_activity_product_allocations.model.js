const { DataTypes } = require("sequelize");
module.exports = (sequelize, type) => {
    return sequelize.define(
        "businesses_activity_product_allocation",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            activity_code: {
                type: DataTypes.STRING,
            },
            product_code: {
                type: DataTypes.STRING,

            },
            allocation_code: {
                type: DataTypes.STRING,

            },
            business_reference: {
                type: DataTypes.STRING,

            }
        },
        {
            timestamps: true,
            createdAt: true,
            updatedAt: true,
            fields: {
                createdAt: {
                    update: false
                }
            }
        }
    );
};

