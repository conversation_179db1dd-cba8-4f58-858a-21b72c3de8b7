module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "equipment",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code:{
        type: DataTypes.STRING,
        unique:true,
        allowNull: false,
      },
      label: {
        type: DataTypes.STRING,
      },
      category_code: {
        type: DataTypes.STRING,
        references: {
            model: "naps",
            key: "code"
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE"
      },
      country_code: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
          model: "countries",
          key: "code"
        },
        onDelete: "Cascade",
        onUpdate: "Cascade"
      },
      market_code: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
          model: "markets",
          key: "code"
        },
        onDelete: "Cascade",
        onUpdate: "Cascade"
      },
      active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
      },
      system_attribute: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
      }
    },
    {
      timestamps: true,
      tableName: "equipments",
    }
  );
};
