module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "application_criteria_actors",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      reference: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        unique:true,
        allowNull: false,
      },
      application_criteria_reference: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "application_criteria",
          key: "reference",
        },
      },
      actor_reference: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      tableName: "application_criteria_actors",
    }
  );
};
