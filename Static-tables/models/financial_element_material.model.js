module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "financial_element_material",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      scale_code: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        references: {
          model: "scales",
          key: "code",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      market_code: {
        type: DataTypes.STRING,
        allowNull: true,
        references: {
          model: "markets",
          key: "code",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      designation: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      createdAt: true,
      updatedAt: true,
      fields: {
        createdAt: {
          update: false
        }
      },
      tableName: "financial_element_material",
    }
  );
};
