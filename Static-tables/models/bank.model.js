module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
        "bank",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            reference: {
                type: DataTypes.UUID,
                allowNull: false,
                unique: true,
            },
            bank_id: {
                type: DataTypes.STRING,
                allowNull: false,
                unique: true,
            },
            country_code: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            bank_name: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            code_swift: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            code_branch: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            city: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            postal_code: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            address: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            second_address: {
                type: DataTypes.STRING,
                allowNull: true,
            },
        },
        {
            timestamps: true,
            createdAt: true,
            updatedAt: true,
            fields: {
                createdAt: {
                    update: false
                }
            }
        }
    );
};
