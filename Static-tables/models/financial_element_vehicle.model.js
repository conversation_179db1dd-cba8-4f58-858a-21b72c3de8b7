module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "financial_element_vehicle",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      scale_code: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        references: {
          model: "scales",
          key: "code",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      maximum_mileage: {
        type: DataTypes.BIGINT,
        allowNull: true,
      },
      minimum_mileage: {
        type: DataTypes.BIGINT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      tableName: "financial_element_vehicle",
    }
  );
};
