const { DataTypes } = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define(
        "tax_rate",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            reference: {
                type: DataTypes.UUIDV4,
                defaultValue: DataTypes.UUIDV4,
                allowNull: false,
                unique: true
            },
            tax_code: {
                type: DataTypes.STRING,
                allowNull: false,
                references: {
                    model: "tax",
                    key: "code",
                },
                onUpdate: "CASCADE",
                onDelete: "CASCADE",
            },
            code: {
                type: DataTypes.STRING,
                unique: true
            },
            label: {
                type: DataTypes.STRING,
            },
            rate: {
                type: DataTypes.DOUBLE,
            },
            active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true // Optional default value
            },
            system_attribute: {
                type: DataTypes.BOOLEAN,
            },
            start_date: {
                type: DataTypes.DATEONLY,
                allowNull: true,
            },
            end_date: {
                type: DataTypes.DATEONLY,
                allowNull: true,
            },
            creationDate: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW,
                field: "createdAt" // Map to "createdAt" in the database
            },
        },
        {
            timestamps: true,
            createdAt: true,
            updatedAt: true,
            fields: {
                createdAt: {
                    update: false
                }
            },
            indexes: [
                {
                    unique: true,
                    fields: ["tax_code", "rate"]
                }
            ]
        }
    );
};