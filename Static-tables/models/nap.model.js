const { DataTypes } = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define(
        "nap",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },

            code: {
                type: DataTypes.STRING,
                unique: true ,
            },
            label: {
                type: DataTypes.STRING,
            },


            system_attribute: {
                type: DataTypes.BOOLEAN,
            },
            country_code: {
                type: DataTypes.STRING,
            },
        },
        {
            timestamps: true
        }
    );
};