/**
 * Mimics the structure of the Java KafkaMessage<T> class.
 *
 * @property {string} operation  One of OperationType.POST, OperationType.PUT, OperationType.DELETE
 * @property {string} class_name Name of the class/type of 'data'
 * @property {string} module     The module or system name
 * @property {Object} data       The actual payload (equivalent to 'T' in Java)
 */
class KafkaMessage {
    constructor(operation, className, module, data) {
        this.operation = operation;
        this.class_name = className;
        this.module = module;
        this.data = data;
    }

    getOperation() {
        return this.operation;
    }

    getClassName() {
        return this.class_name;
    }

    getModule() {
        return this.module;
    }

    getData() {
        return this.data;
    }
}


module.exports = KafkaMessage;
  