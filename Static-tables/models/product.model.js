module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
        "product",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            code: {
                type: DataTypes.STRING,
                unique: true
            },
            label: {
                type: DataTypes.STRING
            },
            activity_code: {
                type: DataTypes.STRING,
                allowNull: true,
              },
            activity_associated_to: {
                type: DataTypes.STRING,
                allowNull: true,
            },
            start_date: {
                type: DataTypes.DATEONLY,
            },
            end_date: {
                type: DataTypes.DATEONLY,
            },
            active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            system_attribute: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            }

        },
        {
            timestamps: true,
            createdAt: true,
            updatedAt: true,
            fields: {
                createdAt: {
                    update: false
                }
            }
        }
    );
};
