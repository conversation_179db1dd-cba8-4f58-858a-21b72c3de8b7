module.exports = (sequelize, type) => {
    return sequelize.define(
        "activity",
        {
            id: {
                type: type.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            code: {
                type: type.STRING,
                unique: true
            },
            label: type.STRING,
            associated_to: {
                type: type.STRING,
                allowNull: false
            },
            active: {
                type: type.BOOLEAN,
                defaultValue: true
            },system_attribute: {
                type: type.BOOLEAN,
                defaultValue: false
            },

        },
        {
            timestamps: true,
            createdAt: true,
            updatedAt: true,
            fields: {
                createdAt: {
                    update: false
                }
            }
        }
    );
};

