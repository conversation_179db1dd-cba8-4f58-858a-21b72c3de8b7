module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "static_role",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: DataTypes.STRING,
        unique: true,
      },
      label: {
        type: DataTypes.STRING,
      },
      is_exclusive: {
        type: DataTypes.BOOLEAN,
      },
      is_client: {
        type: DataTypes.BOOLEAN,
      },
      associated_to: {
        type: DataTypes.STRING,
        defaultValue: "ACTEUR"
      }
    },
    {
      timestamps: true,
    }
  );
};
