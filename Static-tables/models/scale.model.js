module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "scale",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      label: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      reference: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      start_date: {
        type: DataTypes.DATEONLY,
      },
      end_date: {
        type: DataTypes.DATEONLY,
      },
      currency_code: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
          model: "currencies",
          key: "code",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      market_code: {
        type: DataTypes.STRING,
        allowNull: true,
        references: {
          model: "markets",
          key: "code",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      country_code: {
        type: DataTypes.STRING,
        allowNull: true,
        references: {
          model: "countries",
          key: "code",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      description: {
        type: DataTypes.TEXT,
      },
      maximum_eligible_amount: {
        type: DataTypes.INTEGER,
      },
      minimum_eligible_amount: {
        type: DataTypes.INTEGER,
      },
      maximum_financing_duration: {
        type: DataTypes.INTEGER,
      },
      minimum_financing_duration: {
        type: DataTypes.INTEGER,
      },
      rate_period: {
        type: DataTypes.ENUM(
          "ENUM_ANNEE",
          "ENUM_SEMESTRE",
          "ENUM_TRIMESTRE",
          "ENUM_MOIS",
          "ENUM_JOUR"
        ),
      },
      maximum_residual_value: {
        type: DataTypes.INTEGER,
      },
      minimum_residual_value: {
        type: DataTypes.INTEGER,
      },
      minimum_personal_contribution: {
        type: DataTypes.FLOAT,
      },
      maximum_personal_contribution: {
        type: DataTypes.FLOAT,
      },
      maximum_security_deposit: {
        type: DataTypes.FLOAT,
      },
      minimum_security_deposit: {
        type: DataTypes.FLOAT,
      },
      asset_usage: {
        type: DataTypes.ENUM("PROFESSIONAL", "OWN"),
      },
      status: {
        type: DataTypes.ENUM("INI", "OPERATED", "SUSPENDED", "EXPIRED"),
      },
      application_criteria_reference: {
        type: DataTypes.STRING,
        references: {
          model: "application_criteria",
          key: "reference",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      condition: {
        type: DataTypes.ENUM("NEW", "OLD", "REFURBISHED"),
      },
      nature: {
        type: DataTypes.ENUM("VH", "MT"),
      },
      minimum_interest_rate: {
        type: DataTypes.DOUBLE,
      },
      nominal_interest_rate: {
        type: DataTypes.DOUBLE,
      },
      maximum_interest_rate: {
        type: DataTypes.DOUBLE,
      },
      has_personal_contribution: {
        type: DataTypes.BOOLEAN,
      },
      grace_period_duration: {
        type: DataTypes.INTEGER,
      },
      with_interest_payment: {
        type: DataTypes.BOOLEAN,
      },
      has_grace_period: {
        type: DataTypes.BOOLEAN,
      },
      has_security_deposit: {
        type: DataTypes.BOOLEAN,
      }
    },
    {
      timestamps: true
    }
  );
};
