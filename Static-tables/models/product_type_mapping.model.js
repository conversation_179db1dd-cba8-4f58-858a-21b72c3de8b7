module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
        "product_type_mapping",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            product_code: {
                type: DataTypes.STRING,
                allowNull: false,
                references: {
                    model: "products",
                    key: "code"
                },
                onDelete: "CASCADE",
                onUpdate: "CASCADE"
            },
            operation_nature_type_code: {
                type: DataTypes.INTEGER,
                allowNUll: false,
                references: {
                    model: "operation_nature_type_mapping",
                    key: "operation_nature_type_code"
                },
                onDelete: "CASCADE",
                onUpdate: "CASCADE"
            }
        },{
            timestamps: false,
            tableName: "product_type_mapping" 
        }
    )
}