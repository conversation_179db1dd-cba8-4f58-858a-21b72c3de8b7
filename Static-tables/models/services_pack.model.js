module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
        "services_pack",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            code: {
                type: DataTypes.STRING,
                unique: true,
                allowNull: false,
            },
             reference: {
                type: DataTypes.STRING,
                unique: true,
                allowNull: false,
            },
            label: {
                type: DataTypes.STRING,
            },
            invoice_grouping_code: {
                type: DataTypes.STRING,
                allowNull: true,
            },
            grouping_code_regulation: {
                type: DataTypes.STRING,
                allowNull: true,
            },
            type: {
                type: DataTypes.ENUM("COMPATIBLE", "INCOMPATIBLE"),

            },
        },
        {
            timestamps: true
          }
    );
};