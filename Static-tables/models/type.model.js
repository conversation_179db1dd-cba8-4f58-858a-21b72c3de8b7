module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
        "types",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            code: {
                type: DataTypes.STRING,
                unique: true
            },
            label: {
                type: DataTypes.STRING
            },
            system_attribute: {
                type: DataTypes.BOOLEAN,
                defaultValue: false,
            },
        },
        {
            timestamps: true,
        }
    )
}