const { DataTypes } = require("sequelize");

module.exports = (sequelize) => {
    return sequelize.define(
        "payment_method",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            code: {
                type: DataTypes.STRING,
                unique: true,
            },
            label: {
                type: DataTypes.STRING,
            },
            requires_bank_account: {
                type: DataTypes.BOOLEAN,
            },
            manual_transaction: {
                type: DataTypes.BOOLEAN,
            },
            exchange_file: {
                type: DataTypes.BOOLEAN,
            },
            bank_card: {
                type: DataTypes.BOOLEAN,
            },
            system_attribute: {
                type: DataTypes.BOOLEAN,
                defaultValue: false // Optional default value
            },
            active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true // Optional default value
            }
        },
        {
            timestamps: true
        }
    );
};
