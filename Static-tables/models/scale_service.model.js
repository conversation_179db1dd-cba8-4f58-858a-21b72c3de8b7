module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "scale_service",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      reference: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      scale_code: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
          model: "scales",
          key: "code",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      service_reference: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
          model: "services",
          key: "reference",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
    },
    {
      timestamps: true,
      tableName: "scale_services"
    }
  );
};