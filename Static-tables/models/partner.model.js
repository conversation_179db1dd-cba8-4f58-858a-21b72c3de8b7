module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "partner",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      reference: {
        type: DataTypes.UUID,
        unique: true,
        allowNull: false,
      },
      actor_reference: {
        type: DataTypes.STRING,
      },
      operation_nature_type_code: {
        type: DataTypes.STRING,
        references: {
          model: "operation_nature_type_mapping",
          key: "operation_nature_type_code",
        },
      },
      currency_code: {
        type: DataTypes.STRING,
        references: {
          model: "currencies",
          key: "code",
        },
      },
      mandate_type: {
        type: DataTypes.ENUM("OPAQUE", "TRANSPARENT"),
      },
      external_reference: {
        type: DataTypes.STRING,
      },
      remittance_method: {
        type: DataTypes.ENUM("ON_INVOICING", "ON_PAYMENT_RECEIPT"),
      },
      calculation_method: {
        type: DataTypes.ENUM("percentage", "fixed_amount"),
      },
      amount_excl_tax: {
        type: DataTypes.DOUBLE,
      },
      basis_of_calculation: {
        type: DataTypes.ENUM(
          "RENTAL_BASE_EXCL_TAX",
          "FINANCING_AMOUNT_EXCL_TAX",
          "RENT_AMOUNT_EXCL_TAX",
          "RENTAL_BASE_INCL_TAX",
          "FINANCING_AMOUNT_INCL_TAX",
          "RENT_AMOUNT_INCL_TAX"
        ),
      },
      tax_code: {
        type: DataTypes.STRING,

      },
      calculation_percentage: {
        type: DataTypes.DOUBLE,
      },
      tax_value: {
        type: DataTypes.DOUBLE,
      },
    },
    {
      timestamps: true
    }
  );
};
