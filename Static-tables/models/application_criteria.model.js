module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
        "application_criteria",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            reference: {
                type: DataTypes.UUID,
                unique: true,
                allowNull: false,
            },
            channel_of_acquisition: {
                type: DataTypes.ENUM("DIRECT", "INDIRECT", "PARTNER"),
                allowNull: false,
            },
            currency_code: {
                type: DataTypes.STRING,
                allowNull: false,
                references: {
                    model: "currencies",
                    key: "code",
                },
                onDelete: "CASCADE",
                onUpdate: "CASCADE",
            },
            customer_type: {
                type: DataTypes.ENUM("CORPORATE", "INDIVIDUAL", "SOLE_PROPRIETORSHIP", "ENUM_SOLE"),
                allowNull: false,
            },
            financial_scoring: {
                type: DataTypes.DECIMAL,
                allowNull: false,
            },
            scale_code: {
                type: DataTypes.STRING,
                allowNull: false,
                references: {
                    model: "scales",
                    key: "code",
                },
                onDelete: "CASCADE",
                onUpdate: "CASCADE",
            }
        },
        {
            timestamps: true,
            createdAt: true,
            updatedAt: true,
            fields: {
                createdAt: {
                    update: false
                }
            },
            tableName:
                "application_criteria",
        }
    );
}
;
