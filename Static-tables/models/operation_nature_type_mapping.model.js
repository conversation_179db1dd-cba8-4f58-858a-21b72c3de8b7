module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
        "operation_nature_type_mapping", 
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            operation_nature_type_code: {
                type: DataTypes.STRING,
                allowNull: false,
                unique: true
            },
            operation_nature_code: {
                type: DataTypes.STRING,
                references: {
                    model: "operation_nature_mapping",
                    key: "operation_nature_code"
                },
                onDelete: "CASCADE",
                onUpdate: "CASCADE"
            },
            type_code: {
                type: DataTypes.STRING,
                references: {
                    model: "types",
                    key: "code"
                },
                onDelete: "CASCADE",
                onUpdate: "CASCADE"
            },
            type_status: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
        },
        {
            timestamps: false, 
            tableName: "operation_nature_type_mapping" 
        }
    );
};
