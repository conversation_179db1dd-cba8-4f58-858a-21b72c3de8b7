module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "service",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      reference: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      intended_for: {
        type: DataTypes.ENUM("VH", "MT"),
      },
      label: {
        type: DataTypes.STRING,
      },
      type_of_service: {
        type: DataTypes.ENUM(
          "Maintenance",
          "Insurance",
          "Replacement_vehicle",
          "Pecuniary_loss",
          "Financial_loss",
          "Administrative_fees"
        ),
      },
      type_of_cover: {
        type: DataTypes.ENUM("Third_party", "Material"),
      },
      start_date: {
        type: DataTypes.DATE,
      },
      end_date: {
        type: DataTypes.DATE,
      },
      currency_code: {
        type: DataTypes.STRING,
        references: {
          model: "currencies",
          key: "code",
        },
      },
      status: {
        type: DataTypes.ENUM("initial", "active", "expired"),
        defaultValue: "initial",
      },
      out_of_contract_termination: {
        type: DataTypes.BOOLEAN,
      },
      maximum_asset_price: {
        type: DataTypes.DOUBLE,
      },
      minimum_asset_price: {
        type: DataTypes.DOUBLE,
      },
      maximum_contract_duration: {
        type: DataTypes.INTEGER,
      },
      minimum_contract_duration: {
        type: DataTypes.INTEGER,
      },
      is_enterprise: {
        type: DataTypes.BOOLEAN,
      },
      is_enterprise_individuelle: {
        type: DataTypes.BOOLEAN,
      },
      is_enterprise_publique: {
        type: DataTypes.BOOLEAN,
      },
      is_particulier: {
        type: DataTypes.BOOLEAN,
      },
      is_registrable: {
        type: DataTypes.BOOLEAN,
      },
      is_unregistrable: {
        type: DataTypes.BOOLEAN,
      },
      billing_method: {
        type: DataTypes.ENUM("subscription", "flat"),
      },
      calculation_method: {
        type: DataTypes.ENUM("percentage", "fixed_amount", "matrix"),
      },
      amount_excl_tax: {
        type: DataTypes.DOUBLE,
      },
      basis_of_calculation: {
        type: DataTypes.ENUM(
          "RENTAL_BASE_EXCL_TAX",
          "FINANCING_AMOUNT_EXCL_TAX",
          "RENT_AMOUNT_EXCL_TAX",
          "RENTAL_BASE_INCL_TAX",
          "FINANCING_AMOUNT_INCL_TAX",
          "RENT_AMOUNT_INCL_TAX"
        ),
      },
      tax_code: {
        type: DataTypes.STRING,

      },
      calculation_percentage: {
        type: DataTypes.DOUBLE,
      },
      tax_value: {
        type: DataTypes.DOUBLE,
      },
      partner_reference: {
        type: DataTypes.UUID,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
    },
    {
      timestamps: true
    }
  );
};
