const { DataTypes } = require("sequelize");
module.exports = (Sequelize, type) => {
    return Sequelize.define(
        "tax_translations",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            tax_code: {
                type: DataTypes.STRING,
                allowNull: false,
                references: {
                    model: "taxes",
                    key: "code",
                }
            },
            start_date: {
                type: DataTypes.DATEONLY,
            },

            end_date: {
                type: DataTypes.DATEONLY,
            },
            country: {
                type: DataTypes.STRING,
            },

            label: {
                type: DataTypes.STRING,
            },
            language_code: {
                type: DataTypes.STRING,
                allowNull: false
            },
        },
        {
            timestamps: true,
        },
    );

}
