
const { DataTypes } = require("sequelize");
module.exports = (sequelize, type) => {
    return sequelize.define(
        "phase",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            code: {
                type: DataTypes.STRING,

            },
            label: {
                type: DataTypes.STRING,

            },
            associated_to: {
                type: DataTypes.STRING,
            },
            associated_to_label: {
                type: DataTypes.STRING,
            }
        },
        {
            timestamps: true
        }
    );
};
