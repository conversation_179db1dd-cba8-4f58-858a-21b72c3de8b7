const { DataTypes } = require("sequelize");
module.exports = (sequelize, type) => {
    return sequelize.define(
        "allocation_category",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            code: {
                type: DataTypes.STRING,
                unique: true
            },
            label: {
                type: DataTypes.STRING
            },
        },
        {
            timestamps: true
        }
    );
};
