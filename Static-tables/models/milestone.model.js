module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
        "milestone",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            code: {
                type: DataTypes.STRING,
                unique: true,
            },
            label: {
                type: DataTypes.STRING,
            },
            rate: {
                type: DataTypes.DOUBLE,
            },
            start_date: {
                type: DataTypes.DATEONLY,
            },
            end_date: {
                type: DataTypes.DATEONLY,
            },
            system_attribute: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            }
        },
        {
            timestamps: true,
            createdAt: true,
            updatedAt: true,
            fields: {
                createdAt: {
                    update: false
                }
            }
        }
    );
};
