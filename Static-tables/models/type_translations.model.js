module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
        "type_translations",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            type_code: {
                type: DataTypes.STRING,
                allowNull: true,
                references: {
                    model: "types",
                    key: "code"
                },
                onDelete: "CASCADE",
                onUpdate: "CASCADE" 
            },
            label: {
                type: DataTypes.STRING
            },
            language_code: {
                type: DataTypes.STRING,
                allowNull: false
            }
        },
        {
            timestamps: true,
        }
    )
}