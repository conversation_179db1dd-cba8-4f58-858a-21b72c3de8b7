
const { DataTypes } = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define(
        "country",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            code: {
                type: DataTypes.STRING,
                unique: true,
            },
            label: {
                type: DataTypes.STRING,

            },
            language: {
                type: DataTypes.STRING,

            },
            country_tax_code: {
                type: DataTypes.STRING,

            },
        },
        {
            timestamps: true,
            createdAt: true,
            updatedAt: true,
            fields: {
                createdAt: {
                    update: false
                }
            }
        }
    );
};
