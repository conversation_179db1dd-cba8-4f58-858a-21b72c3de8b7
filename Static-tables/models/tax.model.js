const { DataTypes } = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define(
        "tax",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            code: {
                type: DataTypes.STRING,
                unique: true
            },
            label: {
                type: DataTypes.STRING,
            },
            start_date: {
                type: DataTypes.DATEONLY,
                allowNull: true,
            },
            end_date: {
                type: DataTypes.DATEONLY,
                allowNull: true,
            },
            country: {
                type: DataTypes.STRING,
            },
            description: {
                type: DataTypes.STRING,
            },
            type: {
                type: DataTypes.STRING
            },
            active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            system_attribute: {
                type: DataTypes.BOOLEAN,
            },
        },
        {
            timestamps: true
        }
    );
};
