const { DataTypes } = require("sequelize");
module.exports = (sequelize, type) => {
    return sequelize.define(
        "businesses_activity_product",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            activity_code: {
                type: DataTypes.STRING,
            },
            product_code: {
                type: DataTypes.STRING,

            },
            business_reference: {
                type: DataTypes.STRING,

            },

        },
        {
            timestamps: true
        }
    );
};

