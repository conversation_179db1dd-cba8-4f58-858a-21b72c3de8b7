const { DataTypes } = require("sequelize");
module.exports = (sequelize, type) => {
    return sequelize.define(
        "business_activity",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            activity_code: {
                type: DataTypes.STRING,
            },
            business_reference: {
                type: DataTypes.STRING,

            },
            business_name: {
                type: DataTypes.STRING,

            },

        },
        {
            timestamps: true
        }
    );
};

