const { DataTypes } = require("sequelize");
module.exports = (sequelize, type) => {
    return sequelize.define(
        "allocation",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            allocation_category_code: {
                type: DataTypes.STRING,

            },

            operation_code: {
                type: DataTypes.STRING,

            },
            code: {
                type: DataTypes.STRING,
                unique: true
            },
            label: {
                type: DataTypes.STRING
            },
        },
        {
            timestamps: true
        }
    );
};
