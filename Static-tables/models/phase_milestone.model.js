
const { DataTypes } = require("sequelize");
module.exports = (sequelize, type) => {
    return sequelize.define(
        "phase_milestone",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            phase_code: {
                type: DataTypes.STRING,
         
            },
            milestone_code: {
                type: DataTypes.STRING,
            
            },        
            associated_to: { 
                type: DataTypes.STRING,
                      
            },
         
        },
        {
            timestamps: true,
        }
    );
};
