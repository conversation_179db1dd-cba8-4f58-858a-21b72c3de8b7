const { DataTypes } = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define(
        "operation",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },                    
            code: {
                type: DataTypes.STRING,
                unique: true
            },
            label: {
                type: DataTypes.STRING
            },
            system_attribute: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
        },
        {
            timestamps: true,
            createdAt: true,
            updatedAt: true,
            fields: {
                createdAt: {
                    update: false
                }
            }
        }
    );
};
