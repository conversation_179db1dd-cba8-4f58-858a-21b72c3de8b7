const { language } = require("../config/app-config");

module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
        "role",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            code: {
                type: DataTypes.STRING,
                unique: true,
            },
            label: {
                type: DataTypes.STRING,
            },
            static_role_code: {
                type: DataTypes.STRING,
                allowNull: false,
                references: {
                    model: 'static_roles',
                    key: 'code'
                },
                onUpdate: "CASCADE",
                onDelete: "CASCADE",
            },
            system_attribute: {
                type: DataTypes.BOOLEAN,
                defaultValue: false,
            },
            active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            }
        },
        {
            timestamps: true
        }
    );
};
