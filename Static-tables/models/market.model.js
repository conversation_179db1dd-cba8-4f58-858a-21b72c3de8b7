module.exports = (sequelize, type) => {
  return sequelize.define(
    "markets",
    {
      id: {
        type: type.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: type.STRING,
        unique: true,
    },
      label: {
        type: type.STRING,
        allowNull: false,
      },
      description: {
        type: type.STRING,
        allowNull: false,
      },
      system_attribute: {
        type: type.BOOLEAN,
        defaultValue: false, // Optional default value
      },
      active: {
        type: type.BOOLEAN,
        defaultValue: true, // Optional default value
      },
    },
    {
      timestamps: true,
      createdAt: true,
      updatedAt: true,
      fields: {
        createdAt: {
          update: false
        }
      }
    }
  );
};
