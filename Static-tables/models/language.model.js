module.exports = (sequelize, type) => {
    return sequelize.define("languages", {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        code: {
            type: type.STRING,
            allowNull: false,
            unique: true
        },
        name: {
            type: type.STRING,
            allowNull: false
        },
        native_name: {
            type: type.STRING,
            allowNull: false
        }
    }, {
        timestamps: true,
        createdAt: true,
        updatedAt: true,
        fields: {
            createdAt: {
                update: false
            }
        },
        underscored: true,
    });
};