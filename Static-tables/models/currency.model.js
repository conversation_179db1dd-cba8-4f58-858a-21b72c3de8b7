
const { DataTypes } = require("sequelize");
module.exports = (sequelize) => {
    return sequelize.define(
        "currency",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            label: {
                type: DataTypes.STRING,
                unique: true,
            },
            code: {
                type: DataTypes.STRING,
                unique: true,
            },
            symbol: {
                type: DataTypes.STRING,

            },
            language: {
                type: DataTypes.STRING,

            },
            decimal_number: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            unit: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            final_effectiveDate: {
                type: DataTypes.DATEONLY,
                allowNull: true
            },
            intermediate_period_start_date: {
                type: DataTypes.DATEONLY,
                allowNull: true
            },
            default_currency: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true // Optional default value
            },
            country_code: {
                type: DataTypes.STRING,
                allowNull: true
            },
        },
        {
            timestamps: true
        }
    );
};
