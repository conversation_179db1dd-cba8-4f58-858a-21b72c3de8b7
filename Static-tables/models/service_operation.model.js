module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "service_operation",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      reference: {
        type: DataTypes.UUID,
        unique: true,
        allowNull: false,
      },
      service_reference: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
          model: "services",
          key: "reference",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      operation_nature_type_code: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
          model: "operation_nature_type_mapping",
          key: "operation_nature_type_code",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
    },
    {
      timestamps: true,
      indexes: [
        {
          unique: true,
          fields: ["service_reference", "operation_nature_type_code"],
        },
      ],
    }
  );
};
