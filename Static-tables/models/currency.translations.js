const { DataTypes } = require("sequelize");
module.exports = (Sequelize, type) => {
    return Sequelize.define(
        "currency_translations",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            currency_code: {
                type: type.STRING,
                allowNull: false,
                references: {
                    model: "currencies",
                    key: "code"
                }
            },
            label: {
                type: DataTypes.STRING,
            },
            language_code: {
                type: DataTypes.STRING,
                allowNull: false
            },
        },
        {
            timestamps: true,
        },
    );

}
