module.exports = (sequelize, DataTypes) => {
  return sequelize.define(
    "service_product",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      reference: {
        type: DataTypes.UUID,
        unique: true,
        allowNull: false,
      },
      service_reference: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
          model: "services",
          key: "reference",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      product_code: {
        type: DataTypes.STRING,
        references: {
          model: "products",
          key: "code",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
    },
    {
      timestamps: true,
      createdAt: true,
      updatedAt: true,
      fields: {
        createdAt: {
          update: false
        }
      },
      indexes: [
        {
          unique: true,
          fields: ["service_reference", "product_code"],
        },
      ],
    }
  );
};
