module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
        "third_parties",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            reference: {
                type: DataTypes.UUID,
                unique: true,
                allowNull: false,
            },
            actor_reference: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            role_code: {
                type: DataTypes.STRING,
                allowNull: false,
                references: {
                    model: "roles",
                    key: "code",
                },
            },
            scale_code: {
                type: DataTypes.STRING,
                allowNull: false,
                references: {
                    model: "financial_element_material",
                    key: "scale_code",
                },
                onDelete: "CASCADE",
                onUpdate: "CASCADE",
            },
        },
        {
            timestamps: true,
            createdAt: true,
            updatedAt: true,
            fields: {
                createdAt: {
                    update: false
                }
            },
            tableName:
                "third_parties",
        }
    );
};