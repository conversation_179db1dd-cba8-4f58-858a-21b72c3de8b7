const { DataTypes, Sequelize } = require("sequelize");

module.exports = (sequelize) => {
    return sequelize.define("single_auto", {
        reference:{
            type: DataTypes.UUID,
            unique: true,
            allowNull: false,
        },
        brand_code: {
            type: DataTypes.STRING,
            references: {
                model: "auto_catalog",
                key: "code",
            },
            onUpdate: "CASCADE",
            onDelete: "SET NULL",
        },
        brand_label: {
            type: DataTypes.STRING,
        },
        model_group_code: {
            type: DataTypes.STRING,
        },
        model_group_label: {
            type: DataTypes.STRING,
        },
        body_code: {
            type: DataTypes.STRING,
        },
        body_label: {
            type: DataTypes.STRING,
        },
        model_code: {
            type: DataTypes.STRING,
        },
        model_label: {
            type: DataTypes.STRING,
        },
        doors_number: {
            type: DataTypes.STRING,
        },
        version_code: {
            type: DataTypes.STRING,
        },
        version_label: {
            type: DataTypes.STRING,
        },
        variant_code: {
            type: DataTypes.STRING,
        },
        variant_label: {
            type: DataTypes.STRING,
        },
        color_code: {
            type: DataTypes.STRING,
        },
        color_label: {
            type: DataTypes.STRING,
        },
        interior_code: {
            type: DataTypes.STRING,
        },
        interior_label: {
            type: DataTypes.STRING,
        },
        marketing_flag: {
            type: DataTypes.STRING,
        },
        energy_code: {
            type: DataTypes.STRING,
        },
        energy_label: {
            type: DataTypes.STRING,
        },
        type_code: {
            type: DataTypes.STRING,
        },
        type_label: {
            type: DataTypes.STRING,
        },
        power: {
            type: DataTypes.STRING,
        },
        class_code: {
            type: DataTypes.STRING,
        },
        class_label: {
            type: DataTypes.STRING,
        },
        vds_vehicle: {
            type: DataTypes.INTEGER,
        },
        public_price_incl_tax: {
            type: DataTypes.INTEGER,
        },
        painting_payment_code: {
            type: DataTypes.STRING,
        },
        status: {
            type: DataTypes.BOOLEAN,
        },
        equipment: {
            type: Sequelize.JSON,
        },
        is_new: {
            type: DataTypes.BOOLEAN,
        },
    }, {
        timestamps: true,
        createdAt: true,
        updatedAt: true,
        fields: {
            createdAt: {
                update: false
            }
        }
    });
};