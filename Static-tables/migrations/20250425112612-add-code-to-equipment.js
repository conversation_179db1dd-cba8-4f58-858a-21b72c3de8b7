"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkDelete("equipments", null, {});

    await queryInterface.addColumn("equipments", "code", {
      type: Sequelize.STRING,
      unique: true,
      allowNull: false,
    });
  },

  async down(queryInterface) {
    await queryInterface.removeColumn("equipments", "code");
  },
};
