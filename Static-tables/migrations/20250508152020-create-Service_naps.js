'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable("service_naps", {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      reference: {
        type: Sequelize.UUID,
        unique: true,
        allowNull: false,
        defaultValue: Sequelize.UUIDV4,
      },
      service_reference: {
        type: Sequelize.STRING,
        allowNull: false,
        references: {
          model: "services",
          key: "reference",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      nap_code: {
        type: Sequelize.STRING,
        references: {
          model: "naps",
          key: "code",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
    });

    await queryInterface.addIndex(
      "service_naps",
      ["service_reference", "nap_code"],
      {
        unique: true,
        name: "unique_service_nap",
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex("service_naps", "unique_service_nap")
    await queryInterface.dropTable("service_naps");
  },
};