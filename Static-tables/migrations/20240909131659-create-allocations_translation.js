/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up (queryInterface, Sequelize) {

        await queryInterface.createTable("allocation_translations", {
            id: {
                allowNull: false,
                autoIncrement: true,
                primaryKey: true,
                type: Sequelize.INTEGER,
            },
            allocation_code: {
                type: Sequelize.STRING,
                allowNull: false,
                references: {
                    model: "allocations",
                    key: "code"
                },
                onUpdate: "CASCADE",
                onDelete: "CASCADE",
            },
            allocation_category_code: {
                type: Sequelize.STRING,
            },

            operation_code: {
                type: Sequelize.STRING,
            },
            label: {
                type: Sequelize.STRING,
            },
            language_code: {
                type: Sequelize.STRING,
                allowNull: false,
            },
            createdAt: {
                allowNull: false,
                type: Sequelize.DATE,
                defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
            },
            updatedAt: {
                allowNull: false,
                type: Sequelize.DATE,
                defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
            },
            deletedAt: {
                allowNull: true,
                type: Sequelize.DATE,
            },
        });
    },

    async down (queryInterface, Sequelize) {
        await queryInterface.dropTable("allocation_translations", {
            cascade: true
        });
    }
};