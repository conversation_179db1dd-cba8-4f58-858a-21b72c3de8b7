'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // First remove the foreign key constraint from the partners table
    await queryInterface.removeConstraint(
      'partners', 
      'partners_tax_code_fkey'
    );

    // Then remove the foreign key constraint from the services table
    await queryInterface.removeConstraint(
      'services', 
      'services_tax_code_fkey'
    );
  },

  async down(queryInterface, Sequelize) {
    // Restore the foreign key constraint on the services table
    await queryInterface.addConstraint('services', {
      fields: ['tax_code'],
      type: 'foreign key',
      name: 'services_tax_code_fkey',
      references: {
        table: 'taxes',
        field: 'code'
      },
      onDelete: 'NO ACTION',
      onUpdate: 'NO ACTION'
    });

    // Restore the foreign key constraint on the partners table
    await queryInterface.addConstraint('partners', {
      fields: ['tax_code'],
      type: 'foreign key',
      name: 'partners_tax_code_fkey',
      references: {
        table: 'taxes',
        field: 'code'
      },
      onDelete: 'NO ACTION',
      onUpdate: 'NO ACTION'
    });
  }
};