/** @type {import('sequelize-cli').Migration} */
module.exports = {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.addColumn(
            "phase_translations",
            "phase_associated_to_label",
            {
                type: Sequelize.STRING,
                allowNull: true 
            }
        );
    },

    down: async (queryInterface) => {
        await queryInterface.removeColumn(
            "phase_translations",
            "phase_associated_to_label"
        );
    },
};
