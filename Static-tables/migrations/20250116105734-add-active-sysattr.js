/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up (queryInterface, Sequelize) {
        await queryInterface.addColumn("activities", "active", {
            type: Sequelize.BOOLEAN,
            defaultValue: true, 
        });

        await queryInterface.addColumn("activities", "system_attribute", {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        });

        await queryInterface.addColumn("activity_translations", "active", {
            type: Sequelize.BOOLEAN,
            defaultValue: true, 
        });

        await queryInterface.addColumn("activity_translations", "system_attribute", {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
        });
    },

    async down (queryInterface) {
        await queryInterface.removeColumn("activities", "active");
        await queryInterface.removeColumn("activities", "system_attribute");
          
        await queryInterface.removeColumn("activity_translations", "active");
        await queryInterface.removeColumn("activity_translations", "system_attribute");
    }
};
