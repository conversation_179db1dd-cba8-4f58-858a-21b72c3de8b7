'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Check if the table exists and get its description
      const tableExists = await queryInterface.sequelize.query(
        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'service_operations');",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );
      
      if (!tableExists[0].exists) {
        console.log('Table service_operations does not exist, skipping migration');
        await transaction.commit();
        return;
      }

      const tableDescription = await queryInterface.describeTable('service_operations');
      
      //  Clean up existing data - remove rows with null operation_code if they exist
      if (tableDescription.operation_code) {
        await queryInterface.sequelize.query(
          "DELETE FROM service_operations WHERE operation_code IS NULL;",
          { transaction }
        );
        
        // Drop the old foreign key constraint if it exists
        await queryInterface.sequelize.query(
          "ALTER TABLE service_operations DROP CONSTRAINT IF EXISTS service_operations_operation_code_fkey;",
          { transaction }
        );
        
        // Drop the old unique constraint if it exists
        await queryInterface.sequelize.query(
          "ALTER TABLE service_operations DROP CONSTRAINT IF EXISTS service_operations_service_reference_operation_code_key;",
          { transaction }
        );
        
        // Remove the old operation_code column
        await queryInterface.removeColumn('service_operations', 'operation_code', { transaction });
      }
      
      // Add the new column as nullable first
      if (!tableDescription.operation_nature_type_code) {
        await queryInterface.addColumn('service_operations', 'operation_nature_type_code', {
          type: Sequelize.STRING,
          allowNull: true, // Start as nullable
        }, { transaction });
      }
      
      //  Clean up any existing rows with null operation_nature_type_code
      await queryInterface.sequelize.query(
        "DELETE FROM service_operations WHERE operation_nature_type_code IS NULL;",
        { transaction }
      );
      
      //  Now make the column NOT NULL and add constraints
      await queryInterface.changeColumn('service_operations', 'operation_nature_type_code', {
        type: Sequelize.STRING,
        allowNull: false,
      }, { transaction });
      
      //  Add the foreign key constraint
      await queryInterface.sequelize.query(`
        ALTER TABLE service_operations 
        ADD CONSTRAINT service_operations_operation_nature_type_code_fkey 
        FOREIGN KEY (operation_nature_type_code) 
        REFERENCES operation_nature_type_mapping(operation_nature_type_code) 
        ON DELETE CASCADE ON UPDATE CASCADE;
      `, { transaction });
      
      //  Add the unique constraint
      await queryInterface.addConstraint('service_operations', {
        fields: ['service_reference', 'operation_nature_type_code'],
        type: 'unique',
        name: 'service_operations_service_reference_operation_nature_type_code_key',
        transaction
      });
      
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      const tableDescription = await queryInterface.describeTable('service_operations');
      
      if (tableDescription.operation_nature_type_code) {
        // Drop the new foreign key constraint
        await queryInterface.sequelize.query(
          "ALTER TABLE service_operations DROP CONSTRAINT IF EXISTS service_operations_operation_nature_type_code_fkey;",
          { transaction }
        );
        
        // Drop the new unique constraint
        await queryInterface.sequelize.query(
          "ALTER TABLE service_operations DROP CONSTRAINT IF EXISTS service_operations_service_reference_operation_nature_type_code_key;",
          { transaction }
        );
        
        // Remove the new operation_nature_type_code column
        await queryInterface.removeColumn('service_operations', 'operation_nature_type_code', { transaction });
      }
      
      // Add back the old operation_code column if it doesn't exist
      if (!tableDescription.operation_code) {
        await queryInterface.addColumn('service_operations', 'operation_code', {
          type: Sequelize.STRING,
          allowNull: false,
          references: {
            model: 'operations',
            key: 'code'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        }, { transaction });
      }
      
      // Add back the old unique constraint
      await queryInterface.addConstraint('service_operations', {
        fields: ['service_reference', 'operation_code'],
        type: 'unique',
        name: 'service_operations_service_reference_operation_code_key',
        transaction
      });
      
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};