'use strict';

  module.exports = {
    up: async (queryInterface, Sequelize) => {
      await queryInterface.createTable('scale_services', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        reference: {
          type: Sequelize.STRING,
          allowNull: false,
          unique: true
        },
        scale_code: {
          type: Sequelize.STRING,
          allowNull: false,
          references: {
            model: 'scales',
            key: 'code'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        },
        service_reference: {
          type: Sequelize.STRING,
          allowNull: false,
          references: {
            model: 'services',
            key: 'reference'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false
        }
      });

      // Add unique constraint for scale_code and service_reference
      await queryInterface.addConstraint('scale_services', {
        fields: ['scale_code', 'service_reference'],
        type: 'unique',
        name: 'unique_scale_service'
      });
    },

    down: async (queryInterface, Sequelize) => {
      await queryInterface.dropTable('scale_services');
    }
  };