'use strict';

const {CurrencyModel} = require("../db");
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.sequelize.query("DELETE FROM currencies WHERE code = 'DOP '");
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.bulkInsert("currencies", [
        {
            "code": "DOP ",
            "symbol": "RD$",
            "active": true,
        }], { ignoreDuplicates: true });
  }
};
