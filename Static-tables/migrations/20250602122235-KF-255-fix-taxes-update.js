'use strict';
const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Define mapping between tax_rate codes and references
    const referenceMapping = {
      'EXOSU': 'f6109403-b13c-44a7-91a5-810483c8cd3e',
      'TVARSU': 'e3ff5912-9196-457e-ac71-1d0a9874ce92',
      'TVASSU': '5d3b5574-d1b1-4450-90aa-9d48869eeaa1',
      'TVADE': '23d2d9ab-9130-4585-a1f5-25306f104e8d',
      'TVADE1': 'eddf96db-41b5-46f4-85dc-cb1798e6cd64',
      'EXODE': 'a30d2728-ff8e-49f6-9dfc-c95bf7bd5040',
      'TVARDE': '99a417e3-89ff-4186-8334-36abb4a6e90f',
      'TVASDE': '846247f1-c15c-4adb-95e4-0b4bcf32c4a5',
      'TVAIT': '7b142098-4950-43c9-b31f-4b40e2b46bba',
      'TVAIT1': 'e8f16be0-5a36-4520-80bf-3fd048598134',
      'EXOIT': 'f074daac-1a46-4497-8238-856fc081164e',
      'TVARIT': '40242f7b-e088-4e8d-b63b-cb9384d734ba',
      'TVASIT': 'dcde6de8-3fb7-4660-82ce-408d1c57908a',
      'TVAES': '651494fc-97dc-4d47-854c-174c37c5592f',
      'TVAES1': 'c08768f0-0fd3-4737-82ce-39b67f5c811e',
      'EXOES': '9866a3c5-8bdc-4337-b5e1-75f38ddf3895',
      'TVARES': '532b3dbe-0f9c-4b31-a140-9f5a7dc72ad2',
      'TVABE': '7c0d4396-d83f-4b7f-af14-5607daa26a79',
      'TVABE1': '8cbe9c2c-178e-403d-a34e-9dbaff4234d0',
      'EXOBE': '1d8c5bae-4fcc-4f22-b1be-b0ff761aff94',
      'TVARBE': 'bc7eacee-d376-405f-b02a-3d4d6a7299a1',
      'TVALU': 'bae20560-e129-4ec6-b98a-3d98294b6d46',
      'TVALU1': 'a1aadc78-dfdf-41e2-832b-f3e62a8c421f',
      'EXOLU': '771231e5-0e02-49ff-92e2-f09cd35c5061',
      'TVARLU': '7c03b57f-3683-44f3-ad6f-62e47fbce8f6',
      'TVASLU': 'e3f4de87-9463-4fe5-9eb3-c440b1b8943a',
      'TVASLU1': '9bb5df6e-b01d-4ff1-aafe-7c999b7c24eb',
      'TVASLU2': '91c60a20-f06f-40e8-9632-2aa4365e0573',
      'TVASLU3': '69707c92-7d82-483c-a504-842ffca1a640',
      'TVAFR2': '2978b442-9663-4137-bfb8-9c107c91d074',
      'TVAR2': '81e390cd-b389-4d30-a4e5-cf9301001f24',
      'TVAFR3': '23340055-82a8-46c6-898b-c65b818414e1',
      'TVABE2': '94f756b2-cabb-4cc0-8024-7d433156885f',
      'TVALU22': '98d30fb1-10df-4ba1-a138-fe8cadf16107',
      'TVALUBE1': 'de0139b9-aab6-4fe6-b21e-c2207ec2851e'
    };

    // First add the column without constraints
    await queryInterface.addColumn('tax_rates', 'reference', {
      type: Sequelize.UUID,
      allowNull: true, // temporarily allow null
    });

    // Get all rows that need a reference
    const rows = await queryInterface.sequelize.query(
      'SELECT id, code FROM tax_rates WHERE reference IS NULL',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    // Update each row with the correct reference
    for (const row of rows) {
      const reference = row.code && referenceMapping[row.code]
        ? referenceMapping[row.code]
        : uuidv4();

      await queryInterface.sequelize.query(
        'UPDATE tax_rates SET reference = ? WHERE id = ?',
        {
          replacements: [reference, row.id],
          type: queryInterface.sequelize.QueryTypes.UPDATE
        }
      );
    }

    // Apply constraints
    await queryInterface.changeColumn('tax_rates', 'reference', {
      type: Sequelize.UUID,
      allowNull: false,
      unique: true,
      defaultValue: Sequelize.UUIDV4
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('tax_rates', 'reference');
  }
};