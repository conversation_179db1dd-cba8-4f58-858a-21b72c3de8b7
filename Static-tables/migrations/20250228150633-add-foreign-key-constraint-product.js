"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableDescription = await queryInterface.describeTable("products");
        if (tableDescription.activity_id) {
            await queryInterface.removeColumn("products", "activity_id");
        }

    await queryInterface.addColumn("products", "activity_code", {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn("products", "activity_associated_to", {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addConstraint("products", {
      fields: ["activity_code", "activity_associated_to"],
      type: "foreign key",
      name: "fk_code_associated_to_activity",
      references: {
        table: "activities",
        fields: ["code", "associated_to"],
      },
      onDelete: "CASCADE",
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn("products", "activity_id", {
      type: Sequelize.INTEGER,
      allowNull: true,
    })

    await queryInterface.removeConstraint(
      "products",
      "fk_code_associated_to_activity"
    );
    await queryInterface.removeColumn("products", "activity_code");
    await queryInterface.removeColumn("products", "activity_associated_to");
  },
};
