"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.createTable("product_type_mapping",{
            id:{
                type: Sequelize.INTEGER,
                primaryKey: true,
                autoIncrement:true,
                allowNull: false
            },
            product_id:{
                type: Sequelize.INTEGER,
                allowNull: false,
                references: {
                    model: "products",
                    key: "id"
                },
            },
            operation_nature_type_mapping_id:{
                type: Sequelize.INTEGER,
                allowNull: false,
                references: {
                    model: "operation_nature_type_mapping",
                    key: "id"
                },
             
            }
        });
    },

    down: async (queryInterface) => {
        await queryInterface.dropTable("product_type_mapping");
    }
};
