'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable("service_products", {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      reference: {
        type: Sequelize.UUID,
        unique: true,
        allowNull: false,
        defaultValue: Sequelize.UUIDV4,
      },
      service_reference: {
        type: Sequelize.STRING,
        allowNull: false,
        references: {
          model: "services",
          key: "reference",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      product_code: {
        type: Sequelize.STRING,
        references: {
          model: "products",
          key: "code",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
    })

    await queryInterface.addIndex("service_products", ["service_reference", "product_code"], {
      unique: true,
      name: "unique_service_product"
    })

  },

  down: async (queryInterface) => {
    await queryInterface.removeIndex("service_products", "unique_service_product")
    await queryInterface.dropTable("service_products");
  },
};