"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableDescription = await queryInterface.describeTable(
      "operation_nature_type_mapping"
    );

    if (tableDescription.operation_nature_mapping_id) {
      await queryInterface.removeColumn(
        "operation_nature_type_mapping",
        "operation_nature_mapping_id"
      );
    }
    if (tableDescription.type_id) {
      await queryInterface.removeColumn(
        "operation_nature_type_mapping",
        "type_id"
      );
    }

    if (!tableDescription.type_code) {
      await queryInterface.addColumn(
        "operation_nature_type_mapping",
        "type_code",
        {
          type: Sequelize.STRING,
          allowNull: true,
          references: {
            model: "types",
            key: "code",
          },
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
        }
      );
    }
    if (!tableDescription.operation_nature_type_code) {
      await queryInterface.addColumn(
        "operation_nature_type_mapping",
        "operation_nature_type_code",
        {
          type: Sequelize.STRING,
          allowNull: true,
          unique: true,
        }
      );
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(
      "operation_nature_type_mapping",
      "type_code"
    );
    await queryInterface.removeColumn(
      "operation_nature_type_mapping",
      "operation_nature_type_code"
    );

    await queryInterface.addColumn("operation_nature_type_mapping", "type_id", {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: "types",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    });

    await queryInterface.addColumn(
      "operation_nature_type_mapping",
      "operation_nature_mapping_id",
      {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: "operation_nature_mapping",
          key: "id",
        },
      }
    );
  },
};
