"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableDescription = await queryInterface.describeTable(
      "operation_nature_mapping"
    );
    if (tableDescription.operation_id) {
      await queryInterface.removeColumn(
        "operation_nature_mapping",
        "operation_id"
      );
    }
    if (tableDescription.nature_id) {
      await queryInterface.removeColumn(
        "operation_nature_mapping",
        "nature_id"
      );
    }
    if (!tableDescription.operation_code) {
      await queryInterface.addColumn(
        "operation_nature_mapping",
        "operation_code",
        {
          type: Sequelize.STRING,
          allowNull: true,
          references: {
            model: "operations",
            key: "code",
          },
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
        }
      );
    }
    if (!tableDescription.nature_code) {
      await queryInterface.addColumn("operation_nature_mapping", "nature_code", {
        type: Sequelize.STRING,
        allowNull: true,
        references: {
          model: "natures",
          key: "code",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      });
    }
    if (!tableDescription.operation_nature_code) {
      await queryInterface.addColumn(
        "operation_nature_mapping",
        "operation_nature_code",
        {
          type: Sequelize.STRING,
          allowNull: true,
          unique: true,
        }
      );
    }
  },

  async down(queryInterface, Sequelize) {
    queryInterface.removeColumn(
      "operation_nature_mapping",
      "operation_nature_code"
    );
    queryInterface.removeColumn("operation_nature_mapping", "nature_code");
    queryInterface.removeColumn("operation_nature_mapping", "operation_code");

    await queryInterface.addColumn("operation_nature_mapping", "operation_id", {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: "operations",
        key: "id",
      },
    });

    await queryInterface.addColumn("operation_nature_mapping", "nature_id", {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: "natures",
        key: "id",
      },
    });
  },
};
