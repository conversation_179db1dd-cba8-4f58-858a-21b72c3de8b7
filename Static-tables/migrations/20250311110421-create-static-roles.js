"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("static_roles", {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      code: {
        type: Sequelize.STRING,
        unique:true,
      },
      label: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      is_exclusive: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      is_client: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      associated_to: {
        type: Sequelize.STRING,
        defaultValue: "ACTEUR",
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("static_roles");
  },
};
