"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn("role_translations", "role_associated_to");

    await queryInterface.removeConstraint("role_translations", "role_translations_role_code_fkey");

    await queryInterface.addConstraint("role_translations", {
      fields: ["role_code"],
      type: "foreign key",
      name: "role_translations_role_code_fkey",
      references: {
        table: "roles",
        field: "code",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn("role_translations", "role_associated_to", {
        type: DataTypes.STRING,
        allowNull: false,
    });

    await queryInterface.removeConstraint("role_translations", "role_translations_role_code_fkey");

    await queryInterface.addConstraint("role_translations", {
      fields: ["role_code"],
      type: "foreign key",
      name: "role_translations_role_code_fkey",
      references: {
        table: "roles",
        field: "code",
      },
      onUpdate: "NO ACTION",
      onDelete: "NO ACTION",
    });
  },
};
