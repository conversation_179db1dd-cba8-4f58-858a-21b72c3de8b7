package com.datatricks.actors.service;

import com.datatricks.actors.model.Actor;
import com.datatricks.actors.model.ActorRole;
import com.datatricks.actors.model.BillingParameters;
import com.datatricks.actors.model.InvoiceDeliveryMethod;
import com.datatricks.actors.model.dto.*;
import com.datatricks.actors.repository.*;
import com.datatricks.actors.utils.JpaQueryFilters;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;

import java.util.*;

@ExtendWith(MockitoExtension.class)
class BillingParameterServiceTest {

    @Mock
    private BillingParameterRepository billingParameterRepository;

    @Mock
    private ModelMapper modelMapper;

    @Mock
    private AddressRepository addressRepository;

    @Mock
    private ActorRepository actorRepository;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private ActorRoleRepository actorRoleRepository;

    @Mock
    private CommunicationMeanRepository communicationMeanRepository;

    @InjectMocks
    private BillingParameterService billingParameterService;

    @Test
    void testGetBillingParameters() {
        // Arrange
        Map<String, String> params = new HashMap<>(Map.of("key", "value"));
        Page<BillingParameters> mockPage = new PageImpl<>(List.of());

        // Use matchers to avoid argument mismatch issues
        Mockito.when(billingParameterRepository.findAll(Mockito.any(Specification.class), Mockito.any(Pageable.class)))
                .thenReturn(mockPage);

        // Act
        ResponseEntity<PageDto<BillingParametersDto>> response =
                billingParameterService.getBillingParameters(params);

        // Assert
        Assertions.assertNotNull(response);
        Assertions.assertEquals(0, Objects.requireNonNull(response.getBody()).getData().size());
        Mockito.verify(billingParameterRepository).findAll(Mockito.any(Specification.class), Mockito.any(Pageable.class));
    }


    @Test
    void testGetBillingParameterById() {
        // Arrange
        String actorReference = "actor123";
        Long id = 1L;
        String roleCode = "ROLE_TEST";

        BillingParameters mockBillingParameter = new BillingParameters();
        mockBillingParameter.setInvoiceDeliveryMode(InvoiceDeliveryMethod.EMAIL);

        Mockito.when(billingParameterRepository.findByIdAndActorReferenceAndRoleCodeAndDeletedAtIsNull(id, actorReference, roleCode))
                .thenReturn(Optional.of(mockBillingParameter));

        // Act
        ResponseEntity<SingleResultDto<BillingParametersDto>> response =
                billingParameterService.getBillingParameterById(actorReference, id, roleCode);

        // Assert
        Assertions.assertNotNull(response);
        Mockito.verify(billingParameterRepository)
                .findByIdAndActorReferenceAndRoleCodeAndDeletedAtIsNull(id, actorReference, roleCode);
    }

    @Test
    void testCreateBillingParameter() {
        // Arrange
        String actorReference = "actor123";
        String roleCode = "ROLE_TEST";
        NewBillingParametersDto dto = new NewBillingParametersDto();

        Actor mockActor = new Actor();
        mockActor.setId(1L); // Set a valid ID
        mockActor.setReference(actorReference);

        BillingParameters mockBillingParameter = new BillingParameters();
        mockBillingParameter.setActor(mockActor);
        mockBillingParameter.setRoleCode(roleCode);

        // Mock repository behaviors
        Mockito.when(actorRepository.findByReferenceAndDeletedAtIsNull(actorReference))
                .thenReturn(Optional.of(mockActor));
        Mockito.when(actorRoleRepository.existsByActorIdIdAndRoleCodeAndDeletedAtIsNull(1L, roleCode))
                .thenReturn(true);
        Mockito.when(billingParameterRepository.save(Mockito.any(BillingParameters.class)))
                .thenReturn(mockBillingParameter);
        Mockito.when(roleRepository.existsByCode(roleCode)).thenReturn(true);
        // Act
        ResponseEntity<SingleResultDto<BillingParametersDto>> response =
                billingParameterService.createBillingParameter(actorReference, roleCode, dto);

        // Assert
        Assertions.assertNotNull(response);
        Assertions.assertNotNull(response.getBody());
        Assertions.assertEquals(roleCode, response.getBody().getData().getRoleCode());
        Mockito.verify(actorRepository).findByReferenceAndDeletedAtIsNull(Mockito.eq(actorReference));
        Mockito.verify(billingParameterRepository, Mockito.times(2))
                .save(Mockito.any(BillingParameters.class));
    }



    @Test
    void testDeleteBillingParameter() {
        // Arrange
        Long id = 1L;
        String actorReference = "actor123";
        String roleCode = "ROLE_TEST";
        BillingParameters mockBillingParameter = new BillingParameters();

        Mockito.when(billingParameterRepository.findByIdAndActorReferenceAndRoleCodeAndDeletedAtIsNull(
                        id, actorReference, roleCode))
                .thenReturn(Optional.of(mockBillingParameter));

        // Act
        ResponseEntity<SingleResultDto<BillingParametersDto>> response =
                billingParameterService.deleteBillingParameter(id, actorReference, roleCode);

        // Assert
        Assertions.assertNotNull(response);
        Mockito.verify(billingParameterRepository)
                .findByIdAndActorReferenceAndRoleCodeAndDeletedAtIsNull(id, actorReference, roleCode);
        Mockito.verify(billingParameterRepository).save(mockBillingParameter);
    }

    @Test
    void testPatchBillingParameter() {
        // Arrange
        Long id = 1L;
        String actorReference = "actor123";
        String roleCode = "ROLE_TEST";
        NewBillingParametersDto dto = new NewBillingParametersDto();
        dto.setInvoiceDeliveryMode(InvoiceDeliveryMethod.EMAIL);
        dto.setCopyCount(1);

        BillingParameters mockBillingParameter = new BillingParameters();
        mockBillingParameter.setRoleCode("ROLE_TEST");
        mockBillingParameter.setActor(new Actor(1L));
        mockBillingParameter.setInvoiceDeliveryMode(InvoiceDeliveryMethod.EMAIL);
        mockBillingParameter.setCopyCount(1);
        Mockito.when(billingParameterRepository.findByIdAndDeletedAtIsNull(id))
                .thenReturn(Optional.of(mockBillingParameter));
        Mockito.when(roleRepository.existsByCode(roleCode)).thenReturn(true);
        Mockito.when(actorRoleRepository.existsByActorIdIdAndRoleCodeAndDeletedAtIsNull(Mockito.anyLong(), Mockito.eq(roleCode)))
                .thenReturn(true);
        Mockito.when(billingParameterRepository.save(Mockito.any(BillingParameters.class)))
                .thenReturn(mockBillingParameter);

        // Act
        ResponseEntity<SingleResultDto<BillingParametersDto>> response =
                billingParameterService.patchBillingParameter(id, actorReference, roleCode, dto);

        // Assert
        Assertions.assertNotNull(response);
        Mockito.verify(billingParameterRepository).findByIdAndDeletedAtIsNull(id);
        Mockito.verify(billingParameterRepository).save(Mockito.any(BillingParameters.class));
    }
}

