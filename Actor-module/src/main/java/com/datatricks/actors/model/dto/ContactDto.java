package com.datatricks.actors.model.dto;

import com.datatricks.actors.model.Positions;
import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ContactDto implements PageableDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("title")
    @Schema(description = "Title", example = "Mr, Mrs, Miss, Dr, Prof", type = "string", allowableValues = {"Mr", "Mrs", "Miss", "Dr", "Prof"})
    private String title;

    @JsonProperty("first_name")
    @NotBlank(message = "first_name:please provide a first name")
    @Schema(description = "First name", example = "Ahmed", type = "string")
    private String firstName;

    @JsonProperty("last_name")
    @Schema(description = "Last name", example = "BENZINA", type = "string")
    @NotBlank(message = "last_name:please provide a last name")
    private String lastName;

    @JsonProperty("quality")
    @Enumerated(EnumType.STRING)
    @Schema(description = "Quality", example = "SHAREHOLDER, ADMINISTRATOR, ASSISTANT_MANAGER", type = "string")
    private Positions quality;

    @JsonProperty("preferred")
    @Schema(description = "Preferred", example = "true", type = "boolean")
    private Boolean preferred;

    @JsonProperty("status")
    @Schema(description = "Status", example = "true(ACTIVE), false(INACTIVE)", type = "boolean")
    private boolean status;

    @JsonProperty("start_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start at", example = "2021-07-01", type = "Date")
    private Date startAt;

    @JsonProperty("birth_day_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Birth day date", example = "2021-07-01", type = "Date")
    private Date birthDayDate;

    @JsonProperty("memo")
    @Schema(description = "Memo", example = "Memo", type = "string")
    private String memo;
}
