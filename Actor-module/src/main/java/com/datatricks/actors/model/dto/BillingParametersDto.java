package com.datatricks.actors.model.dto;

import com.datatricks.actors.model.*;
import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BillingParametersDto implements PageableDto {
    private Long id;

    @JsonProperty("invoice_delivery_mode")
    @NotNull(message = "Invoice delivery mode is required.")
    @Schema(description = "Invoice delivery mode", requiredMode = Schema.RequiredMode.REQUIRED, example = "EMAIL or ADDRESS", allowableValues = {"EMAIL", "ADDRESS"}, type = "string")
    private InvoiceDeliveryMethod invoiceDeliveryMode;

    @JsonProperty("notes")
    @Schema(description = "Notes", example = "Notes", type = "string")
    private String notes;

    @JsonProperty("emails")
    @Schema(description = "Emails")
    private List<BillingParametersEmailDto> emails = new ArrayList<>();

    @JsonProperty("address")
    @Schema(description = "Address", type = "object")
    private AddressDto address;

    @JsonProperty("copy_count")
    @Schema(description = "Copy count", example = "1", type = "integer")
    private Integer copyCount;

    @JsonIgnore
    @Schema(description = "Actor role code", example = "INI", type = "string")
    private String roleCode;

    @JsonProperty("invoice_edition_date_limit")
    @Schema(description = "Invoice edition date limit", example = "2021-01-01", type = "string")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate invoiceEditionDateLimit;

    public BillingParametersDto(BillingParameters billingParameter) {
        this.id = billingParameter.getId();
        this.invoiceDeliveryMode = billingParameter.getInvoiceDeliveryMode();
        this.notes = billingParameter.getNotes();
        this.roleCode = billingParameter.getRoleCode();
        this.copyCount = billingParameter.getCopyCount();
        this.invoiceEditionDateLimit = billingParameter.getInvoiceEditionDateLimit();
        if (InvoiceDeliveryMethod.EMAIL.equals(billingParameter.getInvoiceDeliveryMode())) {
            for (CommunicationMean communicationMean : billingParameter.getCommunicationMeansList()) {
                    this.emails.add(new BillingParametersEmailDto(communicationMean.getContactId(), communicationMean));
            }
        } else if (InvoiceDeliveryMethod.ADDRESS.equals(billingParameter.getInvoiceDeliveryMode())) {
            this.address = new AddressDto(billingParameter.getAddress());
        }
    }
}
