package com.datatricks.actors.model.dto;

import com.datatricks.actors.model.FiscalYearClosingTypes;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@NoArgsConstructor
public class FiscalYearParams implements PageableDto {
    private Long id;
    @Schema(description = "Exercice", example = "2021", type = "string")
    private String exercice;

    @JsonProperty("start_year")
    @Schema(description = "Start year", example = "2021", type = "integer")
    private Integer startYear;

    @NotNull(message = "startMonth: Start month is required")
    @JsonProperty("start_month")
    @Schema(description = "Start month", example = "1", type = "integer")
    private Integer startMonth;

    @JsonProperty("end_year")
    @Schema(description = "End year", example = "2021", type = "integer")
    private Integer endYear;

    @JsonProperty("end_month")
    @Schema(description = "End month", example = "12", type = "integer")
    private Integer endMonth;

    @JsonProperty("closing_date")
    @Schema(description = "Closing date", example = "2021-12-31", type = "Date")
    private Date closingDate;

    @JsonProperty("closing_type")
    @Schema(description = "Closing type", example = "None", type = "string", allowableValues = {"None", "Provisional", "Reopened", "Final"})
    private FiscalYearClosingTypes closingType;

    @NotNull(message = "numberOfMonths: Number of months is required")
    @JsonProperty("number_of_months")
    @Schema(description = "Number of months", example = "12", type = "integer")
    private Integer numberOfMonths;

    @JsonProperty("entry_date")
    @Schema(description = "Entry date", example = "2021-01-01", type = "Date")
    private Date entryDate;

    @Schema(description = "Recovery", example = "0", type = "integer")
    private Integer recovery;

}
