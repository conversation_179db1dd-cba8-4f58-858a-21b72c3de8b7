package com.datatricks.actors.model.inject;

import com.datatricks.actors.model.Positions;
import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.Set;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ContactInjectDto {
    @JsonApiId
    private Long id;

    @JsonApiType
    private String myType = "contact";

    @JsonProperty("title")
    private String title;

    @JsonProperty("first_name")
    private String firstName;

    @JsonProperty("last_name")
    private String lastName;

    @JsonProperty("quality")
    @Enumerated(EnumType.STRING)
    private Positions quality;

    @JsonProperty("preferred")
    private Boolean preferred;

    @JsonProperty("start_at")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startAt;

    @JsonProperty("birth_day_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date birthDayDate;

    @JsonProperty("memo")
    private String memo;

    @JsonProperty("status")
    private boolean status;

    @JsonApiRelationships("communication_means")
    @JsonIgnore
    @OneToMany
    private Set<CommunicationMeanInjectDto> communicationMeans;
}
