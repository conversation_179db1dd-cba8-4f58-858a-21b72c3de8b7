package com.datatricks.actors.model.dto;

import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UpdateActorDto implements PageableDto {

    @JsonIgnore
    @JsonProperty("id")
    private Long id;

    @JsonProperty("external_reference")
    @Schema(description = "External reference", example = "ACT_123456", type = "string")
    private String externalReference;

    @JsonProperty("short_name")
    @Schema(description = "Short name", example = "DATATRICKS", type = "string")
    private String shortName;

    @JsonProperty("activity_code")
    @Schema(description = "LegalActivities", example = "01.23Z", type = "string")
    private String activityCode;

    @JsonProperty("vat")
    @Schema(description = "Vat", example = "FR*********", type = "string")
    private String vat;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("company_creation_date")
    @Schema(description = "Company creation date", example = "2021-07-01", type = "Date")
    private Date companyCreationDate;

    @JsonProperty("registration_country")
    @Schema(description = "Registration country", example = "FR", type = "string")
    private String registrationCountry;

    @JsonProperty("type")
    @Schema(description = "Type of the actor", example = "NATURAL_PERSON or MANAGEMENT_COMPANY")
    private String type;

    @JsonProperty("legal_category_code")
    @Schema(description = "Legal category", example = "2310", type = "string")
    private String legalCategoryCode;

    @JsonProperty("phase_code")
    @Schema(description = "Phase", example = "INI", type = "string")
    private String phaseCode;

    @JsonProperty("national_identity")
    @Schema(description = "National identity", example = "*********", type = "string")
    private String nationalIdentity;

    @JsonProperty("feed_channel")
    @Schema(description = "Feed channel", example = "1", type = "string")
    private String feedChannel;

    @JsonProperty("memo")
    @Schema(description = "Memo", example = "Memo", type = "string")
    private String memo;

    @JsonProperty("tax_reference")
    @Schema(description = "Tax reference", example = "*********", type = "string")
    private String taxReference;
}
