package com.datatricks.actors.model.inject;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PhaseInjectDto {

    @JsonApiId
    private Long id;

    @JsonApiType
    private String myType = "phase";

    private String code;

    private String label;

    @JsonProperty("associated_to")
    private String associatedTo;

    private String language;
}
