package com.datatricks.actors.model.inject;


import com.datatricks.actors.model.ActorTypes;
import com.datatricks.actors.model.dto.PageableDto;
import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ActorInjectDto implements PageableDto {
    @JsonApiId
    private Long id;

    @JsonApiType
    private String myType = "actor";

    @JsonProperty("reference")
    private String reference;

    @JsonProperty("external_reference")
    private String externalReference;

    @JsonProperty("short_name")
    private String shortName;

    @JsonProperty("name")
    private String name;

    @JsonProperty("vat")
    private String vat;

    @JsonProperty("registration_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date registrationDate;

    @JsonProperty("company_creation_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date companyCreationDate;

    @JsonProperty("registration_country")
    private String registrationCountry;

    @JsonProperty("register_type")
    private String registerType;

    @JsonProperty("register_number")
    private String registerNumber;

    @JsonProperty("type")
    @Enumerated(EnumType.STRING)
    private ActorTypes type;

    @JsonProperty("national_identity")
    private String nationalIdentity;

    @JsonProperty("feed_channel")
    private String feedChannel;

    @JsonProperty("memo")
    private String memo;

    @JsonProperty("tax_reference")
    private String taxReference;

    @JsonProperty("phase")
    @JsonApiRelationships("phase")
    @JsonIgnore
    @ManyToOne
    private PhaseInjectDto phase;

    @JsonProperty("milestone")
    @JsonApiRelationships("milestone")
    @JsonIgnore
    @ManyToOne
    private MilestoneInjectDto milestone;

    @JsonProperty("activity")
    @JsonApiRelationships("activity")
    @JsonIgnore
    @ManyToOne
    private ActivityInjectDto activity;

    @JsonProperty("legal_category")
    @JsonApiRelationships("legal_category")
    @JsonIgnore
    @ManyToOne
    private LegalCategoryInjectDto legalCategory;

    @JsonProperty("country")
    @JsonApiRelationships("country")
    @JsonIgnore
    @ManyToOne
    private CountryInjectDto country;

    @JsonProperty("roles")
    @JsonApiRelationships("roles")
    @JsonIgnore
    @OneToMany
    private Set<RoleInjectDto> roles;

    @JsonProperty("bank_accounts")
    @JsonApiRelationships("bank_accounts")
    @JsonIgnore
    @OneToMany
    private Set<BankAccountInjectDto> bankAccounts;

    @JsonProperty("addresses")
    @JsonApiRelationships("addresses")
    @JsonIgnore
    @OneToMany
    private Set<AddressInjectDto> addresses;

    @JsonProperty("contacts")
    @JsonApiRelationships("contacts")
    @JsonIgnore
    @OneToMany
    private Set<ContactInjectDto> contacts;
}