package com.datatricks.actors.model.inject;

import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RoleInjectDto {
    @JsonApiId
    private Long id;

    @JsonApiType
    private String myType = "role";

    private String code;

    private String label;
    private String language;

    @JsonProperty("is_exclusive")
    private Boolean isExclusive;

    @JsonProperty("is_client")
    private Boolean isClient;

    @JsonProperty("system_role")
    private Boolean systemRole;

    @JsonProperty("is_principal")
    private Boolean isPrincipal;

    @JsonApiRelationships("business_reference")
    @JsonIgnore
    @ManyToOne
    private ActorInjectDto businessReference;

    @JsonProperty("invoice_edition_date_limit")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date invoiceEditionDateLimit;

    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startDate;

    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date endDate;
}
