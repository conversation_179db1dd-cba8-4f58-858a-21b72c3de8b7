package com.datatricks.actors.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RoleDto implements PageableDto {
    @NotNull(message = "id:please provide a role id")
    private Long id;
@Schema(description = "Role code", example = "FOURN", type = "string")
    private String code;
    @Schema(description = "Role label", example = "Fournisseur", type = "string")
    private String label;
    @Schema(description = "Role language", example = "FR", type = "string")
    private String language;
    @JsonProperty("is_exclusive")
    @Schema(description = "Is exclusive", example = "true", type = "boolean")
    private Boolean isExclusive;
    @JsonProperty("is_client")
    @Schema(description = "Is client", example = "true", type = "boolean")
    private Boolean isClient;
    private String associated_to;
}
