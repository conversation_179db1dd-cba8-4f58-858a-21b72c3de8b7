package com.datatricks.actors.model.inject;

import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LegalCategoryInjectDto {
    @JsonApiId
    private Long id;

    @JsonApiType
    private String myType = "legal_category";

    private String code;

    private String label;
}
