package com.datatricks.actors.model.response;

import com.datatricks.actors.model.dto.ContractActorAddressDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
public class ContractActorAddressResponse {
    @JsonProperty("total_count")
    private long total_count;

    @JsonProperty("page")
    private List<ContractActorAddressDto> page;
}
