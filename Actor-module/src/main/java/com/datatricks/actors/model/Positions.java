package com.datatricks.actors.model;

import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

@Getter
public enum Positions {
    SHAREHOLDER("Actionnaire", "Shareholder"),
    ADMINISTRATOR("Administrateur", "Administrator"),
    ASSISTANT_MANAGER("Assistant gestionnaire", "Assistant Manager"),
    SOLE_PARTNER("Associé unique", "Sole Partner"),
    OTHERS("Autres", "Others"),
    CO_MANAGER("CO - gérant", "Co - Manager"),
    CHIEF_ACCOUNTANT("Chef comptable", "Chief Accountant"),
    SECTION_HEAD("Chef de section", "Section Head"),
    SALES_PERSON("Commercial", "Salesperson"),
    ACCOUNTANT("Comptable", "Accountant"),
    SALES_CONSULTANT("Consultant des ventes", "Sales Consultant"),
    DIRECTOR("Directeur", "Director"),
    ADMINISTRATIVE_AND_FINANCIAL_DIRECTOR(
            "Directeur Administratif et Financier", "Administrative and Financial Director"),
    DEPUTY_DIRECTOR("Directeur adjoint", "Deputy Director"),
    ADMINISTRATIVE_DIRECTOR("Directeur administratif", "Administrative Director"),
    COMMERCIAL_DIRECTOR("Directeur commercial", "Commercial Director"),
    AGENCY_DIRECTOR("Directeur d'agence", "Agency Director"),
    LOGISTICS_DIRECTOR("Directeur de la logistique", "Logistics Director"),
    PRODUCTION_DIRECTOR("Directeur de la production", "Production Director"),
    SALES_DIRECTOR("Directeur des ventes", "Sales Director"),
    MARKETING_DIRECTOR("Directeur du marketting", "Marketing Director"),
    FINANCIAL_DIRECTOR("Directeur financier", "Financial Director"),
    GENERAL_DIRECTOR("Directeur général", "General Director"),
    DEPUTY_GENERAL_DIRECTOR("Directeur général adjoint", "Deputy General Director"),
    DELEGATED_GENERAL_DIRECTOR("Directeur général délégué", "Delegated General Director"),
    ECONOMIC_DIRECTOR("Directeur économique", "Economic Director"),
    EXECUTIVE_DIRECTOR("Directeut exécutif", "Executive Director"),
    ECONOMIST("Economiste", "Economist"),
    MANAGER("Gestionnaire", "Manager"),
    IT_MANAGER("Gestionnaire IT", "IT Manager"), // Renamed as specified
    ACQUISITIONS_MANAGER("Gestionnaire des acquisitions", "Acquisitions Manager"),
    MANAGER_GERANT("Gérant", "Manager"), // Adjusted to distinguish from the generic Manager
    MAYOR("Maire", "Mayor"),
    OFFICE("Bureau", "Office"),
    PAYMENT_OPERATOR("Opérateur de paiement", "Payment Operator"),
    ASSOCIATION_PRESIDENT("Président association", "Association President"),
    CEO("Président directeur général", "CEO"),
    CHAIRMAN_OF_THE_SUPERVISORY_BOARD("Président du Conseil de Surveillance", "Chairman of the Supervisory Board"),
    EXECUTIVE_BOARD_PRESIDENT("Président du Directoire", "Executive Board President"),
    CHAIRMAN_OF_THE_BOARD("Président du conseil d'administration", "Chairman of the Board"),
    IT_RESPONSIBLE("Responsable informatique", "IT Manager"),
    GENERAL_SERVICES_MANAGER("Responsable services généraux", "General Services Manager"),
    TECHNICAL_MANAGER("Responsable technique", "Technical Manager"),
    EXECUTIVE_SECRETARY("Secretaire direction", "Executive Secretary"),
    SECRETARY("Secrétaire", "Secretary"),
    SELF("Soi-même", "Self"),
    TECHNICIAN("Technicien", "Technician"),
    VICE_PRESIDENT("Vice-président", "Vice President");

    private final String frenchName;
    private final String englishName;
    private static final Map<String, Positions> BY_LABEL = new HashMap<>();

    static {
        for (Positions e : values()) {
            BY_LABEL.put(e.frenchName.toLowerCase(), e);
            BY_LABEL.put(e.englishName.toLowerCase(), e);
        }
    }

    Positions(String frenchName, String englishName) {
        this.frenchName = frenchName;
        this.englishName = englishName;
    }

    public static Positions getByLabel(String label) {
        return BY_LABEL.get(label.toLowerCase());
    }
}
