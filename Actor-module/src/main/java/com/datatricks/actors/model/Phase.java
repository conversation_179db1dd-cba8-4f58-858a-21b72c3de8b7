package com.datatricks.actors.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.util.Set;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "phases")
public class Phase {
    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "label")
    private String label;

    @Column(name = "associated_to")
    private String associatedTo;

    @Column(name = "language")
    private String language;

    @OneToMany(mappedBy = "phaseId", fetch = FetchType.LAZY)
    private Set<Milestone> milestones;

    public Phase(Long id) {
        this.id = id;
    }
}
