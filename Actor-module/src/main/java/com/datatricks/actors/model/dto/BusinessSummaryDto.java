package com.datatricks.actors.model.dto;

import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class BusinessSummaryDto implements PageableDto {
    private Long id;

    @JsonProperty("reference")
    @Schema(description = "Reference", example = "ACT_814610526", type = "string")
    private String reference;

    @JsonProperty("external_reference")
    @Schema(description = "External reference", example = "ACT_814610526", type = "string")
    private String externalReference;

    @JsonProperty("short_name")
    @Schema(description = "Short name", example = "DATA-TRICKS", type = "string")
    private String shortName;

    @JsonProperty("name")
    @Schema(description = "Name", example = "Data Tricks", type = "string")
    private String name;

    @JsonProperty("vat")
    @Schema(description = "VAT", example = "FR12345678901", type = "string")
    private String vat;

    @JsonProperty("registration_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Registration date", example = "2021-07-01", type = "string")
    private Date registrationDate;

    @JsonProperty("company_creation_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Company creation date", example = "2021-07-01", type = "string")
    private Date companyCreationDate;

    @JsonProperty("registration_country")
    @Schema(description = "Registration country", example = "France", type = "string")
    private String registrationCountry;

    @JsonProperty("registerType")
    @Schema(description = "Register type", example = "Register type", type = "string")
    private String registerType;

    @JsonProperty("register_number")
    @Schema(description = "Register number", example = "Register number", type = "string")
    private String registerNumber;

    @JsonProperty("type")
    @Schema(description = "Type", example = "MANAGEMENT_COMPANY", type = "string", allowableValues = {"MANAGEMENT_COMPANY"})
    private String type;

    @JsonProperty("national_identity")
    @Schema(description = "National identity", example = "National identity", type = "string")
    private String nationalIdentity;

    @JsonProperty("feed_channel")
    @Schema(description = "Feed channel", example = "Feed channel", type = "string")
    private String feedChannel;

    @JsonProperty("memo")
    @Schema(description = "Memo", example = "Memo", type = "string")
    private String memo;

    @JsonProperty("tax_reference")
    @Schema(description = "Tax reference", example = "Tax reference", type = "string")
    private String taxReference;

    @JsonProperty("phase_code")
    @Schema(description = "Phase code", example = "INI", type = "string")
    private String phaseCode;

    @JsonProperty("milestone_code")
    @Schema(description = "Milestone code", example = "OUVERT", type = "string")
    private String milestoneCode;

    @JsonProperty("activity_code")
    @Schema(description = "LegalActivities code", example = "01.12Z", type = "string")
    private String activityCode;

    @JsonProperty("legal_category_code")
    @Schema(description = "Legal category code", example = "1", type = "string")
    private String legalCategoryCode;

    @JsonProperty("country_code")
    @Schema(description = "Country code", example = "FR", type = "string")
    private String countryCode;

    @JsonProperty("phase_label")
    @Schema(description = "Phase label", example = "Initial", type = "string")
    private String phaseLabel;

    @JsonProperty("milestone_label")
    @Schema(description = "Milestone label", example = "Nouveau", type = "string")
    private String milestoneLabel;

    @JsonProperty("activity_label")
    @Schema(description = "LegalActivities label", example = "Culture du riz", type = "string")
    private String activityLabel;

    @JsonProperty("legal_category_label")
    @Schema(description = "Legal category label", example = "Artisan-commerçant", type = "string")
    private String legalCategoryLabel;

    @JsonProperty("country_label")
    @Schema(description = "Country label", example = "France", type = "string")
    private String countryLabel;

    @JsonProperty("principal_bank_account_number")
    @Schema(description = "Principal bank account number", example = "8477756491T", type = "string")
    private String principalBankAccountNumber;

    @JsonProperty("principal_bank_account_title")
    @Schema(description = "Principal bank account title", example = "DATA-TRICKS", type = "string")
    private String principalBankAccountTitle;

    @JsonProperty("principal_role_code")
    @Schema(description = "Principal role code", example = "ASSUR", type = "string")
    private String principalRoleCode;

    @JsonProperty("principal_role_label")
    @Schema(description = "Principal role label", example = "Assureur", type = "string")
    private String principalRoleLabel;
}
