package com.datatricks.actors.model.inject;

import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.ws.rs.DefaultValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BankAccountInjectDto {
    @JsonApiId
    private Long id;

    @JsonApiType
    private String myType = "bank_account";

    @JsonProperty("bank_code")
    private String bankCode;

    @JsonProperty("country_code")
    private String country;

    @JsonProperty("international_number")
    private String internationalNumber;

    @JsonProperty("registration_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date registrationDate;

    @JsonProperty("swift_code")
    private String swiftCode;

    @JsonProperty("account_number")
    private String accountNumber;

    @JsonProperty("status")
    private String status;

    @JsonProperty("iban")
    private String iban;

    @JsonProperty("validity")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date validity;

    @JsonProperty("iban_key")
    private String ibanKey;

    @JsonProperty("rib_key")
    private String ribKey;

    @JsonProperty("title")
    private String title;

    @JsonProperty("type")
    private String type;

    @JsonProperty("replacement")
    @DefaultValue("false")
    private Boolean replacement;

    @JsonProperty("rib_replacement")
    private String ribReplacement;

    @JsonProperty("branch_code")
    private String branchCode;

    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date endDate;

    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startDate;

    @JsonProperty("is_principal")
    private Boolean isPrincipal;
}
