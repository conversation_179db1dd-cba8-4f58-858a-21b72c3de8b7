package com.datatricks.actors.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "companies")
public class Company {
    @Id
    @GeneratedValue
    @JsonProperty("id")
    private Long id;

    @Column(name = "name")
    @JsonProperty("name")
    @NotBlank(message = "please provide a company name")
    private String name;

    @Column(name = "type")
    @JsonProperty("type")
    @NotBlank(message = "please provide a company type")
    private String type;

    @Column(name = "code")
    @JsonProperty("code")
    @NotBlank(message = "please provide a company code")
    private String code;
}
