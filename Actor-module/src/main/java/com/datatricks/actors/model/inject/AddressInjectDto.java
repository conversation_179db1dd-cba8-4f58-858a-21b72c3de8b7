package com.datatricks.actors.model.inject;

import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AddressInjectDto {
    @JsonApiId
    private Long id;

    @JsonApiType
    private String myType = "address";

    @JsonProperty("reference")
    private String reference;

    @JsonProperty("city")
    private String city;

    @JsonProperty("country")
    private String country;

    @JsonProperty("zip_code")
    private String zipCode;

    @JsonProperty("entrance_building")
    private String entranceBuilding;

    @JsonProperty("type")
    private String type;

    @JsonProperty("number")
    private String nbr;

    @JsonProperty("distribution")
    private String distribution;

    @JsonProperty("road_extension")
    private String roadExtension;

    @JsonProperty("commune")
    private String commune;

    @JsonProperty("road_type")
    private String roadType;

    @JsonProperty("subsidiary")
    private Boolean subsidiary;

    @JsonProperty("headquarter")
    private Boolean headquarter;

    @JsonProperty("is_billing")
    private Boolean isBilling;

    @JsonProperty("is_delivery")
    private Boolean isDelivery;

    @JsonProperty("summary")
    private String summary;

    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startDate;

    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date endDate;

    @JsonApiRelationships("physical_address")
    @JsonIgnore
    @ManyToOne
    private PhysicalAddressInjectDto physicalAddress;
}
