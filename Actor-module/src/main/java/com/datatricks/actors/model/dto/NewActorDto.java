package com.datatricks.actors.model.dto;

import com.datatricks.actors.model.ActorTypes;
import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NewActorDto implements PageableDto {

    @JsonIgnore
    @JsonProperty("id")
    private Long id;

    @JsonProperty("external_reference")
    @Schema(description = "External reference of the actor", example = "ACT_814610526")
    private String externalReference;

    @JsonProperty("country_code")
    @NotNull(message = "country_code:please provide a country" )
    @Schema(description = "Country code of the actor", example = "FR")
    private String countryCode;

    @JsonProperty("short_name")
    @NotBlank(message = "short_name:please provide a short name")
    @Schema(description = "Short name of the actor", example = "DATA-TRICKS")
    private String shortName;

    @JsonProperty("name")
    @NotBlank(message = "name:please provide a name")
    @Schema(description = "Name of the actor", example = "Data Tricks")
    private String name;

    @JsonProperty("activity_code")
    @NotNull(message = "activity_code:please provide a country")
    @Schema(description = "LegalActivities of the actor", example = "01.21Z")
    private String activityCode;

    @JsonProperty("vat")
    @Schema(description = "VAT of the actor", example = "FR*********01")
    private String vat;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("registration_date")
    @Schema(description = "Registration date of the actor", example = "2021-07-01")
    private LocalDate registrationDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("company_creation_date")
    @Schema(description = "Company creation date of the actor", example = "2021-07-01")
    private LocalDate companyCreationDate;

    @JsonProperty("registration_country")
    @Schema(description = "Registration country of the actor", example = "FR")
    private String registrationCountry;

    @JsonProperty("registerType")
    @Schema(description = "Register type of the actor", example = "SIREN")
    private String registerType;

    @JsonProperty("register_number")
    @Schema(description = "Register number of the actor", example = "*********")
    private String registerNumber;

    @JsonProperty("type")
    @Schema(description = "Type of the actor", example = "NATURAL_PERSON or MANAGEMENT_COMPANY")
    private ActorTypes type;

    @JsonProperty("legal_category_code")
    @NotNull(message = "legal_category_code:please provide a legal category")
    @Schema(description = "Legal category code of the actor", example = "8490")
    private String legalCategoryCode;

    @JsonProperty("national_identity")
    @NotBlank(message = "national_identity:please provide a national identity")
    @Schema(description = "National identity of the actor", example = "*********")
    private String nationalIdentity;

    @JsonProperty("feed_channel")
    @Schema(description = "Feed channel of the actor", example = "1")
    private String feedChannel;

    @JsonProperty("memo")
    @Schema(description = "Memo of the actor", example = "This is a memo")
    private String memo;

    @JsonProperty("tax_reference")
    @Schema(description = "Tax reference of the actor", example = "*********")
    private String taxReference;

    @JsonProperty("phase_code")
    @NotNull(message = "phase_code:please provide a phase")
    @Schema(description = "Phase code of the actor", example = "INI")
    private String phaseCode;

    @JsonProperty("milestone_code")
    @NotNull(message = "milestone_code:please provide a milestone")
    @Schema(description = "Milestone code of the actor", example = "CTEA")
    private String milestoneCode;

    @JsonProperty("role_code")
    @NotNull(message = "role_code:please provide a role for this actor")
    @Schema(description = "Role code of the actor", example = "FCT")
    private String roleCode;
}
