package com.datatricks.actors.model.dto;

import com.datatricks.actors.model.ActorTypes;
import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ActorDto implements PageableDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("reference")
    @Schema(description = "Reference of the actor", example = "ACT_814610526")
    private String reference;

    @JsonProperty("external_reference")
    @Schema(description = "External reference of the actor", example = "ACT_814610526")
    private String externalReference;

    @JsonProperty("country")
    @NotNull(message = "country:please provide a country")
    @Valid
    @Schema(description = "Country of the actor")
    private CountryDto country;

    @JsonProperty("short_name")
    @NotBlank(message = "short_name:please provide a short name")
    @Schema(description = "Short name of the actor", example = "DATA-TRICKS")
    private String shortName;

    @JsonProperty("name")
    @NotBlank(message = "name:please provide a name")
    @Schema(description = "Name of the actor", example = "Data Tricks")
    private String name;

    @JsonProperty("activity")
    @Schema(description = "LegalActivities of the actor")
    private ActivityDto activity;

    @JsonProperty("vat")
    @Schema(description = "VAT of the actor", example = "FR*********01")
    private String vat;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("registration_date")
    @Schema(description = "Registration date of the actor", example = "2021-07-01")
    private Date registrationDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("company_creation_date")
    @Schema(description = "Company creation date of the actor", example = "2021-07-01")
    private Date companyCreationDate;

    @JsonProperty("registration_country")
    @Schema(description = "Registration country of the actor", example = "FR")
    private String registrationCountry;

    @JsonProperty("registerType")
    @Schema(description = "Register type of the actor", example = "SIREN")
    private String registerType;

    @JsonProperty("register_number")
    @Schema(description = "Register number of the actor", example = "*********")
    private String registerNumber;

    @JsonProperty("type")
    @Schema(description = "Type of the actor", example = "NATURAL_PERSON or MANAGEMENT_COMPANY")
    private ActorTypes type;

    @JsonProperty("legal_category")
    @NotNull(message = "legal_category:please provide a legal category")
    @Valid
    @Schema(description = "Legal category of the actor")
    private LegalCategoryDto legalCategory;

    @JsonProperty("national_identity")
    @NotBlank(message = "national_identity:please provide a national identity")
    @Schema(description = "National identity of the actor", example = "*********")
    private String nationalIdentity;

    @JsonProperty("feed_channel")
    @Schema(description = "Feed channel of the actor", example = "1")
    private String feedChannel;

    @JsonProperty("memo")
    @Schema(description = "Memo of the actor", example = "This is a memo")
    private String memo;

    @JsonProperty("tax_reference")
    @Schema(description = "Tax reference of the actor", example = "*********")
    private String taxReference;

    @JsonProperty("phase")
    @NotNull(message = "phase:please provide a phase")
    @Valid
    @Schema(description = "Phase of the actor")
    private PhaseDto phase;

    @JsonProperty("milestone")
    @NotNull(message = "milestone:please provide a milestone")
    @Valid
    @Schema(description = "Milestone of the actor")
    private MilestoneDto milestone;

    @JsonProperty("role")
    @NotNull(message = "role:please provide a role for this actor")
    @Valid
    @Schema(description = "Role of the actor")
    private RoleDto role;

    @JsonProperty("bank_accounts")
    @Schema(description = "Bank accounts of the actor")
    private List<BankAccountDto> bankAccounts;
}
