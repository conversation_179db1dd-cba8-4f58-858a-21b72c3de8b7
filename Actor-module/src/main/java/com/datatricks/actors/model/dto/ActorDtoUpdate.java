package com.datatricks.actors.model.dto;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class ActorDtoUpdate {

    @Schema(description = "Reference of the actor", example = "ACT_814610526")
    private String reference;
    @Schema(description = "External reference of the actor", example = "31")
    private Long phaseId;
    @Schema(description = "External reference of the actor", example = "31")
    private Long milestoneId;
    private Date createdAt;
}
