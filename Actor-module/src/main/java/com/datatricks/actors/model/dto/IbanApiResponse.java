package com.datatricks.actors.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
public class IbanApiResponse {
    private Integer result;
    private String message;
    private List<Validation> validations;

    @JsonProperty("expremental")
    private Integer expremental;

    private Data data;

    @Setter
    @Getter
    @NoArgsConstructor
    public static class Validation {
        private Integer result;
        private String message;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    public static class Data {
        @JsonProperty("country_code")
        private String countryCode;

        @JsonProperty("iso_alpha3")
        private String isoAlpha3;

        @JsonProperty("country_name")
        private String countryName;

        @JsonProperty("currency_code")
        private String currencyCode;

        @JsonProperty("sepa_member")
        private String sepaMember;

        private Sepa sepa;
        private String bban;

        @JsonProperty("bank_account")
        private String bankAccount;

        private Bank bank;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    public static class Sepa {
        @JsonProperty("sepa_credit_transfer")
        private String sepaCreditTransfer;

        @JsonProperty("sepa_credit_transfer_inst")
        private String sepaCreditTransferInst;

        @JsonProperty("sepa_direct_debit")
        private String sepaDirectDebit;

        @JsonProperty("sepa_sdd_core")
        private String sepaSddCore;

        @JsonProperty("sepa_b2b")
        private String sepaB2b;

        @JsonProperty("sepa_card_clearing")
        private String sepaCardClearing;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    public static class Bank {
        @JsonProperty("bank_name")
        private String bankName;

        private String phone;
        private String address;
        private String bic;
        private String city;
        private String state;
        private String zip;
    }
}
