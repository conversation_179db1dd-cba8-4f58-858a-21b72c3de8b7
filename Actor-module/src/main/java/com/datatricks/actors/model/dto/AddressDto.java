package com.datatricks.actors.model.dto;

import com.datatricks.actors.model.Address;
import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AddressDto implements PageableDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("reference")
    @Schema(description = "Reference of the address", example = "ADDR_ee2bb21fee4b", requiredMode = Schema.RequiredMode.REQUIRED)
    private String reference;

    @JsonProperty("city")
    @NotBlank(message = "city:please provide a city")
    @Schema(description = "City of the address", example = "Paris")
    private String city;

    @JsonProperty("country")
    @Schema(description = "Country of the address", example = "France")
    private String country;

    @JsonProperty("zip_code")
    @NotBlank(message = "zip_code:please provide a zip_code")
    @Schema(description = "Zip code of the address", example = "75001")
    private String zipCode;

    @JsonProperty("entrance_building")
    @Schema(description = "Entrance building of the address", example = "A")
    private String entranceBuilding;

    @Schema(description = "Type of the address", example = "street")
    private String type;

    @JsonProperty("number")
    @NotNull(message = "number:please provide a number")
    @Schema(description = "Number of the address", example = "1")
    private String nbr;

    @JsonProperty("distribution")
    @Schema(description = "Distribution of the address", example = "Rue")
    private String distribution;

    @JsonProperty("road_extension")
    @Schema(description = "Road extension of the address", example = "Bis")
    private String roadExtension;

    @JsonProperty("commune")
    @Schema(description = "Commune of the address", example = "Paris")
    private String commune;

    @JsonProperty("road_type")
    @Schema(description = "Road type of the address", example = "Rue")
    private String roadType;

    @JsonProperty("subsidiary")
    @NotNull(message = "subsidiary:please provide an subsidiary")
    @Schema(description = "Is subsidiary", example = "true", type = "boolean")
    private Boolean subsidiary;

    @JsonProperty("headquarter")
    @NotNull(message = "headquarter: please provide a headquarter")
    @Schema(description = "Is headquarter", example = "true", type = "boolean")
    private Boolean headquarter;
    
    @JsonProperty("is_billing")
    @NotNull(message = "is_billing:please provide is_billing field")
    @Schema(description = "Is billing", example = "true", type = "boolean")
    private Boolean isBilling;

    @JsonProperty("is_delivery")
    @NotNull(message = "is_delivery:please provide is_delivery field")
    @Schema(description = "Is delivery", example = "true", type = "boolean")
    private Boolean isDelivery;

    @JsonProperty("summary")
    @Schema(description = "Summary of the address", example = "This is a summary")
    private String summary;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start date of the address", example = "2021-07-01")
    private Date startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End date of the address", example = "2021-07-02")
    private Date endDate;

    @JsonProperty("physical_address")
    @Valid
    @Schema(description = "Physical address of the address")
    private PhysicalAddressDto physicalAddress;

    public AddressDto(Address address) {
        this.id = address.getId();
        this.reference = address.getReference();
        this.city = address.getCity();
        this.country = address.getCountry();
        this.zipCode = address.getZipCode();
        this.entranceBuilding = address.getEntranceBuilding();
        this.type = address.getType();
        this.nbr = address.getNbr();
        this.distribution = address.getDistribution();
        this.roadExtension = address.getRoadExtension();
        this.commune = address.getCommune();
        this.roadType = address.getRoadType();
        this.subsidiary = address.getSubsidiary();
        this.headquarter = address.getHeadquarter();
        this.isBilling = address.getIsBilling();
        this.isDelivery = address.getIsDelivery();
        this.summary = address.getSummary();
        this.startDate = address.getStartDate();
        this.endDate = address.getEndDate();
        this.physicalAddress = new PhysicalAddressDto(address.getPhysicalAddress());
    }
}
