package com.datatricks.actors.model.dto;

import com.datatricks.actors.model.ActorTypes;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SimplifiedActorDto implements PageableDto{
    @JsonProperty("id")
    private Long id;

    @JsonProperty("reference")
    private String reference;

    @JsonProperty("external_reference")
    private String externalReference;

    @JsonProperty("country")
    private CountryDto country;

    @JsonProperty("short_name")
    @NotBlank(message = "short_name:please provide a short name")
    private String shortName;

    @JsonProperty("name")
    @NotBlank(message = "name:please provide a name")
    private String name;

    @JsonProperty("type")
    @Schema(description = "Type of the actor", example = "NATURAL_PERSON or MANAGEMENT_COMPANY")
    private ActorTypes type;
}
