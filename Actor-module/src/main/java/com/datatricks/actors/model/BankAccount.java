package com.datatricks.actors.model;

import com.datatricks.actors.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.DefaultValue;
import lombok.*;

import java.util.Date;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "bank_accounts")
public class BankAccount extends BaseEntity {

    @Id
    @GeneratedValue
    @JsonProperty("id")
    private Long id;

    @Column(name = "bank_code")
    @JsonProperty("bank_code")
    @NotBlank(message = "bank_code:please provide a bank code")
    private String bankCode;

    @Column(name = "country")
    @JsonProperty("country")
    @NotBlank(message = "country:please provide a country")
    private String country;

    @Column(name = "international_number")
    @JsonProperty("international_number")
    private String internationalNumber;

    @Column(name = "registration_date")
    @JsonProperty("registration_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date registrationDate;

    @Column(name = "swift_code")
    @JsonProperty("swift_code")
    // @NotBlank(message = "swift_code:please provide a swift code")
    private String swiftCode;

    @Column(name = "account_number")
    @JsonProperty("account_number")
    @NotBlank(message = "account_number:please provide an account number")
    private String accountNumber;

    @Column(name = "status")
    @JsonProperty("status")
    // @NotBlank(message = "status:please provide a status")
    private String status;

    @Column(name = "iban")
    @JsonProperty("iban")
    @NotBlank(message = "iban:please provide an iban")
    private String iban;

    @Column(name = "validity")
    @JsonProperty("validity")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    // @NotBlank(message = "validity:please provide a validity")
    private Date validity;

    @Column(name = "iban_key")
    @JsonProperty("iban_key")
    @NotBlank(message = "iban_key:please provide an iban key")
    private String ibanKey;

    @Column(name = "rib_key")
    @JsonProperty("rib_key")
    @NotBlank(message = "rib_key:please provide a rib_key")
    private String ribKey;

    @Column(name = "title")
    @JsonProperty("title")
    private String title;

    @Column(name = "type")
    @JsonProperty("type")
    private String type;

    @Column(name = "replacement")
    @JsonProperty("replacement")
    @NotNull(message = "replacement:please provide a replacement")
    @DefaultValue("false")
    private Boolean replacement;

    @Column(name = "rib_replacement")
    @JsonProperty("rib_replacement")
    private String ribReplacement;

    @Column(name = "branch_code")
    @JsonProperty("branch_code")
    private String branchCode;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date endDate;
    // @NotNull(message = "start_date:please provide a start date for this account")
    @Column(name = "start_date")
    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startDate;

	@Column(name = "is_principal")
	@JsonProperty("is_principal")
	private Boolean isPrincipal;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actorId")
    private Actor actorId;

    public BankAccount(Long id) {
        this.id = id;
    }
}
