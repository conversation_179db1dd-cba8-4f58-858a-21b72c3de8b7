package com.datatricks.actors.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(description = "LegalActivities data transfer object")
public class ActivityDto implements PageableDto {
    @Schema(description = "LegalActivities identifier", example = "6")
    private Long id;
    @Schema(description = "LegalActivities code", example = "01.16Z")
    private String code;
    @Schema(description = "LegalActivities label", example = "Culture de plantes à fibres")
    private String label;
    private String associated_to;
}
