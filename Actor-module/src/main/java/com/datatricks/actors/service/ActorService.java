package com.datatricks.actors.service;

import com.datatricks.actors.exception.ConflictException;
import com.datatricks.actors.exception.ResourcesNotFoundException;
import com.datatricks.actors.exception.handler.InformativeMessage;
import com.datatricks.actors.model.*;
import com.datatricks.actors.model.dto.*;
import com.datatricks.actors.producer.ActorKafkaProducer;
import com.datatricks.actors.repository.*;
import com.datatricks.actors.utils.GenericJsonApiModelAssembler;
import com.datatricks.actors.utils.JpaQueryFilters;
import com.datatricks.actors.utils.PatchHelper;
import com.datatricks.actors.utils.TransactionSynchronizationUtil;
import com.datatricks.kafkacommondomain.enums.OperationType;
import com.toedter.spring.hateoas.jsonapi.JsonApiModelBuilder;
import jakarta.validation.ValidationException;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.hateoas.PagedModel;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.datatricks.actors.utils.ActorsUtils.createReference;

@Service
public class ActorService {

    private final PhaseRepository phaseRepository;
    private final MilestoneRepository milestoneRepository;
    private static final String MODULE = "ACTOR";
    private static final String ACTOR_PREFIX = "ACT_";
    private static final String LEGAL_CATEGORY_NOT_FOUND = "Legal category not found";
    private static final String ACTIVITY_NOT_FOUND = "LegalActivities not found";
    private static final String NO_PHASE_FOUND_FOR_CODE = "No phase found for code ";
    private static final String COUNTRY_NOT_FOUND = "Country not found";
    private static final String ROLE_ASSOCIATED_TO_COMPANY = "COMPANY";
    private static final String ROLE_ASSOCIATED_TO_ACTEUR = "ACTEUR";
    private static final String CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_COMPANY = " Role is not associated to a company";
    private static final String CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_ACTOR = " Role is not associated to an actor";
    private final ActorRepository actorRepository;
    private final ActorPhaseRepository actorPhaseRepository;
    private final ActorRoleRepository actorRoleRepository;
    private final BusinessRepository businessSummaryRepository;
    private final PartiesRepository partiesSummaryRepository;
    private final ModelMapper modelMapper;
    private final ActorKafkaProducer actorKafkaProducer;
    private final TransactionSynchronizationUtil transactionSynchronizationUtil;
    private final RoleRepository roleRepository;
    private final LegalActivityRepository legalActivityRepository;
    private final LegalCategoryRepository legalCategoryRepository;
    private final CountryRepository countryRepository;
    private final PatchHelper<PatchActorDto> patchHelper;
    private final GenericJsonApiModelAssembler genericAssembler;
    private final InclusionService inclusionService;
    private static final String ACTEUR = "ACTEUR";
    private static final String CODE = "CODE";
    private static final String COUNTRY = "COUNTRY";
    private static final String PHASE = "PHASE";
    private static final String ROLE = "ROLE";
    private static final String LEGAL_CATEGORY = "LEGAL_CATEGORY";
    private static final String ACTIVITY = "ACTIVITY";
    private static final String MILESTONE = "MILESTONE";
    private static final String RESOURCE_DELETED_MESSAGE = "Resource with ID %d has been deleted successfully";

    public ActorService(
            PhaseRepository phaseRepository,
            MilestoneRepository milestoneRepository,
            ActorRepository actorRepository,
            ActorPhaseRepository actorPhaseRepository,
            ActorRoleRepository actorRoleRepository,
            BusinessRepository businessSummaryRepository,
            PartiesRepository partiesSummaryRepository,
            ModelMapper modelMapper,
            ActorKafkaProducer actorKafkaProducer,
            TransactionSynchronizationUtil transactionSynchronizationUtil,
            RoleRepository roleRepository,
            LegalActivityRepository legalActivityRepository,
            LegalCategoryRepository legalCategoryRepository,
            CountryRepository countryRepository,
            PatchHelper<PatchActorDto> patchHelper,
            GenericJsonApiModelAssembler genericAssembler,
            InclusionService inclusionService) {
        this.phaseRepository = phaseRepository;
        this.milestoneRepository = milestoneRepository;
        this.actorRepository = actorRepository;
        this.actorPhaseRepository = actorPhaseRepository;
        this.actorRoleRepository = actorRoleRepository;
        this.businessSummaryRepository = businessSummaryRepository;
        this.partiesSummaryRepository = partiesSummaryRepository;
        this.modelMapper = modelMapper;
        this.actorKafkaProducer = actorKafkaProducer;
        this.transactionSynchronizationUtil = transactionSynchronizationUtil;
        this.roleRepository = roleRepository;
        this.legalActivityRepository = legalActivityRepository;
        this.legalCategoryRepository = legalCategoryRepository;
        this.countryRepository = countryRepository;
        this.patchHelper = patchHelper;
        this.genericAssembler = genericAssembler;
        this.inclusionService = inclusionService;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ActorDto>> getActorById(Long id) {
        Actor actor = actorRepository.findByIdAndDeletedAtIsNull(id).orElse(null);
        if (actor != null) {
            ActorDto actorDto = this.modelMapper.map(actor, ActorDto.class);
            ActorRole actorRole = actorRoleRepository.findByActorIdIdAndIsPrincipalIsTrueAndDeletedAtIsNull(id);
            if (actorRole != null) {
                actorDto.setRole(this.modelMapper.map(actorRole.getRole(), RoleDto.class));
            }
            return ResponseEntity.ok(SingleResultDto.<ActorDto>builder().data(actorDto).build());
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ActorDto>> createActor(NewActorDto newActorDto) {
        Actor actor = new Actor(newActorDto);
        actor.setName(newActorDto.getName());
        actor.setCreatedAt(new Date());
        actor.setModifiedAt(new Date());
        actor.setReference(ACTOR_PREFIX + createReference());

        actor.setCountry(countryRepository.findByCode(newActorDto.getCountryCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Country not found", "COUNTRY", MODULE)));
        actor.setMilestone(milestoneRepository.findByCode("OUVERT")
                .orElseThrow(() -> new ResourcesNotFoundException("Milestone not found", MILESTONE, MODULE)));
        actor.setPhase(phaseRepository.findByCodeAndAssociatedTo(newActorDto.getPhaseCode(), ACTEUR).orElseThrow(
                () -> new ResourcesNotFoundException(NO_PHASE_FOUND_FOR_CODE + newActorDto.getPhaseCode(), PHASE, MODULE)));
        actor.setActivity(legalActivityRepository.findByCode(newActorDto.getActivityCode())
                .orElseThrow(() -> new ResourcesNotFoundException("LegalActivities not found", "ACTIVITY", MODULE)));
        actor.setLegalCategory(legalCategoryRepository.findByCode(newActorDto.getLegalCategoryCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Legal category not found", "LEGAL_CATEGORY", MODULE)));

        // Set the principal role
        Role role = roleRepository.findByCode(newActorDto.getRoleCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Role not found", "ROLE", MODULE));
        actor.setActorRole(saveNewActorRole(actor, role));

        actor.setAddresses(new HashSet<>());
        actor.setBankAccounts(new HashSet<>());
        actor.setContractActor(new HashSet<>());
        Actor createdActor = actorRepository.save(actor);
        // Update the Actors phase history
        saveNewActorPhase(actor);

        // Get the principal saved role
        ActorDto createdActorDto = modelMapper.map(createdActor, ActorDto.class);
        createdActorDto.setRole(this.modelMapper.map(role, RoleDto.class));

        // Send the actor to the Kafka topic
        transactionSynchronizationUtil.executeAfterCommit(() -> actorKafkaProducer.sendActorMessage(createdActor, OperationType.POST, MODULE));

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<ActorDto>builder().data(createdActorDto).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ActorDto>> updateActor(Long id, NewActorDto newActorDto) {
        Actor actor = actorRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException("Actor not found", "ACTOR", MODULE));

        Actor newActor = new Actor(newActorDto);

        LegalActivities activity = legalActivityRepository.findByCode(newActorDto.getActivityCode()).orElseThrow(
                () -> new ResourcesNotFoundException("LegalActivities not found", "ACTIVITY", MODULE));
        newActor.setActivity(activity);

        LegalCategory legalCategory = legalCategoryRepository.findByCode(newActorDto.getLegalCategoryCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Legal category not found", "LEGAL_CATEGORY", MODULE));
        newActor.setLegalCategory(legalCategory);

        Country country = countryRepository.findByCode(newActorDto.getCountryCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Country not found", "COUNTRY", MODULE));
        newActor.setCountry(country);

        Phase phase = phaseRepository.findByCodeAndAssociatedTo(newActorDto.getPhaseCode(), "ACTEUR").stream().findFirst().orElseThrow(
                () -> new ResourcesNotFoundException("No phase found for code " + newActorDto.getPhaseCode(), "PHASE", MODULE));
        newActor.setPhase(phase);

        Milestone milestone = milestoneRepository.findByCode("OUVERT").stream().findFirst().orElseThrow(
                () -> new ResourcesNotFoundException("No milestone found for code OUVERT", "MILESTONE_OUVERT", MODULE));
        newActor.setMilestone(milestone);

        newActor.setId(id);
        newActor.setReference(actor.getReference());
        newActor.setName(actor.getName());
        newActor.setCreatedAt(actor.getCreatedAt());
        newActor.setModifiedAt(new Date());
        newActor.setActorRole(actor.getActorRole());
        newActor.setBankAccounts(new HashSet<>());
        newActor.setAddresses(new HashSet<>());
        actorRepository.save(newActor);
        // Send the actor to the Kafka topic
        transactionSynchronizationUtil.executeAfterCommit(() -> actorKafkaProducer.sendActorMessage(newActor, OperationType.PUT, MODULE));

        ActorDto updatedActorDto = modelMapper.map(newActor, ActorDto.class);
        ActorRole actorRole = newActor.getActorRole().stream().filter(ActorRole::getIsPrincipal).findFirst().orElse(null);
        updatedActorDto.setRole(this.modelMapper.map(Objects.requireNonNull(actorRole).getRole(), RoleDto.class));

        return ResponseEntity.ok(SingleResultDto.<ActorDto>builder().data(updatedActorDto).build());
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteActorById(Long id) {
        Optional<Actor> actor = actorRepository.findByIdAndDeletedAtIsNull(id);
        if (actor.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
        Actor actorToDelete = actor.get();
        actorToDelete.setDeletedAt(new Date());
        actorRepository.save(actorToDelete);

        // Send the actor to the Kafka topic
        transactionSynchronizationUtil.executeAfterCommit(() -> actorKafkaProducer.sendActorMessage(actorToDelete, OperationType.DELETE, MODULE));

        return ResponseEntity.ok(new InformativeMessage(String.format(RESOURCE_DELETED_MESSAGE, id)));
    }

    private void saveNewActorPhase(Actor actor) {
        ActorPhase actorPhase = new ActorPhase();
        if (actor.getPhase() != null && actor.getPhase().getCode() != null) {
            actorPhase.setPhase(actor.getPhase().getCode());
        }

        if (actor.getMilestone() != null && actor.getMilestone().getCode() != null) {
            actorPhase.setMilestone(actor.getMilestone().getCode());
        }
        actorPhase.setCreatedAt(new Date());
        actorPhase.setActorId(actor.getId());
        actorPhaseRepository.save(actorPhase);
    }

    @Transactional
    public ResponseEntity<PageDto<ActorDto>> getActors(Map<String, String> params) {
        JpaQueryFilters<Actor> filters = new JpaQueryFilters<>(params, Actor.class);
        Page<Actor> page = actorRepository.findAll(filters.getSpecification(), filters.getPageable());
        List<ActorDto> filteredActors = page.stream()
                .map(actor -> this.modelMapper.map(actor, ActorDto.class))
                .toList();
        for (ActorDto pageableDto : filteredActors) {
            ActorRole actorRole = actorRoleRepository.findByActorIdIdAndIsPrincipalIsTrueAndDeletedAtIsNull(
                    pageableDto.getId());
            if (actorRole != null) {
                pageableDto.setRole(this.modelMapper.map(actorRole.getRole(), RoleDto.class));
            }
        }
        return ResponseEntity.ok(PageDto.<ActorDto>builder()
                .data(filteredActors)
                .total(page.getTotalElements())
                .build());
    }

    @Transactional
    public ResponseEntity<PageDto<BillingParametersEmailDto>> getActorEmails(String actorReference) {
        Actor actor = actorRepository.findByReferenceAndDeletedAtIsNull(actorReference)
                .orElseThrow(() -> new ResourcesNotFoundException("Actor not found", MODULE, MODULE));
        List<BillingParametersEmailDto> emails = new ArrayList<>();
        for (Contact contact : actor.getContacts()) {
            contact.getCommunicationMeans().stream()
                    .filter(communicationMean -> communicationMean.getType().equals(CommunicationMeanTypes.EMAIL))
                    .forEach(communicationMean -> emails.add(new BillingParametersEmailDto(contact, communicationMean)));
        }
        return ResponseEntity.ok(PageDto.<BillingParametersEmailDto>builder()
                .data(emails)
                .total(emails.size())
                .build());
    }

    @Transactional
    public ResponseEntity<PageDto<BusinessSummaryDto>> getBusinesses(Map<String, String> params) {
        JpaQueryFilters<BusinessSummary> filters = new JpaQueryFilters<>(params, BusinessSummary.class);
        Page<BusinessSummary> page = businessSummaryRepository.findAll(filters.getSpecification(), filters.getPageable());
        List<BusinessSummaryDto> filteredBusiness = page.stream()
                .map(business -> this.modelMapper.map(business, BusinessSummaryDto.class))
                .toList();

        return ResponseEntity.ok(PageDto.<BusinessSummaryDto>builder()
                .data(filteredBusiness)
                .total(page.getTotalElements())
                .build());
    }

    @Transactional
    public ResponseEntity<PageDto<PartiesSummaryDto>> getParties(Map<String, String> params) {
        JpaQueryFilters<PartySummary> filters = new JpaQueryFilters<>(params, PartySummary.class);
        Page<PartySummary> page = partiesSummaryRepository.findAll(filters.getSpecification(), filters.getPageable());
        List<PartiesSummaryDto> filteredBusiness = page.stream()
                .map(business -> this.modelMapper.map(business, PartiesSummaryDto.class))
                .toList();

        return ResponseEntity.ok(PageDto.<PartiesSummaryDto>builder()
                .data(filteredBusiness)
                .total(page.getTotalElements())
                .build());
    }

    @Transactional
    public ResponseEntity<PatchResponseDto> patchActor(Long id, PatchDto<PatchActorDto> patchDto) {
        Actor actor = actorRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResourcesNotFoundException("Actor not found", MODULE, MODULE));
        try {
            PatchResult<PatchActorDto> patchResult = patchHelper
                    .applyPatch(
                            new PatchActorDto(actor),
                            patchDto.getData().getAttributes().getValidationGroups(),
                            patchDto.getData().getAttributes());
            Actor updatedActor = new Actor(patchResult.getUpdated());
            updatedActor.setId(id);
            updatedActor.setName(actor.getName());
            updatedActor.setModifiedAt(new Date());
            updatedActor.setReference(actor.getReference());
            updatedActor.setCreatedAt(actor.getCreatedAt());

            updatedActor.setCountry(countryRepository.findByCode(patchResult.getUpdated().getCountryCode())
                    .orElseThrow(() -> new ResourcesNotFoundException(COUNTRY_NOT_FOUND, COUNTRY, MODULE)));

            Phase phase = phaseRepository.findByCodeAndAssociatedTo(patchResult.getUpdated().getPhaseCode(), ACTEUR)
                    .orElseThrow(() -> new ResourcesNotFoundException(NO_PHASE_FOUND_FOR_CODE + patchDto.getData().getAttributes().getPhaseCode(), PHASE, MODULE));
            updatedActor.setPhase(phase);

            LegalActivities activity = legalActivityRepository.findByCode(patchResult.getUpdated().getActivityCode())
                    .orElseThrow(() -> new ResourcesNotFoundException(ACTIVITY_NOT_FOUND, ACTIVITY, MODULE));
            updatedActor.setActivity(activity);


            LegalCategory legalCategory = legalCategoryRepository.findByCode(patchResult.getUpdated().getLegalCategoryCode())
                    .orElseThrow(() -> new ResourcesNotFoundException(LEGAL_CATEGORY_NOT_FOUND, LEGAL_CATEGORY, MODULE));
            updatedActor.setLegalCategory(legalCategory);

            Milestone milestone = milestoneRepository.findByCode(patchResult.getUpdated().getMilestoneCode())
                    .orElseThrow(() -> new ResourcesNotFoundException("Milestone not found", MILESTONE, MODULE));
            updatedActor.setMilestone(milestone);

            updatedActor.setAddresses(actor.getAddresses());
            updatedActor.setBankAccounts(actor.getBankAccounts());
            updatedActor.setContractActor(actor.getContractActor());
            updatedActor.setContacts(actor.getContacts());
            updatedActor.setActorRole(actor.getActorRole());
            Actor savedActor = actorRepository.save(updatedActor);
            transactionSynchronizationUtil.executeAfterCommit(() -> actorKafkaProducer.sendActorMessage(savedActor, OperationType.PUT, MODULE));
            patchResult.getResponse().getData().put("id", savedActor.getId());
            return ResponseEntity.status(HttpStatus.OK).body(PatchResponseDto.builder().data(patchResult.getResponse().getData()).build());
        } catch (IllegalAccessException e) {
            throw new ValidationException("Error applying patch: " + e.getMessage());
        }
    }

    @Transactional
    protected Set<ActorRole> saveNewActorRole(Actor actor, Role role) {
        if (!Objects.equals(actor.getActorRole(), null)) {
            actor.getActorRole().clear();
        } else {
            actor.setActorRole(new HashSet<>());
        }
        if (actor.getType() == ActorTypes.MANAGEMENT_COMPANY) {
            if (role.getStaticRole().getAssociatedTo().contains(ROLE_ASSOCIATED_TO_COMPANY)) {
                actor.getActorRole().add(new ActorRole(actor, role, true));
            } else {
                throw new ConflictException(
                        CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_COMPANY,
                        "CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_COMPANY",
                        CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_COMPANY,
                        MODULE);
            }
        } else {
            if (role.getStaticRole().getAssociatedTo().contains(ROLE_ASSOCIATED_TO_ACTEUR)) {
                actor.getActorRole().add(new ActorRole(actor, role, true));
            } else {
                throw new ConflictException(
                        CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_ACTOR,
                        "CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_ACTOR",
                        CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_ACTOR,
                        MODULE);
            }
        }
        return actor.getActorRole();
    }

    @Transactional
    public ResponseEntity<RepresentationModel<?>> findAllActors(String[] include,
                                                                Map<String, String> params,
                                                                String[] fieldsMovies) {
        JpaQueryFilters<Actor> filters = new JpaQueryFilters<>(params, Actor.class);
        Page<Actor> page = actorRepository.findAll(filters.getSpecification(), filters.getPageable());
        List<? extends RepresentationModel<?>> actorResources = page.stream()
                .map(actor -> genericAssembler.toJsonApiModel(actor, fieldsMovies))
                .toList();

        PagedModel.PageMetadata pageMetadata = new PagedModel.PageMetadata(
                page.getSize(),
                page.getNumber(),
                page.getTotalElements(),
                page.getTotalPages()
        );

        PagedModel<? extends RepresentationModel<?>> pagedModel = PagedModel.of(actorResources, pageMetadata);

        String pageLinksBase = "http://localhost:8080/api/actors"; // Adjust as needed

        JsonApiModelBuilder jsonApiModelBuilder = JsonApiModelBuilder.jsonApiModel()
                .model(pagedModel);

        inclusionService.processIncludes(page.getContent(), include, jsonApiModelBuilder);
        RepresentationModel<?> pagedJsonApiModel = jsonApiModelBuilder.build();
        return ResponseEntity.ok(pagedJsonApiModel);
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ActorDto>> getActorByReference(String reference) {
        Actor actor = actorRepository.findByReferenceAndDeletedAtIsNull(reference).orElse(null);
        if (actor != null) {
            ActorDto actorDto = this.modelMapper.map(actor, ActorDto.class);
            ActorRole actorRole = actorRoleRepository.findByActorIdIdAndIsPrincipalIsTrueAndDeletedAtIsNull(actor.getId());
            if (actorRole != null) {
                actorDto.setRole(this.modelMapper.map(actorRole.getRole(), RoleDto.class));
            }
            return ResponseEntity.ok(SingleResultDto.<ActorDto>builder().data(actorDto).build());
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }
}
