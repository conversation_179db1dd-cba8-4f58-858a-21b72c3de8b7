package com.datatricks.actors.service;

import com.datatricks.actors.exception.ResourcesNotFoundException;
import com.datatricks.actors.exception.TechnicalException;
import com.datatricks.actors.model.*;
import com.datatricks.actors.repository.*;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class StaticTablesService {
    private static final String MODEL_NAME = "StaticTables";
    private static final String ERRORMESSAGE = "Error while processing message : ";
    private final MilestoneRepository milestoneRepository;
    private final PhaseRepository phaseRepository;
    private final LegalCategoryRepository legalCategoryRepository;
    private final DelegationRepository delegationRepository;
    private final CurrencyRepository currencyRepository;
    private final CountryRepository countryRepository;
    private final PaymentMethodRepository paymentMethodRepository;
    private final ActivityRepository activityRepository;
    private final ProductRepository productRepository;
    private final RoleRepository roleRepository;
    private final LineTypeRepository lineTypeRepository;
    private final OperationRepository operationRepository;
    private final NatureRepository natureRepository;
    private final OperationNatureMappingRepository operationNatureMappingRepository;
    private final OperationNatureTypeMappingRepository operationNatureTypeMappingRepository;
    private final ProductLineTypeMappingRepository productLineTypeMappingRepository;
    private final StaticRoleRepository staticRoleRepository;
    private final BankBranchRepository bankBranchRepository;

    public StaticTablesService(MilestoneRepository milestoneRepository, PhaseRepository phaseRepository, LegalCategoryRepository legalCategoryRepository, DelegationRepository delegationRepository, CurrencyRepository currencyRepository, CountryRepository countryRepository, PaymentMethodRepository paymentMethodRepository, ActivityRepository activityRepository, ProductRepository productRepository, RoleRepository roleRepository, LineTypeRepository lineTypeRepository, OperationRepository operationRepository, NatureRepository natureRepository, OperationNatureMappingRepository operationNatureMappingRepository, OperationNatureTypeMappingRepository operationNatureTypeMappingRepository, ProductLineTypeMappingRepository productLineTypeMappingRepository, StaticRoleRepository staticRoleRepository, BankBranchRepository bankBranchRepository) {
        this.milestoneRepository = milestoneRepository;
        this.phaseRepository = phaseRepository;
        this.legalCategoryRepository = legalCategoryRepository;
        this.delegationRepository = delegationRepository;
        this.currencyRepository = currencyRepository;
        this.countryRepository = countryRepository;
        this.paymentMethodRepository = paymentMethodRepository;
        this.activityRepository = activityRepository;
        this.productRepository = productRepository;
        this.roleRepository = roleRepository;
        this.lineTypeRepository = lineTypeRepository;
        this.operationRepository = operationRepository;
        this.natureRepository = natureRepository;
        this.operationNatureMappingRepository = operationNatureMappingRepository;
        this.operationNatureTypeMappingRepository = operationNatureTypeMappingRepository;
        this.productLineTypeMappingRepository = productLineTypeMappingRepository;
        this.staticRoleRepository = staticRoleRepository;
        this.bankBranchRepository = bankBranchRepository;
    }

    @Transactional
    public void createNewMilestone(Milestone milestone, Acknowledgment acknowledgment) {
        try {
            milestoneRepository.save(milestone);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewPhase(Phase phase, Acknowledgment acknowledgment) {
        try {
            phaseRepository.save(phase);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewLegalCategory(LegalCategory legalCategory, Acknowledgment acknowledgment) {
        try {
            legalCategoryRepository.save(legalCategory);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewDelegation(Delegation delegation, Acknowledgment acknowledgment) {
        try {
            delegationRepository.save(delegation);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewCurrency(Currency currency, Acknowledgment acknowledgment) {
        try {
            currencyRepository.save(currency);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewCountry(Country country, Acknowledgment acknowledgment) {
        try {
            countryRepository.save(country);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewPaymentMethod(PaymentMethod paymentMethod, Acknowledgment acknowledgment) {
        try {
            paymentMethodRepository.save(paymentMethod);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewActivity(Activity activity, Acknowledgment acknowledgment) {
        try {
            activityRepository.save(activity);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewRole(Role role, Acknowledgment acknowledgment) {
        try {
            staticRoleRepository.findByCode(role.getStaticRole().getCode()).ifPresentOrElse(
                    r -> {
                        role.setStaticRole(r);
                        roleRepository.save(role);
                    },
                    () -> {
                        throw new ResourcesNotFoundException("AssociatedTo not found", "Role", role.getStaticRole().getCode());
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateMilestone(Milestone milestone, Acknowledgment acknowledgment) {
        try {
            milestoneRepository.findByCode(milestone.getCode()).ifPresentOrElse(
                    m -> {
                        m.setLabel(milestone.getLabel());
                        m.setCode(milestone.getCode());
                        m.setPhaseId(milestone.getPhaseId() != null ? milestone.getPhaseId() : m.getPhaseId());
                        m.setActive(milestone.getActive());
                        milestoneRepository.save(m);
                    },
                    () -> {
                        milestoneRepository.save(milestone);
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updatePhase(Phase phase, Acknowledgment acknowledgment) {
        try {
            phaseRepository.findByCode(phase.getCode()).ifPresentOrElse(
                    p -> {
                        p.setLabel(phase.getLabel());
                        p.setAssociatedTo(phase.getAssociatedTo());
                        p.setLanguage(phase.getLanguage());
                        phaseRepository.save(p);
                    },
                    () -> {
                        phaseRepository.save(phase);
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateLegalCategory(LegalCategory legalCategory, Acknowledgment acknowledgment) {
        try {
            legalCategoryRepository.findByCode(legalCategory.getCode()).ifPresentOrElse(
                    lc -> {
                        lc.setLabel(legalCategory.getLabel());
                        legalCategoryRepository.save(lc);
                    },
                    () -> {
                        legalCategoryRepository.save(legalCategory);
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateDelegation(Delegation delegation, Acknowledgment acknowledgment) {
        try {
            delegationRepository.findByCode(delegation.getCode()).ifPresentOrElse(
                    d -> {
                        d.setLabel(delegation.getLabel());
                        d.setShortLabel(delegation.getShortLabel());
                        delegationRepository.save(d);
                    },
                    () -> {
                        delegationRepository.save(delegation);
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateCurrency(Currency currency, Acknowledgment acknowledgment) {
        try {
            currencyRepository.findByCode(currency.getCode()).ifPresentOrElse(
                    c -> {
                        c.setName(currency.getName());
                        c.setActive(currency.getActive());
                        currencyRepository.save(c);
                    },
                    () -> {
                        currencyRepository.save(currency);
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateCountry(Country country, Acknowledgment acknowledgment) {
        try {
            countryRepository.findByCode(country.getCode()).ifPresentOrElse(
                    c -> {
                        c.setLabel(country.getLabel());
                        c.setLanguage(country.getLanguage());
                        c.setCountryTaxCode(country.getCountryTaxCode());
                        countryRepository.save(c);
                    },
                    () -> {
                        countryRepository.save(country);
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updatePaymentMethod(PaymentMethod paymentMethod, Acknowledgment acknowledgment) {
        try {
            paymentMethodRepository.findByCode(paymentMethod.getCode()).ifPresentOrElse(
                    p -> {
                        p.setLabel(paymentMethod.getLabel());
                        p.setCode(paymentMethod.getCode());
                        p.setRequiresBankAccount(paymentMethod.getRequiresBankAccount());
                        paymentMethodRepository.save(p);
                    },
                    () -> {
                        paymentMethodRepository.save(paymentMethod);
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateActivity(Activity activity, Acknowledgment acknowledgment) {
        try {

            activityRepository.findByCode(activity.getCode()).ifPresentOrElse(
                    a -> {
                        a.setLabel(activity.getLabel());
                        a.setActive(activity.getActive());
                        activityRepository.save(a);
                    },
                    () -> {
                        activityRepository.save(activity);
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateRole(Role role, Acknowledgment acknowledgment) {
        try {
            staticRoleRepository.findByCode(role.getStaticRole().getCode()).ifPresentOrElse(
                    str -> {
                        roleRepository.findByCode(role.getCode()).ifPresentOrElse(
                                r -> {
                                    r.setLabel(role.getLabel());
                                    r.setStaticRole(str);
                                    r.setActive(role.getActive());
                                    roleRepository.save(r);
                                },
                                () -> {
                                    roleRepository.save(role);
                                }
                        );
                    },
                    () -> {
                        throw new ResourcesNotFoundException("AssociatedTo not found", "Role", role.getStaticRole().getCode());
                    }
            );

            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteMilestone(Milestone milestone, Acknowledgment acknowledgment) {
        try {
            milestoneRepository.deleteByCode(milestone.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deletePhase(Phase phase, Acknowledgment acknowledgment) {
        try {
            phaseRepository.deleteByCode(phase.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteLegalCategory(LegalCategory legalCategory, Acknowledgment acknowledgment) {
        try {
            legalCategoryRepository.deleteByCode(legalCategory.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteDelegation(Delegation delegation, Acknowledgment acknowledgment) {
        try {
            delegationRepository.deleteByCode(delegation.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteCurrency(Currency currency, Acknowledgment acknowledgment) {
        try {
            currencyRepository.deleteByCode(currency.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteCountry(Country country, Acknowledgment acknowledgment) {
        try {
            countryRepository.deleteByCode(country.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deletePaymentMethod(PaymentMethod paymentMethod, Acknowledgment acknowledgment) {
        try {
            paymentMethodRepository.deleteByCode(paymentMethod.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteActivity(Activity activity, Acknowledgment acknowledgment) {
        try {
            activityRepository.deleteByCode(activity.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteRole(Role role, Acknowledgment acknowledgment) {
        try {
            roleRepository.deleteByCode(role.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewProduct(Product product, Acknowledgment acknowledgment) {
        try {
            if (product.getActivity() == null) {
                throw new ResourcesNotFoundException("Activity not found", "Activity", Product.class.getSimpleName());
            }
            var activity = activityRepository.findByCode(product.getActivity().getCode())
                    .orElseThrow(() ->
                            new ResourcesNotFoundException("Activity not found", "Activity", Product.class.getSimpleName()));
            product.setActivity(activity);
            productRepository.save(product);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateProduct(Product product, Acknowledgment acknowledgment) {
        try {
            if (product.getActivity() == null) {
                throw new ResourcesNotFoundException("Activity not found", "Activity", Product.class.getSimpleName());
            }
            var activity = activityRepository.findByCode(product.getActivity().getCode())
                    .orElseThrow(() ->
                            new ResourcesNotFoundException("Activity not found", "Activity", Product.class.getSimpleName()));
            productRepository.findByCode(product.getCode()).ifPresentOrElse(
                    p -> {
                        p.setLabel(product.getLabel());
                        p.setCode(product.getCode());
                        p.setActivity(activity);
                        p.setActive(product.getActive());
                        productRepository.save(p);
                    },
                    () -> {
                        productRepository.save(product);
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void deleteProduct(Product product, Acknowledgment acknowledgment) {
        try {
            productRepository.deleteByCode(product.getCode());
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void createNewLineType(LineType lineType, Acknowledgment acknowledgment) {
        lineTypeRepository.save(lineType);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateLineType(LineType lineType, Acknowledgment acknowledgment) {
        lineTypeRepository.findByCode(lineType.getCode()).ifPresentOrElse(
                l -> {
                    l.setLabel(lineType.getLabel());
                    lineTypeRepository.save(l);
                },
                () -> {
                    lineTypeRepository.save(lineType);
                }
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteLineType(LineType lineType, Acknowledgment acknowledgment) {
        lineTypeRepository.deleteByCode(lineType.getCode());
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewOperation(Operation operation, Acknowledgment acknowledgment) {
        operationRepository.save(operation);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateOperation(Operation operation, Acknowledgment acknowledgment) {
        operationRepository.findByCode(operation.getCode()).ifPresentOrElse(
                o -> {
                    o.setLabel(operation.getLabel());
                    o.setActive(operation.getActive());
                    operationRepository.save(o);
                },
                () -> {
                    operationRepository.save(operation);
                }
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteOperation(Operation operation, Acknowledgment acknowledgment) {
        operationRepository.deleteByCode(operation.getCode());
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewNature(Nature nature, Acknowledgment acknowledgment) {
        natureRepository.save(nature);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateNature(Nature nature, Acknowledgment acknowledgment) {
        natureRepository.findByCode(nature.getCode()).ifPresentOrElse(
                n -> {
                    n.setLabel(nature.getLabel());
                    natureRepository.save(n);
                },
                () -> {
                    natureRepository.save(nature);
                }
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteNature(Nature nature, Acknowledgment acknowledgment) {
        natureRepository.deleteByCode(nature.getCode());
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewOperationNatureMapping(OperationNatureMapping operationNatureMapping, Acknowledgment acknowledgment) {
        operationNatureMapping.setOperation(operationRepository.findByCode(operationNatureMapping.getOperation().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Operation not found", "Operation", OperationNatureMapping.class.getSimpleName())));
        operationNatureMapping.setNature(natureRepository.findByCode(operationNatureMapping.getNature().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Nature not found", "Nature", OperationNatureMapping.class.getSimpleName())));
        operationNatureMappingRepository.save(operationNatureMapping);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateOperationNatureMapping(OperationNatureMapping operationNatureMapping, Acknowledgment acknowledgment) {
        operationNatureMapping.setOperation(operationRepository.findByCode(operationNatureMapping.getOperation().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Operation not found", "Operation", OperationNatureMapping.class.getSimpleName())));
        operationNatureMapping.setNature(natureRepository.findByCode(operationNatureMapping.getNature().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Nature not found", "Nature", OperationNatureMapping.class.getSimpleName())));
        operationNatureMappingRepository.findByOperationIdAndNatureId(operationNatureMapping.getOperation().getId()
                , operationNatureMapping.getNature().getId()).ifPresentOrElse(
                o -> {
                    o.setNatureStatus(operationNatureMapping.getNatureStatus());
                    operationNatureMappingRepository.save(o);
                },
                () -> {
                    operationNatureMappingRepository.save(operationNatureMapping);
                }
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteOperationNatureMapping(OperationNatureMapping operationNatureMapping, Acknowledgment acknowledgment) {
        operationNatureMappingRepository.deleteByOperationNatureCode(operationNatureMapping.getOperationNatureCode());
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewOperationNatureTypeMapping(OperationNatureTypeMapping operationNatureTypeMapping, Acknowledgment acknowledgment) {
        operationNatureMappingRepository.findByOperationNatureCode(operationNatureTypeMapping.getOperationNatureMapping().getOperationNatureCode())
                .ifPresentOrElse(
                        operationNatureTypeMapping::setOperationNatureMapping,
                        () -> {
                            throw new ResourcesNotFoundException("OperationNatureMapping not found", "OperationNatureMapping", OperationNatureTypeMapping.class.getSimpleName());
                        }
                );
        lineTypeRepository.findByCode(operationNatureTypeMapping.getType().getCode())
                .ifPresentOrElse(
                        operationNatureTypeMapping::setType,
                        () -> {
                            throw new ResourcesNotFoundException("LineType not found", "LineType", OperationNatureTypeMapping.class.getSimpleName());
                        }
                );
        operationNatureTypeMappingRepository.save(operationNatureTypeMapping);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateOperationNatureTypeMapping(OperationNatureTypeMapping operationNatureTypeMapping, Acknowledgment acknowledgment) {
        operationNatureMappingRepository.findByOperationNatureCode(operationNatureTypeMapping.getOperationNatureMapping().getOperationNatureCode())
                .ifPresentOrElse(
                        operationNatureTypeMapping::setOperationNatureMapping,
                        () -> {
                            throw new ResourcesNotFoundException("OperationNatureMapping not found", "OperationNatureMapping", OperationNatureTypeMapping.class.getSimpleName());
                        }
                );
        lineTypeRepository.findByCode(operationNatureTypeMapping.getType().getCode())
                .ifPresentOrElse(
                        operationNatureTypeMapping::setType,
                        () -> {
                            throw new ResourcesNotFoundException("LineType not found", "LineType", OperationNatureTypeMapping.class.getSimpleName());
                        }
                );
        operationNatureTypeMappingRepository.findByOperationNatureTypeMappingCode(operationNatureTypeMapping.getOperationNatureTypeMappingCode())
                .ifPresentOrElse(
                        o -> {
                            o.setTypeStatus(operationNatureTypeMapping.getTypeStatus());
                            operationNatureTypeMappingRepository.save(o);
                        },
                        () -> {
                            operationNatureTypeMappingRepository.save(operationNatureTypeMapping);
                        }
                );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteOperationNatureTypeMapping(OperationNatureTypeMapping operationNatureTypeMapping, Acknowledgment acknowledgment) {
        operationNatureTypeMappingRepository.deleteByOperationNatureTypeMappingCode(operationNatureTypeMapping.getOperationNatureTypeMappingCode());
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewProductLineTypeMapping(ProductLineTypeMapping productLineTypeMapping, Acknowledgment acknowledgment) {
        productLineTypeMapping.setProduct(productRepository.findByCode(productLineTypeMapping.getProduct().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Product not found", "Product", ProductLineTypeMapping.class.getSimpleName())));
        productLineTypeMapping.setOperationNatureTypeMapping(operationNatureTypeMappingRepository
                .findByOperationNatureTypeMappingCode(productLineTypeMapping.getOperationNatureTypeMapping().getOperationNatureTypeMappingCode())
                .orElseThrow(() -> new ResourcesNotFoundException("operation nature type not found "
                        + productLineTypeMapping.getOperationNatureTypeMapping().getOperationNatureTypeMappingCode(), "Product", ProductLineTypeMapping.class.getSimpleName())));
        productLineTypeMappingRepository.save(productLineTypeMapping);
        acknowledgment.acknowledge();
    }

    @Transactional
    public void updateProductLineTypeMapping(ProductLineTypeMapping productLineTypeMapping, Acknowledgment acknowledgment) {
        productLineTypeMapping.setProduct(productRepository.findByCode(productLineTypeMapping.getProduct().getCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Product not found", "Product", ProductLineTypeMapping.class.getSimpleName())));
        productLineTypeMapping.setOperationNatureTypeMapping(operationNatureTypeMappingRepository
                .findByOperationNatureTypeMappingCode(productLineTypeMapping.getOperationNatureTypeMapping().getOperationNatureTypeMappingCode())
                .orElseThrow(() -> new ResourcesNotFoundException("operation nature type not found "
                        + productLineTypeMapping.getOperationNatureTypeMapping().getOperationNatureTypeMappingCode(), "Product", ProductLineTypeMapping.class.getSimpleName())));
        productLineTypeMappingRepository.findByProduct_CodeAndOperationNatureTypeMappingId(productLineTypeMapping.getProduct().getCode()
                , productLineTypeMapping.getOperationNatureTypeMapping().getId()).ifPresentOrElse(
                p -> {
                    //p.setLineTypeStatus(productLineTypeMapping.getLineTypeStatus());
                    //productLineTypeMappingRepository.save(p);
                },
                () -> {
                    productLineTypeMappingRepository.save(productLineTypeMapping);
                }
        );
        acknowledgment.acknowledge();
    }

    @Transactional
    public void deleteProductLineTypeMapping(ProductLineTypeMapping productLineTypeMapping, Acknowledgment acknowledgment) {
        productLineTypeMappingRepository
                .deleteByProduct_CodeAndOperationNatureTypeMappingOperationNatureTypeMappingCode(productLineTypeMapping.getProductCode(),productLineTypeMapping
                        .getOperationNatureTypeMapping().getOperationNatureTypeMappingCode());
        acknowledgment.acknowledge();
    }

    @Transactional
    public void createNewBankBranch(BankBranch bankBranch, Acknowledgment acknowledgment) {
        try {
            bankBranchRepository.save(bankBranch);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    @Transactional
    public void updateBankBranch(BankBranch bankBranch, Acknowledgment acknowledgment) {
        try {
            var existingBankBranch = bankBranchRepository.findByReferenceAndDeletedAtIsNull(bankBranch.getReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Bank branch not found", "Bank branch", BankBranch.class.getSimpleName()));
            existingBankBranch.setCountry(bankBranch.getCountry());
            existingBankBranch.setInterbankCode(bankBranch.getInterbankCode());
            existingBankBranch.setSwift(bankBranch.getSwift());
            existingBankBranch.setCity(bankBranch.getCity());
            existingBankBranch.setPostalCode(bankBranch.getPostalCode());
            existingBankBranch.setAddress(bankBranch.getAddress());
            existingBankBranch.setDomiciliation(bankBranch.getDomiciliation());
            bankBranchRepository.save(existingBankBranch);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }

    public void deleteBankBranch(BankBranch bankBranch, Acknowledgment acknowledgment) {
        try {
            bankBranchRepository.findByReferenceAndDeletedAtIsNull(bankBranch.getReference()).ifPresentOrElse(
                    b -> {
                        b.setDeletedAt(new Date());
                        bankBranchRepository.save(b);
                    },
                    () -> {
                        throw new ResourcesNotFoundException("Bank branch not found", "Bank branch", BankBranch.class.getSimpleName());
                    }
            );
            acknowledgment.acknowledge();
        } catch (Exception e) {
            throw new TechnicalException(ERRORMESSAGE + e.getMessage(), e.getMessage(), MODEL_NAME);
        }
    }
}
