package com.datatricks.actors.service;

import com.datatricks.actors.exception.ConflictException;
import com.datatricks.actors.exception.ResourcesNotFoundException;
import com.datatricks.actors.exception.TechnicalException;
import com.datatricks.actors.exception.UnicityViolationException;
import com.datatricks.actors.exception.handler.InformativeMessage;
import com.datatricks.actors.model.*;
import com.datatricks.actors.model.dto.*;
import com.datatricks.actors.producer.ActorKafkaProducer;
import com.datatricks.actors.repository.*;
import com.datatricks.actors.utils.ActorsUtils;
import com.datatricks.actors.utils.TransactionSynchronizationUtil;
import com.datatricks.kafkacommondomain.enums.OperationType;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class ActorRoleService {

    private final ActorRoleRepository actorRoleRepository;
    private final RoleRepository roleRepository;
    private final ActorRepository actorRepository;
    private final ModelMapper modelMapper;
    private final TransactionSynchronizationUtil transactionSynchronizationUtil;
    private final ActorKafkaProducer actorKafkaProducer;
    private static final String MODULE = "ACTOR_ROLE";
    private static final String ROLE_ASSOCIATED_TO_COMPANY = "COMPANY";
    private static final String CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_ACTOR = " Role is not associated to an actor";
    private static final String CONFLICT_ACTOR_ROLE_ALREADY_EXISTS = "Actor role already exists";
    private static final String  CONFLICT_ROLE_CANNOT_BE_PRINCIPAL = "Cannot not set this role as principal to a management company";
    @Autowired
    public ActorRoleService(ActorRoleRepository actorRoleRepository,
                            ModelMapper modelMapper,
                            RoleRepository roleRepository,
                            ActorRepository actorRepository,
                            TransactionSynchronizationUtil transactionSynchronizationUtil,
                            ActorKafkaProducer actorKafkaProducer) {
        this.actorRoleRepository = actorRoleRepository;
        this.roleRepository = roleRepository;
        this.actorRepository = actorRepository;
        this.modelMapper = modelMapper;
        this.transactionSynchronizationUtil = transactionSynchronizationUtil;
        this.actorKafkaProducer = actorKafkaProducer;
    }

    @Transactional
    public ActorRole saveNewRole(Long idActor, Role role, NewRoleDto newRoleDto)
            throws ResourcesNotFoundException, UnicityViolationException {
        ActorRole actorRole = new ActorRole(newRoleDto);
        actorRole.setRole(role);
        actorRole.setActorId(new Actor(idActor));
        actorRole.setIsPrincipal(newRoleDto.getIsPrincipal());
        actorRole.setBusinessReference(actorRepository.findByReferenceAndTypeAndDeletedAtIsNull(newRoleDto.getBusinessReference(), ActorTypes.MANAGEMENT_COMPANY)
                .orElse(null));

        actorRole.setCreatedAt(new Date());
        var savedActorRole = actorRoleRepository.save(actorRole);
        // send message to kafka
        transactionSynchronizationUtil.executeAfterCommit(() -> actorKafkaProducer.sendActorRoleMessage(savedActorRole, OperationType.POST, MODULE));

        return savedActorRole;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ActorRoleDto>> saveNewActorRole(Long idActor, NewRoleDto newRoleDto) {

        Role role = roleRepository.findByCode(newRoleDto.getRoleCode())
                .orElseThrow(() -> new ResourcesNotFoundException("Role not found", "ROLE", "ROLE"));
        Actor actor = actorRepository.findById(idActor).orElseThrow(() -> new ResourcesNotFoundException("Actor not found", "ACTOR", "ACTOR"));
        actorRoleRepository.findByActorIdIdAndRoleCodeAndDeletedAtIsNull(idActor, newRoleDto.getRoleCode())
                .ifPresent(actorRole -> {
                    throw new ConflictException(
                            CONFLICT_ACTOR_ROLE_ALREADY_EXISTS,
                            "CONFLICT_ACTOR_ROLE_ALREADY_EXISTS",
                            CONFLICT_ACTOR_ROLE_ALREADY_EXISTS,
                            MODULE);
                });
        boolean isPrincipal = newRoleDto.getIsPrincipal();
        if (ActorTypes.MANAGEMENT_COMPANY.equals(actor.getType())) {
            if(!role.getStaticRole().getAssociatedTo().contains(ROLE_ASSOCIATED_TO_COMPANY) && isPrincipal) {
                throw new ConflictException(
                        CONFLICT_ROLE_CANNOT_BE_PRINCIPAL,
                        "CONFLICT_ROLE_CANNOT_BE_PRINCIPAL",
                        CONFLICT_ROLE_CANNOT_BE_PRINCIPAL,
                        MODULE);
            }
        } else if (ROLE_ASSOCIATED_TO_COMPANY.equals(role.getStaticRole().getAssociatedTo())) {
                throw new ConflictException(
                        CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_ACTOR,
                        "CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_ACTOR",
                        CONFLICT_ROLE_IS_NOT_ASSOCIATED_TO_ACTOR,
                        MODULE);
        }
        if (isPrincipal) {
            deleteOldPrincipalRole(idActor);
        }
        var savedActorRole = saveNewRole(idActor, role, newRoleDto);

        return ResponseEntity
                .status(HttpStatus.CREATED)
                .body(SingleResultDto.<ActorRoleDto>builder()
                        .data(modelMapper.map(savedActorRole, ActorRoleDto.class))
                        .build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ActorRoleDto>> updateActorRole(Long idActor, NewRoleDto roleToUpdate) {
        if (roleToUpdate.getIsPrincipal()) {
            ActorRole actorRole = actorRoleRepository.findByActorIdIdAndRoleCodeAndDeletedAtIsNull(idActor, roleToUpdate.getRoleCode()).orElse(null);
            if (actorRole != null) {
                deleteOldPrincipalRole(idActor);
                actorRole.setIsPrincipal(true);
                actorRole.setBusinessReference(actorRepository.findByReferenceAndTypeAndDeletedAtIsNull(roleToUpdate.getBusinessReference(), ActorTypes.MANAGEMENT_COMPANY)
                        .orElse(null));
                actorRole.setInvoiceEditionDateLimit(ActorsUtils.convertToDate(roleToUpdate.getInvoiceEditionDateLimit()));
                actorRole.setStartDate(ActorsUtils.convertToDate(roleToUpdate.getStartDate()));
                actorRole.setEndDate(ActorsUtils.convertToDate(roleToUpdate.getEndDate()));
                actorRole.setModifiedAt(new Date());
                var updatedActorRole = actorRoleRepository.save(actorRole);

                // send message to kafka
                transactionSynchronizationUtil.executeAfterCommit(() -> actorKafkaProducer.sendActorRoleMessage(updatedActorRole, OperationType.PUT, MODULE));

                return ResponseEntity.ok(SingleResultDto.<ActorRoleDto>builder().data(modelMapper.map(updatedActorRole, ActorRoleDto.class)).build());
            } else {
                throw new ResourcesNotFoundException("Actor role not found", "ACTOR_ROLE", MODULE);
            }
        } else {
            throw new ConflictException("The role to update must be a principal role", "ROLE_NOT_PRINCIPAL", "The role to update must be a principal role", MODULE);
        }
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteRoleForActor(Long idActor, String roleCode) {
        ActorRole actorRole = actorRoleRepository.findByActorIdIdAndRoleCodeAndDeletedAtIsNull(idActor, roleCode).orElse(null);
        if (actorRole != null) {
            actorRole.setDeletedAt(new Date());
            actorRoleRepository.save(actorRole);

            // send message to kafka
            transactionSynchronizationUtil.executeAfterCommit(() -> actorKafkaProducer.sendActorRoleMessage(actorRole, OperationType.DELETE, MODULE));
            return ResponseEntity.ok(new InformativeMessage("Resource with ID " + actorRole.getId() + " has been deleted successfully"));
        } else {
            throw new ResourcesNotFoundException("Actor role not found", "ACTOR_ROLE", MODULE);
        }
    }

    @Transactional
    public void deleteOldPrincipalRole(Long idActor) {
        // Extract the old principal role
        ActorRole oldPrincipalRole = actorRoleRepository.findByActorIdIdAndIsPrincipalIsTrueAndDeletedAtIsNull(idActor);
        if (oldPrincipalRole != null) {
            oldPrincipalRole.setIsPrincipal(false);
            actorRoleRepository.save(oldPrincipalRole);
        }
    }
}
