package com.datatricks.actors.service;

import com.datatricks.actors.model.dto.BankBranchDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class IbanValidateResult {
    @JsonProperty("result")
    private int result;

    @JsonProperty("data")
    private Data data;

    public BankBranchDto toBranchDto() {
        BankBranchDto bankBranchDto = new BankBranchDto();
        if (this.data != null) {
            bankBranchDto.setCountry(this.data.getCountryName());
            if (this.data.getBank() != null) {
                bankBranchDto.setDomiciliation(this.data.getBank().getBankName());
                bankBranchDto.setAddress(this.data.getBank().getAddress());
                bankBranchDto.setSwift(this.data.getBank().getBic());
                bankBranchDto.setPostalCode(this.data.getBank().getZip());
                bankBranchDto.setCity(this.data.getBank().getCity());
            }
        }

        return bankBranchDto;
    }
}

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
class Data {
    @JsonProperty("country_code")
    private String countryCode;

    @JsonProperty("country_name")
    private String countryName;

    @JsonProperty("bank")
    private Bank bank;
}

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
class Bank {
    @JsonProperty("bank_name")
    private String bankName;

    @JsonProperty("address")
    private String address;

    @JsonProperty("bic")
    private String bic;

    @JsonProperty("city")
    private String city;

    @JsonProperty("zip")
    private String zip;
}
