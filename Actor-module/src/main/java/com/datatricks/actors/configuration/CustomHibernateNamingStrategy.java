package com.datatricks.actors.configuration;

import org.hibernate.boot.model.naming.Identifier;
import org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl;
import org.hibernate.engine.jdbc.env.spi.JdbcEnvironment;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CustomHibernateNamingStrategy extends PhysicalNamingStrategyStandardImpl {
    public static final String TABLE_NAME_PREFIX = "DT_";

    @Override
    public Identifier toPhysicalTableName(final Identifier identifier, final JdbcEnvironment jdbcEnvironment) {
        return prefix(identifier);
    }

    private Identifier prefix(Identifier identifier) {
        return Identifier.toIdentifier(TABLE_NAME_PREFIX + identifier.getText());
    }

    @Override
    public Identifier toPhysicalColumnName(final Identifier identifier, final JdbcEnvironment jdbcEnvironment) {
        return convertToSnakeCase(identifier);
    }

    private Identifier convertToSnakeCase(Identifier identifier) {
        String snakeCaseName = identifier.getText()
                .replaceAll("([a-z])([A-Z])", "$1_$2")
                .toLowerCase();
        return Identifier.toIdentifier(snakeCaseName);
    }
}