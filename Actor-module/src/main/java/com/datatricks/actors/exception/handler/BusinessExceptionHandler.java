package com.datatricks.actors.exception.handler;

import com.datatricks.actors.exception.BusinessException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class BusinessExceptionHandler {

	@Value("${api.response.activateDebugInfo}")
	private boolean isDebugActive;

    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<BusinessError> handleConstraintViolation(BusinessException ex) {
        BusinessError businessErrorResponse = new BusinessError();
        businessErrorResponse.setCode("CONSTRAINT_VIOLATION");
        businessErrorResponse.setMessage(ex.getMessage());
        return new ResponseEntity<>(businessErrorResponse, HttpStatus.BAD_REQUEST);
    }
}
