package com.datatricks.actors.exception.handler;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.ValidationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class ConstraintValidationHandler {
    @ApiResponse(responseCode = "400", description = "Bad Request",
            content = @Content(schema = @Schema(implementation = BusinessError.class)))
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<BusinessError> handleConstraintViolation(MethodArgumentNotValidException ex) {
        BusinessError businessErrorResponse = new BusinessError("INVALID_INPUT");
        businessErrorResponse.setMessage("the input provided is invalid");
        ex.getFieldErrors().forEach(error -> businessErrorResponse
                .getBusinessDetails()
                .add(new BusinessDetails(
                        Objects.requireNonNull(error.getDefaultMessage()).split(":")[0],
                        error.getDefaultMessage().split(":")[1])));
        return new ResponseEntity<>(businessErrorResponse, HttpStatus.BAD_REQUEST);
    }

    @ApiResponse(responseCode = "400", description = "Bad Request",
            content = @Content(schema = @Schema(implementation = BusinessError.class)))
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<BusinessError> handleParsingViolation(HttpMessageNotReadableException ex) {
        BusinessError businessErrorResponse = new BusinessError("PARSING_ERROR");
        businessErrorResponse.getBusinessDetails().add(new BusinessDetails("Unknown", ex.getMessage()));
        return new ResponseEntity<>(businessErrorResponse, HttpStatus.BAD_REQUEST);
    }

    @ApiResponse(responseCode = "400", description = "Bad Request",
            content = @Content(schema = @Schema(implementation = BusinessError.class)))
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<BusinessError> handleValidationViolation(ValidationException ex) {
        BusinessError businessErrorResponse = new BusinessError("INVALID_INPUT");
        businessErrorResponse.setMessage("the input provided is invalid");
        List<String> errors = Arrays.asList(ex.getMessage().split("\n"));
        errors.forEach(error -> businessErrorResponse
                .getBusinessDetails()
                .add(new BusinessDetails(error.split(":")[0], error.split(":")[1])));
        return new ResponseEntity<>(businessErrorResponse, HttpStatus.BAD_REQUEST);
    }

}
