package com.datatricks.actors.exception.handler;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
public class BusinessError {
    @JsonProperty("details")
    private final List<BusinessDetails> businessDetails = new ArrayList<>();

    @Setter
    @JsonProperty("errorCode")
    private String code;

    @Setter
    @JsonProperty("message")
    private String message;

    public BusinessError() {}

    public BusinessError(String code) {
        this.code = code;
    }

}

