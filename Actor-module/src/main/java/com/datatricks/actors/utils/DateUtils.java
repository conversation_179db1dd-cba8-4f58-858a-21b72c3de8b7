package com.datatricks.actors.utils;

import java.time.LocalDate;

public class DateUtils {
    private DateUtils() {
        // This is a helper class and does need to be instanced
    }

    public static final String DATE_FORMAT = "yyyy-MM-dd";

    public static Object formatDate(LocalDate localDate) {
        return localDate.format(java.time.format.DateTimeFormatter.ofPattern(DATE_FORMAT));
    }
}
