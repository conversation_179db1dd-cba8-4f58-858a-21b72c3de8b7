package com.datatricks.actors.utils;

import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import java.util.*;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

public class RelationshipUtils {

    /**
     * Retrieves a map of relationship names to their corresponding getter methods for a given entity class.
     *
     * @param entityClass The entity class to inspect.
     * @return A map where keys are relationship names and values are getter methods.
     */
    public static Map<String, Method> getRelationshipGetters(Class<?> entityClass) {
        Map<String, Method> relationships = new HashMap<>();

        // First, check field annotations
        for (Field field : getAllFields(entityClass)) {
            if (isRelationshipField(field)) {
                String propertyName = field.getName();
                Method getter = getGetter(entityClass, propertyName);
                if (getter != null) {
                    relationships.put(propertyName, getter);
                }
            }
        }

        // Then, check method annotations
        for (Method method : entityClass.getMethods()) {
            if (isRelationshipMethod(method)) {
                String propertyName = getPropertyName(method);
                relationships.put(propertyName, method);
            }
        }

        return relationships;
    }

    /**
     * Retrieves all fields of a class, including inherited fields.
     *
     * @param type The class to inspect.
     * @return A list of all fields.
     */
    private static List<Field> getAllFields(Class<?> type) {
        List<Field> fields = new ArrayList<>();
        while (type != null && type != Object.class) {
            fields.addAll(Arrays.asList(type.getDeclaredFields()));
            type = type.getSuperclass();
        }
        return fields;
    }

    /**
     * Determines if a field is a JPA relationship field.
     *
     * @param field The field to inspect.
     * @return True if the field has a JPA relationship annotation, false otherwise.
     */
    private static boolean isRelationshipField(Field field) {
        return field.isAnnotationPresent(OneToOne.class) ||
                field.isAnnotationPresent(OneToMany.class) ||
                field.isAnnotationPresent(ManyToOne.class) ||
                field.isAnnotationPresent(ManyToMany.class);
    }

    /**
     * Determines if a method is a getter for a JPA relationship.
     *
     * @param method The method to inspect.
     * @return True if the method is a relationship getter, false otherwise.
     */
    private static boolean isRelationshipMethod(Method method) {
        if (Modifier.isPublic(method.getModifiers()) &&
                method.getParameterCount() == 0 &&
                method.getReturnType() != void.class) {

            return method.isAnnotationPresent(OneToOne.class) ||
                    method.isAnnotationPresent(OneToMany.class) ||
                    method.isAnnotationPresent(ManyToOne.class) ||
                    method.isAnnotationPresent(ManyToMany.class);
        }
        return false;
    }

    /**
     * Extracts the property name from a getter method.
     *
     * @param method The getter method.
     * @return The corresponding property name.
     */
    private static String getPropertyName(Method method) {
        String methodName = method.getName();
        if (methodName.startsWith("get") && methodName.length() > 3) {
            return decapitalize(methodName.substring(3));
        } else if (methodName.startsWith("is") && methodName.length() > 2) {
            return decapitalize(methodName.substring(2));
        }
        return methodName;
    }

    /**
     * Retrieves the getter method for a given property name.
     *
     * @param clazz The class containing the property.
     * @param propertyName The name of the property.
     * @return The getter method, or null if not found.
     */
    private static Method getGetter(Class<?> clazz, String propertyName) {
        // Construct method names
        String capitalized = capitalize(propertyName);
        String getterName = "get" + capitalized;
        String isName = "is" + capitalized;

        try {
            return clazz.getMethod(getterName);
        } catch (NoSuchMethodException e) {
            // Try isName for boolean properties
            try {
                return clazz.getMethod(isName);
            } catch (NoSuchMethodException ex) {
                // No getter found
                return null;
            }
        }
    }

    /**
     * Determines if a method is a JPA relationship getter.
     *
     * @param method The method to inspect.
     * @return True if the method is a relationship getter, false otherwise.
     */
    private static boolean isRelationshipGetter(Method method) {
        return isRelationshipMethod(method);
    }

    /**
     * Decapitalizes the first character of the string.
     *
     * @param str The string to decapitalize.
     * @return The decapitalized string.
     */
    private static String decapitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return Character.toLowerCase(str.charAt(0)) + str.substring(1);
    }

    /**
     * Capitalizes the first character of the string.
     *
     * @param str The string to capitalize.
     * @return The capitalized string.
     */
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }

    private RelationshipUtils() {
        throw new UnsupportedOperationException("Utility class");
    }
}
