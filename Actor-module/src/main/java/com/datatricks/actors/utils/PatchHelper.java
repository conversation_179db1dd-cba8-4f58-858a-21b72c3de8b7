package com.datatricks.actors.utils;

import com.datatricks.actors.model.PatchResult;
import com.datatricks.actors.model.dto.PatchResponseDto;
import com.datatricks.actors.model.group.ValidationGroupCollector;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.CaseFormat;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class PatchHelper<T extends ValidationGroupCollector> {

    private final List<String> ignoreFields = List.of("id", "reference", "createdAt", "updatedAt", "deletedAt");

    private final CustomValidator<T> customValidator;

    public PatchHelper(CustomValidator<T> customValidator) {
        this.customValidator = customValidator;
    }

    public PatchResult<T> applyPatch(T target, Set<Class<?>> groups, T updates) throws IllegalAccessException {
        applyDynamicValidation(groups, updates);
        Map<String, Object> response = new HashMap<>();
        for (Field field : target.getClass().getDeclaredFields()) {
            if (field.trySetAccessible()) {
                Field updateField = getField(updates.getClass(), CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, field.getName()));
                if (updateField != null) {
                    if (updateField.trySetAccessible()) {
                        if (field.getType() == updateField.getType()) {
                            if (updateField.get(updates) != null && !ignoreFields.contains(field.getName())) {
                                field.set(target, updateField.get(updates));
                                if (field.getType().equals(LocalDate.class)) {
                                    response.put(getJsonPropertyName(field), DateUtils.formatDate((LocalDate) updateField.get(updates)));
                                } else {
                                    response.put(getJsonPropertyName(field), updateField.get(updates));
                                }
                            }
                        }
                    } else {
                        throw new IllegalAccessException("Cannot access field: " + updateField.getName());
                    }
                    field.setAccessible(false);
                }
            } else {
                throw new IllegalAccessException("Cannot access field: " + field.getName());
            }
        }
        return PatchResult.<T>builder()
                .updated(target)
                .response(PatchResponseDto.builder()
                        .data(response)
                        .build())
                .build();
    }

    private Field getField(Class<?> clazz, String fieldName) {
        try {
            return clazz.getDeclaredField(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, fieldName));
        } catch (NoSuchFieldException e) {
            return null;
        }
    }

    private String getJsonPropertyName(Field field) {
        JsonProperty jsonProperty = field.getAnnotation(JsonProperty.class);
        return (jsonProperty != null) ? jsonProperty.value() : field.getName();
    }

    public void applyDynamicValidation(Set<Class<?>> groups, T target) {
        for (Class<?> group : groups) {
            customValidator.validate(target, group);
        }
    }

    // Helper method to determine if a field is a model field (exclude enums)
    private boolean isModelField(Field field) {
        // Check if the field is not a primitive, not a wrapper class, and not an enum
        return !field.getType().isPrimitive() && !field.getType().getName().startsWith("java.lang.") &&  // Exclude common primitive wrappers (Integer, String, etc.)
                !field.getType().isEnum();  // Exclude enums (but allow enums to be updated)
    }
}
