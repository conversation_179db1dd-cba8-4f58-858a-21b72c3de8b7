package com.datatricks.actors.controller;

import com.datatricks.actors.exception.handler.InformativeMessage;
import com.datatricks.actors.model.dto.*;
import com.datatricks.actors.service.ActorService;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.Explode;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.enums.ParameterStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import static com.datatricks.actors.utils.ActorsUtils.handleException;
import static com.toedter.spring.hateoas.jsonapi.MediaTypes.JSON_API_VALUE;

@RestController
@RequestMapping("/api")
@OpenAPIDefinition
@Tag(name = "Actors", description = "Actors API")
public class ActorsController {
    private final ActorService actorService;

    @Autowired
    ActorsController(
            ActorService actorService) {
        this.actorService = actorService;
    }

    @Cacheable(value = "actorsList", key = "#params.toString()")
    @GetMapping(value = "/v1/actors")
    public ResponseEntity<PageDto<ActorDto>> getActors(@Parameter(name = "params",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "object", implementation = ActorParam.class),
            style = ParameterStyle.FORM,
            explode = Explode.TRUE) @RequestParam Map<String, String> params) {
        try {
            return actorService.getActors(params);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @GetMapping(value = "/v1/actors/all-actors", produces = JSON_API_VALUE)
    public ResponseEntity<RepresentationModel<?>> getAllActors(
            @Parameter(name = "params",
                    in = ParameterIn.QUERY,
                    schema = @Schema(type = "object", implementation = ActorParam.class),
                    style = ParameterStyle.FORM,
                    explode = Explode.TRUE)
            @RequestParam Map<String, String> params,
            @RequestParam(value = "include", required = false) String[] include,
            @RequestParam(value = "fields[movies]", required = false) String[] fieldsMovies) {
        try {
            return actorService.findAllActors(include, params, fieldsMovies);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Cacheable(value = "actorEmails", key = "#actorReference", unless = "#result.statusCode.value() == 404")
    @GetMapping(value = "/v1/actors/{actor_reference}/emails")
    public ResponseEntity<PageDto<BillingParametersEmailDto>> getActorEmails(@PathVariable("actor_reference") String actorReference) {
        try {
            return actorService.getActorEmails(actorReference);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Cacheable(value = "businessList", key = "#params.toString()")
    @GetMapping(value = "/v1/businesses")
    public ResponseEntity<PageDto<BusinessSummaryDto>> getBusinesses(@Parameter(name = "params",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "object", implementation = BusinessSummaryDto.class),
            style = ParameterStyle.FORM,
            explode = Explode.TRUE) @RequestParam Map<String, String> params) {
        try {
            return actorService.getBusinesses(params);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Cacheable(value = "partiesList", key = "#params.toString()")
    @GetMapping(value = "/v1/parties")
    public ResponseEntity<PageDto<PartiesSummaryDto>> getParties(@Parameter(name = "params",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "object", implementation = PartiesSummaryDto.class),
            style = ParameterStyle.FORM,
            explode = Explode.TRUE) @RequestParam Map<String, String> params) {
        try {
            return actorService.getParties(params);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Cacheable(value = "actors", key = "#id", unless = "#result.statusCode.value() == 404")
    @GetMapping(value = "/v1/actors/{id}")
    public ResponseEntity<SingleResultDto<ActorDto>> getActorById(@PathVariable Long id) {
        try {
            return actorService.getActorById(id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Cacheable(value = "actors", key = "#reference", unless = "#result.statusCode.value() == 404")
    @GetMapping(value = "/v1/actors/reference/{reference}")
    public ResponseEntity<SingleResultDto<ActorDto>> getActorByReference(@PathVariable String reference) {
        try {
            return actorService.getActorByReference(reference);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(evict = {
            @CacheEvict(value = "actorsList", allEntries = true),
            @CacheEvict(value = "businessList", allEntries = true),
            @CacheEvict(value = "partiesList", allEntries = true),
            @CacheEvict(value = "actors", key = "#result.body.data.id")
    })
    @PostMapping(value = "/v1/actors")
    public ResponseEntity<SingleResultDto<ActorDto>> createActor(@RequestBody @Valid NewActorDto newActor) {
        try {
            return actorService.createActor(newActor);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(evict = {
            @CacheEvict(value = "actorsList", allEntries = true),
            @CacheEvict(value = "businessList", allEntries = true),
            @CacheEvict(value = "partiesList", allEntries = true),
            @CacheEvict(value = "actors", key = "#result.body.data.id")
    })
    @PutMapping(value = "/v1/actors/{id}")
    public ResponseEntity<SingleResultDto<ActorDto>> updateActor(
            @PathVariable Long id, @RequestBody @Valid NewActorDto newActorDto) {
        try {
            return actorService.updateActor(id, newActorDto);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(evict = {
            @CacheEvict(value = "actorsList", allEntries = true),
            @CacheEvict(value = "businessList", allEntries = true),
            @CacheEvict(value = "partiesList", allEntries = true),
            @CacheEvict(value = "actors", key = "#result.body.data['id']")
    })
    @PatchMapping(value = "/v1/actors/{id}")
    public ResponseEntity<PatchResponseDto> patchActor(
            @PathVariable Long id, @RequestBody PatchDto<PatchActorDto> patchDto) {
        try {
            return actorService.patchActor(id, patchDto);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(evict = {
            @CacheEvict(value = "actorsList", allEntries = true),
            @CacheEvict(value = "businessList", allEntries = true),
            @CacheEvict(value = "partiesList", allEntries = true),
            @CacheEvict(value = "actors", key = "#id")
    })
    @DeleteMapping(value = "/v1/actors/{id}")
    public ResponseEntity<InformativeMessage> deleteActorById(@PathVariable Long id) {
        try {
            return actorService.deleteActorById(id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }
}
