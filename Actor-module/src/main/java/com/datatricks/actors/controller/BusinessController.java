package com.datatricks.actors.controller;

import com.datatricks.actors.model.dto.*;
import com.datatricks.actors.service.BusinessService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.Explode;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.enums.ParameterStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Map;

import static com.datatricks.actors.utils.ActorsUtils.handleException;

@RestController
@RequestMapping("/api")
@Tag(name = "Business", description = "Business API")
public class BusinessController {
    private final BusinessService businessService;

    public BusinessController(BusinessService businessService) {
        this.businessService = businessService;
    }

    @Cacheable(value = "businessByActorId", key = "#id")
    @GetMapping(value = "/v1/actors/{id}/business-profile")
    public ResponseEntity<SingleResultDto<BusinessProfileDto>> getBusinessProfiles(@PathVariable Long id) {
        try {
            return businessService.getBusinessProfiles(id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            put = {
                    @CachePut(
                            value = "businessById",
                            key = "#result.body.data.id",
                            unless = "#result.statusCode.value() == 404"
                    )
            },
            evict = {
                    @CacheEvict(value = "businessByActorId", key = "#id"),
            }
    )
    @PostMapping(value = "/v1/actors/{id}/business-profile")
    public ResponseEntity<SingleResultDto<BusinessProfileDto>> createBusinessProfile(@PathVariable Long id, @Valid @RequestBody BusinessProfileDto businessProfileDto) {
        try {
            return businessService.createBusinessProfile(id, businessProfileDto);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            put = {
                    @CachePut(
                            value = "businessById",
                            key = "#result.body.data.id",
                            unless = "#result.statusCode.value() == 404"
                    )
            },
            evict = {
                    @CacheEvict(value = "businessByActorId", key = "#id"),
            }
    )
    @PutMapping(value = "/v1/business-profile/{id}")
    public ResponseEntity<SingleResultDto<BusinessProfileDto>> updateBusinessProfile(@PathVariable Long id, @Valid @RequestBody BusinessProfileDto businessProfileDto) {
        try {
            return businessService.updateBusinessProfile(id, businessProfileDto);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            evict = {
                    @CacheEvict(value = "businessByActorId", allEntries = true),
                    @CacheEvict(value = "businessById", key = "#id")
            }
    )
    @DeleteMapping(value = "/v1/business-profile/{id}")
    public ResponseEntity<SingleResultDto<BusinessProfileDto>> deleteBusinessProfile(@PathVariable Long id) {
        try {
            return businessService.deleteBusinessProfile(id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Cacheable(value = "actorFiscalYearList", key = "#actorId + ':' + #params.toString()")
    @GetMapping(value = "/v1/actors/{actor_id}/fiscal-year")
    public ResponseEntity<PageDto<FiscalYearDto>> getFiscalYear(@PathVariable("actor_id") Long actorId, @Parameter(name = "params",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "object", implementation = FiscalYearParams.class),
            style = ParameterStyle.FORM,
            explode = Explode.TRUE) @RequestParam Map<String, String> params) {
        try {
            return businessService.getFiscalYears(actorId, params);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatusCode.valueOf(200)).body(PageDto.<FiscalYearDto>builder()
                    .data(new ArrayList<>())
                    .total(0).build());
        }
    }

    @Cacheable(value = "actorFiscalYearListById", key = "#actorId + ':' + #id")
    @GetMapping(value = "/v1/actors/{actor_id}/fiscal-year/{id}")
    public ResponseEntity<SingleResultDto<FiscalYearDto>> getFiscalYearById(@PathVariable("actor_id") Long actorId, @PathVariable Long id) {
        try {
            return businessService.getFiscalYearById(actorId, id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            put = {
                    @CachePut(
                            value = "actorFiscalYearListById",
                            key = "#actorId + ':' + #result.body.data.id",
                            unless = "#result.statusCode.value() == 404"
                    )
            },
            evict = {
                    @CacheEvict(value = "actorFiscalYearList", allEntries = true)
            }
    )
    @PostMapping(value = "/v1/actors/{actor_id}/fiscal-year")
    public ResponseEntity<SingleResultDto<FiscalYearDto>> createFiscalYear(@PathVariable("actor_id") Long actorId, @RequestBody @Valid NewFiscalYearDto fiscalYearDto) {
        try {
            return businessService.createFiscalYear(actorId, fiscalYearDto);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            evict = {
                    @CacheEvict(value = "actorFiscalYearListById", key = "#actorId + ':' + #result.body.data.id"),
                    @CacheEvict(value = "actorFiscalYearList", allEntries = true)
            }
    )
    @PutMapping(value = "/v1/actors/{actor_id}/fiscal-year/{id}")
    public ResponseEntity<SingleResultDto<FiscalYearDto>> updateFiscalYear(@PathVariable("actor_id") Long actorId, @PathVariable Long id, @RequestBody @Valid NewFiscalYearDto fiscalYearDto) {
        try {
            return businessService.updateFiscalYear(actorId, id, fiscalYearDto);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            evict = {
                    @CacheEvict(value = "actorFiscalYearListById", key = "#actorId + ':' + #id"),
                    @CacheEvict(value = "actorFiscalYearList", allEntries = true)
            }
    )
    @DeleteMapping(value = "/v1/actors/{actor_id}/fiscal-year/{id}")
    public ResponseEntity<SingleResultDto<FiscalYearDto>> deleteFiscalYear(@PathVariable("actor_id") Long actorId, @PathVariable Long id) {
        try {
            return businessService.deleteFiscalYear(actorId, id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }
}
