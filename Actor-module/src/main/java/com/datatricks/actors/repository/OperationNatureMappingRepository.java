package com.datatricks.actors.repository;

import com.datatricks.actors.model.Nature;
import com.datatricks.actors.model.Operation;
import com.datatricks.actors.model.OperationNatureMapping;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface OperationNatureMappingRepository extends JpaRepository<OperationNatureMapping, Long> {
    Optional<OperationNatureMapping> findByOperationIdAndNatureId(Long operation, Long nature);

    void deleteByOperationIdAndNatureId(Long id, Long id1);

    Optional<OperationNatureMapping> findByOperationNatureCode(String operationNatureCode);

    void deleteByOperationNatureCode(String operationNatureCode);
}
