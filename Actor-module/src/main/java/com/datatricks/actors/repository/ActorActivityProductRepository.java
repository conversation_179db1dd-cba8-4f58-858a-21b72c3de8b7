package com.datatricks.actors.repository;

import com.datatricks.actors.model.Activity;
import com.datatricks.actors.model.ActorActivityProduct;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ActorActivityProductRepository extends JpaRepository<ActorActivityProduct, Long>, JpaSpecificationExecutor<ActorActivityProduct> {
    @Query("SELECT DISTINCT aap.activity FROM ActorActivityProduct aap WHERE aap.actor.reference = :actorReference")
    List<Activity> findActivitiesByActorReference(@Param("actorReference") String actorReference);

    @Query("SELECT aap FROM ActorActivityProduct aap " +
            "WHERE aap.actor.reference = :actorReference " +
            "AND aap.activity.code = :activityCode")
    List<ActorActivityProduct> findByActorReferenceAndActivityCode(
            @Param("actorReference") String actorReference,
            @Param("activityCode") String activityCode
    );

    ActorActivityProduct findByActorReferenceAndActivityCodeAndProductCode(String actorReference, String activityCode, String productCode);

    List<ActorActivityProduct> findByActorReferenceAndActivityCodeIn(String actorReference, List<String> activityCodes);

    @Query("SELECT DISTINCT aap.activity FROM ActorActivityProduct aap WHERE aap.actor.reference = :actorReference AND aap.active = true")
    List<Activity> findActivitiesByActorReferenceAndActiveIsTrue(String actorReference);

    List<ActorActivityProduct> findByActorReferenceAndActivityCodeInAndActiveIsTrue(String actorReference, List<String> activityCodes);
}
