package com.datatricks.actors.repository;

import com.datatricks.actors.model.BankAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface BankAccountRepository extends JpaRepository<BankAccount, Long>, JpaSpecificationExecutor<BankAccount> {
    BankAccount findByActorIdIdAndIdAndDeletedAtIsNull(Long actorId, Long id);

	BankAccount findByActorIdIdAndIsPrincipalIsTrueAndDeletedAtIsNull(Long actorId);

    BankAccount findByActorIdReferenceAndIsPrincipalIsTrueAndDeletedAtIsNull(String reference);

    boolean existsByIdAndActorIdIdAndDeletedAtIsNull(Long id, Long idActor);

    Optional<BankAccount> findByIdAndActorIdIdAndDeletedAtIsNull(Long bankAccountId, Long id);

    Optional<BankAccount> findByActorIdReferenceAndIdAndDeletedAtIsNull(String actorReference, Long bankAccountId);
}
