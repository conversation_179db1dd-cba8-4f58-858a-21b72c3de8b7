<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:pro="http://www.liquibase.org/xml/ns/pro"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd
      http://www.liquibase.org/xml/ns/pro
      http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd">
        <property name="table.prefix" value="DT_"/>
        <include file="db/changeLog/changes/PostgresMigration.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/insert-activities-migration.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/insert-countries-migration.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/insert-milestones-migration.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/insert-roles.migration.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/insert-phases-migration.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/seeders-payment-methods.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-2373-update-phase.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/seeders/phase-seeders.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/seeders/role-seeders.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/seeders/milestones-seeders.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/seeders/activities-seeders.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/seeders/countries-seeders.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/seeders/currencies-seeders.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/seeders/legal-categories-seeders.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/seeders/payment-methods-seeders.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/seeders/delegations-seeders.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-2619-rename-box-code-to-branch-code.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-2684-business-view.sql" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-2684-parties-view.sql" relativeToChangelogFile="false"/>
        <include file="/db/changeLog/changes/WP-2684-drop-view.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-2641.xml" relativeToChangelogFile="false"/>
        <include file="/db/changeLog/changes/WP-2684-create-view.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-2782-add-start-and-end-date-to-actorRole.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-2962.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-2933.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/Bank-account-hotfix.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/********-remove-column-from-address.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-3006-fix-view-parties-business.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-3058.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-3090.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-3386-fix-billing-parameters-bugs.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-3347.xml" relativeToChangelogFile="false"/>
        <include file="/db/changeLog/changes/WP-3516.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-3268-update-billing-parameters.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-3574.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-3583.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-3703-frontend-backend-assigning-an-activity-to-a-management-company.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-3465-backend-implement-synchronization-between-settings-module-and-backend-side.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-3788-manage-roles-add-edit-activate-deactivate-delete.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/WP-3815-refactor-operations-nature-types-by-code.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/KF-261-fix-bank-account-branch.xml" relativeToChangelogFile="false"/>
        <include file="db/changeLog/changes/KF-339.xml" relativeToChangelogFile="false"/>
</databaseChangeLog>
