create or replace view DT_business_summary as
select a.*,
       b.id as bank_account_id,
       b.account_number as bank_account_account_number,
       b.title as bank_account_title,
       b.is_principal as bank_account_is_principal,
       v.code role_code,
       v.is_principal as role_is_principal

from (
        Select actr.is_principal, actr.actor_id, actr.role_id, rr.code
        from dt_actor_roles actr, dt_roles rr
        where actr.role_id = rr.id and
              actr.is_principal = true
     ) as v , dt_actors a
         join dt_bank_accounts as b on a.id = b.actor_id
where a.type = 'MANAGEMENT_COMPANY' and
      b.is_principal = true and
      a.id = v.actor_id
