<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="WP-3006-1" author="HoussemMoussa">
        <sql>
            drop view if exists DT_parties_summary;
        </sql>
    </changeSet>
    <changeSet id="WP-3006-2" author="HoussemMoussa">
        <sql>
            drop view if exists DT_business_summary;
        </sql>
    </changeSet>
    <changeSet id="WP-3006-3" author="HoussemMoussa">
        <sql>
            create or replace view DT_business_summary as
            select a.*,
                   b.account_number as principal_bank_account_number,
                   b.title as principal_bank_account_title,
                   v.code principal_role_code,
                   v.label principal_role_label,
                   p.code as phase_code,
                   p.label as phase_label,
                   m.code as milestone_code,
                   m.code as milestone_label,
                   act.code as activity_code,
                   act.label as activity_label,
                   lc.code as legal_category_code,
                   lc.label as legal_category_label,
                   c.code as country_code,
                   c.label as country_label

            from (
                     Select actr.is_principal, actr.actor_id, rr.id, rr.code, rr.label
                     from dt_actor_roles actr, dt_roles rr
                     where actr.role_code = rr.code and
                         actr.is_principal = true
                 ) as v , dt_actors a
                              left join dt_bank_accounts as b on a.id = b.actor_id
                              join dt_phases as p on a.phase_id = p.id
                              join dt_milestones m on a.milestone_id = m.id
                              join dt_activities as act on a.activity_id = act.id
                              join dt_legal_categories as lc on a.legal_category_id = lc.id
                              join dt_countries as c on a.country_id = c.id

            where
                (b.is_principal = true or b.is_principal is null) and
                a.type = 'MANAGEMENT_COMPANY' and
                a.id = v.actor_id;
        </sql>
    </changeSet>
    <changeSet id="WP-3006-4" author="HoussemMoussa">
        <sql>
            create or replace view DT_parties_summary as
            select a.*,
                   b.account_number as principal_bank_account_number,
                   b.title as principal_bank_account_title,
                   v.code principal_role_code,
                   v.label principal_role_label,
                   p.code as phase_code,
                   p.label as phase_label,
                   m.code as milestone_code,
                   m.code as milestone_label,
                   act.code as activity_code,
                   act.label as activity_label,
                   lc.code as legal_category_code,
                   lc.label as legal_category_label,
                   c.code as country_code,
                   c.label as country_label

            from (
                     Select actr.is_principal, actr.actor_id, rr.id, rr.code, rr.label
                     from dt_actor_roles actr, dt_roles rr
                     where actr.role_code = rr.code and
                         actr.is_principal = true
                 ) as v , dt_actors a
                              left join dt_bank_accounts as b on a.id = b.actor_id
                              join dt_phases as p on a.phase_id = p.id
                              join dt_milestones m on a.milestone_id = m.id
                              join dt_activities as act on a.activity_id = act.id
                              join dt_legal_categories as lc on a.legal_category_id = lc.id
                              join dt_countries as c on a.country_id = c.id

            where
                (b.is_principal = true or b.is_principal is null) and
                a.type != 'MANAGEMENT_COMPANY' and
                a.id = v.actor_id;
        </sql>
    </changeSet>
</databaseChangeLog>