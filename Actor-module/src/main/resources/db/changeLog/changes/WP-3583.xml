<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="WP-3583-1" author="HoussemMoussa">
        <sql>
            update dt_roles set is_exclusive = false
        </sql>
    </changeSet>
    <changeSet id="WP-3583-2" author="HoussemMoussa">
        <sql>
            update dt_roles set is_exclusive = true  where code = 'COLOC' or code = 'SSLOCAT' or code = 'CLIENT';
        </sql>
    </changeSet>
</databaseChangeLog>