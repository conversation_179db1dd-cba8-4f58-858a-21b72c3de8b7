<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="WP-2684-create-view-3" author="HoussemMoussa">
        <sql>
            create or replace view DT_parties_summary as
            select a.*,
                   b.id as bank_account_id,
                   b.account_number as bank_account_account_number,
                   b.title as bank_account_title,
                   b.is_principal as bank_account_is_principal,
                   v.code role_code,
                   v.is_principal as role_is_principal

            from (
                     Select actr.is_principal, actr.actor_id, rr.id, rr.code
                     from dt_actor_roles actr, dt_roles rr
                     where actr.role_code = rr.code and
                         actr.is_principal = true
                 ) as v , dt_actors a
                              join dt_bank_accounts as b on a.id = b.actor_id
            where a.type != 'MANAGEMENT_COMPANY' and
    b.is_principal = true and
    a.id = v.actor_id

        </sql>
    </changeSet>
    <changeSet id="WP-2684-create-view-4" author="HoussemMoussa">
        <sql>
            create or replace view DT_business_summary as
            select a.*,
                   b.id as bank_account_id,
                   b.account_number as bank_account_account_number,
                   b.title as bank_account_title,
                   b.is_principal as bank_account_is_principal,
                   v.code role_code,
                   v.is_principal as role_is_principal

            from (
                     Select actr.is_principal, actr.actor_id, rr.id, rr.code
                     from dt_actor_roles actr, dt_roles rr
                     where actr.role_code = rr.code and
                         actr.is_principal = true
                 ) as v , dt_actors a
                              join dt_bank_accounts as b on a.id = b.actor_id
            where a.type = 'MANAGEMENT_COMPANY' and
                b.is_principal = true and
                a.id = v.actor_id

        </sql>
    </changeSet>
</databaseChangeLog>