<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="AhmedKHIARI (generated)" id="*************-1">
        <createTable tableName="dt_activities">
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_activities_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-2">
        <createTable tableName="dt_legal_categories">
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_legal_categories_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-3">
        <createTable tableName="dt_actor_phases_history">
            <column name="actor_id" type="BIGINT"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_actor_phases_history_pkey"/>
            </column>
            <column name="milestone" type="VARCHAR(255)"/>
            <column name="phase" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-4">
        <createTable tableName="dt_bank_branches">
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_bank_branches_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="address" type="VARCHAR(255)"/>
            <column name="city" type="VARCHAR(255)"/>
            <column name="country" type="VARCHAR(255)"/>
            <column name="domiciliation" type="VARCHAR(255)"/>
            <column name="interbank_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="postal_code" type="VARCHAR(255)"/>
            <column name="region" type="VARCHAR(255)"/>
            <column name="swift" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-5">
        <createTable tableName="dt_actor_settlement_means">
            <column name="actor_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="bank_account_id" type="BIGINT"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_actor_settlement_means_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="payment_method_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="payment_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="role_code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-6">
        <createTable tableName="dt_business_profiles">
            <column name="minimum_due_date" type="INTEGER"/>
            <column name="actor_id" type="BIGINT"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_business_profiles_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="calender" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="ficp_grouping_code" type="VARCHAR(255)"/>
            <column name="ics" type="VARCHAR(255)"/>
            <column name="issuer_number_internal_transfer" type="VARCHAR(255)"/>
            <column name="logo" type="VARCHAR(255)"/>
            <column name="national_issuer_number" type="VARCHAR(255)"/>
            <column name="tax_system" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-7">
        <createTable tableName="dt_business_settlement_means">
            <column name="actor_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="bank_account_id" type="BIGINT"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_business_settlement_means_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="payment_method_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="payment_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-8">
        <createTable tableName="dt_communication_means">
            <column name="end_date" type="date"/>
            <column name="last_contact_date" type="date"/>
            <column name="preferred" type="BOOLEAN"/>
            <column name="start_date" type="date"/>
            <column name="status" type="BOOLEAN"/>
            <column name="contact_id" type="BIGINT"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_communication_means_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="reference" type="VARCHAR(255)"/>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-9">
        <createTable tableName="dt_contacts">
            <column name="birth_day_date" type="date"/>
            <column name="preferred" type="BOOLEAN"/>
            <column name="start_at" type="date"/>
            <column name="status" type="BOOLEAN"/>
            <column name="actor_id" type="BIGINT"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_contacts_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="first_name" type="VARCHAR(255)"/>
            <column name="last_name" type="VARCHAR(255)"/>
            <column name="memo" type="VARCHAR(255)"/>
            <column name="quality" type="VARCHAR(255)"/>
            <column name="title" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-10">
        <createTable tableName="dt_contract_actor_payments">
            <column name="bank_account_id" type="BIGINT"/>
            <column name="contract_actor_id" type="BIGINT"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_contract_actor_payments_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="payment_method_id" type="BIGINT"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="start_date" type="VARCHAR(255)"/>
            <column name="type" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-11">
        <createTable tableName="dt_fiscal_year">
            <column name="closing_date" type="date"/>
            <column name="end_month" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="end_year" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="entry_date" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="number_of_months" type="INTEGER"/>
            <column name="recovery" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="start_month" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="start_year" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="actor_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="currency_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_fiscal_year_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="closing_type" type="VARCHAR(255)"/>
            <column name="exercice" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-12">
        <createTable tableName="dt_actors">
            <column name="company_creation_date" type="date"/>
            <column name="registration_date" type="date"/>
            <column name="activity_id" type="BIGINT"/>
            <column name="country_id" type="BIGINT"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_actors_pkey"/>
            </column>
            <column name="legal_category_id" type="BIGINT"/>
            <column name="milestone_id" type="BIGINT"/>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="phase_id" type="BIGINT"/>
            <column name="external_reference" type="VARCHAR(255)"/>
            <column name="feed_channel" type="VARCHAR(255)"/>
            <column name="memo" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="national_identity" type="VARCHAR(255)"/>
            <column name="reference" type="VARCHAR(255)"/>
            <column name="register_number" type="VARCHAR(255)"/>
            <column name="register_type" type="VARCHAR(255)"/>
            <column name="registration_country" type="VARCHAR(255)"/>
            <column name="short_name" type="VARCHAR(255)"/>
            <column name="tax_reference" type="VARCHAR(255)"/>
            <column name="type" type="VARCHAR(255)"/>
            <column name="vat" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-13">
        <createTable tableName="dt_addresses">
            <column name="end_date" type="date"/>
            <column name="headquarter" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="is_billing" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="is_delivery" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="is_live_here" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" type="date"/>
            <column name="subsidiary" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="actor_id" type="BIGINT"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_addresses_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="physical_adress_id" type="BIGINT"/>
            <column name="city" type="VARCHAR(255)"/>
            <column name="commune" type="VARCHAR(255)"/>
            <column name="country" type="VARCHAR(255)"/>
            <column name="distribution" type="VARCHAR(255)"/>
            <column name="entrance_building" type="VARCHAR(255)"/>
            <column name="nbr" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="reference" type="VARCHAR(255)"/>
            <column name="road_extension" type="VARCHAR(255)"/>
            <column name="road_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="summary" type="VARCHAR(255)"/>
            <column name="type" type="VARCHAR(255)"/>
            <column name="zip_code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-14">
        <createTable tableName="dt_contracts">
            <column name="agreement_as_service" type="BOOLEAN"/>
            <column name="agreement_date" type="date"/>
            <column name="amount_granted" type="FLOAT8"/>
            <column name="amount_rental_base" type="FLOAT8"/>
            <column name="deadline" type="date"/>
            <column name="duration" type="INTEGER"/>
            <column name="end_agreement_date" type="date"/>
            <column name="lessor_payment_date" type="date"/>
            <column name="lessor_selling_price" type="FLOAT8"/>
            <column name="refinancing_rate" type="FLOAT8"/>
            <column name="rental_date" type="date"/>
            <column name="request_date" type="date"/>
            <column name="sign_date" type="date"/>
            <column name="start_date" type="date"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_contracts_pkey"/>
            </column>
            <column name="milestone_code" type="BIGINT"/>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="phase_code" type="BIGINT"/>
            <column name="activity_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="business_reference" type="VARCHAR(255)"/>
            <column name="business_type" type="VARCHAR(255)"/>
            <column name="currency_code" type="VARCHAR(255)"/>
            <column name="external_reference" type="VARCHAR(255)"/>
            <column name="lessor_reference" type="VARCHAR(255)"/>
            <column name="market_type" type="VARCHAR(255)"/>
            <column name="product_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="reference" type="VARCHAR(255)"/>
            <column name="title" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-15">
        <createTable tableName="dt_countries">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_countries_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-16">
        <createTable tableName="dt_delegation">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_delegation_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
            <column name="short_label" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-17">
        <createTable tableName="dt_milestones">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_milestones_pkey"/>
            </column>
            <column name="phase_id" type="BIGINT"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-18">
        <createTable tableName="dt_phases">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_phases_pkey"/>
            </column>
            <column name="associated_to" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-19">
        <createTable tableName="dt_roles">
            <column name="is_client" type="BOOLEAN"/>
            <column name="is_exclusive" type="BOOLEAN"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_roles_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-20">
        <createTable tableName="dt_contract_actors">
            <column name="end_date" type="date"/>
            <column name="start_date" type="date"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_contract_actors_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="actor_reference" type="VARCHAR(255)"/>
            <column name="contract_reference" type="VARCHAR(255)"/>
            <column name="delegation_code" type="VARCHAR(255)"/>
            <column name="role_code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-21">
        <createTable tableName="dt_actor_roles">
            <column name="is_principal" type="BOOLEAN"/>
            <column name="actor_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_actor_roles_pkey"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_actor_roles_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="role_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_actor_roles_pkey"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-22">
        <addUniqueConstraint columnNames="code" constraintName="dt_activities_code_key" tableName="dt_activities"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-23">
        <addUniqueConstraint columnNames="code" constraintName="dt_legal_categories_code_key" tableName="dt_legal_categories"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-24">
        <createIndex indexName="phasehistory_actor_index" tableName="dt_actor_phases_history">
            <column name="actor_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-25">
        <createIndex indexName="interbank_code_index" tableName="dt_bank_branches">
            <column name="interbank_code"/>
        </createIndex>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-26">
        <addUniqueConstraint columnNames="interbank_code, deleted_at" constraintName="uk9i1u4shif2id5cqtfy8x55m07" tableName="dt_bank_branches"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-27">
        <addUniqueConstraint columnNames="payment_type, actor_id, role_code, deleted_at" constraintName="uk1py3d4k6my2xfttp2sageru5" tableName="dt_actor_settlement_means"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-28">
        <addUniqueConstraint columnNames="actor_id" constraintName="dt_business_profiles_actor_id_key" tableName="dt_business_profiles"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-29">
        <addUniqueConstraint columnNames="national_issuer_number, actor_id, issuer_number_internal_transfer, deleted_at" constraintName="uk404vaisf5426mdhi7xysxtnx2" tableName="dt_business_profiles"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-30">
        <addUniqueConstraint columnNames="payment_type, actor_id, deleted_at" constraintName="uk2ssbmeqi15dpis69rakowl8sr" tableName="dt_business_settlement_means"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-31">
        <addUniqueConstraint columnNames="contract_actor_id, type, deleted_at" constraintName="uk696esgkltc3pvfoxn8qi97ax7" tableName="dt_contract_actor_payments"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-32">
        <addUniqueConstraint columnNames="national_identity" constraintName="dt_actors_national_identity_key" tableName="dt_actors"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-33">
        <addUniqueConstraint columnNames="reference" constraintName="dt_actors_reference_key" tableName="dt_actors"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-34">
        <addUniqueConstraint columnNames="physical_adress_id" constraintName="dt_addresses_physical_adress_id_key" tableName="dt_addresses"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-35">
        <addUniqueConstraint columnNames="reference" constraintName="dt_addresses_reference_key" tableName="dt_addresses"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-36">
        <addUniqueConstraint columnNames="reference" constraintName="dt_contracts_reference_key" tableName="dt_contracts"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-37">
        <addUniqueConstraint columnNames="code" constraintName="dt_countries_code_key" tableName="dt_countries"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-38">
        <addUniqueConstraint columnNames="code" constraintName="dt_delegation_code_key" tableName="dt_delegation"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-39">
        <addUniqueConstraint columnNames="code" constraintName="dt_milestones_code_key" tableName="dt_milestones"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-40">
        <addUniqueConstraint columnNames="code" constraintName="dt_phases_code_key" tableName="dt_phases"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-41">
        <addUniqueConstraint columnNames="code" constraintName="dt_roles_code_key" tableName="dt_roles"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-42">
        <addUniqueConstraint columnNames="actor_reference, contract_reference, role_code, deleted_at" constraintName="ukns3h48xqu9tprehu6lkn78cb7" tableName="dt_contract_actors"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-43">
        <addUniqueConstraint columnNames="actor_id, role_id, deleted_at" constraintName="uktk5chsu6d0tj1veryfg7lpvaj" tableName="dt_actor_roles"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-44">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_actor_phases_history_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-45">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_actor_roles_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-46">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_actor_settlement_means_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-47">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_actors_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-48">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_addresses_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-49">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_bank_accounts_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-50">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_bank_branches_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-51">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_business_profiles_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-52">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_business_settlement_means_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-53">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_communication_means_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-54">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_companies_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-55">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_contacts_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-56">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_contract_actor_asset_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-57">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_contract_actor_payments_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-58">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_contract_actors_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-59">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_contracts_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-60">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_countries_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-61">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_currencies_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-62">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_delegation_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-63">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_fiscal_year_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-64">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_milestones_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-65">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_payment_methods_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-66">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_phases_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-67">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_physical_addresses_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-68">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_roles_seq" startValue="1"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-69">
        <createTable tableName="dt_bank_accounts">
            <column name="address_id" type="INTEGER"/>
            <column name="end_date" type="date"/>
            <column name="is_principal" type="BOOLEAN"/>
            <column name="registration_date" type="date"/>
            <column name="replacement" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" type="date"/>
            <column name="validity" type="date"/>
            <column name="actor_id" type="BIGINT"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_bank_accounts_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="account_number" type="VARCHAR(255)"/>
            <column name="bank_code" type="VARCHAR(255)"/>
            <column name="box_code" type="VARCHAR(255)"/>
            <column name="country" type="VARCHAR(255)"/>
            <column name="iban" type="VARCHAR(255)"/>
            <column name="iban_key" type="VARCHAR(255)"/>
            <column name="international_number" type="VARCHAR(255)"/>
            <column name="rib_key" type="VARCHAR(255)"/>
            <column name="rib_replacement" type="VARCHAR(255)"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="swift_code" type="VARCHAR(255)"/>
            <column name="title" type="VARCHAR(255)"/>
            <column name="type" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-70">
        <createTable tableName="dt_companies">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_companies_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="type" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-71">
        <createTable tableName="dt_contract_actor_asset">
            <column name="actor_id" type="BIGINT"/>
            <column name="asset_id" type="BIGINT"/>
            <column name="contract_id" type="BIGINT"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="end_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_contract_actor_asset_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="rental_id" type="BIGINT"/>
            <column name="start_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="note" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-72">
        <createTable tableName="dt_currencies">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_currencies_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-73">
        <createTable tableName="dt_payment_methods">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_payment_methods_pkey"/>
            </column>
            <column name="code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
            <column name="language" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-74">
        <createTable tableName="dt_physical_addresses">
            <column name="status" type="BOOLEAN"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_physical_addresses_pkey"/>
            </column>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="building_name" type="VARCHAR(255)"/>
            <column name="building_number" type="VARCHAR(255)"/>
            <column name="country" type="VARCHAR(255)"/>
            <column name="country_sub_division" type="VARCHAR(255)"/>
            <column name="department" type="VARCHAR(255)"/>
            <column name="district_name" type="VARCHAR(255)"/>
            <column name="floor" type="VARCHAR(255)"/>
            <column name="post_box" type="VARCHAR(255)"/>
            <column name="post_code" type="VARCHAR(255)"/>
            <column name="room" type="VARCHAR(255)"/>
            <column name="street_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="sub_department" type="VARCHAR(255)"/>
            <column name="town_location_name" type="VARCHAR(255)"/>
            <column name="town_name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-75">
        <addForeignKeyConstraint baseColumnNames="bank_account_id" baseTableName="dt_actor_settlement_means" constraintName="fk2rlcxpttpytc97itgnvrmx162" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_bank_accounts" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-76">
        <addForeignKeyConstraint baseColumnNames="contract_reference" baseTableName="dt_contract_actors" constraintName="fk2sdqaal2cnwdwiid0sy5h36fb" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="reference" referencedTableName="dt_contracts" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-77">
        <addForeignKeyConstraint baseColumnNames="physical_adress_id" baseTableName="dt_addresses" constraintName="fk30ra4ub1784jhme95y3ajwbwf" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_physical_addresses" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-78">
        <addForeignKeyConstraint baseColumnNames="phase_id" baseTableName="dt_milestones" constraintName="fk34wxy631v4flhk7e0ep6alknc" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_phases" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-79">
        <addForeignKeyConstraint baseColumnNames="milestone_code" baseTableName="dt_contracts" constraintName="fk40b57g378tpx3ymhg77khqku6" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_milestones" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-80">
        <addForeignKeyConstraint baseColumnNames="bank_account_id" baseTableName="dt_business_settlement_means" constraintName="fk4setcdttai1nl6icicaarftv2" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_bank_accounts" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-81">
        <addForeignKeyConstraint baseColumnNames="actor_id" baseTableName="dt_actor_roles" constraintName="fk6lw0k4g1o0dajglgfnbkjxxdv" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_actors" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-82">
        <addForeignKeyConstraint baseColumnNames="payment_method_id" baseTableName="dt_business_settlement_means" constraintName="fk7pg25bap9pkf36dwtsqv11xcl" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_payment_methods" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-83">
        <addForeignKeyConstraint baseColumnNames="actor_reference" baseTableName="dt_contract_actors" constraintName="fk87gdj356e98nko8knufjuv9x7" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="reference" referencedTableName="dt_actors" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-84">
        <addForeignKeyConstraint baseColumnNames="actor_id" baseTableName="dt_addresses" constraintName="fk8gkw44w03fkrgruiel2epec7v" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_actors" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-85">
        <addForeignKeyConstraint baseColumnNames="actor_id" baseTableName="dt_actor_settlement_means" constraintName="fk8jgatv3adcom1k9it445woaj" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_actors" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-86">
        <addForeignKeyConstraint baseColumnNames="bank_account_id" baseTableName="dt_contract_actor_payments" constraintName="fk9vrjyjasapyiuthxc0c0q8ucp" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_bank_accounts" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-87">
        <addForeignKeyConstraint baseColumnNames="phase_code" baseTableName="dt_contracts" constraintName="fkagmfqy1tw8jv9cm39ax54a4yx" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_phases" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-88">
        <addForeignKeyConstraint baseColumnNames="actor_id" baseTableName="dt_bank_accounts" constraintName="fkccydfpmxmech5fj90stpixp7g" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_actors" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-89">
        <addForeignKeyConstraint baseColumnNames="legal_category_id" baseTableName="dt_actors" constraintName="fkdgllc1c8home1nqkiaae11g6w" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_legal_categories" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-90">
        <addForeignKeyConstraint baseColumnNames="actor_id" baseTableName="dt_business_profiles" constraintName="fke7hf9e5jmvgibenraufdwh2ri" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_actors" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-91">
        <addForeignKeyConstraint baseColumnNames="contact_id" baseTableName="dt_communication_means" constraintName="fkemhjtxsem8cyvkn2u23m71egy" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_contacts" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-92">
        <addForeignKeyConstraint baseColumnNames="actor_id" baseTableName="dt_fiscal_year" constraintName="fkfie26k8869ux9nwyykqrconvo" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_actors" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-93">
        <addForeignKeyConstraint baseColumnNames="actor_id" baseTableName="dt_contacts" constraintName="fkgrjx65wvbhd79qtivoi6x9lhq" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_actors" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-94">
        <addForeignKeyConstraint baseColumnNames="activity_id" baseTableName="dt_actors" constraintName="fkhsn7jvubng0svi187e1tsjcnq" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_activities" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-95">
        <addForeignKeyConstraint baseColumnNames="currency_id" baseTableName="dt_fiscal_year" constraintName="fkkjpvwkokdc2uy5jqcgnq594f4" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_currencies" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-96">
        <addForeignKeyConstraint baseColumnNames="business_reference" baseTableName="dt_contracts" constraintName="fklx7vcbk3nabwhgqlyq24y9s6i" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="reference" referencedTableName="dt_actors" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-97">
        <addForeignKeyConstraint baseColumnNames="role_id" baseTableName="dt_actor_roles" constraintName="fkmghmbrcyye4efvx22fd76dmw1" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_roles" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-98">
        <addForeignKeyConstraint baseColumnNames="country_id" baseTableName="dt_actors" constraintName="fknjrkp8pdvbuy8wyx792qfpkua" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_countries" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-99">
        <addForeignKeyConstraint baseColumnNames="payment_method_id" baseTableName="dt_contract_actor_payments" constraintName="fko1ngilf4x7l054wxf8ubaddsm" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_payment_methods" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-100">
        <addForeignKeyConstraint baseColumnNames="delegation_code" baseTableName="dt_contract_actors" constraintName="fko6yswdcukpw1h58lio86qm0hu" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="code" referencedTableName="dt_delegation" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-101">
        <addForeignKeyConstraint baseColumnNames="payment_method_id" baseTableName="dt_actor_settlement_means" constraintName="fkovpkni54bpm42mmyqr06ymqdk" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_payment_methods" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-102">
        <addForeignKeyConstraint baseColumnNames="role_code" baseTableName="dt_contract_actors" constraintName="fkplqw7prwqtpde9yafje9l8111" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="code" referencedTableName="dt_roles" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-103">
        <addForeignKeyConstraint baseColumnNames="actor_id" baseTableName="dt_business_settlement_means" constraintName="fkr9jceumaakal61x1l97bek5bi" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_actors" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-104">
        <addForeignKeyConstraint baseColumnNames="contract_actor_id" baseTableName="dt_contract_actor_payments" constraintName="fkrbtwvia7nljibwyya3h78yc4w" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_contract_actors" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-105">
        <addForeignKeyConstraint baseColumnNames="phase_id" baseTableName="dt_actors" constraintName="fkrku2m6td0bguf3edpmaxsm32i" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_phases" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="*************-106">
        <addForeignKeyConstraint baseColumnNames="milestone_id" baseTableName="dt_actors" constraintName="fksltdbrtkb279uic8c5w9og43h" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="dt_milestones" validate="true"/>
    </changeSet>
</databaseChangeLog>
