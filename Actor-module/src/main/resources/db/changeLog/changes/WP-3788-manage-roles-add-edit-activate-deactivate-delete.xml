<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.5.xsd">

    <changeSet id="WP-3788-0" author="ahmedkhiari (generated)">
        <createTable tableName="dt_static_roles">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="code" type="varchar(255)">
                <constraints unique="true" nullable="false"/>
            </column>
            <column name="label" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="is_exclusive" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="is_client" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="associated_to" type="varchar(255)">
                <constraints nullable="false"/>
            </column>

            <!-- BaseEntity fields -->
            <column name="created_at" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="modified_at" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="deleted_at" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <createIndex indexName="idx_static_roles_code" tableName="dt_static_roles">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet id="WP-3788-0-1" author="ahmedkhiari (generated)">
        <dropColumn tableName="dt_static_roles" columnName="deleted_at"/>
        <addColumn tableName="dt_static_roles">
            <column name="deleted_at" type="datetime">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="WP-3788-1" author="ahmedkhiari (generated)">
        <sql>
            INSERT INTO dt_static_roles (id, code, label, is_exclusive, is_client, associated_to, created_at,
                                         modified_at)
            VALUES (1, 'ACHET', 'Buyer', false, false, 'ACTEUR', '2025-03-20 16:34:43.675067',
                    '2025-03-20 16:34:43.675067'),
                   (2, 'CLIENT', 'Client', true, true, 'ACTEUR', '2025-03-20 16:34:43.681737',
                    '2025-03-20 16:34:43.681737'),
                   (3, 'PARTEN', 'Partner', false, false, 'ACTEUR', '2025-03-20 16:34:43.686589',
                    '2025-03-20 16:34:43.686589'),
                   (4, 'APPORT', 'Contributor', false, false, 'ACTEUR', '2025-03-20 16:34:43.692948',
                    '2025-03-20 16:34:43.692948'),
                   (5, 'FOURN', 'Supplier', false, false, 'ACTEUR', '2025-03-20 16:34:43.702034',
                    '2025-03-20 16:34:43.702034'),
                   (6, 'GARANT', 'Guarantor', false, false, 'ACTEUR', '2025-03-20 16:34:43.710972',
                    '2025-03-20 16:34:43.710972'),
                   (7, 'OWNER', 'Owner', false, false, 'ACTEUR', '2025-03-20 16:34:43.718541',
                    '2025-03-20 16:34:43.718541'),
                   (8, 'AVOCAT', 'Lawyer', false, false, 'ACTEUR', '2025-03-20 16:34:43.725254',
                    '2025-03-20 16:34:43.725254'),
                   (9, 'BAIL', 'Lessor', false, false, 'ACTEUR', '2025-03-20 16:34:43.731454',
                    '2025-03-20 16:34:43.731454'),
                   (10, 'BROKER', 'Broker', false, false, 'ACTEUR', '2025-03-20 16:34:43.738332',
                    '2025-03-20 16:34:43.738332'),
                   (11, 'CREANC', 'Creditor', false, false, 'ACTEUR', '2025-03-20 16:34:43.744385',
                    '2025-03-20 16:34:43.744385'),
                   (12, 'DELEG', 'Delegate', false, false, 'ACTEUR', '2025-03-20 16:34:43.751042',
                    '2025-03-20 16:34:43.751042'),
                   (13, 'ENVEL', 'Envelope', false, false, 'ACTEUR', '2025-03-20 16:34:43.757737',
                    '2025-03-20 16:34:43.757737'),
                   (14, 'HOLDER', 'Holder', false, false, 'ACTEUR', '2025-03-20 16:34:43.765506',
                    '2025-03-20 16:34:43.765506'),
                   (15, 'HUISSIE', 'Bailiff', false, false, 'ACTEUR', '2025-03-20 16:34:43.771299',
                    '2025-03-20 16:34:43.771299'),
                   (16, 'NOTAIRE', 'Notary', false, false, 'ACTEUR', '2025-03-20 16:34:43.778120',
                    '2025-03-20 16:34:43.778120'),
                   (17, 'NOTOR', 'Public Official', false, false, 'ACTEUR', '2025-03-20 16:34:43.785291',
                    '2025-03-20 16:34:43.785291'),
                   (18, 'PRETEUR', 'Lender', false, false, 'ACTEUR', '2025-03-20 16:34:43.791829',
                    '2025-03-20 16:34:43.791829');
        </sql>
    </changeSet>

    <changeSet id="WP-3788-2" author="ahmedkhiari (generated)">
        <dropColumn tableName="dt_roles" columnName="is_exclusive"/>
        <dropColumn tableName="dt_roles" columnName="is_client"/>
        <dropColumn tableName="dt_roles" columnName="associated_to"/>
        <addColumn tableName="dt_roles">
            <column name="active" type="boolean" defaultValue="true">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <addColumn tableName="dt_roles">
            <column name="associated_to" type="varchar(255)"/>
        </addColumn>
        <addForeignKeyConstraint baseTableName="dt_roles" baseColumnNames="associated_to"
                                 constraintName="fk_roles_associated_to"
                                 referencedTableName="dt_static_roles" referencedColumnNames="code"/>
    </changeSet>
    <changeSet id="WP-3788-3" author="AhmedKHIARI (generated)">
        <addColumn tableName="dt_roles">
        <column name="created_at" type="datetime">
        </column>
        <column name="modified_at" type="datetime">
        </column>
        <column name="deleted_at" type="datetime">
        </column>
        </addColumn>
    </changeSet>
    <changeSet id="WP-3788-3-0" author="AhmedKHIARI (generated)">
        <sql>
            INSERT INTO public.dt_static_roles (id, code, label, is_exclusive, is_client, associated_to, created_at,
                                                modified_at, deleted_at)
            VALUES (20, 'RENT', 'Renter', false, false, 'ACTEUR', '2025-03-21 01:51:40.953976',
                    '2025-03-21 01:51:40.953976', null);
            INSERT INTO public.dt_static_roles (id, code, label, is_exclusive, is_client, associated_to, created_at,
                                                modified_at, deleted_at)
            VALUES (19, 'LESSOR', 'Lessor', false, false, 'ACTEUR', '2025-03-21 01:51:40.936832',
                    '2025-03-21 01:51:40.936832', null);
        </sql>
    </changeSet>
    <changeSet id="WP-3788-4" author="ahmedkhiari">
        <!-- Create temporary table for roles data -->
        <createTable tableName="temp_roles">
            <column name="id" type="INT"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
            <column name="createdAt" type="VARCHAR(255)"/>
            <column name="updatedAt" type="VARCHAR(255)"/>
            <column name="deletedAt" type="VARCHAR(255)"/>
            <column name="active" type="VARCHAR(50)"/>
            <column name="system_attribute" type="VARCHAR(50)"/>
            <column name="associated_to" type="VARCHAR(255)"/>
        </createTable>

        <!-- Load data from CSV into temporary table - store timestamps as strings -->
        <loadData
                file="roles.csv"
                relativeToChangelogFile="false"
                tableName="temp_roles"
                separator=","
                quotchar="&quot;"
                encoding="UTF-8">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="createdAt" type="STRING"/>
            <column name="updatedAt" type="STRING"/>
            <column name="deletedAt" type="STRING"/>
            <column name="active" type="STRING"/>
            <column name="system_attribute" type="STRING"/>
            <column name="associated_to" type="STRING"/>
        </loadData>

        <!-- SQL to update and merge data -->
        <sql>
            -- Create tracking table for processed IDs
            CREATE TEMPORARY TABLE processed_ids (id INT);

        -- Update existing records based on code match
            WITH updated_roles AS (
            UPDATE dt_roles dr
            SET
                label = r.label,
                active = CASE WHEN r.active = 'true' THEN true ELSE false END,
                associated_to = r.associated_to,
                created_at = to_timestamp(substring(r."createdAt", 1, 26), 'YYYY-MM-DD HH24:MI:SS.US'),
                modified_at = to_timestamp(substring(r."updatedAt", 1, 26), 'YYYY-MM-DD HH24:MI:SS.US'),
                deleted_at = CASE
                                 WHEN r."deletedAt" = '' THEN NULL
                                 ELSE to_timestamp(substring(r."deletedAt", 1, 26), 'YYYY-MM-DD HH24:MI:SS.US')
                    END,
                system_role = CASE WHEN r.system_attribute = 'true' THEN true ELSE false END
                FROM temp_roles r
            WHERE dr.code = r.code
                RETURNING dr.id
                )
            INSERT INTO processed_ids
            SELECT id FROM updated_roles;

            -- Insert new records
            WITH inserted_roles AS (
            INSERT INTO dt_roles (
                id, code, label, system_role, active, associated_to, created_at, modified_at, deleted_at
            )
            SELECT
                r.id,
                r.code,
                r.label,
                CASE WHEN r.system_attribute = 'true' THEN true ELSE false END as system_role,
                CASE WHEN r.active = 'true' THEN true ELSE false END as active,
                r.associated_to,
                to_timestamp(substring(r."createdAt", 1, 26), 'YYYY-MM-DD HH24:MI:SS.US') as created_at,
                to_timestamp(substring(r."updatedAt", 1, 26), 'YYYY-MM-DD HH24:MI:SS.US') as modified_at,
                CASE
                    WHEN r."deletedAt" = '' THEN NULL
                    ELSE to_timestamp(substring(r."deletedAt", 1, 26), 'YYYY-MM-DD HH24:MI:SS.US')
                    END as deleted_at
            FROM temp_roles r
            WHERE NOT EXISTS (
                SELECT 1 FROM dt_roles dr WHERE dr.code = r.code
            )
                RETURNING id
        )
            INSERT INTO processed_ids
            SELECT id FROM inserted_roles;

            -- Mark records as deleted if they don't exist in the processed list
            UPDATE dt_roles
            SET deleted_at = CURRENT_TIMESTAMP
            WHERE id NOT IN (SELECT id FROM processed_ids)
              AND deleted_at IS NULL;

            -- Clean up temporary tables
            DROP TABLE processed_ids;
            DROP TABLE temp_roles;
        </sql>
    </changeSet>
    <changeSet id="WP-3788-5" author="AhmedKHIARI (generated)">
        <sql>
            update dt_static_roles set associated_to = 'COMPANY' where code = 'RENT';
            update dt_static_roles set associated_to = 'ACTEURCOMPANY' where code = 'LESSOR';
        </sql>
    </changeSet>
    <changeSet id="WP-3788-6" author="HoussemMoussa">
        <renameColumn tableName="dt_roles" oldColumnName="associated_to" newColumnName="static_role_code"/>
    </changeSet>
</databaseChangeLog>