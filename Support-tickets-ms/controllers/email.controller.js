// email.controller.js
const { createTransport } = require("nodemailer");
const transporter = createTransport({
    host: "mail.infomaniak.com",
    port: 465,
    secure: true,
    auth: {
        user: "<EMAIL>",
        pass: "M%!d3eD66DUn$G",
    },
    debug: true,
    logger: true,
  
});
async function sendEmail(req, res) {
    try {
        const {  to, subject, body } = req.body;

        let info = await transporter.sendMail({
            from: "Customer Support " + "<<EMAIL>>",
            to: to,
            subject: subject,
            text: body,
            html: `<p>${body}</p>`,
        });

        
        res
            .status(200)
            .json({ message: "Email sent successfully", messageId: info.messageId });
    } catch (error) {
        console.error("Error sending email:", error);
        const responseBody = { error: { message: "Failed to send email" } };
        if (process.env.DEBUG === "true") {
            responseBody.error.debug = {
                detail: error.message || "Internal Server Error",
                source: error,
            };
        }
        res.status(500).json(responseBody);
    }
}

module.exports = {
    sendEmail,
};
