const db = require("../db");
const SupportMessageModel = db.SupportMessageModel;
const SupportRequestModel = db.SupportRequestModel;
const { Op } = require("sequelize");
const config = require("../config/app-config");

async function create(req, res) {
    try {
        if (!req.body.supportRequestId) {
            return res.status(400).json({ error: "supportRequestId is required" });
        }

        const supportRequest = await SupportRequestModel.findByPk(req.body.supportRequestId);
        if (!supportRequest) {
            return res.status(404).json({ error: "Support request not found" });
        }
        
        const supportMessage = await SupportMessageModel.create({
            ...req.body,
            attachment: req.filename || req.fileUrl || req.body.attachment || null
        });
       
        const createdMessage = await SupportMessageModel.findByPk(supportMessage.id, {
            include: [{ model: SupportRequestModel }]
        });

        res.status(201).json({ data: createdMessage });
    } catch (error) {
        console.error(error);
        const responseBody = { error: { message: "Internal Server Error" } };
        if (process.env.DEBUG === "true") {
            responseBody.error.debug = { detail: error.message || "Internal Server Error", source: error };
        }
        res.status(400).json(responseBody);
    }
}

async function search(req, res) {
    try {
        let whereCondition = {};
        const limit = req.query.limit ? parseInt(req.query.limit) : config.limit;
        const offset = req.query.offset ? parseInt(req.query.offset) * limit : config.offset * limit;
        const sortBy = req.query.sort_by || config.SortBy;
        const orderBy = req.query.order_by || config.OrderBy;
        Object.keys(req.query).forEach(key => {
            if (!["offset", "limit", "sort_by", "order_by"].includes(key)) {
                whereCondition[key] = { [Op.iLike]: `%${req.query[key]}%` };
            }
        });
  
        const [supportMessages, total_count] = await Promise.all([
            SupportMessageModel.findAll({
                order: [[sortBy, orderBy]],
                offset,
                limit,
                where: whereCondition,
                attributes: { include: ["supportRequestId"] }
            }),
            SupportMessageModel.count({ where: whereCondition })
        ]);
  
        if (!supportMessages || supportMessages.length === 0) {
            return res.status(404).send({ error: "Support messages not found" });
        }
        res.send({ data: supportMessages, total_count });
    } catch (error) {
        console.error(error);
        const responseBody = { error: { message: "Internal Server Error" } };
        if (process.env.DEBUG === "true") {
            responseBody.error.debug = { detail: error.message || "Internal Server Error", source: error };
        }
        res.status(500).json(responseBody);
    }
}

async function findOne(req, res) {
    try {
        const supportMessage = await SupportMessageModel.findByPk(req.params.id, {
            attributes: { include: ["supportRequestId"] }
        });
        if (!supportMessage) {
            return res.status(404).json({ error: "Support message not found" });
        }
        res.json({data:supportMessage});
    } catch (error) {
        console.error(error);
        const responseBody = { error: { message: "Internal Server Error" } };
        if (process.env.DEBUG === "true") {
            responseBody.error.debug = { detail: error.message || "Internal Server Error", source: error };
        }
        res.status(500).json(responseBody);
    }
}

async function update(req, res) {
    try {
        const [updated] = await SupportMessageModel.update({
            ...req.body,
            attachment: req.fileUrl || req.body.attachment
        }, {
            where: { id: req.params.id }
        });
        if (updated) {
            const updatedSupportMessage = await SupportMessageModel.findByPk(req.params.id);
            return res.json({data:updatedSupportMessage});
        }
        throw new Error("Support message not found");
    } catch (error){
        console.error(error);
        const responseBody = { error: { message: "Internal Server Error" } };
        if (process.env.DEBUG === "true") {
            responseBody.error.debug = { detail: error.message || "Internal Server Error", source: error };
        }
        res.status(400).json(responseBody);
    }
}

async function remove(req, res) {
    try {
        const deleted = await SupportMessageModel.destroy({
            where: { id: req.params.id }
        });
        if (deleted) {
            return res.status(204).send({
                data: {
                    message: `Resource with ID ${req.params.id} has been deleted successfully`,
                },
            });
        }
        throw new Error("Support message not found");
    } catch (error) {
        console.error(error);
        const responseBody = { error: { message: "Internal Server Error" } };
        if (process.env.DEBUG === "true") {
            responseBody.error.debug = { detail: error.message || "Internal Server Error", source: error };
        }
        res.status(400).json(responseBody);
    }
}

async function findAllBySupportTicket(req, res) {
    try {
        const supportRequestId = req.params.supportRequestId;
        
        const supportRequest = await SupportRequestModel.findByPk(supportRequestId);
        if (!supportRequest) {
            return res.status(404).json({ error: "Support request not found" });
        }

        const messages = await SupportMessageModel.findAll({
            where: { supportRequestId: supportRequestId },
            order: [["createdAt", "ASC"]]
        });

        if (messages.length === 0) {
            return res.status(404).json({ error: "No messages found for this support request" });
        }

        res.json({data:messages});
    } catch (error) {
        console.error(error);
        const responseBody = { error: { message: "Internal Server Error" } };
        if (process.env.DEBUG === "true") {
            responseBody.error.debug = { detail: error.message || "Internal Server Error", source: error };
        }
        res.status(500).json(responseBody);
    }
}

module.exports = {
    create,
    search,
    findOne,
    update,
    remove,
    findAllBySupportTicket
};