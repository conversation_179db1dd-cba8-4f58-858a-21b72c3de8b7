FROM public.ecr.aws/docker/library/maven:3-amazoncorretto-21 AS builder
WORKDIR /app
COPY pom.xml .
COPY src src
#cache busting.
RUN mvn package -P docker

# Use Eclipse Temurin JRE Alpine image for a lightweight runtime
FROM public.ecr.aws/docker/library/eclipse-temurin:21-jre-alpine AS runtime
# Install Infisical
RUN apk add --no-cache bash curl && curl -1sLf \
'https://dl.cloudsmith.io/public/infisical/infisical-cli/setup.alpine.sh' | bash \
&& apk add infisical
# Set working directory
WORKDIR /app
# Copy the Infisical script
COPY infisical.sh .
RUN chmod +x infisical.sh
# Copy the built application JAR file from the builder stage
COPY --from=builder /app/target/*.jar app.jar
# Expose the application port
EXPOSE 8810
# Run the application
CMD ["java", "-jar", "app.jar"]

