#!/bin/sh

# Load Infisical environment variables
export INFISICAL_CLIENT_ID=$(cat "$INFISICAL_CLIENT_ID")
export INFISICAL_CLIENT_SECRET=$(cat "$INFISICAL_CLIENT_SECRET")
export INFISICAL_PROJECT_ID=$(cat "$INFISICAL_PROJECT_ID")
export INFISICAL_API_URL=$(cat "$INFISICAL_API_URL")

# Debugging (remove in production)
echo "INFISICAL_API_URL: $INFISICAL_API_URL"

# Login to Infisical
export INFISICAL_TOKEN=$(infisical login --method=universal-auth \
    --client-id="$INFISICAL_CLIENT_ID" \
    --client-secret="$INFISICAL_CLIENT_SECRET" \
    --plain --silent)

exec infisical run --token "$INFISICAL_TOKEN" \
    --projectId "$INFISICAL_PROJECT_ID" \
    --env $INFISICAL_ENV \
    --path "/auth" \
    --domain "$INFISICAL_API_URL" \
    -- java -jar app.jar
