package com.datatricks.authmodule.auth;

import com.datatricks.authmodule.constraints.ValidPassword;
import com.datatricks.authmodule.user.Role;
import jakarta.validation.constraints.Email;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegisterRequest {

  private String firstname;
  private String lastname;
  @Email
  private String email;
  @ValidPassword
  private char[] password;
  private Role role;
}
