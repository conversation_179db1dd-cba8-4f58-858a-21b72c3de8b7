SET DEFINE OFF;
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (1,'EXO',null,null,null,'FR',2);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (2,'TVA',null,null,to_date('05-SEP-94','DD-MON-RR'),null,null,'FR',1);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (3,'TVAR',null,null,to_date('01-JAN-00','DD-MON-RR'),null,null,'FR',4);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (4,'TVAC',null,null,to_date('01-JAN-00','DD-MON-RR'),null,null,'FR',6);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (5,'TVADT',null,null,to_date('01-JAN-00','DD-MON-RR'),null,null,'FR',5);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (6,'TVASR',null,null,to_date('01-JAN-00','DD-MON-RR'),null,null,'FR',7);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (7,'TVASU',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'CH',8);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (8,'TVASU1',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'CH',9);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (9,'EXOSU',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'CH',10);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (10,'TVARSU',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'CH',11);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (11,'TVASSU',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'CH',12);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (12,'TVADE',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'DE',13);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (13,'TVADE1',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'DE',14);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (14,'EXODE',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'DE',15);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (15,'TVARDE',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'DE',16);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (16,'TVASDE',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'DE',17);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (17,'TVAIT',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'IT',18);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (18,'TVAIT1',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'IT',19);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (19,'EXOIT',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'IT',20);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (20,'TVARIT',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'IT',21);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (21,'TVASIT',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'IT',22);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (22,'TVAES',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'ES',23);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (23,'TVAES1',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'ES',24);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (24,'EXOES',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'ES',25);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (25,'TVARES',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'ES',26);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (26,'TVABE',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'BE',27);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (27,'TVABE1',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'BE',28);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (28,'EXOBE',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'BE',29);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (29,'TVARBE',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'BE',30);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (30,'TVALU',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'LU',31);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (31,'TVALU1',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'LU',32);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (32,'EXOLU',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'LU',33);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (33,'TVARLU',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'LU',34);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (34,'TVASLU',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'LU',35);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (35,'TVASLU1',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'LU',36);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (36,'TVASLU2',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'LU',37);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (37,'TVASLU3',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'LU',38);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (38,'TVAFR2',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'FR',39);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (39,'TVAR2',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'FR',40);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (40,'TVAFR3',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'FR',41);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (41,'TVABE2',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'BE',42);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (42,'TVALU22',null,null,to_date('24-NOV-20','DD-MON-RR'),null,null,'LU',44);
INSERT INTO "DTDB1"."TAXES" (ID, REFERENCE, CREATED_AT, DELETED_AT,START_DATE, END_DATE, MODIFIED_AT, COUNTRY, TAXE_RATE_ID)    values (43,'TVALUBE1',null,null,to_date('24-NOV-16','DD-MON-RR'),null,null,'BE',45);