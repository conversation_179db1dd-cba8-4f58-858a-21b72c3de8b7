scheduler:
  contract-check:
    cron: 0 0 0 * * ? # Runs every day at midnight
spring:
  application:
    name: ContractService
  services:
    auth:
      url: http://auth:8810/api/v1/
    actor:
      url: http://actor:8802/api/v1/
    engine:
      url: http://engine:8806/api/v1/
    invoice:
      url: http://invoice:8804/api/v1/
    asset:
      url: http://asset:8805/api/v1/  
  datasource:
    driver-class-name: org.postgresql.Driver
    hikari:
      maxLifetime: 270000
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  kafka:
    listener:
      ack-mode: MANUAL
    consumer:
      enable-auto-commit: false
      group-id: contract-group
      auto-offset-reset: earliest
      key-serializer: org.apache.kafka.common.serialization.StringDeserializer
      value-serializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        spring:
          json:
            trusted:
              packages: "*"
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      transaction-id-prefix: "tx-contract-producer-"
      properties:
        spring:
          json:
            add-type-info: true
    topic:
      produceTo:
        contract-topic: contract-topic
        contract-actor-topic: contract-actor-topic
        contract-accessory-topic: contract-accessory-topic
        contract-rental-topic: contract-rental-topic
        asset-contract-actor-inject-topic: asset-contract-actor-inject-topic
        contract-timetable-asset-topic: contract-timetable-asset-topic
      listenTo:
        actor-topic: actor-topic
        actor-address-topic: actor-address-topic
        actor-role-topic: actor-role-topic
        actor-bank-account-topic: actor-bank-account-topic
        asset-topic: asset-topic
        asset-contract-actor-topic: asset-contract-actor-topic
        asset-timetable-asset-topic: asset-timetable-asset-topic
        business-settlement-topic: business-settlement-topic
        actor-settlement-means-topic: actor-settlement-means-topic
        static-tables-topic: static-tables-topic
        invoice-contract-timetable-items-topic: invoice-contract-timetable-items-topic
      replicationFactor: 2
      numPartitions: 3
  data:
    redis:
      cluster:
        nodes:
          - ${REDIS_CLUSTER_HOST}:${REDIS_CLUSTER_PORT} 
  redis:
    ttl: 1     # time to live in days
server:
  port: 8803
  tomcat:
    additional-tld-skip-patterns: "*.tld"
api:
  response:
    activateDebugInfo: true
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
# local profile configuration
---
scheduler:
  contract-check:
  # cron: 0 * * * * ? # Runs every minute
    cron: 0 0 0 * * ? # Runs every day at midnight
spring:
  services:
    auth:
      url: http://localhost:8810/api/v1/
    actor:
      url: http://localhost:8802/api/v1/
    engine:
      url: http://localhost:8806/api/v1/
    invoice:
      url: http://localhost:8804/api/v1/
    asset:
      url: http://localhost:8805/api/v1/
  kafka:
    bootstrap-servers: localhost:9092
  config:
    activate:
      on-profile: local
  datasource:
    url: ********************************************
    username: postgres
    password: U6GjpKQpsrwjZI
  jpa:
    hibernate:
      naming:
        physical-strategy: com.datatricks.contracts.config.CustomHibernateNamingStrategy
      ddl-auto: validate
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  liquibase:
    change-log: classpath:db/changeLog/db.changelog-master.xml
    enabled: true
logging:
  level:
    root: info
redis:
    prefix: local

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
# docker local profile configuration
---
spring:
  kafka:
    bootstrap-servers: broker:9092
  config:
    activate:
      on-profile: docker-local
  datasource:
    url: *******************************************
    username: postgres
    password: U6GjpKQpsrwjZI
  jpa:
    hibernate:
      naming:
        physical-strategy: com.datatricks.contracts.config.CustomHibernateNamingStrategy
      ddl-auto: validate
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  liquibase:
    change-log: classpath:db/changeLog/db.changelog-master.xml
    enabled: true
logging:
  level:
    root: info
redis:
  prefix: docker-local
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
# DEV profile configuration
---
spring:
  kafka:
    bootstrap-servers: ${KAFKA_HOST}:${KAFKA_PORT}
    producer:
      transaction-id-prefix: "dev-tx-contract-producer-"
  config:
    activate:
      on-profile: dev
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?prepareThreshold=0
    username: ${DB_USER}
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      naming:
        physical-strategy: com.datatricks.contracts.config.CustomHibernateNamingStrategy
      ddl-auto: validate
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  liquibase:
    change-log: classpath:db/changeLog/db.changelog-master.xml
    enabled: true
logging:
  level:
    root: INFO
    logger.org.apache.kafka: info
  hibernate:
    show_sql: true
    format_sql: true
    use_sql_comments: true
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
redis:
    prefix: dev

# TEST profile configuration
---
spring:
  kafka:
    bootstrap-servers: ${KAFKA_HOST}:${KAFKA_PORT}
    producer:
      transaction-id-prefix: "test-tx-contract-producer-"
  config:
    activate:
      on-profile: test
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?prepareThreshold=0
    username: ${DB_USER}
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      naming:
        physical-strategy: com.datatricks.contracts.config.CustomHibernateNamingStrategy
      ddl-auto: validate
    open-in-view: false
    show-sql: false
    properties:
      hibernate:
        format_sql: true
  liquibase:
    change-log: classpath:db/changeLog/db.changelog-master.xml
    enabled: true
logging:
  level:
    root: info
    logger.org.apache.kafka: info
  pattern:
    console: '%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n'
redis:
    prefix: test

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

 # STAGING profile configuration
---
spring:
  kafka:
    bootstrap-servers: ${KAFKA_HOST}:${KAFKA_PORT}
    producer:
      transaction-id-prefix: "staging-tx-contract-producer-"
  config:
    activate:
      on-profile: staging
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?prepareThreshold=0
    username: ${DB_USER}
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      naming:
        physical-strategy: com.datatricks.contracts.config.CustomHibernateNamingStrategy
      ddl-auto: validate
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  liquibase:
    change-log: classpath:db/changeLog/db.changelog-master.xml
    enabled: true
logging:
  level:
    root: INFO
    logger.org.apache.kafka: info
  hibernate:
    show_sql: true
    format_sql: true
    use_sql_comments: true
redis:
    prefix: staging
