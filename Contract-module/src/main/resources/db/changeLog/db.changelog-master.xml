<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <property name="table.prefix" value="dt_"/>
    <include file="db/changeLog/changes/PostgresMigration-11-09-2024.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-activities-migration.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-countries-migration.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-legal-categories-migration.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-milestones-migration.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert-roles.migration.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2269-remove-sequences.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/000007-code-refactor-repayment.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/000002-add-foreign-key-currency-contract.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/insert_currencies.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2269-Remove-extra-tables.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2333-Remove-sequences.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/000008-add-title-to-rentals-retributions-accessories.xml"
             relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2373-update-phase.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/phase-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/role-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/milestones-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/currencies-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/0000013-create-time-tabele-views.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/delegations-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2641.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/activities-seeders.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2642-finance-elements-view.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2815-finance-elements-view-after-fix.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/WP-2852-seeds-payment-methods.sql" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2852-restore-national-identity.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-2853-rename-actor-reference-of-repayment.xml"
             relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-2783-add-activity-relation-with-contract.xml"
             relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/seeders/WP-2783-seeds-products.sql" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-2783-add-product-relation-with-contract.xml"
             relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-2783-modify-start-end-date.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-2962.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-2933.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-2921.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/00000014-remove-column-from-address.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-3096-add-missing-unique-constraint-to-contract-actor-address.xml"
             relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-3090.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-3386-fix-billing-parameters-bugs.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-3347.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-3405-update-timetable-views.xml" relativeToChangelogFile="false"/>
    <include file="db/changeLog/changes/WP-3354.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3228.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3516.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3513-batch-is-not-working-for-all-cases.xml"
             relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3540-add-invoice-reference-to-echeance.xml"
             relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3461.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3468.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3590-Fix-timetable-summary-view-duplicates.xml"
             relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3590-1-Fix-timetable-summary-view-duplicates.xml"
             relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3583.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3655-create-or-edit-a-line-rubric.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3606.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3686-Fix-batch-status-add-failed-time-table-items-to-view.xml"
             relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3708.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3704-backend-implement-management-interface-for-batch-part-2.xml"
             relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3759.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3465-backend-implement-synchronization-between-settings-module-and-backend-side.xml"
             relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3732.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3788-manage-roles-add-edit-activate-deactivate-delete.xml"
             relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3738.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/fix-bug-tax-rate.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3815-refactor-operations-nature-types-by-code.xml"
                relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-3722.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/WP-bug-tax-tax-rate.xml" relativeToChangelogFile="false"/>
    <include file="/db/changeLog/changes/KF-339.xml" relativeToChangelogFile="false"/>
</databaseChangeLog>
