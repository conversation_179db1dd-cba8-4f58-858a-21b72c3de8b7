<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="KF-207-1" author="houssem moussa">
        <modifyDataType tableName="dt_taxes" columnName="start_date" newDataType="timestamp"/>
        <modifyDataType tableName="dt_taxes" columnName="end_date" newDataType="timestamp"/>
    </changeSet>
    <changeSet id="KF-207-2" author="houssem moussa">
        <modifyDataType tableName="dt_taxerate" columnName="start_date" newDataType="timestamp"/>
        <modifyDataType tableName="dt_taxerate" columnName="end_date" newDataType="timestamp"/>
    </changeSet>
    <changeSet id="KF-207-3" author="houssem moussa">
        <sql>
            delete from dt_taxerate where true;
        </sql>
    </changeSet>
    <changeSet id="KF-207-4" author="houssem moussa">
        <sql>
            delete from dt_taxes where true;
        </sql>
    </changeSet>
    <changeSet id="KF-207-5" author="houssem moussa">
        <addColumn tableName="dt_taxerate">
            <column name="system_attribute" type="boolean" defaultValue="false"/>
        </addColumn>
    </changeSet>
    <changeSet id="KF-207-6" author="houssem moussa">
        <addColumn tableName="dt_taxes">
            <column name="system_attribute" type="boolean" defaultValue="false"/>
        </addColumn>
    </changeSet>
    <changeSet id="KF-207-7" author="houssem moussa">
        <addColumn tableName="dt_taxes">
            <column name="label" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet id="KF-207-10-load-new-taxs" author="houssem moussa">
        <loadData tableName="dt_taxes"
                  file="classpath:/dt_taxes_updated.csv"
                  separator=",">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="start_date" type="TIMESTAMP"/>
            <column name="end_date" type="TIMESTAMP"/>
            <column name="country" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="created_at" type="TIMESTAMP"/>
            <column name="modified_at" type="TIMESTAMP"/>
            <column name="deleted_at" type="TIMESTAMP"/>
            <column name="active" type="BOOLEAN"/>
            <column name="system_attribute" type="BOOLEAN"/>
        </loadData>
    </changeSet>
    <changeSet id="KF-207-3-load-new-tax-rate" author="houssem moussa">
        <loadData tableName="dt_taxerate"
                  file="classpath:dt_tax_rates_updated_1.csv"
                  separator=",">
            <column name="id" type="NUMERIC"/>
            <column name="tax_code" type="STRING"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="start_date" type="TIMESTAMP"/>
            <column name="end_date" type="TIMESTAMP"/>
            <column name="rate" type="NUMERIC"/>
            <column name="created_at" type="TIMESTAMP"/>
            <column name="modified_at" type="TIMESTAMP"/>
            <column name="deleted_at" type="TIMESTAMP"/>
            <column name="active" type="BOOLEAN"/>
            <column name="system_attribute" type="BOOLEAN"/>
        </loadData>
    </changeSet>
</databaseChangeLog>
