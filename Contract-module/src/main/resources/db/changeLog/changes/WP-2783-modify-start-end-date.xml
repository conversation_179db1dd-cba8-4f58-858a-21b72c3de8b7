<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
                   http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd
                   http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="WP-2783-start-end-date-1" author="Houssem">
        <modifyDataType tableName="${table.prefix}amortizations" columnName="start_date" newDataType="date"/>
    </changeSet>
    <changeSet id="WP-2783-start-end-date-2" author="Houssem">
            <modifyDataType tableName="${table.prefix}amortizations" columnName="end_date" newDataType="date"/>
    </changeSet>
    <changeSet id="WP-2783-start-end-date-3" author="Houssem">
            <modifyDataType tableName="${table.prefix}contract_actor_asset" columnName="start_date" newDataType="date"/>
    </changeSet>
    <changeSet id="WP-2783-start-end-date-4" author="Houssem">
            <modifyDataType tableName="${table.prefix}contract_actor_asset" columnName="end_date" newDataType="date"/>
    </changeSet>
    <changeSet id="WP-2783-start-end-date-5" author="Houssem">
            <modifyDataType tableName="${table.prefix}shippings" columnName="start_date" newDataType="date"/>
    </changeSet>
    <changeSet id="WP-2783-start-end-date-6" author="Houssem">
            <modifyDataType tableName="${table.prefix}shippings" columnName="end_date" newDataType="date"/>
    </changeSet>
    <changeSet id="WP-2783-start-end-date-7" author="Houssem">
            <modifyDataType tableName="${table.prefix}taxes" columnName="start_date" newDataType="date"/>
    </changeSet>
    <changeSet id="WP-2783-start-end-date-8" author="Houssem">
            <modifyDataType tableName="${table.prefix}taxes" columnName="end_date" newDataType="date"/>
    </changeSet>
    <changeSet id="WP-2783-start-end-date-9" author="Houssem">
            <modifyDataType tableName="${table.prefix}taxerate" columnName="start_date" newDataType="date"/>
    </changeSet>
    <changeSet id="WP-2783-start-end-date-10" author="Houssem">
            <modifyDataType tableName="${table.prefix}taxerate" columnName="end_date" newDataType="date"/>
    </changeSet>
    <changeSet id="WP-2783-start-end-date-11" author="Houssem">
        <modifyDataType tableName="${table.prefix}contract_actor_payments" columnName="start_date" newDataType="date"/>
    </changeSet>
</databaseChangeLog>