
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="AhmedKHIARI (generated)" id="WP-2921-0">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_management_mandates_seq" startValue="1"/>
    </changeSet>

    <changeSet author="AhmedKHIARI (generated)" id="WP-2921-1">
        <createTable tableName="dt_management_mandates">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_management_mandates_pkey"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="label" type="VARCHAR(255)"/>
            <column name="occult" type="BOOLEAN"/>
            <column name="reference" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <changeSet id="WP-2921-2" author="AhmedKHIARI">
        <sql>
            ALTER TABLE dt_management_mandates
                ADD CONSTRAINT unique_reference
                    UNIQUE NULLS NOT DISTINCT (reference);
        </sql>
    </changeSet>

    <changeSet author="AhmedKHIARI (generated)" id="WP-2921-3">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="dt_lease_transfer_seq" startValue="1"/>
    </changeSet>

    <changeSet author="AhmedKHIARI (generated)" id="WP-2921-4">
        <createTable tableName="dt_lease_transfer">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="dt_lease_transfer_pkey"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="modified_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="lessor_name" type="VARCHAR(255)"/>
            <column name="lessor_reference" type="VARCHAR(255)"/>
            <column name="price" type="FLOAT8"/>
            <column name="transfer_type" type="VARCHAR(255)"/>
            <column name="contract_reference" type="VARCHAR(255)"/>
            <column name="management_mandate_reference" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <changeSet author="AhmedKHIARI (generated)" id="WP-2921-5">
        <addForeignKeyConstraint baseColumnNames="contract_reference" baseTableName="dt_lease_transfer" constraintName="fkmsqttrwf8v8gkkdhr6elncdn6" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="reference" referencedTableName="dt_contracts" validate="true"/>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="WP-2921-6">
        <addForeignKeyConstraint baseColumnNames="management_mandate_reference" baseTableName="dt_lease_transfer" constraintName="fkob8yx8k20qw87wa7yd33fxbt6" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="reference" referencedTableName="dt_management_mandates" validate="true"/>
    </changeSet>
</databaseChangeLog>