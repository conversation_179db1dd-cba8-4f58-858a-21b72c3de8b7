<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="WP-3461-1" author="HoussemMoussa">
        <sql>
            CREATE OR REPLACE VIEW dt_contract_summary AS
            SELECT con.agreement_as_service,
                   con.agreement_date,
                   con.amount_granted,
                   con.amount_rental_base,
                   con.deadline,
                   con.duration,
                   con.end_agreement_date,
                   con.lessor_payment_date,
                   con.lessor_selling_price,
                   con.refinancing_rate,
                   con.rental_date,
                   con.request_date,
                   con.sign_date,
                   con.start_date,
                   con.created_at,
                   con.deleted_at,
                   con.id,
                   con.milestone_code,
                   con.modified_at,
                   con.phase_code,
                   con.activity_code,
                   con.business_reference,
                   con.business_type,
                   con.currency_code,
                   con.external_reference,
                   con.market_type,
                   con.product_code,
                   con.reference,
                   con.title,
                   c1.supplier_reference,
                   c1.supplier_id,
                   c1.supplier_name,
                   c2.lessor_reference,
                   c2.lessor_id,
                   c2.lessor_name
            FROM dt_contracts con
                     LEFT JOIN (
                SELECT c.reference, ca.actor_reference supplier_reference, ca.role_code, a.name AS supplier_name, a.id AS supplier_id
                FROM dt_contracts c
                         LEFT JOIN dt_contract_actors ca ON c.reference = ca.contract_reference
                         JOIN dt_actors a ON ca.actor_reference = a.reference
                WHERE ca.role_code = 'FOURN'
            ) AS c1 ON con.reference = c1.reference
                     LEFT JOIN (
                SELECT c.reference, ca.actor_reference lessor_reference, ca.role_code, a.name AS lessor_name, a.id AS lessor_id
                FROM dt_contracts c
                         LEFT JOIN dt_contract_actors ca ON c.reference = ca.contract_reference
                         JOIN dt_actors a ON ca.actor_reference = a.reference
                WHERE ca.role_code = 'LESSOR'
            ) AS c2 ON con.reference = c2.reference;
        </sql>
    </changeSet>

</databaseChangeLog>