<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.5.xsd">

    <changeSet id="WP-3788-0" author="ahmedkhiari (generated)">
        <createTable tableName="dt_static_roles">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="code" type="varchar(255)">
                <constraints unique="true" nullable="false"/>
            </column>
            <column name="label" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="is_exclusive" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="is_client" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="associated_to" type="varchar(255)">
                <constraints nullable="false"/>
            </column>

            <!-- BaseEntity fields -->
            <column name="created_at" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="modified_at" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="deleted_at" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <createIndex indexName="idx_static_roles_code" tableName="dt_static_roles">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet id="WP-3788-0-1" author="ahmedkhiari (generated)">
        <dropColumn tableName="dt_static_roles" columnName="deleted_at"/>
        <addColumn tableName="dt_static_roles">
            <column name="deleted_at" type="datetime">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="WP-3788-1" author="ahmedkhiari (generated)">
        <sql>
            INSERT INTO dt_static_roles (id, code, label, is_exclusive, is_client, associated_to, created_at,
                                         modified_at)
            VALUES (1, 'ACHET', 'Buyer', false, false, 'ACTEUR', '2025-03-20 16:34:43.675067',
                    '2025-03-20 16:34:43.675067'),
                   (2, 'CLIENT', 'Client', true, true, 'ACTEUR', '2025-03-20 16:34:43.681737',
                    '2025-03-20 16:34:43.681737'),
                   (3, 'PARTEN', 'Partner', false, false, 'ACTEUR', '2025-03-20 16:34:43.686589',
                    '2025-03-20 16:34:43.686589'),
                   (4, 'APPORT', 'Contributor', false, false, 'ACTEUR', '2025-03-20 16:34:43.692948',
                    '2025-03-20 16:34:43.692948'),
                   (5, 'FOURN', 'Supplier', false, false, 'ACTEUR', '2025-03-20 16:34:43.702034',
                    '2025-03-20 16:34:43.702034'),
                   (6, 'GARANT', 'Guarantor', false, false, 'ACTEUR', '2025-03-20 16:34:43.710972',
                    '2025-03-20 16:34:43.710972'),
                   (7, 'OWNER', 'Owner', false, false, 'ACTEUR', '2025-03-20 16:34:43.718541',
                    '2025-03-20 16:34:43.718541'),
                   (8, 'AVOCAT', 'Lawyer', false, false, 'ACTEUR', '2025-03-20 16:34:43.725254',
                    '2025-03-20 16:34:43.725254'),
                   (9, 'BAIL', 'Lessor', false, false, 'ACTEUR', '2025-03-20 16:34:43.731454',
                    '2025-03-20 16:34:43.731454'),
                   (10, 'BROKER', 'Broker', false, false, 'ACTEUR', '2025-03-20 16:34:43.738332',
                    '2025-03-20 16:34:43.738332'),
                   (11, 'CREANC', 'Creditor', false, false, 'ACTEUR', '2025-03-20 16:34:43.744385',
                    '2025-03-20 16:34:43.744385'),
                   (12, 'DELEG', 'Delegate', false, false, 'ACTEUR', '2025-03-20 16:34:43.751042',
                    '2025-03-20 16:34:43.751042'),
                   (13, 'ENVEL', 'Envelope', false, false, 'ACTEUR', '2025-03-20 16:34:43.757737',
                    '2025-03-20 16:34:43.757737'),
                   (14, 'HOLDER', 'Holder', false, false, 'ACTEUR', '2025-03-20 16:34:43.765506',
                    '2025-03-20 16:34:43.765506'),
                   (15, 'HUISSIE', 'Bailiff', false, false, 'ACTEUR', '2025-03-20 16:34:43.771299',
                    '2025-03-20 16:34:43.771299'),
                   (16, 'NOTAIRE', 'Notary', false, false, 'ACTEUR', '2025-03-20 16:34:43.778120',
                    '2025-03-20 16:34:43.778120'),
                   (17, 'NOTOR', 'Public Official', false, false, 'ACTEUR', '2025-03-20 16:34:43.785291',
                    '2025-03-20 16:34:43.785291'),
                   (18, 'PRETEUR', 'Lender', false, false, 'ACTEUR', '2025-03-20 16:34:43.791829',
                    '2025-03-20 16:34:43.791829');
        </sql>
    </changeSet>

    <changeSet id="WP-3788-2" author="ahmedkhiari (generated)">
        <dropView viewName="DT_TIMETABLES_SUMMARY_VIEW_SEPARATE_BILLING"/>
        <dropView viewName="DT_TIMETABLES_SUMMARY_VIEW_NOT_SEPARATE_BILLING"/>
        <dropView viewName="DT_TIMETABLES_SUMMARY_VIEW"/>
        <dropColumn tableName="dt_roles" columnName="is_exclusive"/>
        <dropColumn tableName="dt_roles" columnName="is_client"/>
        <dropColumn tableName="dt_roles" columnName="associated_to"/>
        <addColumn tableName="dt_roles">
            <column name="active" type="boolean" defaultValue="true">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <addColumn tableName="dt_roles">
            <column name="associated_to" type="varchar(255)"/>
        </addColumn>
        <addForeignKeyConstraint baseTableName="dt_roles" baseColumnNames="associated_to"
                                 constraintName="fk_roles_associated_to"
                                 referencedTableName="dt_static_roles" referencedColumnNames="code"/>
    </changeSet>
    <changeSet id="WP-3788-3" author="AhmedKHIARI (generated)">
        <addColumn tableName="dt_roles">
        <column name="created_at" type="datetime">
        </column>
        <column name="modified_at" type="datetime">
        </column>
        <column name="deleted_at" type="datetime">
        </column>
        </addColumn>
    </changeSet>
    <changeSet id="WP-3788-3-0" author="AhmedKHIARI (generated)">
        <sql>
            INSERT INTO public.dt_static_roles (id, code, label, is_exclusive, is_client, associated_to, created_at,
                                                modified_at, deleted_at)
            VALUES (20, 'RENT', 'Renter', false, false, 'ACTEUR', '2025-03-21 01:51:40.953976',
                    '2025-03-21 01:51:40.953976', null);
            INSERT INTO public.dt_static_roles (id, code, label, is_exclusive, is_client, associated_to, created_at,
                                                modified_at, deleted_at)
            VALUES (19, 'LESSOR', 'Lessor', false, false, 'ACTEUR', '2025-03-21 01:51:40.936832',
                    '2025-03-21 01:51:40.936832', null);
        </sql>
    </changeSet>
    <changeSet id="WP-3788-4" author="ahmedkhiari">
        <!-- Create temporary table for roles data -->
        <createTable tableName="temp_roles">
            <column name="id" type="INT"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
            <column name="createdAt" type="VARCHAR(255)"/>
            <column name="updatedAt" type="VARCHAR(255)"/>
            <column name="deletedAt" type="VARCHAR(255)"/>
            <column name="active" type="VARCHAR(50)"/>
            <column name="system_attribute" type="VARCHAR(50)"/>
            <column name="associated_to" type="VARCHAR(255)"/>
        </createTable>

        <!-- Load data from CSV into temporary table - store timestamps as strings -->
        <loadData
                file="roles.csv"
                relativeToChangelogFile="false"
                tableName="temp_roles"
                separator=","
                quotchar="&quot;"
                encoding="UTF-8">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="createdAt" type="STRING"/>
            <column name="updatedAt" type="STRING"/>
            <column name="deletedAt" type="STRING"/>
            <column name="active" type="STRING"/>
            <column name="system_attribute" type="STRING"/>
            <column name="associated_to" type="STRING"/>
        </loadData>

        <!-- SQL to update and merge data -->
        <sql>
            -- Create tracking table for processed IDs
            CREATE TEMPORARY TABLE processed_ids (id INT);

        -- Update existing records based on code match
            WITH updated_roles AS (
            UPDATE dt_roles dr
            SET
                label = r.label,
                active = CASE WHEN r.active = 'true' THEN true ELSE false END,
                associated_to = r.associated_to,
                created_at = to_timestamp(substring(r."createdAt", 1, 26), 'YYYY-MM-DD HH24:MI:SS.US'),
                modified_at = to_timestamp(substring(r."updatedAt", 1, 26), 'YYYY-MM-DD HH24:MI:SS.US'),
                deleted_at = CASE
                                 WHEN r."deletedAt" = '' THEN NULL
                                 ELSE to_timestamp(substring(r."deletedAt", 1, 26), 'YYYY-MM-DD HH24:MI:SS.US')
                    END,
                system_role = CASE WHEN r.system_attribute = 'true' THEN true ELSE false END
                FROM temp_roles r
            WHERE dr.code = r.code
                RETURNING dr.id
                )
            INSERT INTO processed_ids
            SELECT id FROM updated_roles;

            -- Insert new records
            WITH inserted_roles AS (
            INSERT INTO dt_roles (
                id, code, label, system_role, active, associated_to, created_at, modified_at, deleted_at
            )
            SELECT
                r.id,
                r.code,
                r.label,
                CASE WHEN r.system_attribute = 'true' THEN true ELSE false END as system_role,
                CASE WHEN r.active = 'true' THEN true ELSE false END as active,
                r.associated_to,
                to_timestamp(substring(r."createdAt", 1, 26), 'YYYY-MM-DD HH24:MI:SS.US') as created_at,
                to_timestamp(substring(r."updatedAt", 1, 26), 'YYYY-MM-DD HH24:MI:SS.US') as modified_at,
                CASE
                    WHEN r."deletedAt" = '' THEN NULL
                    ELSE to_timestamp(substring(r."deletedAt", 1, 26), 'YYYY-MM-DD HH24:MI:SS.US')
                    END as deleted_at
            FROM temp_roles r
            WHERE NOT EXISTS (
                SELECT 1 FROM dt_roles dr WHERE dr.code = r.code
            )
                RETURNING id
        )
            INSERT INTO processed_ids
            SELECT id FROM inserted_roles;

            -- Mark records as deleted if they don't exist in the processed list
            UPDATE dt_roles
            SET deleted_at = CURRENT_TIMESTAMP
            WHERE id NOT IN (SELECT id FROM processed_ids)
              AND deleted_at IS NULL;

            -- Clean up temporary tables
            DROP TABLE processed_ids;
            DROP TABLE temp_roles;
        </sql>
    </changeSet>
    <changeSet id="WP-3788-5" author="AhmedKHIARI (generated)">
        <sql>
            update dt_static_roles set associated_to = 'COMPANY' where code = 'RENT';
            update dt_static_roles set associated_to = 'ACTEURCOMPANY' where code = 'LESSOR';
        </sql>
    </changeSet>
    <changeSet id="WP-3788-6" author="ahmed khiari">
        <sql>
            CREATE
            OR REPLACE VIEW DT_TIMETABLES_SUMMARY_VIEW AS
WITH principal_bank_accounts AS (
    SELECT ACTOR_ID, IBAN
    FROM DT_BANK_ACCOUNTS
    WHERE DELETED_AT IS NULL AND IS_PRINCIPAL = TRUE
),
     filtered_business_payments AS (
         SELECT *
         FROM DT_SETTLEMENT_MEANS
         WHERE DELETED_AT IS NULL
     ),
     filtered_bank_accounts AS (
         SELECT *
         FROM DT_BANK_ACCOUNTS
         WHERE DELETED_AT IS NULL
     ),
     filtered_roles AS (
         SELECT *
         FROM DT_ROLES
     )
            SELECT DISTINCT  company.REFERENCE                                                            AS company_reference,
                             company.NAME                                                                 AS company_name,
                             actor.REFERENCE                                                              AS actor_reference,
                             actor.NAME                                                                   AS actor_name,
                             actor.ID                                                                     AS actor_id,
                             c.REFERENCE                                                                  AS contract_reference,
                             c.id                                                                         AS contract_id,
                             company.ID                                                                   AS company_id,
                             c.ACTIVITY_CODE                                                              AS contract_activity_code,
                             c.PRODUCT_CODE                                                               AS contract_product_code,
                             T.STATUS                                                                     AS timetable_status,
                             Ti.STATUS                                                                    AS timetable_item_status,
                             --L.ID                                                                         AS level_id,
                             Ti.ID,
                             Ti.DEPRECIATION,
                             Ti.DUE_DATE,
                             Ti.END_DATE,
                             Ti.INTEREST,
                             Ti.NOMINAL_RATE,
                             Ti.RATE,
                             Ti.RENT,
                             Ti.RESIDUAL_VALUE,
                             Ti.START_DATE,
                             Ti.TAX_AMOUNT,
                             Ti.amortization,
                             Ti.unpaid,
                             Ti.TIMETABLE_ID,
                             ROL.CODE                                                                     AS role_code,
                             COALESCE(B.IBAN, P.IBAN)                                                     AS iban,
                             (SELECT ID
                              FROM DT_ADDRESSES a
                              WHERE a.ACTOR_ID = actor.ID
                                  FETCH FIRST 1 ROWS ONLY)                                                AS client_address_id,
                             COALESCE(R.SEPARATE_INVOICE, A.SEPARATE_INVOICE, RE.SEPARATE_INVOICE, FALSE) AS is_separate_billing,
                             CASE
                                 WHEN R.ID IS NOT NULL THEN R.line_type_code
                                 WHEN A.ID IS NOT NULL THEN A.line_type_code
                                 WHEN RE.ID IS NOT NULL THEN RE.line_type_code
                                 ELSE NULL
                                 END                                                                      AS line_type_code,
                             CASE
                                 WHEN R.ID IS NOT NULL THEN R.TAX_RATE
                                 WHEN A.ID IS NOT NULL THEN A.TAX_RATE
                                 WHEN RE.ID IS NOT NULL THEN RE.TAX_RATE
                                 ELSE NULL
                                 END                                                                      AS tax,
                             CASE
                                 WHEN R.ID IS NOT NULL THEN R.TAX
                                 WHEN A.ID IS NOT NULL THEN A.TAX
                                 WHEN RE.ID IS NOT NULL THEN RE.TAX
                                 ELSE NULL
                                 END                                                                      AS tax_code,
                             Ti.DELETED_AT,
                             Ti.CREATED_AT,
                             Ti.MODIFIED_AT
            FROM DT_TIMETABLE_ITEMS Ti
                     JOIN DT_TIMETABLES T ON T.ID = Ti.TIMETABLE_ID AND T.DELETED_AT IS NULL
                --JOIN DT_LEVELS L ON L.TIMETABLE_ID = T.ID AND L.DELETED_AT IS NULL
                     LEFT JOIN DT_RENTALS R ON R.TIMETABLE_ID = T.ID AND R.DELETED_AT IS NULL
                     LEFT JOIN DT_ACCESSORIES A ON A.TIMETABLE_ID = T.ID AND A.DELETED_AT IS NULL
                     LEFT JOIN DT_RETRIBUTIONS RE ON RE.TIMETABLE_ID = T.ID AND RE.DELETED_AT IS NULL
                     JOIN DT_CONTRACT_ACTORS Ca
                          ON Ca.ID = COALESCE(R.CONTRACT_ACTOR_ID, A.CONTRACT_ACTOR_ID, RE.CONTRACT_ACTOR_ID) AND
                             Ca.DELETED_AT IS NULL
                     JOIN DT_CONTRACTS C ON C.REFERENCE = Ca.CONTRACT_REFERENCE AND C.DELETED_AT IS NULL
                     JOIN DT_ACTORS company ON company.REFERENCE = C.BUSINESS_REFERENCE AND company.DELETED_AT IS NULL
                     JOIN DT_ACTORS actor ON actor.REFERENCE = Ca.ACTOR_REFERENCE AND actor.DELETED_AT IS NULL
                     LEFT JOIN filtered_business_payments DBP
                               ON company.reference = DBP.ACTOR_reference AND DBP.PAYMENT_TYPE = 'Encashment'
                     LEFT JOIN filtered_bank_accounts B ON B.ID = DBP.BANK_ACCOUNT_ID
                     LEFT JOIN principal_bank_accounts P ON P.ACTOR_ID = company.ID
                     JOIN filtered_roles ROL ON ROL.CODE = Ca.ROLE_CODE
            WHERE (Ti.STATUS IS NULL OR Ti.STATUS IN ('INITIALIZED', 'MARKED_FOR_FAILED', 'MARKED_FOR_COMPLETION', 'IN_PROGRESS'))
              AND Ti.RENT != 0 -- ensure that we don't get the extra items (debut + end)
  AND Ti.DELETED_AT IS NULL;
        </sql>
        <sql>
            CREATE
            OR REPLACE VIEW DT_TIMETABLES_SUMMARY_VIEW_SEPARATE_BILLING AS
            select *
            from DT_TIMETABLES_SUMMARY_VIEW
            where IS_SEPARATE_BILLING = true;
        </sql>
        <sql>
            CREATE
            OR REPLACE VIEW DT_TIMETABLES_SUMMARY_VIEW_NOT_SEPARATE_BILLING AS
            select *
            from DT_TIMETABLES_SUMMARY_VIEW
            where IS_SEPARATE_BILLING = false;
        </sql>
    </changeSet>
    <changeSet id="WP-3788-6" author="HoussemMoussa">
        <renameColumn tableName="dt_roles" oldColumnName="associated_to" newColumnName="static_role_code"/>
    </changeSet>
</databaseChangeLog>