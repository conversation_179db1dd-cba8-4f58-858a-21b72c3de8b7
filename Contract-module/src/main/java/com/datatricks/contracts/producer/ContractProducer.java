package com.datatricks.contracts.producer;

import com.datatricks.contracts.exception.BusinessException;
import com.datatricks.contracts.model.*;
import com.datatricks.kafkacommondomain.enums.OperationType;
import com.datatricks.kafkacommondomain.model.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ContractProducer {

    @Value("${spring.kafka.topic.produceTo.contract-topic}")
    private String contractTopic;

    @Value("${spring.kafka.topic.produceTo.contract-actor-topic}")
    private String contractActorTopic;

    @Value("${spring.kafka.topic.produceTo.contract-accessory-topic}")
    private String contractAccessoryTopic;

    @Value("${spring.kafka.topic.produceTo.contract-rental-topic}")
    private String contractRentalsTopic;

    @Value("${spring.kafka.topic.produceTo.asset-contract-actor-inject-topic}")
    private String contractActorAssetTopic;

    @Value("${spring.kafka.topic.produceTo.contract-timetable-asset-topic}")
    private String contractTimetableAssetTopic;

    private final KafkaTemplate<String, String > kafkaTemplate;

    private final ObjectMapper objectMapper;

    private static final String ERRORMESSAGE = "Error while processing message ";

    private final ModelMapper modelMapper;
    private final String KAFKAERROR = "DATA-SYNC-ERROR";

    public ContractProducer(KafkaTemplate<String, String> kafkaTemplate, ObjectMapper objectMapper, ModelMapper modelMapper) {
        this.kafkaTemplate = kafkaTemplate;
        this.objectMapper = objectMapper;
        this.modelMapper = modelMapper;
    }

    public void sendContractMessage(Contract contract, OperationType operationType, String moduleName) throws BusinessException {
        ContractStreamDto contractDto = modelMapper.map(contract, ContractStreamDto.class);
        contractDto.setCreatedAt(contract.getCreatedAt());
        contractDto.setModifiedAt(contract.getModifiedAt());
        contractDto.setDeletedAt(contract.getDeletedAt());


        KafkaMessage<ContractStreamDto> kafkaMessage = KafkaMessage.<ContractStreamDto>builder()
                .operation(operationType)
                .module(moduleName)
                .data(contractDto)
                .className("Contract")
                .build();

        try {
            kafkaTemplate.send(contractTopic, objectMapper.writeValueAsString(kafkaMessage));
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    public void sendContractActorMessage(ContractActor contractActor, OperationType operationType, String moduleName) throws BusinessException {
        var contractActorDto = modelMapper.map(contractActor, ContractActorDto.class);
        contractActorDto.setCreatedAt(contractActor.getCreatedAt());
        contractActorDto.setModifiedAt(contractActor.getModifiedAt());
        contractActorDto.setDeletedAt(contractActor.getDeletedAt());

        KafkaMessage<ContractActorDto> kafkaMessage = KafkaMessage.<ContractActorDto>builder()
                .operation(operationType)
                .module(moduleName)
                .data(contractActorDto)
                .build();

        try {
            kafkaTemplate.send(contractActorTopic, objectMapper.writeValueAsString(kafkaMessage));
        } catch (JsonProcessingException e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    public void sendContractAccessoryMessage(Accessory accessory, OperationType operationType, String moduleName) throws BusinessException {
        AccessoryDto accessoryDto = modelMapper.map(accessory, AccessoryDto.class);
        accessoryDto.setCreatedAt(accessory.getCreatedAt());
        accessoryDto.setModifiedAt(accessory.getModifiedAt());
        accessoryDto.setDeletedAt(accessory.getDeletedAt());

        KafkaMessage<AccessoryDto> kafkaMessage = KafkaMessage.<AccessoryDto>builder()
                .operation(operationType)
                .module(moduleName)
                .data(accessoryDto)
                .build();

        try {
            kafkaTemplate.send(contractAccessoryTopic, objectMapper.writeValueAsString(kafkaMessage));
        } catch (JsonProcessingException e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    public void sendContractRentalMessage(Rental rental, OperationType operationType, String moduleName) throws BusinessException {
        RentalDto rentalDto = modelMapper.map(rental, RentalDto.class);
        rentalDto.setCreatedAt(rental.getCreatedAt());
        rentalDto.setModifiedAt(rental.getModifiedAt());
        rentalDto.setDeletedAt(rental.getDeletedAt());

        KafkaMessage<RentalDto> kafkaMessage = KafkaMessage.<RentalDto>builder()
                .operation(operationType)
                .module(moduleName)
                .data(rentalDto)
                .build();

        try {
            kafkaTemplate.send(contractRentalsTopic, objectMapper.writeValueAsString(kafkaMessage));
        } catch (JsonProcessingException e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    public void sendContractTimetableItemsMessage(List<TimetableItem> timetableItems, OperationType operationType, String moduleName) throws BusinessException {
        List<TimetableItemStreamDto> timetableItemsDto = timetableItems.stream()
                .map(timetableItem -> {
                    TimetableItemStreamDto timetableItemDto = modelMapper.map(timetableItem, TimetableItemStreamDto.class);
                    timetableItemDto.setCreatedAt(timetableItem.getCreatedAt());
                    timetableItemDto.setModifiedAt(timetableItem.getModifiedAt());
                    timetableItemDto.setDeletedAt(timetableItem.getDeletedAt());
                    return timetableItemDto;
                })
                .toList();
        KafkaMessage<List<TimetableItemStreamDto>> kafkaMessage = KafkaMessage.<List<TimetableItemStreamDto>>builder()
                .operation(operationType)
                .module(moduleName)
                .data(timetableItemsDto)
                .className("TimetableItem")
                .build();

        try {
            kafkaTemplate.send(contractTopic, objectMapper.writeValueAsString(kafkaMessage));
        } catch (JsonProcessingException e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    public void sendContractActorAsset(ContractActorAsset contractActorAsset, OperationType operationType, String moduleName) throws BusinessException {
        ContractActorAssetStreamDto contractActorAssetDto = modelMapper.map(contractActorAsset, ContractActorAssetStreamDto.class);
        contractActorAssetDto.setCreatedAt(contractActorAsset.getCreatedAt());
        contractActorAssetDto.setModifiedAt(contractActorAsset.getModifiedAt());
        contractActorAssetDto.setDeletedAt(contractActorAsset.getDeletedAt());

        KafkaMessage<ContractActorAssetStreamDto> kafkaMessage = KafkaMessage.<ContractActorAssetStreamDto>builder()
                .operation(operationType)
                .module(moduleName)
                .data(contractActorAssetDto)
                .build();

        try {
            kafkaTemplate.send(contractActorAssetTopic, objectMapper.writeValueAsString(kafkaMessage));
        } catch (JsonProcessingException e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    public void sendTimetableAsset(TimetableAsset timetableAsset, OperationType operationType, String moduleName) throws BusinessException {
        TimetableAssetStreamDto timetableAssetDto = modelMapper.map(timetableAsset, TimetableAssetStreamDto.class);
        timetableAssetDto.setCreatedAt(timetableAsset.getCreatedAt());
        timetableAssetDto.setModifiedAt(timetableAsset.getModifiedAt());
        timetableAssetDto.setDeletedAt(timetableAsset.getDeletedAt());

        KafkaMessage<TimetableAssetStreamDto> kafkaMessage = KafkaMessage.<TimetableAssetStreamDto>builder()
                .operation(operationType)
                .module(moduleName)
                .data(timetableAssetDto)
                .build();

        try {
            kafkaTemplate.send(contractTimetableAssetTopic, objectMapper.writeValueAsString(kafkaMessage));
        } catch (JsonProcessingException e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    public void sendUpdateToExpense(List<ExpenseStreamDto> expenses, OperationType operationType, String moduleName) throws BusinessException {
        KafkaMessage<List<ExpenseStreamDto>> kafkaMessage = KafkaMessage.<List<ExpenseStreamDto>>builder()
                .operation(operationType)
                .module(moduleName)
                .data(expenses)
                .className("Expense")
                .build();

        try {
            kafkaTemplate.send(contractTopic, objectMapper.writeValueAsString(kafkaMessage));
        } catch (JsonProcessingException e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }
}


