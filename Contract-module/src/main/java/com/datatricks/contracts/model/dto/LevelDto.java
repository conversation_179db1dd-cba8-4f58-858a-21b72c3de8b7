package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.enums.TypeTerme;
import com.datatricks.contracts.enums.TypeUnitePeriode;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
public class LevelDto implements PageableDto {
    @JsonProperty("id")
    @Schema(description = "id of the level", example = "1")
    private Long id;

    @NotNull(message = "levels.order:order cannot be null")
    @Min(value = 1, message = "levels.order:order value must be greater than 0")
    private Integer order;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "levels.start_date:start date cannot be null")
    @Schema(description = "start date of the level", example = "2021-01-01")
    private Date startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "levels.end_date:end date cannot be null")
    @Schema(description = "end date of the level", example = "2022-01-01")
    private Date endDate;

    @JsonProperty("due_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "due date of the level", example = "2022-01-01")
    private Date dueDate;

    @JsonProperty("payment_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "payment date of the level", example = "2022-01-01")
    private Date paymentDate;

    @JsonProperty("period")
    @NotNull(message = "levels.period:period cannot be null")
    @Schema(description = "period of the level", example = "ENUM_ANNEE, ENUM_SEMESTRE, ENUM_TRIMESTRE, ENUM_MOIS, ENUM_JOUR")
    private TypeUnitePeriode period;

    @JsonProperty("period_multiple")
    @NotNull(message = "levels.period_multiple:period multiple cannot be null")
    @Schema(description = "period multiple of the level", example = "8")
    private Integer periodMultiple;

    @JsonProperty("period_number")
    @NotNull(message = "levels.period_number:period numbers cannot be null")
    @Schema(description = "period number of the level", example = "ENUM_AVANCE or ENUM_ECHU")
    private Integer periodNumber;

    @JsonProperty("rent")
    @NotNull(message = "levels.rent:rent cannot be null")
    @Schema(description = "rent of the level", example = "1000.0")
    private Double rent;

    @JsonProperty("perception")
    @NotNull(message = "levels.perception:perception mode cannot be null")
    @Schema(description = "perception mode of the level", example = "ENUM_AVANCE or ENUM_ECHU")
    private TypeTerme perception;

    @JsonProperty("rate")
    @Schema(description = "rate of the level", example = "2.0")
    private Double rate;

    @JsonProperty("nominal_rate")
    @Schema(description = "nominal rate of the level", example = "2.0")
    private Double nominalRate;
}
