package com.datatricks.contracts.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "static_roles")
public class StaticRole extends BaseEntity {
    @Id
    private Long id;

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "label")
    private String label;

    @Column(name = "is_exclusive")
    private Boolean isExclusive;

    @Column(name = "is_client")
    private Boolean isClient;

    @Column(name = "associated_to")
    private String associatedTo;

    public StaticRole(String associatedTo) {
        this.code = associatedTo;
    }
}
