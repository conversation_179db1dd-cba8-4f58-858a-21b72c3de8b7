package com.datatricks.contracts.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;

@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Table(name = "actor_roles", uniqueConstraints = @UniqueConstraint(columnNames = {"actorId", "role_code", "deleted_at"}))
public class ActorRole extends BaseEntity {
    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "is_principal")
    @JsonProperty("is_principal")
    private Boolean isPrincipal;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actorId")
    private Actor actorId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_code", referencedColumnName = "code")
    private Role role;

    @JsonProperty("invoice_edition_date_limit")
    @Column(name = "invoice_edition_date_limit")
    private LocalDate invoiceEditionDateLimit;
}
