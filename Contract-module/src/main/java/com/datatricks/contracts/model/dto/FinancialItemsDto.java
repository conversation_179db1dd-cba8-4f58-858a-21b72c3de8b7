package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.model.FinancialItemsType;
import com.datatricks.contracts.model.FinancingStatus;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FinancialItemsDto implements PageableDto {
    private Long id;
    private FinancialItemsType type;
    @JsonProperty("suspended_invoice")
    private Boolean suspendedInvoice;
    @JsonProperty("mobile_extension")
    private Boolean mobileExtension;
    @JsonProperty("separate_invoice")
    private Boolean separateInvoice;
    @JsonProperty("contract_actor_id")
    private Long contractActorId;
    @JsonProperty("status")
    private FinancingStatus status;
    @JsonProperty("title")
    private String title;
    @JsonProperty("line_type_code")
    @NotBlank(message = "line_type_code:please provide a line type code")
    private String lineTypeCode;
}
