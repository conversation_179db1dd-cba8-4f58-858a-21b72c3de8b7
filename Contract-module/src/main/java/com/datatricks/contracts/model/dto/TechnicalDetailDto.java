package com.datatricks.contracts.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TechnicalDetailDto {
    @Schema(description = "Detailed error message", example = "The requested resource does not exist")
    private String detail;

    @Schema(description = "Source of the error", example = "Contract-module")
    private String source;
}