package com.datatricks.contracts.model;

import com.datatricks.contracts.model.dto.PhaseDto;
import jakarta.persistence.*;
import lombok.*;

import java.util.Set;

@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Table(name = "phases")
public class Phase {
    @Id
    private Long id;

    @Column(name = "code")
    private String code;

    @Column(name = "label")
    private String label;

    @Column(name = "associated_to")
    private String associatedTo;

    @OneToMany(mappedBy = "phaseId", fetch = FetchType.LAZY)
    private Set<Milestone> milestones;

    public Phase(String code) {
        this.code = code;
    }

    public Phase(PhaseDto phase) {
        this.code = phase.getCode();
        this.label = phase.getLabel();
        this.associatedTo = phase.getAssociatedTo();
    }
}
