package com.datatricks.contracts.model;

import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "shippings")
public class Shipping extends BaseEntity {
    @Id
    @GeneratedValue
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "start_date")
    @JsonFormat(pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @Column(name = "end_date")
    @JsonFormat(pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @Column(name = "shipping_mode")
    private String mode;

    @Column(name = "note")
    private String note;

    public Shipping(Long id) {
        this.id = id;
    }
}
