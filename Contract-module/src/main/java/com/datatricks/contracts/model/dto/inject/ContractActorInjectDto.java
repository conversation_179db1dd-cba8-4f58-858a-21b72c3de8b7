package com.datatricks.contracts.model.dto.inject;

import com.datatricks.contracts.model.dto.*;
import com.datatricks.contracts.utils.ContractUtils;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.ManyToOne;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ContractActorInjectDto implements PageableDto {
    @JsonProperty("id")
    @Schema(description = "id of the contract actor", example = "1")
    @JsonApiId
    @JsonIgnore
    private Long id;

    @JsonApiType
    private String type = "contract_actor";

    @JsonProperty("delegation")
    @NotNull(message = "contract_actor.delegation:please provide a delegation")
    @Valid
    @Schema(description = "delegation of the contract actor")
    @JsonApiRelationships("delegation")
    @ManyToOne
    private DelegationInjectDto delegation;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "start date of the contract actor", example = "2021-01-01")
    private Date startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "end date of the contract actor", example = "2022-01-01")
    private Date endDate;

    @JsonApiRelationships("actor")
    @JsonIgnore
    @ManyToOne
    private ActorInjectDto actorInjectDto;

    public ContractActorInjectDto(ContractActorResponseDto contractActorResponseDto) {
        this.id = contractActorResponseDto.getId();
        this.delegation = new DelegationInjectDto(contractActorResponseDto.getDelegation());
        this.startDate = ContractUtils.convertToDate(contractActorResponseDto.getStartDate());
        this.endDate = ContractUtils.convertToDate(contractActorResponseDto.getEndDate());
        this.actorInjectDto = new ActorInjectDto(contractActorResponseDto.getActor());
    }
}
