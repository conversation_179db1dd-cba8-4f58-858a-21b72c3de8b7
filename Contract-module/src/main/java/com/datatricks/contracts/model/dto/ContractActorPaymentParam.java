package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.model.ContractActorPaymentsType;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
public class ContractActorPaymentParam {

    @JsonProperty("id")
    @Schema(description = "id of the contract actor payments", example = "1")
    private Long id;

    @JsonProperty("type")
    @Schema(description = "type of the contract actor payments", example = "Target, Decashment or Collection")
    private ContractActorPaymentsType type;

    @JsonProperty("name")
    @Schema(description = "name of the contract actor payments", example = "Contract Actor Payments 1")
    private String name;

    @JsonProperty("start_date")
    @Schema(description = "start date of the contract actor payments", example = "2021-01-01")
    private String startDate;
}
