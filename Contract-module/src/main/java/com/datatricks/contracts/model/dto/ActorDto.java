package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.model.Actor;
import com.datatricks.contracts.model.ActorTypes;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActorDto implements PageableDto{
    @JsonProperty("id")
    @Schema(description = "id of the actor", example = "1")
    private Long id;

    @JsonProperty("reference")
    @NotBlank(message = "contract_actor.actor.reference:please provide a reference")
    @Schema(description = "reference of the actor", example = "ACT_9805862")
    private String reference;

    @JsonProperty("name")
    @NotBlank(message = "contract_actor.actor.name:please provide a name")
    @Schema(description = "short name of the actor", example = "DT")
    private String name;

    @JsonProperty("national_identity")
    @NotBlank(message = "contract_actor.actor.national_identity:please provide a national identity")
    @Schema(description = "national identity of the actor", example = "1234567890")
    private String nationalIdentity;

    @JsonProperty("short_name")
    @NotBlank(message = "short_name:please provide a short name")
    @Schema(description = "short name of the actor", example = "DT")
    private String shortName;

    @JsonProperty("vat")
    @Schema(description = "vat of the actor", example = "vat")
    private String vat;

    @JsonProperty("phase")
    @Schema(description = "phase of the actor")
    private PhaseDto phase;

    @Schema(description = "type of the actor", example = "MANAGEMENT_COMPANY or NATURAL_PERSON")
    private ActorTypes type;

    @JsonProperty("actor_roles")
    @Schema(description = "roles of the actor")
    private Set<ActorRoleDto> actorRoles;

    @JsonProperty("bank_accounts")
    @Schema(description = "bank accounts of the actor")
    private Set<BankAccountDto> bankAccounts;

    @JsonProperty("addresses")
    @Schema(description = "addresses of the actor")
    private Set<AddressDto> addresses;

    public ActorDto(Actor actor) {
        this.id = actor.getId();
        this.reference = actor.getReference();
        this.name = actor.getName();
        this.nationalIdentity = actor.getNationalIdentity();
        this.shortName = actor.getShortName();
        this.vat = actor.getVat();
        this.type = actor.getType();
        //this.actorRoles = actor.get().stream().map(ActorRoleDto::new).collect(Collectors.toSet());
    }
}
