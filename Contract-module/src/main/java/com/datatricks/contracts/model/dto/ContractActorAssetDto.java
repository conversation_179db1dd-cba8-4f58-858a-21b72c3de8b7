package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ContractActorAssetDto implements PageableDto {

    @JsonProperty("id")
    @Schema(description = "Id of the contract actor asset", example = "1")
    private Long id;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start date of the contract actor asset", example = "2021-01-01")
    private LocalDate startDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("end_date")
    @Schema(description = "End date of the contract actor asset", example = "2022-01-01")
    private LocalDate endDate;

    @JsonProperty("note")
    @Schema(description = "Note of the contract actor asset", example = "This is a note")
    private String note;

    @JsonProperty("actor")
    @Schema(description = "Actor of the contract actor asset")
    private SimplifiedActorDto actor = new SimplifiedActorDto();

    @JsonProperty("contract")
    @Schema(description = "Contract of the contract actor asset")
    private ContractDto contract = new ContractDto();

    @JsonProperty("rentals")
    @Schema(description = "Rental of the contract actor asset")
    private List<RentalDto> rentals;

    @JsonProperty("accessories")
    @Schema(description = "Accessory of the contract actor asset")
    private List<AccessoryDto> accessories;

    @JsonProperty("asset")
    @Schema(description = "Asset of the contract actor asset")
    private SimplifiedAssetDto asset = new SimplifiedAssetDto();
}
