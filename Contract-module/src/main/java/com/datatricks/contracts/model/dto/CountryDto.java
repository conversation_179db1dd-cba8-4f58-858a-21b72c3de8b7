package com.datatricks.contracts.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CountryDto {

    @Schema(description = "Id of the country", example = "17")
    private Long id;

    @Schema(description = "Code of the country", example = "BH")
    private String code;

    @Schema(description = "Name of the country", example = "Bahrain")
    private String label;
}
