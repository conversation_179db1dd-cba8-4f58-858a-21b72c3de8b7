package com.datatricks.contracts.model;

import com.datatricks.contracts.model.dto.ActorDto;
import com.datatricks.contracts.model.dto.inject.ActorInjectDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

import java.util.Set;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "actors")
public class Actor extends BaseEntity {

    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @Column(name = "national_identity")
    @JsonProperty("national_identity")
    @NotBlank(message = "national_identity:please provide a national identity")
    private String nationalIdentity;

    @Column(name = "external_reference")
    @JsonProperty("external_reference")
    private String externalReference;

    @Column(name = "short_name")
    @JsonProperty("short_name")
    @NotBlank(message = "short_name:please provide a short name")
    private String shortName;

    @Column(name = "name")
    @JsonProperty("name")
    @NotBlank(message = "name:please provide a name")
    private String name;

    @Column(name = "vat")
    @JsonProperty("vat")
    private String vat;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "phaseId")
    private Phase phase;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "countryId")
    private Country country;

    @Column(name = "type")
    @JsonProperty("actor_type")
    @Enumerated(EnumType.STRING)
    private ActorTypes type;

    @OneToMany(mappedBy = "actorId", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JsonApiRelationships("actor_role")
    private Set<ActorRole> actorRole;

    @OneToMany(mappedBy = "actorId", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JsonApiRelationships("bank_account")
    private Set<BankAccount> bankAccounts;

    @OneToMany(mappedBy = "actorId", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JsonApiRelationships("addresses")
    private Set<Address> addresses;

    @OneToMany(mappedBy = "actor", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JsonProperty("contract_actor")
    private Set<ContractActor> contractActor;

    public Actor(Long id) {
        this.id = id;
    }

    public Actor(String reference) {
        this.reference = reference;
    }

    public Actor(ActorDto business) {
        this.id = business.getId();
        this.reference = business.getReference();
        this.nationalIdentity = business.getNationalIdentity();
        this.shortName = business.getShortName();
        this.name = business.getName();
        this.vat = business.getVat();
        this.phase = new Phase(business.getPhase());
        this.type = business.getType();
    }

    public Actor(ActorInjectDto actorInjectDto) {
        super();
    }
}
