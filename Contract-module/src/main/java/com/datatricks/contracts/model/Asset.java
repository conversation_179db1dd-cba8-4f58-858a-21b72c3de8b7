package com.datatricks.contracts.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "assets")
public class Asset extends BaseEntity {

    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @Column(name = "label")
    @JsonProperty("label")
    private String label;

    @Column(name = "description")
    @JsonProperty("description")
    private String description;

    @Column(name = "total_value")
    @JsonProperty("total_value")
    private double totalValue;


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "milestone_id")
    @JsonProperty("milestone")
    private Milestone milestone;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "phase_id")
    @JsonProperty("phase")
    private Phase phase;

    public Asset(Long assetId) {
        this.id = assetId;
    }
}
