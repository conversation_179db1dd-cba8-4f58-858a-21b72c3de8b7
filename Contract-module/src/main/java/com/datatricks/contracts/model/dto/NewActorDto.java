package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.model.Actor;
import com.datatricks.contracts.model.dto.inject.AddressInjectDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NewActorDto extends Actor {
    @JsonApiType
    private String myType = "actor";

    @JsonProperty("address")
    @Schema(description = "Address of the actor")
    @JsonApiRelationships("address")
    private AddressInjectDto address;
}
