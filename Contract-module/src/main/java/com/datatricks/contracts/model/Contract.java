package com.datatricks.contracts.model;

import com.datatricks.contracts.enums.ContractState;
import com.datatricks.contracts.model.dto.ContractDto;
import com.datatricks.contracts.model.dto.PatchContractDto;
import com.datatricks.contracts.model.dto.inject.ContractInjectDto;
import com.datatricks.contracts.model.validationLevel.LevelOne;
import com.datatricks.contracts.utils.ContractUtils;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import jakarta.persistence.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;
import java.util.Set;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "contracts")
public class Contract extends BaseEntity {

    @Id
    @GeneratedValue
    @JsonProperty("id")
    @JsonIgnore
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @Column(name = "title")
    @JsonProperty("title")
    @NotBlank(message = "title:please provide a title", groups = {LevelOne.class})
    private String title;

    @Column(name = "refinancing_rate")
    @JsonProperty("refinancing_rate")
    private Double refinancingRate;

    @Column(name = "business_type")
    @JsonProperty("business_type")
    @NotBlank(message = "business_type:please provide a business type")
    private String businessType;

    @Column(name = "market_type")
    @JsonProperty("market_type")
    @NotBlank(message = "market_type:please provide a market type")
    private String marketType;

    @Column(name = "external_reference")
    @JsonProperty("external_reference")
    private String externalReference;

    @Column(name = "lessor_reference")
    @JsonProperty("lessor_reference")
    @Valid
    private String lessorReference;

    @Column(name = "agreement_as_service")
    @JsonProperty("agreement_as_service")
    @Valid
    private Boolean agreementAsService;

    @Column(name = "agreement_date")
    @JsonProperty("agreement_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate agreementDate;

    @Column(name = "end_agreement_date")
    @JsonProperty("end_agreement_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endAgreementDate;

    @Column(name = "sign_date")
    @JsonProperty("sign_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate signDate;

    @Column(name = "rental_date")
    @JsonProperty("rental_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate rentalDate;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @Column(name = "deadline")
    @JsonProperty("deadline")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate deadline;

    @Column(name = "lessor_selling_price")
    @JsonProperty("lessor_selling_price")
    private Double lessorSellingPrice;

    @Column(name = "amount_rental_base")
    @JsonProperty("amount_rental_base")
    private Double amountRentalBase;

    @Column(name = "amount_granted")
    @JsonProperty("amount_granted")
    private Double amountGranted;

    @Column(name = "duration")
    @JsonProperty("duration")
    private Integer duration;

    @Column(name = "request_date")
    @JsonProperty("request_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate requestDate;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private ContractState status;

    @Column(name = "lessor_payment_date")
    @JsonProperty("lessor_payment_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate lessorPaymentDate;

    @Column(name = "business_introducer")
    @JsonProperty("business_introducer")
    private String businessIntroducer;

    /*
     * Foreign keys columns
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "business_reference", referencedColumnName = "reference")
    @JsonIgnore
    @JsonApiRelationships("management_company")
    @NotNull(message = "business_reference:please provide a business reference for this contract")
    private Actor managementCompany;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "activity_code", referencedColumnName = "code")
    @JsonIgnore
    @JsonApiRelationships("activity")
    @NotNull(message = "activity_code:please provide an activity for this contract")
    private Activity activity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "product_code", referencedColumnName = "code")
    @JsonIgnore
    @JsonApiRelationships("product")
    @NotNull(message = "product_code:please provide a product for this contract")
    private Product product;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "currency_code", referencedColumnName = "code")
    @JsonIgnore
    @JsonApiRelationships("currency")
    @NotNull(message = "currency_code:please provide a currency for this contract")
    private Currency currency;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "phase_code")
    @JsonIgnore
    @JsonApiRelationships("phase")
    @NotNull(message = "phase_code: please provide a phase code for this contract")
    private Phase phase;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "milestone_code")
    @JsonIgnore
    @JsonApiRelationships("milestone")
    @NotNull(message = "milestone_code:please provide a milestone code for this contract")
    private Milestone milestone;

    @OneToMany(mappedBy = "contract", fetch = FetchType.EAGER)
    @JsonIgnore
    @JsonApiRelationships("contract_actor")
    private Set<ContractActor> contractActor;

    public Contract(Long id) {
        this.id = id;
    }

    public Contract(ContractDto contractDto) {
        this.id = contractDto.getId();
        this.reference = contractDto.getReference();
        this.title = contractDto.getTitle();
        this.refinancingRate = contractDto.getRefinancingRate();
        this.businessType = contractDto.getBusinessType();
        this.marketType = contractDto.getMarketType();
        this.externalReference = contractDto.getExternalReference();
        this.lessorReference = contractDto.getLessorReference();
        this.agreementAsService = contractDto.getAgreementAsService();
        this.agreementDate = contractDto.getAgreementDate();
        this.endAgreementDate = contractDto.getEndAgreementDate();
        this.signDate = contractDto.getSignDate();
        this.rentalDate = contractDto.getRentalDate();
        this.startDate = contractDto.getStartDate();
        this.deadline = contractDto.getDeadline();
        this.lessorSellingPrice = contractDto.getLessorSellingPrice();
        this.amountRentalBase = contractDto.getAmountRentalBase();
        this.amountGranted = contractDto.getAmountGranted();
        this.duration = contractDto.getDuration();
        this.requestDate = contractDto.getRequestDate();
        this.lessorPaymentDate = contractDto.getLessorPaymentDate();
        this.businessIntroducer = contractDto.getBusinessIntroducer();
        this.managementCompany = new Actor(contractDto.getBusinessReference());
        this.activity = new Activity(contractDto.getActivityCode());
        this.product = new Product(contractDto.getProductCode());
        this.currency = new Currency(contractDto.getCurrencyCode());
        this.phase = new Phase(contractDto.getPhaseCode());
        this.milestone = new Milestone(contractDto.getMilestoneCode());
    }

    public Contract(ContractInjectDto content) {
        this.id = content.getId();
        this.reference = content.getReference();
        this.title = content.getTitle();
        this.refinancingRate = content.getRefinancingRate();
        this.businessType = content.getBusinessType();
        this.marketType = content.getMarketType();
        this.externalReference = content.getExternalReference();
        this.lessorReference = content.getLessorReference();
        this.agreementAsService = content.getAgreementAsService();
        this.agreementDate = ContractUtils.convertToLocalDate(content.getAgreementDate());
        this.endAgreementDate = ContractUtils.convertToLocalDate(content.getEndAgreementDate());
        this.signDate = ContractUtils.convertToLocalDate(content.getSignDate());
        this.rentalDate = ContractUtils.convertToLocalDate(content.getRentalDate());
        this.startDate = ContractUtils.convertToLocalDate(content.getStartDate());
        this.deadline = ContractUtils.convertToLocalDate(content.getDeadline());
        this.lessorSellingPrice = content.getLessorSellingPrice();
        this.amountRentalBase = content.getAmountRentalBase();
        this.amountGranted = content.getAmountGranted();
        this.duration = content.getDuration();
        this.requestDate = ContractUtils.convertToLocalDate(content.getRequestDate());
        this.lessorPaymentDate = ContractUtils.convertToLocalDate(content.getLessorPaymentDate());
        this.businessIntroducer = content.getBusinessIntroducer();
    }
    public Contract(PatchContractDto contractDto) {
        this.id = contractDto.getId();
        this.reference = contractDto.getReference();
        this.title = contractDto.getTitle();
        this.refinancingRate = contractDto.getRefinancingRate();
        this.businessType = contractDto.getBusinessType();
        this.marketType = contractDto.getMarketType();
        this.externalReference = contractDto.getExternalReference();
        this.lessorReference = contractDto.getLessorReference();
        this.agreementAsService = contractDto.getAgreementAsService();
        this.agreementDate = contractDto.getAgreementDate();
        this.endAgreementDate = contractDto.getEndAgreementDate();
        this.signDate = contractDto.getSignDate();
        this.rentalDate = contractDto.getRentalDate();
        this.startDate = contractDto.getStartDate();
        this.deadline = contractDto.getDeadline();
        this.lessorSellingPrice = contractDto.getLessorSellingPrice();
        this.amountRentalBase = contractDto.getAmountRentalBase();
        this.amountGranted = contractDto.getAmountGranted();
        this.duration = contractDto.getDuration();
        this.requestDate = contractDto.getRequestDate();
        this.lessorPaymentDate = contractDto.getLessorPaymentDate();
        this.managementCompany = new Actor(contractDto.getBusinessReference());
        this.activity = new Activity(contractDto.getActivityCode());
        this.product = new Product(contractDto.getProductCode());
        this.currency = new Currency(contractDto.getCurrencyCode());
        this.phase = new Phase(contractDto.getPhaseCode());
        this.milestone = new Milestone(contractDto.getMilestoneCode());
    }
}
