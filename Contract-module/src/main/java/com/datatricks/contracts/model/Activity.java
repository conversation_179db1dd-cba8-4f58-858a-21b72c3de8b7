package com.datatricks.contracts.model;

import com.datatricks.contracts.model.dto.ActivityDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "activities")
public class Activity extends BaseEntity {
    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "label")
    private String label;

    @Column(name = "associated_to")
    @JsonProperty("associated_to")
    private String associatedTo;

    @Column(name = "active")
    private Boolean active;

    public Activity(String code) {
        this.code = code;
    }

    public Activity(ActivityDto activity) {
        this.code = activity.getCode();
        this.label = activity.getLabel();
    }
}
