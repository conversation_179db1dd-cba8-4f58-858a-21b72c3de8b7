package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.model.Role;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RoleDto implements Serializable {
    @Schema(description = "id of the role", example = "1")
    private Long id;

    @JsonApiType
    private String myType = "role";

    @NotBlank(message = "contract_actor.role.code:please provide a code")
    @Schema(description = "code of the role", example = "COLOC")
    private String code;

    @Schema(description = "label of the role", example = "Colocataire (Facturé)")
    private String label;

     @Schema(description = "description of the role", example = "FR")
    private String language;

    @Schema(description = "description of the role", example = "true")
    private Boolean isExclusive;

    @Schema(description = "description of the role", example = "true")
    private Boolean isClient;

    private String associated_to;

    public RoleDto(Role role) {
        this.id = role.getId();
        this.code = role.getCode();
        this.label = role.getLabel();
        this.isExclusive = role.getAssociatedTo().getIsExclusive();
        this.isClient = role.getAssociatedTo().getIsClient();
    }
}
