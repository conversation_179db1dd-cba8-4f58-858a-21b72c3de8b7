package com.datatricks.contracts.model.dto.inject;

import com.datatricks.contracts.model.Actor;
import com.datatricks.contracts.model.ActorTypes;
import com.datatricks.contracts.model.dto.CountryDto;
import com.datatricks.contracts.model.dto.PageableDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SimplifiedActorInjectDto implements PageableDto {
    @JsonProperty("id")
    @Schema(description = "id of the actor", example = "1")
    @JsonApiId
    private Long id;

    @JsonApiType
    private String myType = "actor";

    @JsonProperty("reference")
    @Schema(description = "reference of the actor", example = "ACT_9805862")
    private String reference;

    @JsonProperty("external_reference")
    @Schema(description = "external reference of the actor", example = "ACT_9805863")
    private String externalReference;

    @JsonProperty("country")
    @Schema(description = "country of the actor")
    private CountryDto country;

    @JsonProperty("short_name")
    @NotBlank(message = "short_name:please provide a short name")
    @Schema(description = "short name of the actor", example = "DT")
    private String shortName;

    @JsonProperty("name")
    @NotBlank(message = "name:please provide a name")
    @Schema(description = "name of the actor", example = "DataTricks")
    private String name;

    @JsonProperty("type")
    @Schema(description = "type of the actor", example = "MANAGEMENT_COMPANY or NATURAL_PERSON")
    private ActorTypes type;

    @JsonProperty("national_identity")
    @NotBlank(message = "national_identity:please provide a national identity")
    @Schema(description = "National identity of the actor", example = "*********")
    private String nationalIdentity;

    public SimplifiedActorInjectDto(Actor actor) {
        this.id = actor.getId();
        this.reference = actor.getReference();
        this.externalReference = actor.getExternalReference();
        this.shortName = actor.getShortName();
        this.name = actor.getName();
        this.type = actor.getType();
        this.nationalIdentity = actor.getNationalIdentity();
    }
}
