package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.enums.TypeBase;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.time.LocalDate;
@Getter
public class FinanceElementsParam {

    @Schema(description = "id of the finance element", example = "1")
    private Long id;

    @Schema(description = "title of the finance element", example = "Finance Element 1")
    private String title;

    @JsonProperty("allocation_code")
    @Schema(description = "allocation code of the finance element", example = "ALC-001")
    private String allocationCode;

    @JsonProperty("arrangement_type")
    @Schema(description = "arrangement type of the finance element", example = "Arrangement Type 1")
    private String arrangementType;

    @JsonProperty("amount")
    @Schema(description = "amount of the finance element", example = "1000.0")
    private Double amount;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "start date of the finance element", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "end date of the finance element", example = "2022-01-01")
    private LocalDate endDate;

    @JsonProperty("separate_invoice")
    @Schema(description = "separate invoice of the finance element", example = "true")
    private Boolean separateInvoice;

    @JsonProperty("suspended_invoice")
    @Schema(description = "suspended invoice of the finance element", example = "true")
    private Boolean suspendedInvoice;

    @JsonProperty("mobile_extension")
    @Schema(description = "mobile extension of the finance element", example = "true")
    private Boolean mobileExtension;

    @JsonProperty("calculation_basis")
    @Schema(description = "calculation basis of the finance element", example = "ENUM_BASE360, ENUM_BASE365_366, ENUM_BASE365, ENUM_BASE365_25, ENUM_BASE_FREQUENCY or ENUM_BASE_YEARLY")
    private TypeBase calculationBasis;

    @JsonProperty("calculation_mode")
    @Schema(description = "calculation mode of the finance element", example = "ENUM_BASE360, ENUM_BASE365_366, ENUM_BASE365, ENUM_BASE365_25, ENUM_BASE_FREQUENCY or ENUM_BASE_YEARLY")
    private TypeBase calculationMode;

    @JsonProperty("effective_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "effective date of the finance element", example = "2021-01-01")
    private LocalDate effectiveDate;

    @JsonProperty("nominal_rate")
    @Schema(description = "nominal rate of the finance element", example = "1.5")
    private Double nominalRate;

    @JsonProperty("equivalent_rate")
    @Schema(description = "equivalent rate of the finance element", example = "1.5")
    private Double equivalentRate;

    @JsonProperty("residual_value")
    @Schema(description = "residual value of the finance element", example = "1.5")
    private Double residualValue;

    @JsonProperty("tax")
    @Schema(description = "tax of the finance element", example = "Tax 1")
    private String tax;

    @JsonProperty("tax_rate")
    @Schema(description = "tax rate of the finance element", example = "1.5")
    private Double taxRate;

    @JsonProperty("status")
    @Schema(description = "status of the finance element", example = "Status 1")
    private String status;

    @JsonProperty("type")
    @Schema(description = "type of the finance element", example = "RENTAL, ACCESSORY, RETRIBUTION")
    private String type;

    @JsonProperty("timetable_id")
    @Schema(description = "timetable id of the finance element", example = "1")
    private Long timetable;
}