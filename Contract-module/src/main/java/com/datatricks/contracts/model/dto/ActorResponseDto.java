package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.model.Actor;
import com.datatricks.contracts.model.ActorTypes;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActorResponseDto implements PageableDto {

    @JsonProperty("id")
    @Schema(description = "id of the actor", example = "1")
    private Long id;

    @JsonProperty("reference")
    @Schema(description = "reference of the actor", example = "ACT_9805862")
    private String reference;

    @JsonProperty("name")
    @Schema(description = "short name of the actor", example = "DT")
    private String name;

    @JsonProperty("national_identity")
    @Schema(description = "national identity of the actor", example = "1234567890")
    private String nationalIdentity;

    @JsonProperty("short_name")
    @Schema(description = "short name of the actor", example = "DT")
    private String shortName;

    @JsonProperty("vat")
    @Schema(description = "vat of the actor", example = "vat")
    private String vat;

    @Schema(description = "type of the actor", example = "MANAGEMENT_COMPANY or NATURAL_PERSON")
    private ActorTypes type;

    @JsonProperty("addresses")
    @Schema(description = "addresses of the actor")
    private Set<AddressDto> addresses;

    public ActorResponseDto(Actor actor) {
        this.id = actor.getId();
        this.reference = actor.getReference();
        this.name = actor.getName();
        this.nationalIdentity = actor.getNationalIdentity();
        this.shortName = actor.getShortName();
        this.vat = actor.getVat();
        this.type = actor.getType();
        this.addresses = actor.getAddresses().stream().map(AddressDto::new).collect(HashSet::new, HashSet::add, HashSet::addAll);
    }
}