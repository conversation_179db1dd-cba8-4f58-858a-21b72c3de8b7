package com.datatricks.contracts.model.dto.inject;

import com.datatricks.contracts.model.CommunicationMeanTypes;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class CommunicationMeanDto {

    @JsonProperty("id")
    @JsonApiId
    @JsonIgnore
    private Long id;

    @JsonApiType
    private String myType = "communication_mean";

    @JsonProperty("type")
    @Schema(description = "Type", example = "EMAIL, PHONE, FAX, MOBILE, OTHER", type = "string", allowableValues = {"Email", "Phone", "Fax", "Mobile", "Other"})
    private CommunicationMeanTypes type;

    @JsonProperty("reference")
    @Schema(description = "Reference", example = "<EMAIL>", type = "string")
    private String reference;

    @JsonProperty("preferred")
    @Schema(description = "Preferred", example = "true", type = "boolean")
    private Boolean preferred;

    @JsonProperty("status")
    @Schema(description = "Status", example = "true(ACTIVE), false(INACTIVE)", type = "boolean")
    private boolean status;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start date", example = "2021-07-01", type = "Date")
    private Date startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End date", example = "2021-07-02", type = "Date")
    private Date endDate;

    @JsonProperty("last_contact_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Last contact date", example = "2021-07-01", type = "Date")
    private Date lastContactDate;
}
