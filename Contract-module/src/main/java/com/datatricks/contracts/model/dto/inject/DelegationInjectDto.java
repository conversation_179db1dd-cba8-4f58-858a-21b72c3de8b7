package com.datatricks.contracts.model.dto.inject;

import com.datatricks.contracts.model.Delegation;
import com.datatricks.contracts.model.dto.DelegationDto;
import com.datatricks.contracts.model.dto.PageableDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class DelegationInjectDto implements PageableDto {

    @JsonProperty("id")
    @Schema(description = "id of the delegation", example = "30")
    @JsonApiId
    private Long id;

    @JsonApiType
    private String myType = "delegation";

    @JsonProperty("label")
    @Schema(description = "label of the delegation", example = "Rien")
    private String label;

    @JsonProperty("code")
    @Schema(description = "code of the delegation", example = "3")
    private String code;

    @JsonProperty("short_label")
    @Schema(description = "short label of the delegation", example = "Rien")
    private String shortLabel;

    public DelegationInjectDto(Delegation delegation) {
        this.id = delegation.getId();
        this.label = delegation.getLabel();
        this.code = delegation.getCode();
        this.shortLabel = delegation.getShortLabel();
    }

    public DelegationInjectDto(DelegationDto delegation) {
        this.id = delegation.getId();
        this.label = delegation.getLabel();
        this.code = delegation.getCode();
        this.shortLabel = delegation.getShortLabel();
    }
}
