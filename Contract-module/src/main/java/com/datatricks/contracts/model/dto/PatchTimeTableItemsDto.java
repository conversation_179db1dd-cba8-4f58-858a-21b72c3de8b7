package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.enums.TimeTableStatusType;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PatchTimeTableItemsDto {
    private Long id;
    @JsonProperty("invoice_reference")
    private String invoiceReference;
    private List<String> errors;
    private TimeTableStatusType status;
}
