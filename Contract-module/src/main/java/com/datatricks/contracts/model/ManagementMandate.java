package com.datatricks.contracts.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "management_mandates", uniqueConstraints = {
        @UniqueConstraint(columnNames = "reference")
})
public class ManagementMandate extends BaseEntity{
    @Id
    @GeneratedValue
    private Long id;

    @Column(name = "reference")
    private String reference;

    @Column(name = "label")
    private String label;

    @Column(name = "occult")
    private boolean occult;
}
