package com.datatricks.contracts.model.dto.inject;

import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class BankAccountInjectDto{

    @JsonProperty("id")
    @JsonApiId
    @JsonIgnore
    private Long id;

    @JsonApiType
    private String myType = "bank_account";

    @JsonProperty("bank_code")
    @Schema(description = "Bank code", example = "30002")
    private String bankCode;

    @JsonProperty("country_code")
    @Schema(description = "Country of the bank account", example = "FR")
    private String country;

    @JsonProperty("international_number")
    @Schema(description = "International number of the bank account", example = "***************************")
    private String internationalNumber;

    @JsonProperty("registration_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Registration date of the bank account", example = "2021-07-01")
    private Date registrationDate;

    @JsonProperty("swift_code")
    @Schema(description = "Swift code of the bank account", example = "BNPAFRPPXXX")
    private String swiftCode;

    @JsonProperty("account_number")
    @Schema(description = "Account number", example = "***********")
    private String accountNumber;

    @JsonProperty("status")
    @Schema(description = "Status of the bank account", example = "active")
    private String status;

    @JsonProperty("iban")
    @Schema(description = "Iban of the bank account", example = "***************************")
    private String iban;

    @JsonProperty("validity")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Validity of the bank account", example = "2021-07-01")
    private Date validity;

    @JsonProperty("iban_key")
    @Schema(description = "Iban key", example = "FR")
    private String ibanKey;

    @JsonProperty("rib_key")
    @Schema(description = "Rib key", example = "30002")
    private String ribKey;

    @JsonProperty("title")
    @Schema(description = "Title of the bank account", example = "Data Tricks")
    private String title;

    @JsonProperty("type")
    @Schema(description = "Type of the bank account", example = "current")
    private String type;

    @JsonProperty("replacement")
    @Schema(description = "Replacement", example = "true")
    private Boolean replacement;

    @JsonProperty("rib_replacement")
    @Schema(description = "Rib replacement", example = "true")
    private String ribReplacement;

    @JsonProperty("branch_code")
    @Schema(description = "Branch code", example = "00040")
    private String branchCode;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End date of the bank account", example = "2021-07-02")
    private Date endDate;

    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("start_date")
    @Schema(description = "Start date of the bank account", example = "2021-07-01")
    private Date startDate;

	@JsonProperty("is_principal")
    @Schema(description = "Is principal", example = "true", type = "boolean")
	private Boolean isPrincipal;
}
