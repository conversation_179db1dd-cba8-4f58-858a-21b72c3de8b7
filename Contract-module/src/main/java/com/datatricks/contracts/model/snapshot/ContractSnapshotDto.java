package com.datatricks.contracts.model.snapshot;

import com.datatricks.contracts.model.Contract;
import com.datatricks.contracts.model.dto.ContractDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ContractSnapshotDto extends ContractDto {

    @JsonProperty("contract_actor")
    private List<ContractActorSnapshotDto> contractActors = new ArrayList<>();

    public ContractSnapshotDto(Contract contract) {
        super(contract);
        if (contract.getContractActor() != null)
            this.contractActors = contract.getContractActor().stream()
                .map(ContractActorSnapshotDto::new)
                .toList();
    }
}
