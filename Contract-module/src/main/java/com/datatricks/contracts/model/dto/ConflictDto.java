package com.datatricks.contracts.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ConflictDto {
    @JsonProperty("details")
    @Schema(description = "Technical details of the error")
    private ConflictSourceDto technicalDetail;

    @JsonProperty("errorCode")
    @Schema(description = "Error code", example = "CONFLICT")
    private String code;

    @JsonProperty("message")
    @Schema(description = "Error message", example = "Conflict error")
    private String message;
}