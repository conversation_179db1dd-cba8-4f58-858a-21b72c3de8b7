package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AmortizationDto implements PageableDto {
    private Long id;
    @JsonProperty("name")
    private String name;

    @JsonProperty("amortization_law")
    private String amortizationLaw;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @JsonProperty("rental_base")
    private double rentalBase;

    @JsonProperty("fiscal_periods")
    private int fiscalPeriods;
}
