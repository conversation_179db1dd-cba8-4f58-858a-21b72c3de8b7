package com.datatricks.contracts.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class PaymentMethodDto implements PageableDto {

    @JsonProperty("id")
    @NotNull(message = "payment_method.id:please provide an id")
    @Schema(description = "id of the payment method", example = "4")
    private Long id;

    @JsonProperty("code")
    @NotBlank(message = "payment_method.code:please provide a code")
    @Schema(description = "code of the payment method", example = "CHQ")
    private String code;

    @JsonProperty("label")
    @NotBlank(message = "payment_method.label:please provide a label")
    @Schema(description = "label of the payment method", example = "Check")
    private String label;

    @JsonProperty("language")
    @Schema(description = "language of the payment method", example = "EN")
    private String language;

    @JsonProperty("requires_bank_account")
    @Schema(description = "requires bank account of the payment method", example = "true")
    private Boolean requiresBankAccount;
}
