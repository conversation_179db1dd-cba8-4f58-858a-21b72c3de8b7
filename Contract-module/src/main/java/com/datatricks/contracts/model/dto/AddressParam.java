package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.time.LocalDate;
@Getter
public class AddressParam {

    @JsonProperty("id")
    @Schema(description = "id of the address", example = "1")
    private Long id;

    @JsonProperty("reference")
    @Schema(description = "reference of the address", example = "ACT_9805862")
    private String reference;

    @JsonProperty("city")
    @Schema(description = "city of the address", example = "Paris")
    private String city;

    @JsonProperty("country")
    @Schema(description = "country of the address", example = "France")
    private String country;

    @JsonProperty("zip_code")
    @Schema(description = "zip code of the address", example = "75000")
    private String zipCode;

    @JsonProperty("region")
    @Schema(description = "region of the address", example = "Ile-de-France")
    private String region;

    @Schema(description = "type of the address", example = "BILLING or DELIVERY")
    private String type;

    @JsonProperty("number")
    @Schema(description = "number of the address", example = "1")
    private String nbr;

    @JsonProperty("distribution")
    @Schema(description = "distribution of the address", example = "Rue de la Paix")
    private String distribution;

    @JsonProperty("road_extension")
    @Schema(description = "road extension of the address", example = "Bis")
    private String roadExtension;

    @JsonProperty("EntranceBuilding")
    @Schema(description = "entrance building of the address", example = "A")
    private String entranceBuilding;

    @JsonProperty("headquarter")
    @Schema(description = "headquarter of the address", example = "true")
    private Boolean headquarter;

    @JsonProperty("commune")
    @Schema(description = "commune of the address", example = "Paris")
    private String commune;

    @JsonProperty("subsidiary")
    @Schema(description = "subsidiary of the address", example = "true")
    private Boolean subsidiary;

    @JsonProperty("is_billing")
    @Schema(description = "is billing of the address", example = "true")
    private Boolean isBilling;

    @JsonProperty("is_delivery")
    @Schema(description = "is delivery of the address", example = "true")
    private Boolean isDelivery;

    @JsonProperty("summary")
    @Schema(description = "summary of the address", example = "Rue de la Paix 75000 Paris")
    private String summary;

    @JsonProperty("road_type")
    @Schema(description = "road type of the address", example = "Rue")
    private String roadType;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "start date of the address", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "end date of the address", example = "2022-01-01")
    private LocalDate endDate;
}