package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.model.Contract;
import com.datatricks.contracts.model.dto.inject.ContractInjectDto;
import com.datatricks.contracts.model.group.ValidationGroupCollector;
import com.datatricks.contracts.model.validationLevel.*;
import com.datatricks.contracts.utils.ContractUtils;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
public class PatchContractDto extends ValidationGroupCollector implements PageableDto {

    @Schema(description = "id of the contract.", example = "1")
    private Long id;

    @Schema(description = "Reference of the contract.", example = "SOC_9805862")
    private String reference;

    @NotBlank(message = "title:please provide a title", groups = {LevelOne.class})
    @Schema(description = "Title of the contract.", example = "title")
    private String title;

    @JsonProperty("refinancing_rate")
    @Schema(description = "Refinancing rate of the contract.", example = "1.5")
    private Double refinancingRate;

    @JsonProperty("business_type")
    @NotBlank(message = "business_type:please provide a business type", groups = {LevelTwo.class})
    @Schema(description = "Business type of the contract.", example = "A/R or NCAC or NCNC")
    private String businessType;

    @JsonProperty("market_type")
    @NotBlank(message = "market_type:please provide a market type", groups = {LevelThree.class})
    @Schema(description = "Market type of the contract.", example = "[Autres, Divers, Healthcare, IT, Industrie, Mobile, Motors, NA]")
    private String marketType;

    @JsonProperty("external_reference")
    @Schema(description = "External reference of the contract.", example = "SOC_9805863")
    private String externalReference;

    @JsonProperty("lessor_reference")
    @Schema(description = "Lessor reference of the contract.", example = "SOC_9805865")
    private String lessorReference;

    @JsonProperty("agreement_as_service")
    @Schema(description = "Agreement as service of the contract.", example = "true")
    private Boolean agreementAsService;

    @JsonProperty("agreement_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Agreement date of the contract.", example = "2021-01-01")
    private LocalDate agreementDate;

    @JsonProperty("end_agreement_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End agreement date of the contract.", example = "2022-01-01")
    private LocalDate endAgreementDate;

    @JsonProperty("sign_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Sign date of the contract.", example = "2021-01-01")
    private LocalDate signDate;

    @JsonProperty("rental_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Rental date of the contract.", example = "2021-01-01")
    private LocalDate rentalDate;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start date of the contract.", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End date of the contract.", example = "2022-01-01")
    private LocalDate endDate;

    @JsonProperty("deadline")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Deadline of the contract.", example = "2022-01-02")
    private LocalDate deadline;

    @JsonProperty("lessor_selling_price")
    @Schema(description = "Lessor selling price of the contract.", example = "1.5")
    private Double lessorSellingPrice;

    @JsonProperty("amount_rental_base")
    @Schema(description = "Amount rental base of the contract.", example = "1.5")
    private Double amountRentalBase;

    @JsonProperty(value = "amount_granted")
    @Schema(description = "Amount granted of the contract.", example = "1.5")
    private Double amountGranted;

    @JsonProperty("duration")
    @Schema(description = "Duration per days of the contract.", example = "365")
    private Integer duration;

    @JsonProperty("request_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Request date of the contract.", example = "2021-01-01")
    private LocalDate requestDate;

    @JsonProperty("lessor_payment_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Lessor payment date of the contract.", example = "2021-01-01")
    private LocalDate lessorPaymentDate;

    @JsonProperty("business_reference")
    @NotNull(message = "business_reference:please provide a business reference for this contract", groups = {LevelFour.class})
    @Schema(description = "Business reference of the contract", example = "ACT_645886459")
    private String businessReference;

    @JsonProperty("activity_code")
    @NotNull(message = "activity_code:please provide an activity for this contract", groups = {LevelFive.class})
    @Schema(description = "Activity code of the contract.", example = "CBI")
    private String activityCode;

    @JsonProperty("product_code")
    @NotNull(message = "product_code:please provide a product for this contract", groups = {LevelSix.class})
    @Schema(description = "Product code of the contract.", example = "CESCOM")
    private String productCode;

    @JsonProperty("currency_code")
    @NotNull(message = "currency_code:please provide a currency for this contract", groups = {LevelSeven.class})
    @Schema(description = "Currency code of the contract", example = "EUR")
    private String currencyCode;

    @JsonProperty("phase_code")
    @NotNull(message = "phase_code: please provide a phase code for this contract", groups = {LevelEight.class})
    @Schema(description = "Phase code of the contract", example = "INI")
    private String phaseCode;

    @JsonProperty("milestone_code")
    @NotNull(message = "milestone_code:please provide a milestone code for this contract", groups = {LevelNine.class})
    @Schema(description = "Milestone code of the contract", example = "OUVERT")
    private String milestoneCode;

    public PatchContractDto(Contract contract) {
        this.id = contract.getId();
        this.reference = contract.getReference();
        this.title = contract.getTitle();
        this.refinancingRate = contract.getRefinancingRate();
        this.businessType = contract.getBusinessType();
        this.marketType = contract.getMarketType();
        this.externalReference = contract.getExternalReference();
        this.lessorReference = contract.getLessorReference();
        this.agreementAsService = contract.getAgreementAsService();
        this.agreementDate = contract.getAgreementDate();
        this.endAgreementDate = contract.getEndAgreementDate();
        this.signDate = contract.getSignDate();
        this.rentalDate = contract.getRentalDate();
        this.startDate = contract.getStartDate();
        this.deadline = contract.getDeadline();
        this.lessorSellingPrice = contract.getLessorSellingPrice();
        this.amountRentalBase = contract.getAmountRentalBase();
        this.amountGranted = contract.getAmountGranted();
        this.duration = contract.getDuration();
        this.requestDate = contract.getRequestDate();
        this.lessorPaymentDate = contract.getLessorPaymentDate();
        this.businessReference = contract.getManagementCompany().getReference();
        this.activityCode = contract.getActivity().getCode();
        this.productCode = contract.getProduct().getCode();
        this.currencyCode = contract.getCurrency().getCode();
        this.phaseCode = contract.getPhase().getCode();
        this.milestoneCode = contract.getMilestone().getCode();
    }

    @Override
    public Set<Class<?>> getValidationGroups() {
        Set<Class<?>> groups = new HashSet<>();
        if (this.title != null) {
            groups.add(LevelOne.class);
        }
        if (this.businessType != null) {
            groups.add(LevelTwo.class);
        }
        if (this.marketType != null) {
            groups.add(LevelThree.class);
        }
        if (this.businessReference != null) {
            groups.add(LevelFour.class);
        }
        if (this.activityCode != null) {
            groups.add(LevelFive.class);
        }
        if (this.productCode != null) {
            groups.add(LevelSix.class);
        }
        if (this.currencyCode != null) {
            groups.add(LevelSeven.class);
        }
        if (this.phaseCode != null) {
            groups.add(LevelEight.class);
        }
        if (this.milestoneCode != null) {
            groups.add(LevelNine.class);
        }
        return groups;
    }
}