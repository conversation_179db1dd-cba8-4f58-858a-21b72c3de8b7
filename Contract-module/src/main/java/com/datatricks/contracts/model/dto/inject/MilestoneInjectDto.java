package com.datatricks.contracts.model.dto.inject;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MilestoneInjectDto implements Serializable {
    @Schema(description = "id of the milestone code.", example = "64")
    @JsonApiId
    @JsonIgnore
    private Long id;

    @JsonApiType
    private String myType = "milestone";

    @NotBlank(message = "milestone_code.code: please provide a code")
    @Schema(description = "Code of the milestone code.", example = "OUVERT")
    private String code;

    @NotBlank(message = "milestone_code.label: please provide a label")
    @Schema(description = "Label of the milestone code.", example = "Nouveau")
    private String label;
}
