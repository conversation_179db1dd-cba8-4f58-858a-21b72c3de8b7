package com.datatricks.contracts.model.dto.inject;

import com.datatricks.contracts.model.DepreciationMethod;
import com.datatricks.contracts.model.dto.PageableDto;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "AmortizationDto", description = "Amortization details")
public class AmortizationInjectDto implements PageableDto {
    private Long id;

    @Schema(description = "Name ", example = "Test")
    @JsonProperty("name")
    private String name;

    @JsonProperty("amortization_law")
    @Enumerated(jakarta.persistence.EnumType.STRING)
    @Schema(description = "Amortization law", example = "STRAIGHT_LINE", allowableValues = {"STRAIGHT_LINE", "DECLINING_BALANCE", "DEROGATORY", "ACCOUNTING", "EXCEPTIONAL", "FISCAL"})
    private DepreciationMethod amortizationLaw;

    @JsonProperty("start_date")
    @JsonFormat(pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "startDate: Start date is required")
    @Schema(description = "Start date", example = "2021-01-01")
    private Date startDate;

    @JsonProperty("end_date")
    @JsonFormat(pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "endDate: End date is required")
    @Schema(description = "End date", example = "2021-12-31")
    private Date endDate;

    @JsonProperty("rental_base")
    @Schema(description = "Rental base", example = "1000.00")
    private double rentalBase;

    @JsonProperty("fiscal_periods")
    @NotNull(message = "fiscalPeriods: Fiscal periods is required")
    @Schema(description = "Fiscal periods", example = "12")
    private int fiscalPeriods;
}
