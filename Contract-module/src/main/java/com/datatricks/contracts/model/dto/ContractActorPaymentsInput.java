package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.model.ContractActorPaymentsType;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class ContractActorPaymentsInput {

    @JsonProperty("id")
    @Schema(description = "id of the contract actor payments", example = "1")
    private Long id;

    @JsonProperty("type")
    @NotNull(message = "type:please provide a type")
    @Schema(description = "type of the contract actor payments", example = "Target, Decashment or Collection")
    private ContractActorPaymentsType type;

    @JsonProperty("start_date")
    @Schema(description = "start date of the contract actor payments", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("payment_method_code")
    @NotNull(message = "payment_method_code:please provide a payment method")
    @Schema(description = "payment method code of the contract actor payments")
    private String paymentMethodCode;

    @JsonProperty("bank_account_id")
    @Schema(description = "bank account of the contract actor payments")
    private Long bankAccountId;
}
