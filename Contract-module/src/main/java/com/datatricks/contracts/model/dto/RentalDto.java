package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.enums.TypeBase;
import com.datatricks.contracts.model.FinancingStatus;
import com.datatricks.contracts.model.Rental;
import com.datatricks.contracts.model.dto.inject.RentalInjectDto;
import com.datatricks.contracts.utils.ContractUtils;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RentalDto implements PageableDto {


    @JsonProperty("id")
    @Schema(description = "id of the rental", example = "1")
    private Long id;

    @JsonProperty("title")
    @Schema(description = "title of the rental", example = "Rental 1")
    private String title;

    @JsonProperty("line_type")
    @Schema(description = "line_type code of the rental", example = "RMAINT")
    private String lineTypeCode;

    @JsonProperty("type")
    @Schema(description = "type of the rental", example = "RENTAL or LEASE")
    private String type;

    @JsonProperty("arrangement_type")
    @NotBlank(message = "arrangement_type:please provide an arrangement type for this rental")
    @Schema(description = "arrangement type of the rental", example = "FINANCIAL or OPERATIONAL")
    private String arrangementType;

    @NotNull(message = "amount:please provide an amount for this rental")
    @JsonProperty("amount")
    @Schema(description = "amount of the rental", example = "1000.0")
    private Double originalAmount = 0.0;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "startDate:please provide a start date for this rental")
    @Schema(description = "start date of the rental", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "endDate:please provide an end date for this rental")
    @Schema(description = "end date of the rental", example = "2022-01-01")
    private LocalDate endDate;

    @JsonProperty("effective_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "effective date of the rental", example = "2021-01-01")
    private LocalDate effectiveDate;

    @JsonProperty("nominal_rate")
    @NotNull(message = "nominal_rate:please provide a nominal rate for this rental")
    @Schema(description = "nominal rate of the rental", example = "2.0")
    private Double nominalRate;

    @JsonProperty("equivalent_rate")
    @Schema(description = "equivalent rate of the rental", example = "5.0")
    private Double equivalentRate;

    @JsonProperty("residual_value")
    @NotNull(message = "residual_value:please provide a residual value for this rental")
    @Schema(description = "residual value of the rental", example = "10.0")
    private Double residualValue;

    @JsonProperty("separate_invoice")
    @Schema(description = "separate invoice of the rental", example = "true")
    private Boolean separateInvoice;

    @JsonProperty("suspended_invoice")
    @Schema(description = "suspended invoice of the rental", example = "true")
    private Boolean suspendedInvoice;

    @JsonProperty("mobile_extension")
    @Schema(description = "mobile extension of the rental", example = "true")
    private Boolean mobileExtension;

    @JsonProperty("calculation_basis")
    @NotNull(message = "calculation_basis:please provide a calculation basis for this rental")
    @Schema(description = "calculation basis of the rental", example = "ENUM_BASE360, ENUM_BASE365_366, ENUM_BASE365, ENUM_BASE365_25, ENUM_BASE_FREQUENCY or ENUM_BASE_YEARLY")
    private TypeBase calculationBasis = TypeBase.ENUM_BASE360;

    @JsonProperty("calculation_mode")
    @NotNull(message = "calculation_mode:please provide a calculation mode for this rental")
    @Schema(description = "calculation mode of the rental", example = "ENUM_BASE360, ENUM_BASE365_366, ENUM_BASE365, ENUM_BASE365_25, ENUM_BASE_FREQUENCY or ENUM_BASE_YEARLY")
    private TypeBase calculationMode = TypeBase.ENUM_BASE360;

    @JsonProperty("tax")
    @Schema(description = "tax of the rental", example = "TVA")
    private String tax;

    @JsonProperty("tax_rate")
    @Schema(description = "tax rate of the rental", example = "10.0")
    private Double taxRate;

    @JsonProperty("status")
    @Schema(description = "status of the rental", example = "INITIALIZED or EN_SERVICE")
    private FinancingStatus status;

    @JsonProperty("timetable_id")
    @Schema(description = "timetable of the rental", example = "1")
    private Long timetableId;

    @JsonProperty("contract_actor_id")
    @NotNull(message = "contract_actor_id:please provide a contract actor id for this rental")
    @Schema(description = "contract actor of the rental")
    private Long contractActorId;

    @JsonProperty("levels")
    @Schema(description = "levels of the rental")
    @JsonApiRelationships("levels")
    private List<@Valid LevelDto> rentalLevelsList;

    public RentalDto(RentalInjectDto rentalInjectDto) {
        this.title = rentalInjectDto.getTitle();
        this.lineTypeCode = rentalInjectDto.getLineType().getCode();
        this.type = rentalInjectDto.getType();
        this.arrangementType = rentalInjectDto.getArrangementType();
        this.originalAmount = rentalInjectDto.getOriginalAmount();
        this.startDate = ContractUtils.convertToLocalDate(rentalInjectDto.getStartDate());
        this.endDate = ContractUtils.convertToLocalDate(rentalInjectDto.getEndDate());
        this.effectiveDate = ContractUtils.convertToLocalDate(rentalInjectDto.getEffectiveDate());
        this.nominalRate = rentalInjectDto.getNominalRate();
        this.equivalentRate = rentalInjectDto.getEquivalentRate();
        this.residualValue = rentalInjectDto.getResidualValue();
        this.separateInvoice = rentalInjectDto.getSeparateInvoice();
        this.suspendedInvoice = rentalInjectDto.getSuspendedInvoice();
        this.mobileExtension = rentalInjectDto.getMobileExtension();
        this.calculationBasis = rentalInjectDto.getCalculationBasis();
        this.calculationMode = rentalInjectDto.getCalculationMode();
        this.tax = rentalInjectDto.getTax();
        this.taxRate = rentalInjectDto.getTaxRate();
        this.status = rentalInjectDto.getStatus();
        this.rentalLevelsList = rentalInjectDto.getRentalLevelsList();
    }

    public RentalDto(FinancialItemsDto financialItemsDto) {
        this.title = financialItemsDto.getTitle();
        this.separateInvoice = financialItemsDto.getSeparateInvoice();
        this.suspendedInvoice = financialItemsDto.getSuspendedInvoice();
        this.mobileExtension = financialItemsDto.getMobileExtension();
        this.contractActorId = financialItemsDto.getContractActorId();
        this.status = financialItemsDto.getStatus();
        this.rentalLevelsList = new ArrayList<>();
    }

    public RentalDto(Rental rental) {
        this.id = rental.getId();
        this.title = rental.getTitle();
        this.lineTypeCode = rental.getLineType().getCode();
        this.type = rental.getType();
        this.arrangementType = rental.getArrangementType();
        this.originalAmount = rental.getOriginalAmount();
        this.startDate = rental.getStartDate();
        this.endDate = rental.getEndDate();
        this.effectiveDate = rental.getEffectiveDate();
        this.nominalRate = rental.getNominalRate();
        this.equivalentRate = rental.getEquivalentRate();
        this.residualValue = rental.getResidualValue();
        this.separateInvoice = rental.getSeparateInvoice();
        this.suspendedInvoice = rental.getSuspendedInvoice();
        this.mobileExtension = rental.getMobileExtension();
        this.calculationBasis = rental.getCalculationBasis();
        this.calculationMode = rental.getCalculationMode();
        this.tax = rental.getTax();
        this.taxRate = rental.getTaxRate();
        this.status = rental.getStatus();
        this.timetableId = rental.getTimetableId().getId();
        this.contractActorId = rental.getContractActorId().getId();
        this.rentalLevelsList = new ArrayList<>();
    }
}
