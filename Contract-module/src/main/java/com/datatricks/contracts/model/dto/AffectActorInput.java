package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.model.dto.inject.ContractActorInjectDto;
import com.datatricks.contracts.utils.ContractUtils;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AffectActorInput {

    @JsonProperty("delegation_code")
    @Schema(description = "delegation code of the affect actor input", example = "3")
    private String delegation;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "start date of the affect actor input", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "end date of the affect actor input", example = "2024-01-01")
    private LocalDate endDate;

    public AffectActorInput(ContractActorInjectDto contractActorInjectDto) {
        this.delegation = contractActorInjectDto.getDelegation().getCode();
        this.startDate = ContractUtils.convertToLocalDate(contractActorInjectDto.getStartDate());
        this.endDate = ContractUtils.convertToLocalDate(contractActorInjectDto.getEndDate());
    }
}
