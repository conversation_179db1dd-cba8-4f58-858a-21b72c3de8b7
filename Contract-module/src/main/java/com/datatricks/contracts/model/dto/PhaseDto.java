package com.datatricks.contracts.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PhaseDto implements Serializable {

    @Schema(description = "id of the phase code.", example = "37")
    private Long id;

    @NotBlank(message = "phase_code.code: please provide a code")
    @Schema(description = "Code of the phase code.", example = "INI")
    private String code;

    @NotBlank(message = "phase_code.label: please provide a label")
    @Schema(description = "Label of the phase code.", example = "Initiale")
    private String label;

    @JsonProperty("associated_to")
    @Schema(description = "Associated to of the phase code.", example = "DOSSIER")
    private String associatedTo;

    private String language;
}
