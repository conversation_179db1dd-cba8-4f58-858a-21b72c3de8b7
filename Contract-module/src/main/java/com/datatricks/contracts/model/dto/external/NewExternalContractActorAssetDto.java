package com.datatricks.contracts.model.dto.external;

import com.datatricks.contracts.model.dto.NewContractActorAssetDto;
import com.datatricks.contracts.model.dto.PageableDto;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NewExternalContractActorAssetDto implements PageableDto {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("end_date")
    private LocalDate endDate;

    @JsonProperty("note")
    private String note;

    @JsonProperty("actor_reference")
    private String actorReference;

    @JsonProperty("contract_reference")
    private String contractReference;

    @JsonProperty("rental_id")
    private Long rentalId;

    @JsonProperty("accessory_id")
    private Long accessoryId;

    public NewExternalContractActorAssetDto(NewContractActorAssetDto newContractActorAssetDto, String contractReference, String actorReference) {
        this.id = newContractActorAssetDto.getId();
        this.startDate = newContractActorAssetDto.getStartDate();
        this.endDate = newContractActorAssetDto.getEndDate();
        this.note = newContractActorAssetDto.getNote();
        this.actorReference = actorReference;
        this.contractReference = contractReference;
        this.rentalId = newContractActorAssetDto.getRentalId();
        this.accessoryId = newContractActorAssetDto.getAccessoryId();
    }
}
