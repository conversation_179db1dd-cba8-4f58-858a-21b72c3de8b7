package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.enums.TypeBase;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.time.LocalDate;
@Getter
public class RentalParam {
    @JsonProperty("id")
    @Schema(description = "id of the rental", example = "1")
    private Long id;

    @JsonProperty("title")
    @Schema(description = "title of the rental", example = "Rental 1")
    private String title;

    @JsonProperty("allocation_code")
    @Schema(description = "allocation code of the rental", example = "RMAINT")
    private String allocationCode;

    @JsonProperty("type")
    @Schema(description = "type of the rental", example = "RENTAL or LEASE")
    private String type;

    @JsonProperty("arrangement_type")
    @Schema(description = "arrangement type of the rental", example = "FINANCIAL or OPERATIONAL")
    private String arrangementType;

    @JsonProperty("amount")
    @Schema(description = "amount of the rental", example = "1000.0")
    private Double originalAmount;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "start date of the rental", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "end date of the rental", example = "2022-01-01")
    private LocalDate endDate;

    @JsonProperty("effective_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "effective date of the rental", example = "2021-01-01")
    private LocalDate effectiveDate;

    @JsonProperty("nominal_rate")
    @Schema(description = "nominal rate of the rental", example = "2.0")
    private Double nominalRate;

    @JsonProperty("equivalent_rate")
    @Schema(description = "equivalent rate of the rental", example = "5.0")
    private Double equivalentRate;

    @JsonProperty("residual_value")
    @Schema(description = "residual value of the rental", example = "10.0")
    private Double residualValue;

    @JsonProperty("separate_invoice")
    @Schema(description = "separate invoice of the rental", example = "true")
    private Boolean separateInvoice;

    @JsonProperty("suspended_invoice")
    @Schema(description = "suspended invoice of the rental", example = "true")
    private Boolean suspendedInvoice;

    @JsonProperty("mobile_extension")
    @Schema(description = "mobile extension of the rental", example = "true")
    private Boolean mobileExtension;

    @JsonProperty("calculation_basis")
    @Schema(description = "calculation basis of the rental", example = "ENUM_BASE360, ENUM_BASE365_366, ENUM_BASE365, ENUM_BASE365_25, ENUM_BASE_FREQUENCY or ENUM_BASE_YEARLY")
    private TypeBase calculationBasis;

    @JsonProperty("calculation_mode")
    @Schema(description = "calculation mode of the rental", example = "ENUM_BASE360, ENUM_BASE365_366, ENUM_BASE365, ENUM_BASE365_25, ENUM_BASE_FREQUENCY or ENUM_BASE_YEARLY")
    private TypeBase calculationMode;

    @JsonProperty("tax")
    @Schema(description = "tax of the rental", example = "TVA")
    private String tax;

    @JsonProperty("tax_rate")
    @Schema(description = "tax rate of the rental", example = "10.0")
    private Double taxRate;

    @JsonProperty("status")
    @Schema(description = "status of the rental", example = "ACTIVE or INACTIVE")
    private String status;

    @JsonProperty("timetable_id")
    @Schema(description = "timetable of the rental", example = "1")
    private Long timetableId;
}
