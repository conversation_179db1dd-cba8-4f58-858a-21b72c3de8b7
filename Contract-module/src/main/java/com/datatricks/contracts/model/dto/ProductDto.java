package com.datatricks.contracts.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProductDto {

    @Schema(description = "id of the product.", example = "66")
    private Long id;

    @Schema(description = "label of the product.", example = "Full Transfer")
    private String label;

    @Schema(description = "code of the product.", example = "CESCOM")
    private String code;
}
