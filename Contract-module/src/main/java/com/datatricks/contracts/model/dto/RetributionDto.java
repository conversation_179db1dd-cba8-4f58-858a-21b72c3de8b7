package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.enums.TypeBase;
import com.datatricks.contracts.model.FinancingStatus;
import com.datatricks.contracts.model.Retribution;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RetributionDto implements PageableDto {

    @JsonProperty("id")
    @Schema(description = "id of the retribution", example = "1")
    private Long id;

    @JsonProperty("title")
    @Schema(description = "title of the retribution", example = "Rental 1")
    private String title;

    @JsonProperty("line_type")
    @Schema(description = "line_type code of the retribution", example = "RMAINT")
    private String lineTypeCode;

    @JsonProperty("arrangement_type")
    @NotBlank(message = "arrangement_type:please provide an arrangement type for this retribution")
    @Schema(description = "arrangement type of the retribution", example = "FINANCIAL or OPERATIONAL")
    private String arrangementType;

    @NotNull(message = "amount:please provide an amount for this retribution")
    @JsonProperty("amount")
    @Schema(description = "amount of the retribution", example = "1000.0")
    private Double originalAmount = 0.0;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "startDate:please provide a start date for this retribution")
    @Schema(description = "start date of the retribution", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "endDate:please provide an end date for this retribution")
    @Schema(description = "end date of the retribution", example = "2022-01-01")
    private LocalDate endDate;

    @JsonProperty("separate_invoice")
    @Schema(description = "separate invoice of the retribution", example = "true")
    private Boolean separateInvoice;

    @JsonProperty("suspended_invoice")
    @Schema(description = "suspended invoice of the retribution", example = "true")
    private Boolean suspendedInvoice;

    @JsonProperty("calculation_basis")
    @NotNull(message = "calculation_basis:please provide a calculation basis for this retribution")
    @Schema(description = "calculation basis of the retribution", example = "ENUM_BASE360, ENUM_BASE365_366, ENUM_BASE365, ENUM_BASE365_25, ENUM_BASE_FREQUENCY or ENUM_BASE_YEARLY")
    private TypeBase calculationBasis = TypeBase.ENUM_BASE360;

    @JsonProperty("calculation_mode")
    @NotNull(message = "calculation_mode:please provide a calculation mode for this retribution")
    @Schema(description = "calculation mode of the retribution", example = "ENUM_BASE360, ENUM_BASE365_366, ENUM_BASE365, ENUM_BASE365_25, ENUM_BASE_FREQUENCY or ENUM_BASE_YEARLY")
    private TypeBase calculationMode = TypeBase.ENUM_BASE360;

    @JsonProperty("tax")
    @Schema(description = "tax of the retribution", example = "TVA")
    private String tax;

    @JsonProperty("tax_rate")
    @Schema(description = "tax rate of the retribution", example = "10.0")
    private Double taxRate;

    @JsonProperty("status")
    @Schema(description = "status of the retribution", example = "ACTIVE or INACTIVE")
    private FinancingStatus status;

    @JsonProperty("timetable_id")
    @Schema(description = "timetable of the retribution", example = "1")
    private Long timetableId;

    @JsonProperty("contract_actor_id")
    @NotNull(message = "contract_actor_id:please provide a contract actor id for this retribution")
    @Schema(description = "contract actor of the retribution")
    private Long contractActorId;

    @JsonProperty("levels")
    @Schema(description = "levels of the retribution")
    private List<@Valid LevelDto> retributionLevelsList;

    public RetributionDto(FinancialItemsDto financialItemsDto) {
        this.id = financialItemsDto.getId();
        this.title = financialItemsDto.getTitle();
        this.separateInvoice = financialItemsDto.getSeparateInvoice();
        this.suspendedInvoice = financialItemsDto.getSuspendedInvoice();
        this.status = financialItemsDto.getStatus();
        this.contractActorId = financialItemsDto.getContractActorId();
        this.retributionLevelsList = new ArrayList<>();
    }
    public RetributionDto(Retribution retribution) {
        this.id = retribution.getId();
        this.title = retribution.getTitle();
        this.lineTypeCode = retribution.getLineType().getCode();
        this.arrangementType = retribution.getArrangementType();
        this.startDate = retribution.getStartDate();
        this.endDate = retribution.getEndDate();
        this.separateInvoice = retribution.getSeparateInvoice();
        this.suspendedInvoice = retribution.getSuspendedInvoice();
        this.calculationBasis = retribution.getCalculationBasis();
        this.calculationMode = retribution.getCalculationMode();
        this.tax = retribution.getTax();
        this.taxRate = retribution.getTaxRate();
        this.status = retribution.getStatus();
        this.timetableId = retribution.getTimetableId().getId();
        this.contractActorId = retribution.getContractActorId().getId();
        this.retributionLevelsList = new ArrayList<>();
    }
}
