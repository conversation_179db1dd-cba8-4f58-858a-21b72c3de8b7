package com.datatricks.contracts.model;

import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(
        name = "contract_actor_payments",
        uniqueConstraints = @UniqueConstraint(columnNames = {"contract_actor_id", "type", "deleted_at"}))
public class ContractActorPayments extends BaseEntity {
    @Id
    @GeneratedValue
    @JsonProperty("id")
    private Long id;

    @JsonProperty("type")
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private ContractActorPaymentsType type;

    @JsonProperty("start_date")
    @Column(name = "start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonProperty("PaymentMethod")
    private PaymentMethod paymentMethod;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bank_account_id")
    @JsonProperty("bank_account")
    private BankAccount bankAccount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonProperty("contract_actor")
    @JoinColumn(name = "contract_actor_id")
    private ContractActor contractActor;
}
