package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TimetablesSummaryViewNotSeparateBillingDto implements PageableDto{

    @Schema(description = "Id of the timetable", example = "1")
    private Long id;

    @JsonProperty("company_reference")
    @Schema(description = "Company reference", example = "ACT_001")
    private String companyReference;

    @JsonProperty("company_name")
    @Schema(description = "Company name", example = "Company 1")
    private String companyName;

    @JsonProperty("actor_reference")
    @Schema(description = "Actor reference", example = "ACT_001")
    private String actorReference;

    @JsonProperty("actor_name")
    @Schema(description = "Actor name", example = "Actor 1")
    private String actorName;

    @JsonProperty("actor_id")
    @Schema(description = "Id of actor", example = "1")
    private Long actorId;

    @JsonProperty("contract_reference")
    @Schema(description = "Contract reference", example = "SOC_001")
    private String contractReference;

    @JsonProperty("contract_id")
    @Schema(description = "Id of contract", example = "1")
    private Long contractId;

    @JsonProperty("company_id")
    @Schema(description = "Id of company", example = "1")
    private Long companyId;

    @JsonProperty("contract_activity_code")
    @Schema(description = "Code of activity of a contract", example = "CIB")
    private String contractActivityCode;

    @JsonProperty("contract_product_code")
    @Schema(description = "Code of product of a contract", example = "CIBCIB")
    private String contractProductCode;

    @JsonProperty("time_table_status")
    @Schema(description = "Status of timetable", example = "ACTIVE")
    private String timetableStatus;

    @JsonProperty("timetable_item_status")
    @Schema(description = "Status of timetable item", example = "ACTIVE")
    private String timetableItemStatus;

    @JsonProperty("depreciation")
    @Schema(description = "Depreciation", example = "1.0")
    private Float depreciation;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Due date", example = "2021-01-01")
    private LocalDate dueDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End date", example = "2023-01-01")
    private LocalDate endDate;

    @JsonProperty("interest")
    @Schema(description = "Interest", example = "1.0")
    private Float interest;

    @JsonProperty("nominal_rate")
    @Schema(description = "Nominal rate", example = "1.0")
    private Float nominalRate;

    @JsonProperty("rate")
    @Schema(description = "Rate", example = "1.0")
    private Float rate;

    @JsonProperty("rent")
    @Schema(description = "Rent", example = "1.0")
    private Float rent;

    @JsonProperty("residual_value")
    @Schema(description = "Residual value", example = "1.0")
    private Float residualValue;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start date", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("tax_amount")
    @Schema(description = "Tax amount", example = "1.0")
    private Float taxAmount;

    @JsonProperty("amortization")
    @Schema(description = "Amortization", example = "1.0")
    private Float amortization;

    @JsonProperty("unpaid")
    @Schema(description = "Unpaid", example = "1.0")
    private Float unpaid;

    @JsonProperty("timetable_id")
    @Schema(description = "Id of timetable", example = "1")
    private Long timetableId;

    @JsonProperty("role_code")
    @Schema(description = "Role code", example = "ROLE_001")
    private String roleCode;

    @JsonProperty("iban")
    @Schema(description = "Iban", example = "ES123456789")
    private String iban;

    @JsonProperty("client_address_id")
    @Schema(description = "Id of client address", example = "1")
    private Long clientAddressId;

    @JsonProperty("is_separate_billing")
    @Schema(description = "Is separate billing", example = "true")
    private boolean isSeparateBilling;

    @JsonProperty("tax")
    @Schema(description = "Tax", example = "1.0")
    private Double tax;

    @JsonProperty("tax_code")
    @Schema(description = "Tax code", example = "TAX_001")
    private String taxCode;

    @JsonProperty("deleted_at")
    @Schema(description = "Deleted at", example = "2021-01-01")
    private Instant deletedAt;

    @JsonProperty("created_at")
    @Schema(description = "Created at", example = "2021-01-01")
    private Instant createdAt;

    @JsonProperty("modified_at")
    @Schema(description = "Modified at", example = "2021-01-01")
    private Instant modifiedAt;

}