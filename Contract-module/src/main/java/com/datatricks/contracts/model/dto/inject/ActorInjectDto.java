package com.datatricks.contracts.model.dto.inject;

import com.datatricks.contracts.model.ActorTypes;
import com.datatricks.contracts.model.dto.*;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiMeta;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.Set;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActorInjectDto {
    @JsonProperty("id")
    @JsonIgnore
    @JsonApiId
    private Long id;

    @JsonApiType
    private String myType = "actor";

    @JsonApiMeta
    private String role_to_be_assigned_to_contract;

    @JsonProperty("reference")
    @Schema(description = "Reference of the actor", example = "ACT_814610526")
    private String reference;

    @JsonProperty("external_reference")
    @Schema(description = "External reference of the actor", example = "ACT_814610526")
    private String externalReference;

    @JsonProperty("country")
    @NotNull(message = "country:please provide a country")
    @Valid
    @Schema(description = "Country of the actor")
    @JsonApiRelationships("country")
    @JsonIgnore
    @ManyToOne
    private CountryDto country;

    @JsonProperty("short_name")
    @NotBlank(message = "short_name:please provide a short name")
    @Schema(description = "Short name of the actor", example = "DATA-TRICKS")
    private String shortName;

    @JsonProperty("name")
    @NotBlank(message = "name:please provide a name")
    @Schema(description = "Name of the actor", example = "Data Tricks")
    private String name;

    @JsonProperty("activity")
    @Schema(description = "Activity of the actor")
    @JsonApiRelationships("activity")
    @ManyToOne
    @JsonIgnore
    private ActivityInjectDto activity;

    @JsonProperty("vat")
    @Schema(description = "VAT of the actor", example = "FR*********01")
    private String vat;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("registration_date")
    @Schema(description = "Registration date of the actor", example = "2021-07-01")
    private Date registrationDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("company_creation_date")
    @Schema(description = "Company creation date of the actor", example = "2021-07-01")
    private Date companyCreationDate;

    @JsonProperty("registration_country")
    @Schema(description = "Registration country of the actor", example = "FR")
    private String registrationCountry;

    @JsonProperty("register_type")
    @Schema(description = "Register type of the actor", example = "SIREN")
    private String registerType;

    @JsonProperty("register_number")
    @Schema(description = "Register number of the actor", example = "*********")
    private String registerNumber;

    @JsonProperty("type")
    @Schema(description = "Type of the actor", example = "NATURAL_PERSON or MANAGEMENT_COMPANY")
    private ActorTypes type;

    @JsonProperty("legal_category")
    @NotNull(message = "legal_category:please provide a legal category")
    @Schema(description = "Legal category of the actor")
    @JsonApiRelationships("legal_category")
    @ManyToOne
    @JsonIgnore
    private LegalCategoryDto legalCategory;

    @JsonProperty("national_identity")
    @NotBlank(message = "national_identity:please provide a national identity")
    @Schema(description = "National identity of the actor", example = "*********")
    private String nationalIdentity;

    @JsonProperty("feed_channel")
    @Schema(description = "Feed channel of the actor", example = "1")
    private String feedChannel;

    @JsonProperty("memo")
    @Schema(description = "Memo of the actor", example = "This is a memo")
    private String memo;

    @JsonProperty("tax_reference")
    @Schema(description = "Tax reference of the actor", example = "*********")
    private String taxReference;

    @JsonProperty("phase")
    @NotNull(message = "phase:please provide a phase")
    @Valid
    @Schema(description = "Phase of the actor")
    @JsonApiRelationships("phase")
    @ManyToOne
    @JsonIgnore
    private PhaseInjectDto phase;

    @JsonProperty("milestone")
    @NotNull(message = "milestone:please provide a milestone")
    @Valid
    @Schema(description = "Milestone of the actor")
    @JsonApiRelationships("milestone")
    @ManyToOne
    @JsonIgnore
    private MilestoneInjectDto milestone;

    @JsonProperty("roles")
    @NotNull(message = "role:please provide a role for this actor")
    @Valid
    @Schema(description = "Role of the actor")
    @JsonApiRelationships("roles")
    @OneToMany
    @JsonIgnore
    private Set<RoleInjectDto> roles;

    @JsonProperty("bank_accounts")
    @Schema(description = "Bank accounts of the actor")
    @JsonApiRelationships("bank_accounts")
    @OneToMany
    @JsonIgnore
    private Set<BankAccountInjectDto> bankAccounts;

    @JsonProperty("addresses")
    @JsonApiRelationships("addresses")
    @Schema(description = "Address of the actor")
    @OneToMany
    @JsonIgnore
    private Set<AddressInjectDto> addresses;

    @JsonProperty("contacts")
    @Schema(description = "Contacts of the actor")
    @JsonApiRelationships("contacts")
    @OneToMany
    @JsonIgnore
    private Set<ContactDto> contacts;

    public ActorInjectDto(SimplifiedActorDto actor) {
        this.id = actor.getId();
        this.reference = actor.getReference();
        this.externalReference = actor.getExternalReference();
        this.country = actor.getCountry();
        this.shortName = actor.getShortName();
        this.name = actor.getName();
    }

    public ActorInjectDto(ActorResponseDto actor) {
        this.id = actor.getId();
        this.reference = actor.getReference();
        this.shortName = actor.getShortName();
        this.name = actor.getName();
    }
}

