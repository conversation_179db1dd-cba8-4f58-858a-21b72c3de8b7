package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.enums.TypeBase;
import com.datatricks.contracts.model.Accessory;
import com.datatricks.contracts.model.FinancingStatus;
import com.datatricks.contracts.model.dto.inject.AccessoryInjectDto;
import com.datatricks.contracts.utils.ContractUtils;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AccessoryDto implements PageableDto {
    @JsonProperty("id")
    @Schema(description = "id of the rental", example = "1")
    private Long id;

    @JsonProperty("title")
    @Schema(description = "title of the rental", example = "Rental 1")
    private String title;

    @JsonProperty("line_type")
    @Schema(description = "line_type code of the accessory", example = "RMAINT")
    private String lineTypeCode;

    @JsonProperty("arrangement_type")
    @NotBlank(message = "arrangement_type:please provide an arrangement type for this accessory")
    @Schema(description = "arrangement type of the accessory", example = "FINANCIAL or OPERATIONAL")
    private String arrangementType;

    @JsonProperty("amount")
    @Schema(description = "amount of the rental", example = "1000.0")
    private Double originalAmount = 0.0;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "startDate:please provide a start date for this accessory")
    @Schema(description = "start date of the rental", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @NotNull(message = "endDate:please provide an end date for this accessory")
    @Schema(description = "end date of the rental", example = "2022-01-01")
    private LocalDate endDate;

    @JsonProperty("separate_invoice")
    @Schema(description = "separate invoice of the accessory", example = "true")
    private Boolean separateInvoice;

    @JsonProperty("suspended_invoice")
    @Schema(description = "suspended invoice of the accessory", example = "true")
    private Boolean suspendedInvoice;

    @JsonProperty("mobile_extension")
    @Schema(description = "mobile extension of the accessory", example = "true")
    private Boolean mobileExtension;

    @JsonProperty("calculation_basis")
    @Schema(description = "calculation basis of the accessory", example = "ENUM_BASE360, ENUM_BASE365_366, ENUM_BASE365, ENUM_BASE365_25, ENUM_BASE_FREQUENCY or ENUM_BASE_YEARLY")
    private TypeBase calculationBasis = TypeBase.ENUM_BASE360;

    @JsonProperty("calculation_mode")
    @Schema(description = "calculation mode of the accessory", example = "ENUM_BASE360, ENUM_BASE365_366, ENUM_BASE365, ENUM_BASE365_25, ENUM_BASE_FREQUENCY or ENUM_BASE_YEARLY")
    private TypeBase calculationMode = TypeBase.ENUM_BASE360;

    @JsonProperty("tax")
    @Schema(description = "tax of the accessory", example = "TVA")
    private String tax;

    @JsonProperty("tax_rate")
    @Schema(description = "tax rate of the accessory", example = "10.0")
    private Double taxRate;

    @JsonProperty("status")
    @Schema(description = "status of the accessory", example = "INITIALIZED or EN_SERVICE")
    private FinancingStatus status;

    @JsonProperty("timetable_id")
    @Schema(description = "timetable of the accessory", example = "1")
    private Long timetableId;

    @JsonProperty("contract_actor_id")
    @NotNull(message = "contract_actor_id:please provide a contract actor id for this accessory")
    @Schema(description = "contract actor of the accessory")
    private Long contractActorId;

    @JsonProperty("levels")
    @Schema(description = "levels of the accessory")
    @JsonApiRelationships("levels")
    private List<@Valid LevelDto> accessoryLevelsList;

    public AccessoryDto(AccessoryInjectDto accessoryInjectDto) {
        this.id = accessoryInjectDto.getId();
        this.title = accessoryInjectDto.getTitle();
        this.lineTypeCode = accessoryInjectDto.getLineType().getCode();
        this.arrangementType = accessoryInjectDto.getArrangementType();
        this.originalAmount = accessoryInjectDto.getOriginalAmount();
        this.startDate = ContractUtils.convertToLocalDate(accessoryInjectDto.getStartDate());
        this.endDate = ContractUtils.convertToLocalDate(accessoryInjectDto.getEndDate());
        this.separateInvoice = accessoryInjectDto.getSeparateInvoice();
        this.suspendedInvoice = accessoryInjectDto.getSuspendedInvoice();
        this.mobileExtension = accessoryInjectDto.getMobileExtension();
        this.calculationBasis = accessoryInjectDto.getCalculationBasis();
        this.calculationMode = accessoryInjectDto.getCalculationMode();
        this.tax = accessoryInjectDto.getTax();
        this.taxRate = accessoryInjectDto.getTaxRate();
        this.status = accessoryInjectDto.getStatus();
        this.accessoryLevelsList = accessoryInjectDto.getAccessoryLevelsList();
    }

    public AccessoryDto(FinancialItemsDto financialItemsDto) {
        this.id = financialItemsDto.getId();
        this.title = financialItemsDto.getTitle();
        this.separateInvoice = financialItemsDto.getSeparateInvoice();
        this.suspendedInvoice = financialItemsDto.getSuspendedInvoice();
        this.mobileExtension = financialItemsDto.getMobileExtension();
        this.contractActorId = financialItemsDto.getContractActorId();
        this.status = financialItemsDto.getStatus();
        this.accessoryLevelsList = new ArrayList<>();
    }

    public AccessoryDto(Accessory accessory) {
        this.id = accessory.getId();
        this.title = accessory.getTitle();
        this.lineTypeCode = accessory.getLineType().getCode();
        this.arrangementType = accessory.getArrangementType();
        this.startDate = accessory.getStartDate();
        this.endDate = accessory.getEndDate();
        this.separateInvoice = accessory.getSeparateInvoice();
        this.suspendedInvoice = accessory.getSuspendedInvoice();
        this.mobileExtension = accessory.getMobileExtension();
        this.calculationBasis = accessory.getCalculationBasis();
        this.calculationMode = accessory.getCalculationMode();
        this.tax = accessory.getTax();
        this.taxRate = accessory.getTaxRate();
        this.status = accessory.getStatus();
        this.timetableId = accessory.getTimetableId().getId();
        this.contractActorId = accessory.getContractActorId().getId();
        this.accessoryLevelsList = new ArrayList<>();
    }
}
