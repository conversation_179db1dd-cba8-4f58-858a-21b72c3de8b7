package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.time.LocalDate;

@Getter
public class RepaymentParam {

    @JsonProperty("id")
    @Schema(description = "Id of the repayment", example = "1")
    private Long id;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start date of the repayment", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End date of the repayment", example = "2021-01-01")
    private LocalDate endDate;

    @JsonProperty("due_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Due date of the repayment", example = "2021-01-01")
    private LocalDate dueDate;

    @JsonProperty("tax_code")
    @Schema(description = "Tax code of the repayment", example = "VAT")
    private String taxCode;

	@JsonProperty("tax_rate")
    @Schema(description = "Tax rate of the repayment", example = "20.0")
	private Double taxRate;

    @JsonProperty("rental")
    @Schema(description = "Rental id of the repayment", example = "1")
    private Long rentalId;

    @JsonProperty("accessory")
    @Schema(description = "Accessory id of the repayment", example = "1")
    private Long accessoryId;

    @JsonProperty("contract_actor")
    @Schema(description = "Contract actor id of the repayment", example = "1")
    private Long contractActorId;
}
