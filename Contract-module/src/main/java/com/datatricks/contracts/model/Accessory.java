package com.datatricks.contracts.model;

import com.datatricks.contracts.enums.TypeBase;
import com.datatricks.contracts.model.dto.AccessoryDto;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "accessories")
public class Accessory extends BaseEntity implements Rubrique {
    @Id
    @GeneratedValue
    @JsonProperty("id")
    private Long id;

    @Column(name = "title")
    private String title;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "line_type_code", referencedColumnName = "code")
    private LineType lineType;

    @Column(name = "arrangement_type")
    @JsonProperty("arrangement_type")
    private String arrangementType;

    @Column(name = "amount")
    @JsonProperty("amount")
    private Double amount = 0.0;

    @Column(name = "start_date")
    @JsonProperty("start_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startDate;

    @Column(name = "end_date")
    @JsonProperty("end_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endDate;

    @Column(name = "separate_invoice")
    @JsonProperty("separate_invoice")
    private Boolean separateInvoice;

    @Column(name = "suspended_invoice")
    @JsonProperty("suspended_invoice")
    private Boolean suspendedInvoice;

    @Column(name = "mobile_extension")
    @JsonProperty("mobile_extension")
    private Boolean mobileExtension;

    @Setter(AccessLevel.NONE)
    @Column(name = "calculation_basis")
    @JsonProperty("calculation_basis")
    private TypeBase calculationBasis;

    @JsonProperty("calculation_basis")
    public void setCalculationBasisFromString(String base) {
        this.calculationBasis = TypeBase.fromString(base);
    }

    @JsonProperty("calculation_basis")
    public void setCalculationBasis(TypeBase base) {
        this.calculationBasis = base;
    }

    @Setter(AccessLevel.NONE)
    @Column(name = "calculation_mode")
    @JsonProperty("calculation_mode")
    private TypeBase calculationMode;

    @JsonProperty("calculation_mode")
    public void setCalculationModeFromString(String mode) {
        this.calculationMode = TypeBase.fromString(mode);
    }

    @JsonProperty("calculation_mode")
    public void setCalculationMode(TypeBase mode) {
        this.calculationMode = mode;
    }

    @JsonProperty("tax")
    @Column(name = "tax")
    private String tax;

    @JsonProperty("tax_rate")
    @Column(name = "tax_rate")
    private Double taxRate;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    @JsonProperty("status")
    private FinancingStatus status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "contractActorId")
    private ContractActor contractActorId;

    @OneToOne(fetch = FetchType.EAGER) // don't change it please
    @JoinColumn(name = "timetableId")
    private Timetable timetableId;

    public Accessory(AccessoryDto accessoryDto) {
        this.title = accessoryDto.getTitle();
        this.arrangementType = accessoryDto.getArrangementType();
        this.amount = accessoryDto.getOriginalAmount();
        this.startDate = accessoryDto.getStartDate();
        this.endDate = accessoryDto.getEndDate();
        this.separateInvoice = accessoryDto.getSeparateInvoice();
        this.suspendedInvoice = accessoryDto.getSuspendedInvoice();
        this.mobileExtension = accessoryDto.getMobileExtension();
        this.calculationBasis = accessoryDto.getCalculationBasis();
        this.calculationMode = accessoryDto.getCalculationMode();
        this.tax = accessoryDto.getTax();
        this.taxRate = accessoryDto.getTaxRate();
        this.status = accessoryDto.getStatus();
    }
}
