package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.enums.TypeBase;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
@Getter
@Setter
public class RetributionResponseDto implements PageableDto {

    @JsonProperty("id")
    @Schema(description = "id of the retribution", example = "1")
    private Long id;

    @JsonProperty("title")
    @Schema(description = "title of the retribution", example = "Rental 1")
    private String title;

    @JsonProperty("allocation_code")
    @Schema(description = "allocation code of the retribution", example = "RMAINT")
    private String allocationCode;

    @JsonProperty("arrangement_type")
    @Schema(description = "arrangement type of the retribution", example = "FINANCIAL or OPERATIONAL")
    private String arrangementType;

    @JsonProperty("amount")
    @Schema(description = "amount of the retribution", example = "1000.0")
    private Double originalAmount;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "start date of the retribution", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "end date of the retribution", example = "2022-01-01")
    private LocalDate endDate;

    @JsonProperty("separate_invoice")
    @Schema(description = "separate invoice of the retribution", example = "true")
    private Boolean separateInvoice;

    @JsonProperty("suspended_invoice")
    @Schema(description = "suspended invoice of the retribution", example = "true")
    private Boolean suspendedInvoice;

    @JsonProperty("calculation_basis")
    @Schema(description = "calculation basis of the retribution", example = "ENUM_BASE360, ENUM_BASE365_366, ENUM_BASE365, ENUM_BASE365_25, ENUM_BASE_FREQUENCY or ENUM_BASE_YEARLY")
    private TypeBase calculationBasis;

    @JsonProperty("calculation_mode")
    @Schema(description = "calculation mode of the retribution", example = "ENUM_BASE360, ENUM_BASE365_366, ENUM_BASE365, ENUM_BASE365_25, ENUM_BASE_FREQUENCY or ENUM_BASE_YEARLY")
    private TypeBase calculationMode;

    @JsonProperty("tax")
    @Schema(description = "tax of the retribution", example = "TVA")
    private String tax;

    @JsonProperty("tax_rate")
    @Schema(description = "tax rate of the retribution", example = "10.0")
    private Double taxRate;

	@JsonProperty("status")
    @Schema(description = "status of the retribution", example = "ACTIVE or INACTIVE")
    private String status;

    @JsonProperty("timetable")
    @Schema(description = "timetable of the retribution")
    private TimetableDto timetableId;

    @JsonProperty("contract_actor")
    @Schema(description = "contract actor of the retribution")
    private ContractActorResponseDto contractActorId;

    @JsonProperty("levels")
    @Schema(description = "levels of the retribution")
    private List<LevelDto> retributionLevelsList = new ArrayList<>();
}