package com.datatricks.contracts.model.dto.external;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@AllArgsConstructor
@Getter
@Setter
public class EngineLevelFrontDto {
    @JsonProperty("period_number")
    private int periodNumber;

    @JsonProperty("amount")
    private double amount;

    @JsonProperty("order")
    private int order;
}
