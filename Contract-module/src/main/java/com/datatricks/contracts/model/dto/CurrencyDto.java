package com.datatricks.contracts.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
public class CurrencyDto {
    @Schema(description = "id of the currency code.", example = "1460")
    private Long id;

    @NotBlank(message = "currency_code.name:please provide a name")
    @Schema(description = "Name of the currency code.", example = "Dollars")
    private String name;

    @NotBlank(message = "currency_code.code:please provide a code")
    @Schema(description = "Code of the currency code.", example = "USD")
    private String code;
}
