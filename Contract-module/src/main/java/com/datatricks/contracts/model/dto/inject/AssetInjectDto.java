package com.datatricks.contracts.model.dto.inject;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AssetInjectDto {

    @JsonApiId
    private Long id;

    @JsonApiType
    private String myType = "asset";

    private String reference;

    private String label;

    @JsonProperty("order_number")
    private int orderNumber;

    private String description;

    @JsonProperty("total_value")
    private double totalValue;

    @JsonApiRelationships("country")
    @JsonIgnore
    @ManyToOne
    private CountryInjectDto country;

    @JsonApiRelationships("milestone")
    @JsonIgnore
    @ManyToOne
    private MilestoneInjectDto milestone;

    @JsonApiRelationships("phase")
    @JsonIgnore
    @ManyToOne
    private PhaseInjectDto phase;

    @JsonApiRelationships("management_company")
    @JsonIgnore
    @OneToMany
    private ActorInjectDto managementCompany;

    @JsonApiRelationships("elements")
    @JsonIgnore
    @OneToMany
    private Set<ElementInjectDto> elements;

    @JsonApiRelationships("contract_actor_asset")
    @JsonIgnore
    @ManyToOne
    private ContractActorAssetInjectDto contractActorAsset;
}
