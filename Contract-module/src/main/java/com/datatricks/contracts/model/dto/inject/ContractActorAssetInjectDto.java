package com.datatricks.contracts.model.dto.inject;

import com.datatricks.contracts.model.dto.PageableDto;
import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import com.toedter.spring.hateoas.jsonapi.JsonApiRelationships;
import com.toedter.spring.hateoas.jsonapi.JsonApiType;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ContractActorAssetInjectDto implements PageableDto {
    @JsonProperty("id")
    @JsonApiId
    @JsonIgnore
    private Long id;

    @JsonApiType
    private String myType = "contract_actor_asset";

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("end_date")
    private Date endDate;

    @JsonProperty("note")
    private String note;

    @JsonProperty("actor")
    @JsonApiRelationships("actor")
    @JsonIgnore
    @ManyToOne
    private ActorInjectDto actor;

    @JsonProperty("contract")
    @JsonApiRelationships("contract")
    @JsonIgnore
    @ManyToOne
    private ContractInjectDto contract;

    @JsonProperty("rental")
    @JsonApiRelationships("rental")
    @JsonIgnore
    @ManyToOne
    private RentalInjectDto rental;

    @JsonProperty("accessory")
    @JsonApiRelationships("accessory")
    @JsonIgnore
    @ManyToOne
    private AccessoryInjectDto accessory;

    @JsonProperty("asset")
    @JsonApiRelationships("asset")
    @JsonIgnore
    @ManyToOne
    private AssetInjectDto asset;
}
