package com.datatricks.contracts.model.dto;

import com.datatricks.contracts.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class NormalizedAddressDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("reference")
    private String reference;

    @JsonProperty("city_zip_country")
    private String cityZipCountry;

    @JsonProperty("complete_street")
    private String completeStreet;

    @JsonProperty("head_office")
    private Boolean headOffice;

    @JsonProperty("is_principal")
    private Boolean isPrincipal;
    
    @JsonProperty("is_delivery")
    private Boolean isDelivery;

    @JsonProperty("effective_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate effectiveDate;
}
