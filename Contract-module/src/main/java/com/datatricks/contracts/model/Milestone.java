package com.datatricks.contracts.model;

import com.datatricks.contracts.model.dto.MilestoneDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.toedter.spring.hateoas.jsonapi.JsonApiId;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "milestones")
public class Milestone {
    @Id
    @JsonProperty("id")
    @JsonApiId
    private Long id;

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "label")
    private String label;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "phaseId")
    @JsonIgnore
    private Phase phaseId;

    @Column(name = "language")
    private String language;

    @Column(name = "active")
    private Boolean active;

    public Milestone(String code) {
        this.code = code;
    }

    public Milestone(MilestoneDto milestone) {
        this.code = milestone.getCode();
        this.label = milestone.getLabel();
    }
}
