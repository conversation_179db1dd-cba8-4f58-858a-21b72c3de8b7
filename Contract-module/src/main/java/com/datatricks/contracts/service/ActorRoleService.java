package com.datatricks.contracts.service;

import com.datatricks.contracts.exception.BusinessException;
import com.datatricks.contracts.exception.ResourcesNotFoundException;
import com.datatricks.contracts.model.ActorRole;
import com.datatricks.contracts.repository.ActorRoleRepository;
import com.datatricks.contracts.utils.TransactionSynchronizationUtil;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class ActorRoleService {
    private final ActorRoleRepository actorRoleRepository;
    private static final String ERRORMESSAGE = "Error while processing message ";
    private final TransactionSynchronizationUtil transactionSynchronizationUtil;
    private final String KAFKAERROR = "DATA-SYNC-ERROR";
    public ActorRoleService(ActorRoleRepository actorRoleRepository, TransactionSynchronizationUtil transactionSynchronizationUtil) {
        this.actorRoleRepository = actorRoleRepository;
        this.transactionSynchronizationUtil = transactionSynchronizationUtil;
    }

    @Transactional
    public void createActorRole(ActorRole newActorRole, Acknowledgment acknowledgment) throws BusinessException {
        try{
            actorRoleRepository.save(newActorRole);
            transactionSynchronizationUtil.executeAfterCommit(acknowledgment::acknowledge);
        } catch (Exception e){
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    @Transactional
    public void updateActorRole(ActorRole newActorRole, Acknowledgment acknowledgment) throws BusinessException {
        try{
            var actorRole = actorRoleRepository.findByActorIdIdAndRoleCodeAndDeletedAtIsNull(
                    newActorRole.getActorId().getId(),
                    newActorRole.getRole().getCode()).orElseThrow(
                            () -> new ResourcesNotFoundException("Actor not found", "actor_role", KAFKAERROR));

            // Delete the old principal role if the new role is principal
            if (Boolean.TRUE.equals(newActorRole.getIsPrincipal())) {
                var oldPrincipalRole = actorRoleRepository.findByActorIdIdAndIsPrincipalIsTrueAndDeletedAtIsNull(newActorRole.getActorId().getId());
                if (oldPrincipalRole != null) {
                    oldPrincipalRole.setIsPrincipal(false);
                    actorRoleRepository.save(oldPrincipalRole);
                }
            }
            actorRole.setIsPrincipal(newActorRole.getIsPrincipal());
            actorRoleRepository.save(actorRole);

            transactionSynchronizationUtil.executeAfterCommit(acknowledgment::acknowledge);
        } catch (Exception e){
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }
    @Transactional
    public void deleteActorRole(ActorRole deletedActor, Acknowledgment acknowledgment) throws BusinessException{
        try{
            var actorRole = actorRoleRepository.findByActorIdIdAndRoleCodeAndDeletedAtIsNull(
                    deletedActor.getActorId().getId(),
                    deletedActor.getRole().getCode()).orElseThrow(
                            () -> new ResourcesNotFoundException("Actor not found", "actor_role", KAFKAERROR));
            actorRole.setDeletedAt(new Date());
            actorRoleRepository.save(actorRole);
            transactionSynchronizationUtil.executeAfterCommit(acknowledgment::acknowledge);
        } catch (Exception e){
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }
}
