package com.datatricks.contracts.service;

import com.datatricks.contracts.exception.ConflictException;
import com.datatricks.contracts.exception.ResourcesNotFoundException;
import com.datatricks.contracts.exception.TechnicalException;
import com.datatricks.contracts.exception.handler.InformativeMessage;
import com.datatricks.contracts.model.*;
import com.datatricks.contracts.model.dto.LeaseTransferDto;
import com.datatricks.contracts.model.dto.NewLeaseTransferDto;
import com.datatricks.contracts.model.dto.SingleResultDto;
import com.datatricks.contracts.repository.*;
import jakarta.transaction.Transactional;
import org.modelmapper.ModelMapper;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * Service class for managing lease transfers.
 */
@Service
public class LeaseTransferService {
    private final LeaseTransferRepository leaseTransferRepository;
    private final ContractRepository contractRepository;
    private final ActorRepository actorRepository;
    private final ContractActorRepository contractActorRepository;
    private final RentalRepository rentalRepository;
    private final ManagementMandateRepository managementMandateRepository;
    private static final String LEASE_TRANSFER_NOT_FOUND = "Lease transfer not found";
    private static final String MODULE = "LEASE_TRANSFER";
    private static final String CONTRACT_NOT_FOUND = "Contract not found";
    private static final String MANDATE_REFERENCE_REQUIRED = "Mandate reference is required";
    private static final String LESSOR_NOT_FOUND = "Lessor not found";
    private static final String LESSOR_ROLE = "LESSOR";
    private final ModelMapper modelMapper;

    /**
     * Constructor for LeaseTransferService.
     *
     * @param leaseTransferRepository     the lease transfer repository
     * @param contractRepository          the contract repository
     * @param actorRepository             the actor repository
     * @param contractActorRepository     the contract actor repository
     * @param rentalRepository            the rental repository
     * @param managementMandateRepository the management mandate repository
     * @param modelMapper                 the model mapper
     */
    public LeaseTransferService(LeaseTransferRepository leaseTransferRepository, ContractRepository contractRepository, ActorRepository actorRepository, ContractActorRepository contractActorRepository, RentalRepository rentalRepository, ManagementMandateRepository managementMandateRepository, ModelMapper modelMapper) {
        this.leaseTransferRepository = leaseTransferRepository;
        this.contractRepository = contractRepository;
        this.actorRepository = actorRepository;
        this.contractActorRepository = contractActorRepository;
        this.rentalRepository = rentalRepository;
        this.managementMandateRepository = managementMandateRepository;
        this.modelMapper = modelMapper;
    }

    /**
     * Retrieves a lease transfer by contract reference.
     *
     * @param contractReference the contract reference
     * @return the lease transfer DTO wrapped in a ResponseEntity
     */
    @Transactional
    public ResponseEntity<SingleResultDto<LeaseTransferDto>> getLeaseTransferByContractReference(String contractReference) {
        // First we need to find the lease transfer by contract reference
        var leaseTransfer = leaseTransferRepository.findByContractReferenceAndDeletedAtIsNull(contractReference)
                .orElseThrow(() -> new ResourcesNotFoundException(LEASE_TRANSFER_NOT_FOUND, "lease_transfer", MODULE));
        // Then we need to find the actor by reference
        var actor = actorRepository.findByReferenceAndDeletedAtIsNull(leaseTransfer.getLessorReference())
                .orElseThrow(() -> new ResourcesNotFoundException(LESSOR_NOT_FOUND, "actor", MODULE));
        // Then we need to find the contract actor by contract reference, actor reference, and role code (BAIL or BAILS)
        var contractActor = getContractActor(contractReference, actor.getReference());
        // Finally, we need to find the rental by contract actor ID
        var rental = rentalRepository.findFirstByContractActorIdIdAndDeletedAtIsNull(contractActor.getId())
                .orElseThrow(() -> new ResourcesNotFoundException("Rental not found", "rental", MODULE));
        var response = modelMapper.map(leaseTransfer, LeaseTransferDto.class);
        response.setRefinancingRate(rental.getNominalRate());
        response.setRefinancingAmount(rental.getOriginalAmount());
        return ResponseEntity.ok(SingleResultDto.<LeaseTransferDto>builder().data(response).build());
    }

    private ContractActor getContractActor(String contractReference, String actorReference) {
        Specification<ContractActor> contractActorSpecification = (root, query, criteriaBuilder)
                -> criteriaBuilder.equal(root.get("contract").get("reference"), contractReference);
        Specification<ContractActor> actorSpecification = (root, query, criteriaBuilder)
                -> criteriaBuilder.equal(root.get("actor").get("reference"), actorReference);
        Specification<ContractActor> roleSpecification = (root, query, criteriaBuilder)
                -> criteriaBuilder.in(root.get("role").get("code")).value("BAILS").value("BAIL");
        Specification<ContractActor> contractActorSpecificationAndActor = contractActorSpecification.and(actorSpecification).and(roleSpecification);
        return contractActorRepository
                .findOne(contractActorSpecificationAndActor)
                .orElseThrow(() -> new ResourcesNotFoundException("Contract Actor not Found", "contract_actor", MODULE));
    }

    /**
     * Creates a new lease transfer.
     *
     * @param contractReference the contract reference
     * @param leaseTransferDto  the new lease transfer DTO
     * @return the created lease transfer DTO wrapped in a ResponseEntity
     * @throws TechnicalException if an error occurs during creation
     */
    public ResponseEntity<SingleResultDto<LeaseTransferDto>> createLeaseTransfer(String contractReference, NewLeaseTransferDto leaseTransferDto) throws TechnicalException {
        try {
            // This is temporary, we will remove this check once we have the UI in place
            var lessor = leaseTransferRepository.findByContractReferenceAndDeletedAtIsNull(contractReference);
            if (lessor.isPresent()) {
                throw new ConflictException("Lease transfer already exists for this contract", "LEASE_TRANSFER_ALREADY_EXISTS", "Lease transfer already exists for this contract", MODULE);
            }

            Contract contract = contractRepository.findByReferenceAndDeletedAtIsNull(contractReference).orElseThrow(() -> new ResourcesNotFoundException(CONTRACT_NOT_FOUND, "contract", MODULE));

            if (!actorRepository.existsByReferenceAndDeletedAtIsNull(leaseTransferDto.getLessorReference())) {
                throw new ResourcesNotFoundException(LESSOR_NOT_FOUND, "actor", MODULE);
            }

            // We need to find the contract actor by contract reference and actor reference
            // and role code (BAIL or BAILS)
            // This is to make sure that the actor is affiliated with the contract
            var contractActor = getContractActor(contractReference, leaseTransferDto.getLessorReference());
            if (!rentalRepository.existsByContractActorIdIdAndDeletedAtIsNull(contractActor.getId())) {
                throw new ConflictException("We could not find rental for buyer " + leaseTransferDto.getLessorReference()
                        , "RENTAL_RELATED_TO_LESSOR_NOT_FOUND", "We could not find rental for buyer " + leaseTransferDto.getLessorReference(), MODULE);
            }
            LeaseTransfer newLeaseTransfer;
            if (leaseTransferDto.getTransferType() == TransferType.WITH_MANDATE) {
                if (leaseTransferDto.getManagementMandateReference() == null || leaseTransferDto.getManagementMandateReference().isEmpty()) {
                    throw new ConflictException(MANDATE_REFERENCE_REQUIRED, "MANDATE_REFERENCE_REQUIRED", MANDATE_REFERENCE_REQUIRED, MODULE);
                }
                var managementMandate = managementMandateRepository.findByReferenceAndDeletedAtIsNull(leaseTransferDto.getManagementMandateReference())
                        .orElseThrow(() -> new ResourcesNotFoundException("Management mandate not found", "management_mandate", MODULE));
                newLeaseTransfer =
                        new LeaseTransfer(leaseTransferDto.getTransferType(), leaseTransferDto.getPrice(), contractActor.getActor().getReference(), contractActor.getActor().getName(), contract, managementMandate);
            } else {
                newLeaseTransfer =
                        new LeaseTransfer(leaseTransferDto.getTransferType(), leaseTransferDto.getPrice(), contractActor.getActor().getReference(), contractActor.getActor().getName(), contract);
            }
            var leaseTransfer = leaseTransferRepository.save(newLeaseTransfer);

            return ResponseEntity.ok(SingleResultDto.<LeaseTransferDto>builder().data(modelMapper.map(leaseTransfer, LeaseTransferDto.class)).build());
        } catch (Exception e) {
            throw new TechnicalException("Error creating lease transfer", e.getMessage(), MODULE);
        }
    }

    /**
     * Updates an existing lease transfer.
     *
     * @param leaseTransferId   the lease transfer ID
     * @param contractReference the contract reference
     * @param leaseTransferDto  the new lease transfer DTO
     * @return the updated lease transfer DTO wrapped in a ResponseEntity
     */
    @Transactional
    public ResponseEntity<SingleResultDto<LeaseTransferDto>> updateLeaseTransfer(Long leaseTransferId, String contractReference, NewLeaseTransferDto leaseTransferDto) {

        Contract contract = contractRepository.findByReference(contractReference).orElseThrow(() -> new ResourcesNotFoundException(CONTRACT_NOT_FOUND, "contract", MODULE));

        var leaseTransfer = leaseTransferRepository.findById(leaseTransferId).orElseThrow(() -> new ResourcesNotFoundException(LEASE_TRANSFER_NOT_FOUND, "lease_transfer", MODULE));

        if (!actorRepository.existsByReferenceAndDeletedAtIsNull(leaseTransferDto.getLessorReference())) {
            throw new ResourcesNotFoundException(LESSOR_NOT_FOUND, "actor", MODULE);
        }

        // We need to find the contract actor by contract reference and actor reference
        // and role code (BAIL or BAILS)
        // This is to make sure that the actor is affiliated with the contract
        var contractActor = getContractActor(contractReference, leaseTransferDto.getLessorReference());
        if (!rentalRepository.existsByContractActorIdIdAndDeletedAtIsNull(contractActor.getId())) {
            throw new ConflictException("We could not find rental for buyer " + leaseTransferDto.getLessorReference()
                    , "RENTAL_RELATED_TO_LESSOR_NOT_FOUND", "We could not find rental for buyer " + leaseTransferDto.getLessorReference(), MODULE);
        }
        if (leaseTransferDto.getTransferType() == TransferType.WITH_MANDATE) {
            if (leaseTransferDto.getManagementMandateReference() == null || leaseTransferDto.getManagementMandateReference().isEmpty()) {
                throw new ConflictException(MANDATE_REFERENCE_REQUIRED, "MANDATE_REFERENCE_REQUIRED", MANDATE_REFERENCE_REQUIRED, MODULE);
            }
            var managementMandate = managementMandateRepository.findByReferenceAndDeletedAtIsNull(leaseTransferDto.getManagementMandateReference())
                    .orElseThrow(() -> new ResourcesNotFoundException("Management mandate not found", "management_mandate", MODULE));
            leaseTransfer.setManagementMandate(managementMandate);
        } else {
            leaseTransfer.setManagementMandate(null);
        }

        leaseTransfer.setTransferType(leaseTransferDto.getTransferType());
        leaseTransfer.setPrice(leaseTransferDto.getPrice());
        leaseTransfer.setLessorReference(contractActor.getActor().getReference());
        leaseTransfer.setLessorName(contractActor.getActor().getName());
        leaseTransfer.setContract(contract);
        leaseTransferRepository.save(leaseTransfer);
        return ResponseEntity.ok(SingleResultDto.<LeaseTransferDto>builder().data(modelMapper.map(leaseTransfer, LeaseTransferDto.class)).build());
    }

    /**
     * Retrieves an actor by reference.
     *
     * @param leaseTransferReference the lease transfer reference
     * @return the actor
     */
    private Actor getActorByReference(String leaseTransferReference) {
        Specification<Actor> actorSpecification = (root, query, criteriaBuilder)
                -> criteriaBuilder.equal(root.get("reference"), leaseTransferReference);
        Specification<Actor> actorRoleSpecification = (root, query, criteriaBuilder)
                -> criteriaBuilder.in(root.get("actorRole").get("role").get("code")).value("BAIL").value("BAILS");
        Specification<Actor> actorSpecificationAndRole = actorSpecification.and(actorRoleSpecification);

        return actorRepository.findOne(actorSpecificationAndRole).orElseThrow(() -> new ResourcesNotFoundException(LESSOR_NOT_FOUND, "actor", MODULE));
    }

    /**
     * Deletes a lease transfer.
     *
     * @param leaseTransferId   the lease transfer ID
     * @param contractReference the contract reference
     * @return an informative message wrapped in a ResponseEntity
     */
    @Transactional
    public ResponseEntity<InformativeMessage> deleteLeaseTransfer(Long leaseTransferId, String contractReference) {
        var leaseTransfer = leaseTransferRepository.findById(leaseTransferId).orElseThrow(() -> new ResourcesNotFoundException(LEASE_TRANSFER_NOT_FOUND, "lease_transfer", MODULE));
        leaseTransfer.setDeletedAt(new Date());
        if (leaseTransfer.getManagementMandate() != null) {
            leaseTransfer.getManagementMandate().setDeletedAt(new Date());
        }
        leaseTransferRepository.save(leaseTransfer);
        return ResponseEntity.ok(new InformativeMessage("Resource with ID " + leaseTransferId + " has been deleted successfully"));
    }
}