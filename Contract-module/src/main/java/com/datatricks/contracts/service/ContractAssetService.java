package com.datatricks.contracts.service;

import com.datatricks.contracts.exception.BusinessException;
import com.datatricks.contracts.exception.ResourcesNotFoundException;
import com.datatricks.contracts.model.ContractActorAsset;
import com.datatricks.contracts.model.TimetableAsset;
import com.datatricks.contracts.model.dto.inject.ContractActorAssetInjectDto;
import com.datatricks.contracts.producer.ContractProducer;
import com.datatricks.contracts.repository.*;
import com.datatricks.contracts.utils.TransactionSynchronizationUtil;
import com.datatricks.kafkacommondomain.enums.OperationType;
import org.modelmapper.ModelMapper;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class ContractAssetService {
    private static final String ERRORMESSAGE = "Error while processing message ";
    private final String KAFKAERROR = "DATA-SYNC-ERROR";
    private final TransactionSynchronizationUtil transactionSynchronizationUtil;
    private final ContractActorAssetRepository contractActorAssetRepository;
    private final ActorRepository actorRepository;
    private final ContractRepository contractRepository;
    private final RentalRepository rentalRepository;
    private final AccessoryRepository accessoryRepository;
    private final AssetRepository assetRepository;
    private final ModelMapper modelMapper;
    private final ContractProducer contractProducer;
    private final TimetableAssetRepository timetableAssetRepository;

    public ContractAssetService(TransactionSynchronizationUtil transactionSynchronizationUtil,
                                ContractActorAssetRepository contractActorAssetRepository,
                                ActorRepository actorRepository,
                                ContractRepository contractRepository,
                                RentalRepository rentalRepository,
                                AccessoryRepository accessoryRepository,
                                AssetRepository assetRepository,
                                ModelMapper modelMapper,
                                ContractProducer sendContractActorAsset, TimetableAssetRepository timetableAssetRepository) {
        this.transactionSynchronizationUtil = transactionSynchronizationUtil;
        this.contractActorAssetRepository = contractActorAssetRepository;
        this.actorRepository = actorRepository;
        this.contractRepository = contractRepository;
        this.rentalRepository = rentalRepository;
        this.accessoryRepository = accessoryRepository;
        this.assetRepository = assetRepository;
        this.modelMapper = modelMapper;
        this.contractProducer = sendContractActorAsset;
        this.timetableAssetRepository = timetableAssetRepository;
    }

    @Transactional
    public void createContractAsset(ContractActorAsset contractActorAsset, Acknowledgment acknowledgment) {
        try {

            contractActorAssetRepository.save(contractActorAsset);
            transactionSynchronizationUtil.executeAfterCommit(acknowledgment::acknowledge);
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    @Transactional
    public void updateContractAsset(ContractActorAsset contractActorAsset, Acknowledgment acknowledgment) {
        try {
            if (!contractActorAssetRepository.existsByIdAndDeletedAtIsNull(contractActorAsset.getId()))
                throw new ResourcesNotFoundException("ContractActorAsset not found", "contract_actor", "contract_actor_asset");

            contractActorAssetRepository.save(contractActorAsset);

            transactionSynchronizationUtil.executeAfterCommit(acknowledgment::acknowledge);
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    @Transactional
    public void deleteContractAsset(ContractActorAsset contractActorAsset, Acknowledgment acknowledgment) {
        try {
            var contractActorAssetToDelete = contractActorAssetRepository.findByIdAndDeletedAtIsNull(
                    contractActorAsset.getId()).orElseThrow(
                    () -> new ResourcesNotFoundException("ContractActorAsset not found", "contract_actor", KAFKAERROR));
            contractActorAssetToDelete.setDeletedAt(contractActorAsset.getDeletedAt());
            contractActorAssetRepository.save(contractActorAssetToDelete);

            transactionSynchronizationUtil.executeAfterCommit(acknowledgment::acknowledge);
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    @Transactional
    public void assignContractToAsset(ContractActorAssetInjectDto contractActorAsset) {
        try {
            var contractActorAssetToAssign = contractActorAssetRepository.findByIdAndDeletedAtIsNull(
                    contractActorAsset.getId()).orElseThrow(
                    () -> new ResourcesNotFoundException("ContractActorAsset not found", "contract_actor", "contract_actor_asset"));
            contractActorAssetToAssign.setContract(contractRepository.findByReferenceAndDeletedAtIsNull(
                    contractActorAsset.getContract().getReference()).orElse(null));
            contractActorAssetToAssign.setModifiedAt(new Date());
            contractActorAssetToAssign.setActor(actorRepository.findByReferenceAndDeletedAtIsNull(
                    contractActorAsset.getActor().getReference()).orElse(null));

            contractActorAssetRepository.save(contractActorAssetToAssign);

            transactionSynchronizationUtil.executeAfterCommit(() -> contractProducer.sendContractActorAsset(
                    contractActorAssetToAssign, OperationType.PUT, ContractActorAsset.class.getSimpleName()));
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    @Transactional
    public void createAssetToRentalOrAccessory(TimetableAsset timetableAsset, Acknowledgment acknowledgment) {
        try {
            timetableAssetRepository.save(timetableAsset);
            transactionSynchronizationUtil.executeAfterCommit(acknowledgment::acknowledge);
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    @Transactional
    public void updateAssetToRentalOrAccessory(TimetableAsset timetableAsset, Acknowledgment acknowledgment) {
        try {
            if (!timetableAssetRepository.existsByIdAndDeletedAtIsNull(timetableAsset.getId()))
                throw new ResourcesNotFoundException("TimetableAsset not found", "timetable", "timetable_asset");

            timetableAssetRepository.save(timetableAsset);

            transactionSynchronizationUtil.executeAfterCommit(acknowledgment::acknowledge);
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    @Transactional
    public void deleteAssetToRentalOrAccessory(TimetableAsset timetableAsset, Acknowledgment acknowledgment) {
        try {
            if (!timetableAssetRepository.existsByIdAndDeletedAtIsNull(timetableAsset.getId()))
                throw new ResourcesNotFoundException("TimetableAsset not found", "timetable", "timetable_asset");

            timetableAsset.setDeletedAt(timetableAsset.getDeletedAt());
            timetableAssetRepository.save(timetableAsset);
            transactionSynchronizationUtil.executeAfterCommit(acknowledgment::acknowledge);
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }
}
