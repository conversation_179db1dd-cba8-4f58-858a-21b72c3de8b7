package com.datatricks.contracts.service;

import com.datatricks.contracts.enums.TypeUnitePeriode;
import com.datatricks.contracts.exception.BusinessException;
import com.datatricks.contracts.exception.ConflictException;
import com.datatricks.contracts.exception.ResourcesNotFoundException;
import com.datatricks.contracts.exception.handler.InformativeMessage;
import com.datatricks.contracts.model.*;
import com.datatricks.contracts.model.dto.*;
import com.datatricks.contracts.model.dto.external.EngineRateCalculation;
import com.datatricks.contracts.model.dto.external.EngineRubriqueFrontDto;
import com.datatricks.contracts.model.dto.external.EngineScheduleLine;
import com.datatricks.contracts.model.dto.external.EngineTimetableLevelFrontDto;
import com.datatricks.contracts.producer.ContractProducer;
import com.datatricks.contracts.repository.*;
import com.datatricks.contracts.utils.ContractUtils;
import com.datatricks.contracts.utils.JpaQueryFilters;
import com.datatricks.contracts.utils.TransactionSynchronizationUtil;
import com.datatricks.kafkacommondomain.enums.OperationType;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Service
public class RentalService {

    private final RentalRepository rentalRepository;
    private final LevelRepository levelRepository;
    private final ContractActorRepository contractActorRepository;
    private final TimetableRepository timetableRepository;
    private final TimetableItemRepository timetableItemRepository;
    private final RepaymentRepository repaymentRepository;
    private final ActorRepository actorRepository;
    private final TimetableAssetRepository timetableAssetRepository;
    private final ModelMapper modelMapper;
    private final RestTemplate restTemplate;
    @Value("${spring.services.asset.url}")
    private String ASSETS_SERVICE;
    @Value("${spring.services.engine.url}")
    private String ENGINE_SERVER_URL;
    private final ContractProducer contractProducer;
    private final ContractActorAssetRepository contractActorAssetRepository;
    private static final String RENTAL_NOT_FOUND = "Rental not found";
    private static final String CONTRACT_ACTOR_NOT_FOUND = "Contract actor not found";
    private static final String CONTRACT_ACTOR_ASSET_NOT_FOUND = "Contract actor asset not found";
    private static final String MODULE = "RENTAL";
    private final TransactionSynchronizationUtil transactionSynchronizationUtil;
    private static final String RENTAL_UPDATE_FORBIDDEN = "Cannot update a rental in a service state";
    private final LineTypeRepository lineTypeRepository;

    public RentalService(RentalRepository rentalRepository, LevelRepository levelRepository, ContractActorRepository contractActorRepository, TimetableRepository timetableRepository, TimetableItemRepository timetableItemRepository, RepaymentRepository repaymentRepository, ActorRepository actorRepository, TimetableAssetRepository timetableAssetRepository, ModelMapper modelMapper, RestTemplate restTemplate, ContractProducer contractProducer, ContractActorAssetRepository contractActorAssetRepository, TransactionSynchronizationUtil transactionSynchronizationUtil, LineTypeRepository lineTypeRepository) {
        this.rentalRepository = rentalRepository;
        this.levelRepository = levelRepository;
        this.contractActorRepository = contractActorRepository;
        this.timetableRepository = timetableRepository;
        this.timetableItemRepository = timetableItemRepository;
        this.repaymentRepository = repaymentRepository;
        this.actorRepository = actorRepository;
        this.timetableAssetRepository = timetableAssetRepository;
        this.modelMapper = modelMapper;
        this.restTemplate = restTemplate;
        this.contractProducer = contractProducer;
        this.contractActorAssetRepository = contractActorAssetRepository;
        this.transactionSynchronizationUtil = transactionSynchronizationUtil;
        this.lineTypeRepository = lineTypeRepository;
    }


    @Transactional
    public ResponseEntity<SingleResultDto<RentalResponseDto>> createRental(Long id, RentalDto rentalDto) {
        var contractActor = contractActorRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE)
        );
        LineType lineType = lineTypeRepository.findByCode(rentalDto.getLineTypeCode()).orElseThrow(
                () -> new ResourcesNotFoundException("LineType not found", "LineType", MODULE));
        if (!CollectionUtils.isEmpty(rentalDto.getRentalLevelsList()) && !isRentalSectionValid(rentalDto)) {
            throw new ConflictException(
                    "Invalid rental rates",
                    "RENTAL_INVALID_RATES",
                    "Invalid rental rates",
                    MODULE);
        }
        Timetable timetable = timetableRepository.save(new Timetable("INITIALIZED"));
        Rental rental = modelMapper.map(rentalDto, Rental.class);
        rental.setId(null);
        rental.setTimetableId(timetable);
        rental.setCalculationBasis(rentalDto.getCalculationBasis());
        rental.setCalculationMode(rentalDto.getCalculationMode());
        rental.setContractActorId(new ContractActor(id));
        rental.setLineType(lineType);
        rental.setCreatedAt(new Date());
        rental.setStartDate(rentalDto.getStartDate());
        rental.setEndDate(rentalDto.getEndDate());
        rental.setStatus(rentalDto.getStatus());
        if (contractActor.getContract() != null) {
            rental.setStartDate(contractActor.getContract().getStartDate());
            rental.setEndDate(contractActor.getContract().getDeadline());
        } else {
            rental.setStartDate(rentalDto.getStartDate());
            rental.setEndDate(rentalDto.getEndDate());
        }
        Rental newRental = rentalRepository.save(rental);
        RentalDto newRentalDto = modelMapper.map(newRental, RentalDto.class);
        newRentalDto.setRentalLevelsList(new ArrayList<>());
        rentalDto.getRentalLevelsList().forEach(levelDto -> {
            Level level = modelMapper.map(levelDto, Level.class);
            level.setId(null);
            level.setTimetableId(timetable);
            level.setCreatedAt(new Date());
            Level newLevel = levelRepository.save(level);
            LevelDto newLevelDto = modelMapper.map(newLevel, LevelDto.class);
            newRentalDto.getRentalLevelsList().add(newLevelDto);
        });
        if (!CollectionUtils.isEmpty(newRentalDto.getRentalLevelsList())) {
            createSchedule(id, newRentalDto);
        }

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractRentalMessage(newRental, OperationType.POST, MODULE));

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<RentalResponseDto>builder()
                        .data(modelMapper.map(newRentalDto, RentalResponseDto.class)).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<RentalResponseDto>> updateRental(
            Long id,
            Long rentalId,
            RentalDto rentalDto) {

        if (!CollectionUtils.isEmpty(rentalDto.getRentalLevelsList()) && !isRentalSectionValid(rentalDto)) {
            throw new ConflictException(
                    "Invalid rental rates",
                    "RENTAL_INVALID_RATES",
                    "Invalid rental rates",
                    MODULE);
        }
        Rental rentalToUpdate =
                rentalRepository.findByContractActorIdIdAndContractActorIdDeletedAtIsNullAndIdAndDeletedAtIsNull(id, rentalId);
        if (rentalToUpdate == null) {
            throw new ResourcesNotFoundException(RENTAL_NOT_FOUND, "Rental", MODULE);
        }

        if (FinancingStatus.EN_SERVICE.equals(rentalToUpdate.getStatus())) {
            throw new ConflictException(
                    RENTAL_UPDATE_FORBIDDEN,
                    "RENTAL_UPDATE_FORBIDDEN",
                    RENTAL_UPDATE_FORBIDDEN,
                    MODULE);
        }

        List<Repayment> repayments =
                repaymentRepository.findByTimetableIdIdAndDeletedAtIsNull(rentalToUpdate.getTimetableId().getId());

        if (repayments != null) {
            repayments.forEach(repayment -> {
                if (repayment.getContractActor().getId().equals(id)) {
                    throw new ConflictException(
                            "accessory contract actor id CANNOT be the same as the contract actor of its repayment",
                            "ACCESSORY_REPAYMENT_CONFLICT",
                            "accessory contract actor id CANNOT be the same as the contract actor of its repayment",
                            MODULE);
                }
            });
        }
        Date newDate = new Date();
        Rental rental = new Rental(rentalDto);
        rental.setId(rentalToUpdate.getId());
        rental.setLineType(rentalToUpdate.getLineType());
        rental.setContractActorId(new ContractActor(id));
        rental.setTimetableId(rentalToUpdate.getTimetableId());
        rental.setCreatedAt(rentalToUpdate.getCreatedAt());
        rental.setModifiedAt(newDate);

        var updatedRental = rentalRepository.save(rental);

        levelRepository.deleteAllByTimetableIdIdAndDeletedAtIsNull(rental.getTimetableId().getId());
        List<Level> newLevels = new ArrayList<>();
        List<LevelDto> newSortedLevelsDto = rentalDto.getRentalLevelsList().stream()
                .sorted(Comparator.comparing(LevelDto::getStartDate))
                .toList();
        for (var i = 0; i < newSortedLevelsDto.size(); i++) {
            Level level = modelMapper.map(newSortedLevelsDto.get(i), Level.class);
            level.setId(null);
            level.setOrder(i + 1);
            level.setTimetableId(rental.getTimetableId());
            level.setCreatedAt(newDate);
            level.setModifiedAt(newDate);
            newLevels.add(level);
        }

        levelRepository.saveAll(newLevels);
        ResponseEntity<SingleResultDto<RentalResponseDto>> createdRental = getRental(id, rentalId);
        if (!CollectionUtils.isEmpty(
                (Objects.requireNonNull(createdRental.getBody()).getData()).getRentalLevelsList())) {
            createSchedule(
                    id,
                    modelMapper.map(
                            Objects.requireNonNull(createdRental.getBody())
                                    .getData(),
                            RentalDto.class));
        }

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractRentalMessage(updatedRental, OperationType.PUT, MODULE));
        return createdRental;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<RentalResponseDto>> updateRentalToServiceState(Long rentalId) {

        Rental rentalToUpdate = rentalRepository.findByIdAndDeletedAtIsNull(rentalId).orElseThrow(
                () -> new ResourcesNotFoundException(RENTAL_NOT_FOUND, "Rental", MODULE));

        if (FinancingStatus.EN_SERVICE.equals(rentalToUpdate.getStatus())) {
            throw new ConflictException(
                    RENTAL_UPDATE_FORBIDDEN,
                    "RENTAL_UPDATE_FORBIDDEN",
                    RENTAL_UPDATE_FORBIDDEN,
                    MODULE);
        }

        rentalToUpdate.setStatus(FinancingStatus.EN_SERVICE);
        rentalToUpdate.setModifiedAt(new Date());
        Rental updatedRental = rentalRepository.save(rentalToUpdate);

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractRentalMessage(updatedRental, OperationType.PUT, MODULE));

        return ResponseEntity.ok(SingleResultDto.<RentalResponseDto>builder()
                .data(modelMapper.map(updatedRental, RentalResponseDto.class)).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<RentalResponseDto>> getRental(Long id, Long rentalId) {

        Rental rental =
                rentalRepository
                        .findByContractActorIdIdAndContractActorIdDeletedAtIsNullAndIdAndDeletedAtIsNull(id, rentalId);
        if (rental == null) {
            throw new ResourcesNotFoundException(RENTAL_NOT_FOUND, "Rental", MODULE);
        }
        RentalResponseDto rentalDto = new RentalResponseDto(rental);
        rentalDto.getContractActorId().getContract()
                .setEndDate(ContractUtils.calculateEndDate(rentalDto.getContractActorId().getContract().getStartDate(),
                        rentalDto.getContractActorId().getContract().getDuration()));
        rentalDto.setTimetableId(new TimetableDto(rental.getTimetableId()));
        List<Level> levels = levelRepository.findByTimetableIdIdAndDeletedAtIsNull(
                rental.getTimetableId().getId());
        rentalDto.setRentalLevelsList(levels.stream()
                .map(level -> modelMapper.map(level, LevelDto.class))
                .sorted(Comparator.comparing(LevelDto::getStartDate))
                .toList());
        return ResponseEntity.ok(SingleResultDto.<RentalResponseDto>builder().data(rentalDto).build());
    }

    @Transactional
    public ResponseEntity<PageDto<RentalResponseDto>> getRentals(Long id, Map<String, String> params) {

        JpaQueryFilters<Rental> filters = new JpaQueryFilters<>(params, Rental.class);
        Specification<Rental> rentalSpec = (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("contractActorId").get("contract").get("id"), id);
        Specification<Rental> contractActorSpec = (root, query, builder) ->
                builder.isNull(root.get("contractActorId").get("deletedAt"));

        Page<Rental> page = rentalRepository.findAll(
                filters.getSpecification().and(rentalSpec).and(contractActorSpec),
                filters.getPageable()
        );
        List<RentalResponseDto> rentals = page.stream()
                .map((element) -> {
                    RentalResponseDto rentalDto = modelMapper.map(element, RentalResponseDto.class);
                    rentalDto.getContractActorId().getContract()
                            .setEndDate(ContractUtils.calculateEndDate(rentalDto.getContractActorId().getContract().getStartDate(),
                                    rentalDto.getContractActorId().getContract().getDuration()));
                    List<Level> levels = levelRepository
                            .findByTimetableIdIdAndDeletedAtIsNull(rentalDto.getTimetableId().getId());
                    rentalDto.setRentalLevelsList(levels.stream()
                            .map(level -> modelMapper.map(level, LevelDto.class))
                            .sorted(Comparator.comparing(LevelDto::getStartDate))
                            .toList());
                    return rentalDto;
                })
                .toList();
        return ResponseEntity.ok(PageDto.<RentalResponseDto>builder()
                .data(rentals)
                .total(page.getTotalElements()).build());
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteRental(Long id, Long rentalId) {

        Rental rental =
                rentalRepository
                        .findByContractActorIdIdAndContractActorIdDeletedAtIsNullAndIdAndDeletedAtIsNull(id, rentalId);
        if (rental == null) {
            throw new ResourcesNotFoundException(RENTAL_NOT_FOUND, "Rental", MODULE);
        }
        Date deletedDate = new Date();
        rental.setDeletedAt(deletedDate);
        rentalRepository.save(rental);
        List<Level> levels = levelRepository.findByTimetableIdIdAndDeletedAtIsNull(
                rental.getTimetableId().getId());
        levels.forEach(level -> level.setDeletedAt(deletedDate));
        levelRepository.saveAll(levels);
        rental.getTimetableId().setDeletedAt(deletedDate);
        timetableRepository.save(rental.getTimetableId());
        timetableItemRepository.deleteAllByTimetableIdId(rental.getTimetableId().getId());

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractRentalMessage(rental, OperationType.DELETE, MODULE));

        return ResponseEntity.ok(new InformativeMessage("Resource with ID " + rentalId + " has been deleted successfully"));
    }

    @Transactional
    public ResponseEntity<PageDto<TimetableItemDto>> createSchedule(Long id, RentalDto lastRentalDto) {
        List<EngineScheduleLine> generatedSchedule = generateSchedule(lastRentalDto);

        Long finalLastRentalDto = lastRentalDto.getTimetableId();
        List<TimetableItem> allByTimetableIdId = timetableItemRepository.findByTimetableIdId(finalLastRentalDto);
        timetableItemRepository.deleteAllByTimetableIdId(finalLastRentalDto);

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractTimetableItemsMessage(allByTimetableIdId, OperationType.DELETE, MODULE));

        if (generatedSchedule != null) {
            List<TimetableItem> timetableItems = generatedSchedule.stream()
                    .map(engineScheduleLine -> {
                        TimetableItem timetableItem = new TimetableItem();
                        timetableItem.setDueDate(engineScheduleLine.getDueDate());
                        timetableItem.setStartDate(engineScheduleLine.getStartDate());
                        timetableItem.setEndDate(engineScheduleLine.getEndDate());
                        timetableItem.setUnpaid(engineScheduleLine.getUnpaid());
                        timetableItem.setAmortization(engineScheduleLine.getAmortization());
                        timetableItem.setInterest(engineScheduleLine.getInterest());
                        timetableItem.setRate(engineScheduleLine.getRate());
                        timetableItem.setNominalRate(engineScheduleLine.getNominalRate());
                        timetableItem.setRent(engineScheduleLine.getRent());
                        timetableItem.setTaxAmount(engineScheduleLine.getTaxAmount());
                        timetableItem.setResidualValue(engineScheduleLine.getCumulativeResidualValue());
                        timetableItem.setStatus("INITIALIZED");
                        timetableItem.setTimetableId(new Timetable(finalLastRentalDto));
                        return timetableItem;
                    })
                    .toList();
            List<TimetableItem> createdTimetableItems = timetableItemRepository.saveAll(timetableItems);

            // send message to the kafka topic
            transactionSynchronizationUtil.executeAfterCommit(
                    () -> contractProducer.sendContractTimetableItemsMessage(createdTimetableItems, OperationType.POST, MODULE));

            return ResponseEntity.ok(PageDto.<TimetableItemDto>builder()
                    .data(timetableItems.stream()
                            .map(timetableItem -> modelMapper.map(timetableItem, TimetableItemDto.class))
                            .toList())
                    .total(timetableItems.size())
                    .build());
        } else {
            throw new BusinessException("error creating schedule", MODULE);
        }
    }

    @Transactional
    public ResponseEntity<PageDto<TimetableItemDto>> getAllRentalsSchedule(Long id) {
        List<ContractActor> contractActors = contractActorRepository.findAllByContractIdAndDeletedAtIsNull(id);
        List<Long> contractActorsIds =
                contractActors.stream().map(ContractActor::getId).toList();
        List<Rental> contractActorRentals =
                rentalRepository.findByContractActorIdIdInAndDeletedAtIsNull(contractActorsIds);
        List<TimetableItemDto> timetableItemDtoList = new ArrayList<>();
        for (Rental rental : contractActorRentals) {
            List<TimetableItem> timetableItems = timetableItemRepository.findByTimetableIdId(
                    rental.getTimetableId().getId());
            List<TimetableItemDto> timetableItemsDtos = timetableItems.stream()
                    .map(timetableItem -> {
                        TimetableItemDto timetableItemDto = modelMapper.map(timetableItem, TimetableItemDto.class);
                        timetableItemDto.setType("RENTAL");
                        if (rental.getLineType() != null)
                            timetableItemDto.setLineTypeLabel(rental.getLineType().getLabel());
                        return timetableItemDto;
                    })
                    .toList();
            timetableItemDtoList.addAll(timetableItemsDtos);
        }
        timetableItemDtoList.sort(
                Comparator.comparing((TimetableItemDto timetableItemDto) -> timetableItemDto.getTimetableId().getId())
                        .thenComparing(TimetableItemDto::getStartDate));
        return ResponseEntity.ok(
                PageDto.<TimetableItemDto>builder().data(timetableItemDtoList).total(timetableItemDtoList.size()).build());
    }

    private List<EngineScheduleLine> generateSchedule(RentalDto rentalDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = getEngineRubriqueFrontDto(rentalDto);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<EngineRubriqueFrontDto> requestEntity = new HttpEntity<>(rubriqueFrontDto, headers);

        ResponseEntity<PageDto<EngineScheduleLine>> schedule = restTemplate.exchange(
                ENGINE_SERVER_URL + "finance-calculator/calculate-schedule",
                HttpMethod.POST,
                requestEntity,
                new ParameterizedTypeReference<PageDto<EngineScheduleLine>>() {
                }
        );

        if (schedule.getStatusCode() == HttpStatus.OK) {
            return Objects.requireNonNull(schedule.getBody()).getData();
        }

        return Collections.emptyList();
    }

    private boolean isRentalSectionValid(RentalDto rentalDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = getEngineRubriqueFrontDto(rentalDto);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<EngineRubriqueFrontDto> requestEntity = new HttpEntity<>(rubriqueFrontDto, headers);

        ResponseEntity<SingleResultDto<EngineRateCalculation>> rateCalculation = restTemplate.exchange(
                ENGINE_SERVER_URL + "finance-calculator/calculate-rate",
                HttpMethod.POST,
                requestEntity,
                new ParameterizedTypeReference<SingleResultDto<EngineRateCalculation>>() {
                }
        );

        return rateCalculation.getStatusCode() == HttpStatus.OK
                && rentalDto.getNominalRate()
                == Objects.requireNonNull(rateCalculation.getBody()).getData().getNominalRate() * 100;
    }

    private static EngineRubriqueFrontDto getEngineRubriqueFrontDto(RentalDto rentalDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = getRubriqueFrontDto(rentalDto);
        for (int i = 0; i < rentalDto.getRentalLevelsList().size(); i++) {
            EngineTimetableLevelFrontDto timetableLevel = new EngineTimetableLevelFrontDto();
            LevelDto levelDto = rentalDto.getRentalLevelsList().get(i);
            timetableLevel.setOrder(i + 1);
            timetableLevel.setStartDate(levelDto.getStartDate());
            timetableLevel.setTypeUnitePeriode(levelDto.getPeriod().name());
            timetableLevel.setFrequency(1);
            timetableLevel.setDuration(levelDto.getPeriodNumber());
            timetableLevel.setAmount(levelDto.getRent());
            timetableLevel.setMultiple(levelDto.getPeriodMultiple());
            rubriqueFrontDto.getTimetableLevelFrontDtoList().add(timetableLevel);
        }
        return rubriqueFrontDto;
    }

    private static EngineRubriqueFrontDto getRubriqueFrontDto(RentalDto rentalDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = new EngineRubriqueFrontDto();
        rubriqueFrontDto.setBase(rentalDto.getCalculationBasis().name());
        rubriqueFrontDto.setDecompte(rentalDto.getCalculationMode().name());
        rubriqueFrontDto.setFinancialAmount(rentalDto.getOriginalAmount());
        rubriqueFrontDto.setStartDate(ContractUtils.convertToDate(rentalDto.getStartDate()));
        rubriqueFrontDto.setEndDate(ContractUtils.convertToDate(rentalDto.getEndDate()));
        rubriqueFrontDto.setTaxValue(rentalDto.getTaxRate() == null ? 0.0 : rentalDto.getTaxRate());
        rubriqueFrontDto.setMultiple(1);
        rubriqueFrontDto.setRate(rentalDto.getNominalRate() == null ? 0.0 : rentalDto.getNominalRate());
        rubriqueFrontDto.setRatePeriod(TypeUnitePeriode.ENUM_MOIS);
        rubriqueFrontDto.setVr(rentalDto.getResidualValue());
        return rubriqueFrontDto;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractActorAssetDto>> createRentalAsset(
            Long contractActorId,
            Long rentalId,
            Long contractActorAssetId) {

        contractActorRepository
                .findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                        () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE));

        var rental = rentalRepository.findByIdAndDeletedAtIsNull(rentalId).orElseThrow(
                () -> new ResourcesNotFoundException(RENTAL_NOT_FOUND, "Rental", MODULE));
        try {

            var contractActorAsset =
                    contractActorAssetRepository
                            .findByIdAndDeletedAtIsNull(contractActorAssetId).orElseThrow(
                                    () -> new ResourcesNotFoundException(CONTRACT_ACTOR_ASSET_NOT_FOUND, "ContractActorAsset", MODULE));

            if (contractActorAsset.getContract() != null
                    && !contractActorAsset.getContract().getId().equals(rental.getContractActorId().getContract().getId())) {
                throw new ConflictException(
                        "Asset not in the same contract",
                        "ASSET_NOT_IN_SAME_CONTRACT",
                        "Asset not in the same contract",
                        MODULE);
            }

            if (rental.getContractActorId().getActor() != null) {
                contractActorAsset.setActor(
                        actorRepository.existsByIdAndDeletedAtIsNull(rental.getContractActorId().getActor().getId()) ?
                                new Actor(rental.getContractActorId().getActor().getId()) : null);
            }

            TimetableAsset timetableAsset = new TimetableAsset();
            timetableAsset.setTimetable(rental.getTimetableId());
            timetableAsset.setContractActorAsset(contractActorAsset);
            TimetableAsset createdTimetableAsset = timetableAssetRepository.save(timetableAsset);

            // send message to the kafka topic
            transactionSynchronizationUtil.executeAfterCommit(
                    () -> contractProducer.sendTimetableAsset(createdTimetableAsset, OperationType.POST, MODULE));

            return ResponseEntity.ok(
                    SingleResultDto.<ContractActorAssetDto>builder()
                            .data(this.modelMapper.map(contractActorAsset, ContractActorAssetDto.class)).build());
        } catch (BusinessException ex) {
            throw ex;
        }
    }

    @Transactional
    public ResponseEntity<PageDto<ContractActorAssetDto>> getRentalAssets(Long contractActorId, Long rentalId) {

        contractActorRepository
                .findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                        () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE));

        var rental = rentalRepository.findByIdAndDeletedAtIsNull(rentalId).orElseThrow(
                () -> new ResourcesNotFoundException(RENTAL_NOT_FOUND, "Rental", MODULE));

        try {
            List<ContractActorAssetDto> assets = timetableAssetRepository
                    .findAllByTimetableIdAndDeletedAtIsNull(rental.getTimetableId().getId())
                    .stream()
                    .map(timetableAsset -> this.modelMapper.map(timetableAsset.getContractActorAsset(), ContractActorAssetDto.class))
                    .toList();

            return ResponseEntity.ok(PageDto.<ContractActorAssetDto>builder().data(assets).total(assets.size()).build());
        } catch (BusinessException ex) {
            throw ex;
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractActorAssetDto>> updateRentalAsset(
            Long contractActorId,
            Long rentalId,
            Long contractActorAssetId,
            AssetInput assetInput) {

        contractActorRepository.findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE));

        var rental = rentalRepository.findByIdAndDeletedAtIsNull(rentalId).orElseThrow(
                () -> new ResourcesNotFoundException(RENTAL_NOT_FOUND, "Rental", MODULE));
        //Remove the old relation between the asset and the rental
        var oldContractActorAsset =
                contractActorAssetRepository
                        .findByIdAndDeletedAtIsNull(contractActorAssetId).orElse(null);
        TimetableAsset oldTimetableAsset = new TimetableAsset();
        if (oldContractActorAsset != null) {
            oldTimetableAsset = timetableAssetRepository
                    .findByTimetableIdAndContractActorAssetIdAndDeletedAtIsNull(rental.getTimetableId().getId(), oldContractActorAsset.getId())
                    .orElseThrow(() -> new ResourcesNotFoundException("TimetableAsset not found", "TimetableAsset", MODULE));
        }
        //Add the new relation between the asset and the rental
        var contractActorAssetToUpdate =
                contractActorAssetRepository
                        .findByIdAndDeletedAtIsNull(assetInput.getContractActorAssetId()).orElseThrow(
                                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_ASSET_NOT_FOUND, "ContractActorAsset", MODULE));

        var rentalToUpdate = rentalRepository.findByIdAndDeletedAtIsNull(assetInput.getRentalId()).orElseThrow(
                () -> new ResourcesNotFoundException(RENTAL_NOT_FOUND, "Rental", MODULE));

        if (contractActorAssetToUpdate.getContract() != null
                && !contractActorAssetToUpdate.getContract().getId().equals(rentalToUpdate.getContractActorId().getContract().getId())) {

            throw new ConflictException(
                    "Asset is affected to another contract",
                    "ASSET_ALREADY_AFFECTED",
                    "Asset is affected to another contract",
                    MODULE);
        }

        oldTimetableAsset.setTimetable(rentalToUpdate.getTimetableId());
        oldTimetableAsset.setContractActorAsset(contractActorAssetToUpdate);
        oldTimetableAsset.setModifiedAt(new Date());
        TimetableAsset savedTimetableAsset = timetableAssetRepository.save(oldTimetableAsset);

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendTimetableAsset(savedTimetableAsset, OperationType.PUT, MODULE));

        return ResponseEntity.ok(SingleResultDto.<ContractActorAssetDto>builder()
                .data(this.modelMapper.map(contractActorAssetToUpdate, ContractActorAssetDto.class)).build());
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteRentalAsset(
            Long contractActorId,
            Long rentalId,
            Long contractActorAssetId) {

        var contractActor =
                contractActorRepository
                        .findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE));

        var rental = rentalRepository.findByIdAndDeletedAtIsNull(rentalId).orElseThrow(
                () -> new ResourcesNotFoundException(RENTAL_NOT_FOUND, "Rental", MODULE));

        var contractActorAsset =
                contractActorAssetRepository
                        .findByIdAndDeletedAtIsNull(contractActorAssetId).orElseThrow(
                                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_ASSET_NOT_FOUND, "ContractActorAsset", MODULE));

        TimetableAsset timetableAsset = timetableAssetRepository
                .findByTimetableIdAndContractActorAssetIdAndDeletedAtIsNull(rental.getTimetableId().getId(), contractActorAsset.getId())
                .orElseThrow(() -> new ResourcesNotFoundException("TimetableAsset not found", "TimetableAsset", MODULE));

        timetableAsset.setDeletedAt(new Date());
        timetableAssetRepository.save(timetableAsset);

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendTimetableAsset(timetableAsset, OperationType.DELETE, MODULE));

        return ResponseEntity.ok(
                new InformativeMessage("Resource with ID " + contractActorAssetId + " has been deleted successfully"));
    }

    @Transactional
    public ResponseEntity<PageDto<RentalDto>> getRentalsByContractAndActor(Long contractId, Long actorId, Map<String, String> params) {
        List<ContractActor> contractActors = contractActorRepository.findAllByContractIdAndActorIdAndDeletedAtIsNull(contractId, actorId);
        if (contractActors.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
        List<RentalDto> results = new ArrayList<>();
        for (ContractActor contractActor : contractActors) {
            params.put("PEqual_contract_Actor", contractActor.getId().toString());
            JpaQueryFilters<Rental> filters = new JpaQueryFilters<>(params, Rental.class);
            Page<Rental> page = rentalRepository.findAll(filters.getSpecification(), filters.getPageable());
            if (page.isEmpty()) {
                continue;
            }
            results.addAll(page.stream()
                    .map(rental -> this.modelMapper.map(rental, RentalDto.class))
                    .toList());
        }
        return ResponseEntity.ok(PageDto.<RentalDto>builder().data(results).total(results.size()).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<FinancialItemsDto>> updateRentalDetails(Long id, FinancialItemsDto financialItemsDto) {
        Rental rental = rentalRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(RENTAL_NOT_FOUND, "Rental", MODULE));
        rental.setTitle(financialItemsDto.getTitle());
        rental.setSuspendedInvoice(financialItemsDto.getSuspendedInvoice());
        rental.setMobileExtension(financialItemsDto.getMobileExtension());
        rental.setSeparateInvoice(financialItemsDto.getSeparateInvoice());
        rental.setContractActorId(contractActorRepository.findByIdAndDeletedAtIsNull(financialItemsDto.getContractActorId()).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE)));
        rental.setStatus(financialItemsDto.getStatus());
        rental.setLineType(lineTypeRepository.findByCode(financialItemsDto.getLineTypeCode()).orElseThrow(
                () -> new ResourcesNotFoundException("LineType not found", "LineType", MODULE)));
        rental.setModifiedAt(new Date());
        rentalRepository.save(rental);
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractRentalMessage(rental, OperationType.PUT, MODULE));
        return ResponseEntity.ok(SingleResultDto.<FinancialItemsDto>builder().data(financialItemsDto).build());
    }
}
