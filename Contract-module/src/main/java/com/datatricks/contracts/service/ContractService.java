package com.datatricks.contracts.service;

import com.datatricks.contracts.enums.ContractEvent;
import com.datatricks.contracts.enums.ContractState;
import com.datatricks.contracts.enums.TypeUnitePeriode;
import com.datatricks.contracts.exception.BusinessException;
import com.datatricks.contracts.exception.ConflictException;
import com.datatricks.contracts.exception.ResourcesNotFoundException;
import com.datatricks.contracts.exception.handler.InformativeMessage;
import com.datatricks.contracts.model.Currency;
import com.datatricks.contracts.model.*;
import com.datatricks.contracts.model.dto.*;
import com.datatricks.contracts.producer.ContractProducer;
import com.datatricks.contracts.repository.*;
import com.datatricks.contracts.utils.*;
import com.datatricks.kafkacommondomain.enums.OperationType;
import com.toedter.spring.hateoas.jsonapi.JsonApiModelBuilder;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.hateoas.PagedModel;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;

import static com.datatricks.contracts.utils.ContractUtils.calculateEndDate;
import static com.datatricks.contracts.utils.ContractUtils.createReference;

@Service
public class ContractService {
    private static final String CONTRACT_NOT_FOUND = "Contract not found";
    private static final String ASSOCIATED_TO_DOSSIER = "DOSSIER";
    private static final String MODULE = "Contract";
    private static final String CONTRACT_UPDATE_FORBIDDEN = "Cannot update a contract in the 'ES' phase";
    private static final String SERVICE_PHASE = "ES";
    private final ContractRepository contractRepository;
    private final PhaseRepository phaseRepository;
    private final MilestoneRepository milestoneRepository;
    private final FinanceViewRepository financeViewRepository;
    private final RentalService rentalService;
    private final AccessoryService accessoryService;
    private final ModelMapper modelMapper;
    private final ContractProducer contractProducer;
    private final TransactionSynchronizationUtil transactionSynchronizationUtil;
    private final CurrencyRepository currencyRepository;
    private final ActorRepository actorRepository;
    private final ActivityRepository activityRepository;
    private final ProductRepository productRepository;
    private final PatchHelper<PatchContractDto> patchHelper;
    private final GenericJsonApiModelAssembler genericAssembler;
    private final InclusionService inclusionService;
    private final EntityManager entityManager;
    private final ContractSummaryRepository contractSummaryRepository;
    private final LevelRepository levelRepository;
    private final RetributionService retributionService;
    private final LineTypeRepository lineTypeRepository;
    private final ContractStateLogRepository contractStateLogRepository;
    private final ContractStateMachineService contractStateMachineService;

    ContractService(
            ContractRepository repository,
            PhaseRepository phaseRepository,
            MilestoneRepository milestoneRepository,
            FinanceViewRepository financeViewRepository,
            RentalService rentalService,
            AccessoryService accessoryService,
            ModelMapper modelMapper,
            ContractProducer contractProducer,
            TransactionSynchronizationUtil transactionSynchronizationUtil,
            CurrencyRepository currencyRepository,
            ActorRepository actorRepository,
            ActivityRepository activityRepository,
            ProductRepository productRepository,
            PatchHelper<PatchContractDto> patchHelper,
            GenericJsonApiModelAssembler genericAssembler,
            InclusionService inclusionService,
            EntityManager entityManager,
            ContractSummaryRepository contractSummaryRepository,
            LevelRepository levelRepository,
            RetributionService retributionService,
            ContractStateLogRepository contractStateLogRepository,
            ContractStateMachineService contractStateMachineService,
            LineTypeRepository lineTypeRepository) {
        this.contractRepository = repository;
        this.phaseRepository = phaseRepository;
        this.milestoneRepository = milestoneRepository;
        this.financeViewRepository = financeViewRepository;
        this.rentalService = rentalService;
        this.accessoryService = accessoryService;
        this.modelMapper = modelMapper;
        this.contractProducer = contractProducer;
        this.transactionSynchronizationUtil = transactionSynchronizationUtil;
        this.currencyRepository = currencyRepository;
        this.actorRepository = actorRepository;
        this.activityRepository = activityRepository;
        this.productRepository = productRepository;
        this.patchHelper = patchHelper;
        this.genericAssembler = genericAssembler;
        this.inclusionService = inclusionService;
        this.entityManager = entityManager;
        this.contractSummaryRepository = contractSummaryRepository;
        this.levelRepository = levelRepository;
        this.retributionService = retributionService;
        this.lineTypeRepository = lineTypeRepository;
        this.contractStateLogRepository = contractStateLogRepository;
        this.contractStateMachineService = contractStateMachineService;
    }

    @Transactional
    public ResponseEntity<PageDto<ContractResponseDto>> getAllFilteredContracts(Map<String, String> params) {
        JpaQueryFilters<Contract> filters = new JpaQueryFilters<>(params, Contract.class);
        Page<Contract> page = contractRepository.findAll(filters.getSpecification(), filters.getPageable());
        List<ContractResponseDto> filteredContracts = page.stream()
                .map(contract -> modelMapper.map(contract, ContractResponseDto.class))
                .map(contract -> {
                    contract.setEndDate(calculateEndDate(contract.getStartDate(), contract.getDuration()));
                    return contract;
                })
                .toList();
        return ResponseEntity.ok(PageDto.<ContractResponseDto>builder()
                .data(filteredContracts)
                .total(page.getTotalElements())
                .build());
    }

    @Transactional
    public ResponseEntity<PageDto<ContractSummaryDto>> getAllSummaryContracts(Map<String, String> params) {
        JpaQueryFilters<ContractSummary> filters = new JpaQueryFilters<>(params, ContractSummary.class);
        Page<ContractSummary> page = contractSummaryRepository.findAll(filters.getSpecification(), filters.getPageable());
        List<ContractSummaryDto> contractSummaryDto = page.stream()
                .map(contract -> this.modelMapper.map(contract, ContractSummaryDto.class))
                .peek(contract -> contract.setEndDate(calculateEndDate(contract.getStartDate(), contract.getDuration())))
                .toList();
        return ResponseEntity.ok(PageDto.<ContractSummaryDto>builder()
                .data(contractSummaryDto)
                .total(page.getTotalElements())
                .build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractResponseDto>> getContractById(Long id) {
        Contract contract = contractRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_NOT_FOUND, "Contract", MODULE));

        ContractResponseDto response = modelMapper.map(contract, ContractResponseDto.class);
        response.setEndDate(calculateEndDate(contract.getStartDate(), contract.getDuration()));
        return ResponseEntity.ok(SingleResultDto.<ContractResponseDto>builder()
                .data(response)
                .build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractResponseDto>> createContract(ContractDto contractDto) {
        Contract contract = new Contract(contractDto);
        contract.setId(null);
        contract.setCreatedAt(new Date());
        contract.setModifiedAt(new Date());
        contract.setReference(null);
        if (!checkEndDate(contract.getStartDate(),
                contractDto.getEndDate(),
                contract.getDuration())) {
            throw new BusinessException("CONTRACT_DURATION_INVALID", "The contract duration should be greater that zero.");
        }
        if (!checkDeadline(contractDto.getEndDate(), contract.getDeadline())) {
            throw new ConflictException("The deadline is invalid", "DEADLINE_DATE_INVALID", "The deadline is invalid", MODULE);
        }
        Actor business = actorRepository.findByReferenceAndTypeAndDeletedAtIsNull(
                contractDto.getBusinessReference(),
                ActorTypes.MANAGEMENT_COMPANY).orElseThrow(
                () -> new ResourcesNotFoundException("Business not found", "business", MODULE));

        contract.setManagementCompany(business);
        Milestone optionalMilestone = milestoneRepository.findByCode("OUVERT").orElseThrow(
                () -> new ResourcesNotFoundException("No milestone found for code 'OUVERT'", "Milestone", MODULE));

        contract.setMilestone(optionalMilestone);
        Phase optionalPhase = phaseRepository.findByCodeAndAssociatedTo("INI", ASSOCIATED_TO_DOSSIER).orElseThrow(
                () -> new ResourcesNotFoundException("No phase found for code 'INI' and associated to 'Dossier'", "Phase", MODULE));

        contract.setPhase(optionalPhase);
        Currency currency = currencyRepository.findByCode(contractDto.getCurrencyCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Currency not found", "Currency", MODULE));

        contract.setCurrency(currency);
        Activity activity = activityRepository.findByCode(contractDto.getActivityCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Activity not found", "Activity", MODULE));

        contract.setActivity(activity);
        Product product = productRepository.findByCode(contractDto.getProductCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Product not found", "Product", MODULE));

        contract.setProduct(product);
        contract.setStatus(ContractState.INITIAL);
        Contract createdContract = contractRepository.saveAndFlush(contract);
        entityManager.clear();
        var savedContract = contractRepository.findById(createdContract.getId()).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_NOT_FOUND, "Contract", MODULE)
        );
        Long contractId;
        if (contractDto.getId() != null) {
            contractId = contractDto.getId();
        } else {
            contractId = savedContract.getId();
        }
        savedContract.setReference(createReference(savedContract.getActivity().getCode(), contractId.toString()));
        createdContract = contractRepository.saveAndFlush(savedContract);
        writeContractStateLogs(createdContract);
        ContractResponseDto response = modelMapper.map(createdContract, ContractResponseDto.class);
        response.setEndDate(calculateEndDate(createdContract.getStartDate(), createdContract.getDuration()));

        // Send message to Kafka
        Contract finalCreatedContract = createdContract;
        transactionSynchronizationUtil.executeAfterCommit(() -> contractProducer.sendContractMessage(finalCreatedContract, OperationType.POST, MODULE));

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<ContractResponseDto>builder()
                        .data(response)
                        .build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractResponseDto>> updateContract(Long id, ContractDto newContractDto) {

        Contract contract = contractRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_NOT_FOUND, "Contract", MODULE));

        if (SERVICE_PHASE.equals(contract.getPhase().getCode())) {
            throw new ConflictException(
                    CONTRACT_UPDATE_FORBIDDEN,
                    "CONTRACT_UPDATE_FORBIDDEN",
                    CONTRACT_UPDATE_FORBIDDEN,
                    MODULE);
        }

        if (!checkEndDate(
                newContractDto.getStartDate(),
                newContractDto.getEndDate(),
                newContractDto.getDuration())) {
            throw new BusinessException("CONTRACT_DURATION_INVALID", "The contract duration should be greater that zero.");
        }
        if (!checkDeadline(newContractDto.getEndDate(), newContractDto.getDeadline())) {
            throw new ConflictException("The deadline is invalid", "DEADLINE_DATE_INVALID", "The deadline is invalid", MODULE);
        }
        Actor business = actorRepository.findByReferenceAndTypeAndDeletedAtIsNull(
                newContractDto.getBusinessReference(),
                ActorTypes.MANAGEMENT_COMPANY).orElseThrow(
                () -> new ResourcesNotFoundException("Business not found", "Actor", MODULE));

        Milestone optionalMilestone = milestoneRepository.findByCode(newContractDto.getMilestoneCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Milestone not found", "Milestone", MODULE));

        Phase optionalPhase = phaseRepository.findByCodeAndAssociatedTo(newContractDto.getPhaseCode(), ASSOCIATED_TO_DOSSIER).orElseThrow(
                () -> new ResourcesNotFoundException("Phase not found", "Phase", MODULE));

        Currency currency = currencyRepository.findByCode(newContractDto.getCurrencyCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Currency not found", "Currency", MODULE));

        Activity activity = activityRepository.findByCode(newContractDto.getActivityCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Activity not found", "Activity", MODULE));

        Product product = productRepository.findByCode(newContractDto.getProductCode()).orElseThrow(
                () -> new ResourcesNotFoundException("Product not found", "Product", MODULE));


        Contract newContract = new Contract(newContractDto);
        newContract.setId(id);
        newContract.setModifiedAt(new Date());
        newContract.setCreatedAt(contract.getCreatedAt());
        newContract.setReference(contract.getReference());
        newContract.setManagementCompany(business);
        newContract.setMilestone(optionalMilestone);
        newContract.setPhase(optionalPhase);
        newContract.setCurrency(currency);
        newContract.setActivity(activity);
        newContract.setProduct(product);
        newContract.setStatus(contract.getStatus());

        Contract updatedContract = contractRepository.save(newContract);
        ContractResponseDto response = modelMapper.map(updatedContract, ContractResponseDto.class);
        response.setEndDate(calculateEndDate(updatedContract.getStartDate(), updatedContract.getDuration()));

        // Send message to Kafka
        transactionSynchronizationUtil.executeAfterCommit(() -> contractProducer.sendContractMessage(updatedContract, OperationType.PUT, MODULE));

        return ResponseEntity.ok(SingleResultDto.<ContractResponseDto>builder()
                .data(response)
                .build());
    }

    //@Transactional
    //public ResponseEntity<PageDto<ContractResponseDto>> completeContract (List<Long> id) {
    //    List<ContractResponseDto> responses = new ArrayList<>();
    //    List<Contract> contracts = contractRepository.findByIdInAndDeletedAtIsNull(id);
    //    contracts.forEach(contract -> {
    //        if (contract == null) {
    //            throw new ResourcesNotFoundException(CONTRACT_NOT_FOUND, "Contract", MODULE);
    //        } else {
    //            if (COMPLETED_PHASE.equals(contract.getPhase().getCode())) {
    //                throw new ConflictException(
    //                        CONTRACT_UPDATE_FORBIDDEN,
    //                        "CONTRACT_UPDATE_FORBIDDEN",
    //                        CONTRACT_UPDATE_FORBIDDEN,
    //                        MODULE);
    //            }
    //            Phase completedPhase = phaseRepository.findByCodeAndAssociatedTo(COMPLETED_PHASE, ASSOCIATED_TO_DOSSIER).orElseThrow(
    //                    () -> new ResourcesNotFoundException("Phase not found", "Phase", MODULE));
//
    //            Milestone completedMilestone = milestoneRepository.findByCode(ENDED_MILESTONE).orElseThrow(
    //                    () -> new ResourcesNotFoundException("Milestone not found", "Milestone", MODULE));
//
    //            contract.setPhase(completedPhase);
    //            contract.setMilestone(completedMilestone);
    //            Contract updatedContract = contractRepository.save(contract);
    //            ContractResponseDto response = modelMapper.map(updatedContract, ContractResponseDto.class);
    //            response.setEndDate(calculateEndDate(updatedContract.getStartDate(), updatedContract.getDuration()));
    //            responses.add(response);
//
    //            // Send message to Kafka
    //            transactionSynchronizationUtil.executeAfterCommit(() -> contractProducer.sendContractMessage(updatedContract, OperationType.PUT, MODULE));
    //        }
    //    });
    //    return ResponseEntity.ok(PageDto.<ContractResponseDto>builder()
    //            .data(responses)
    //            .build());
    //}

    @Transactional
    public ResponseEntity<PatchResponseDto> patchContract(
            Long id,
            PatchDto<PatchContractDto> newContractDto) {

        Contract contract = contractRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_NOT_FOUND, "Contract", MODULE));

        try {
            PatchResult<PatchContractDto> result = patchHelper.applyPatch(
                    new PatchContractDto(contract),
                    newContractDto.getData().getAttributes().getValidationGroups(),
                    newContractDto.getData().getAttributes());

            Contract patchedContract = new Contract(result.getUpdated());
            patchedContract.setModifiedAt(new Date());

            if (!checkEndDate(
                    patchedContract.getStartDate(),
                    calculateEndDate(patchedContract.getStartDate(), patchedContract.getDuration()),
                    patchedContract.getDuration())) {
                throw new ConflictException("The contract duration should be greater that zero.", "CONTRACT_DURATION_INVALID", "The contract duration should be greater that zero.", MODULE);
            }
            if (!checkDeadline(
                    calculateEndDate(patchedContract.getStartDate(), patchedContract.getDuration()),
                    patchedContract.getDeadline())) {
                throw new ConflictException("The deadline is invalid", "DEADLINE_DATE_INVALID", "The deadline is invalid", MODULE);
            }

            Actor business = actorRepository.findByReferenceAndTypeAndDeletedAtIsNull(
                    patchedContract.getManagementCompany().getReference(),
                    ActorTypes.MANAGEMENT_COMPANY).orElseThrow(
                    () -> new ResourcesNotFoundException("Business not found", "business", MODULE));

            patchedContract.setManagementCompany(business);
            Milestone optionalMilestone = milestoneRepository.findByCode("OUVERT").orElseThrow(
                    () -> new ResourcesNotFoundException("No milestone found for code 'OUVERT'", "Milestone", MODULE));

            patchedContract.setMilestone(optionalMilestone);
            Phase optionalPhase = phaseRepository.findByCodeAndAssociatedTo("INI", ASSOCIATED_TO_DOSSIER).orElseThrow(
                    () -> new ResourcesNotFoundException("No phase found for code 'INI' and associated to 'Dossier'", "Phase", MODULE));

            patchedContract.setPhase(optionalPhase);
            Currency currency = currencyRepository.findByCode(patchedContract.getCurrency().getCode()).orElseThrow(
                    () -> new ResourcesNotFoundException("Currency not found", "Currency", MODULE));

            patchedContract.setCurrency(currency);
            Activity activity = activityRepository.findByCode(patchedContract.getActivity().getCode()).orElseThrow(
                    () -> new ResourcesNotFoundException("Activity not found", "Activity", MODULE));

            patchedContract.setActivity(activity);
            Product product = productRepository.findByCode(patchedContract.getProduct().getCode()).orElseThrow(
                    () -> new ResourcesNotFoundException("Product not found", "Product", MODULE));

            patchedContract.setProduct(product);
            Contract updatedContract = contractRepository.save(patchedContract);
            // Send message to Kafka
            transactionSynchronizationUtil.executeAfterCommit(() -> contractProducer.sendContractMessage(updatedContract, OperationType.PUT, MODULE));

            return ResponseEntity.ok(result.getResponse());
        } catch (IllegalAccessException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteContractById(Long id) {
        Contract contract = contractRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_NOT_FOUND, "Contract", MODULE)
        );
        contract.setDeletedAt(new Date());
        transactionSynchronizationUtil.executeAfterCommit(() -> contractProducer.sendContractMessage(contract, OperationType.DELETE, MODULE));
        contractRepository.save(contract);
        return ResponseEntity.ok(new InformativeMessage("Resource with ID " + id + " has been deleted successfully"));
    }

    private boolean checkEndDate(
            LocalDate startDate, LocalDate endDate, Integer duration) {
        try {
            if (startDate == null && endDate == null) {
                return true;
            }
            if (startDate != null && endDate != null) {

                return startDate
                        .plusDays(duration)
                        .isEqual(endDate);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean checkDeadline(LocalDate endDate, LocalDate deadline) {
        try {
            if (endDate == null && deadline == null) {
                return false;
            }
            if (endDate != null && deadline != null) {
                return endDate.plusDays(1).isEqual(deadline);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    @Transactional
    public ResponseEntity<PageDto<FinanceElementsViewDto>> getFinances(Map<String, String> params, Long id) {
        Specification<FinanceElementsView> financeSpec = (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("contractActorId").get("contract").get("id"), id);
        Specification<FinanceElementsView> contractActorSpec = (root, query, builder) ->
                builder.isNull(root.get("contractActorId").get("deletedAt"));

        JpaQueryFilters<FinanceElementsView> filters = new JpaQueryFilters<>(params, FinanceElementsView.class);
        Page<FinanceElementsView> page = financeViewRepository.findAll(
                filters.getSpecification().and(financeSpec).and(contractActorSpec),
                filters.getPageable()
        );
        List<FinanceElementsViewDto> finances = page.stream()
                .map(element -> {
                    FinanceElementsViewDto financeElementsDto = modelMapper.map(element, FinanceElementsViewDto.class);
                    financeElementsDto.getContractActorId().getContract().setEndDate(
                            ContractUtils.calculateEndDate(financeElementsDto.getContractActorId().getContract().getStartDate(),
                                    financeElementsDto.getContractActorId().getContract().getDuration()));
                    List<Level> levels = levelRepository.findByTimetableIdIdAndDeletedAtIsNull(
                            financeElementsDto.getTimetableId().getId());
                    financeElementsDto.setLevelsList(levels.stream()
                            .map(level -> modelMapper.map(level, LevelDto.class))
                            .sorted(Comparator.comparing(LevelDto::getStartDate))
                            .toList());
                    financeElementsDto.setDuration(levels.stream()
                            .mapToInt(this::calculateDuration)
                            .sum());
                    return financeElementsDto;
                })
                .toList();

        return ResponseEntity
                .ok(PageDto.<FinanceElementsViewDto>builder()
                        .total(page.getTotalElements())
                        .data(finances)
                        .build());
    }

    @Transactional
    public ResponseEntity<RepresentationModel<?>> findAllContracts(String[] include,
                                                                   Map<String, String> params,
                                                                   String[] fieldsMovies) {
        JpaQueryFilters<Contract> filters = new JpaQueryFilters<>(params, Contract.class);
        Page<Contract> page = contractRepository.findAll(filters.getSpecification(), filters.getPageable());
        List<? extends RepresentationModel<?>> contractResources = page.stream()
                .map(contract -> genericAssembler.toJsonApiModel(contract, fieldsMovies))
                .toList();

        PagedModel.PageMetadata pageMetadata = new PagedModel.PageMetadata(
                page.getSize(),
                page.getNumber(),
                page.getTotalElements(),
                page.getTotalPages()
        );

        PagedModel<? extends RepresentationModel<?>> pagedModel = PagedModel.of(contractResources, pageMetadata);

        String pageLinksBase = "http://localhost:8080/api/contracts"; // Adjust as needed

        JsonApiModelBuilder jsonApiModelBuilder = JsonApiModelBuilder.jsonApiModel()
                .model(pagedModel);

        inclusionService.processIncludes(page.getContent(), include, jsonApiModelBuilder);
        RepresentationModel<?> pagedJsonApiModel = jsonApiModelBuilder.build();
        return ResponseEntity.ok(pagedJsonApiModel);
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractResponseDto>> getContractByReference(String reference) {
        Contract contract = contractRepository.findByReferenceAndDeletedAtIsNull(reference).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_NOT_FOUND, "Contract", MODULE)
        );
        ContractResponseDto response = modelMapper.map(contract, ContractResponseDto.class);
        response.setEndDate(calculateEndDate(contract.getStartDate(), contract.getDuration()));
        return ResponseEntity.ok(SingleResultDto.<ContractResponseDto>builder()
                .data(response)
                .build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<FinancialItemsDto>> createFinancialItem(Long id, FinancialItemsDto financialItemsDto) {
        return switch (financialItemsDto.getType()) {
            case RENTAL -> {
                RentalDto rentalDto = new RentalDto(financialItemsDto);
                rentalDto.setLineTypeCode(financialItemsDto.getLineTypeCode());
                var newRental = rentalService.createRental(financialItemsDto.getContractActorId(), rentalDto);
                var rental = modelMapper.map(Objects.requireNonNull(newRental.getBody()).getData(), FinancialItemsDto.class);
                rental.setType(FinancialItemsType.RENTAL);
                yield ResponseEntity.ok(SingleResultDto.<FinancialItemsDto>builder()
                        .data(modelMapper.map(rental, FinancialItemsDto.class))
                        .build());
            }
            case ACCESSORY -> {
                AccessoryDto accessoryDto = new AccessoryDto(financialItemsDto);
                accessoryDto.setLineTypeCode(financialItemsDto.getLineTypeCode());
                var newAccessory = accessoryService.createAccessory(financialItemsDto.getContractActorId(), accessoryDto);
                var accessory = modelMapper.map(Objects.requireNonNull(newAccessory.getBody()).getData(), FinancialItemsDto.class);
                accessory.setType(FinancialItemsType.ACCESSORY);
                yield ResponseEntity.ok(SingleResultDto.<FinancialItemsDto>builder()
                        .data(accessory)
                        .build());
            }
            case RETRIBUTION -> {
                RetributionDto retributionDto = new RetributionDto(financialItemsDto);
                retributionDto.setLineTypeCode(financialItemsDto.getLineTypeCode());
                var newRetribution = retributionService.createRetribution(financialItemsDto.getContractActorId(), retributionDto);
                var retribution = modelMapper.map(Objects.requireNonNull(newRetribution.getBody()).getData(), FinancialItemsDto.class);
                retribution.setType(FinancialItemsType.RETRIBUTION);
                yield ResponseEntity.ok(SingleResultDto.<FinancialItemsDto>builder()
                        .data(retribution)
                        .build());
            }
            default -> throw new BusinessException("Invalid financial item type", "INVALID_FINANCIAL_ITEM_TYPE");
        };
    }

    @Transactional
    public ResponseEntity<SingleResultDto<FinancialItemsDto>> updateFinancialItem(Long id, Long financial_item_id, FinancialItemsDto financialItemsDto) {
        return switch (financialItemsDto.getType()) {
            case RENTAL -> rentalService.updateRentalDetails(financial_item_id, financialItemsDto);
            case ACCESSORY -> accessoryService.updateAccessoryDetails(financial_item_id, financialItemsDto);
            case RETRIBUTION -> retributionService.updateRetributionDetails(financial_item_id, financialItemsDto);
            default -> throw new BusinessException("Invalid financial item type", "INVALID_FINANCIAL_ITEM_TYPE");
        };
    }

    public Integer calculateDuration(Level level) {
        Integer defaultValue = 0;
        switch (level.getPeriod()) {
            case TypeUnitePeriode.ENUM_JOUR -> {
                return Math.ceilDiv(level.getPeriodNumber(), 30);
            }
            case TypeUnitePeriode.ENUM_MOIS -> {
                return level.getPeriodNumber();
            }
            case TypeUnitePeriode.ENUM_TRIMESTRE -> {
                return Math.multiplyExact(level.getPeriodNumber(), 3);
            }
            case TypeUnitePeriode.ENUM_SEMESTRE -> {
                return Math.multiplyExact(level.getPeriodNumber(), 6);
            }
            case TypeUnitePeriode.ENUM_ANNEE -> {
                return Math.multiplyExact(level.getPeriodNumber(), 12);
            }
            default -> {
                return defaultValue;
            }
        }
    }

    public void toUnpaidStatus(Contract contract) {
        contractStateMachineService.sendEvent(contract, ContractEvent.DUE_DATE_REACHED);
    }

    public void paidTimetableItem(Contract contract, Long timetableItemId) {
        contractStateMachineService.sendEvent(contract, ContractEvent.PAYMENT_RECEIVED, timetableItemId);
    }

    public void returnToUnpaidStatus(Contract contract) {
        contractStateMachineService.sendEvent(contract, ContractEvent.RETURN_CONTRACT);
    }

    public void toPreCanceledStatus(Contract contract) {
        contractStateMachineService.sendEvent(contract, ContractEvent.PRE_CANCEL_CONTRACT);
    }

    public void toFinishState(Contract contract) {
        contractStateMachineService.sendEvent(contract, ContractEvent.COMPLETE_CONTRACT);
    }

    @Transactional
    public void toServiceState(Contract contract) {
        contractStateMachineService.sendEvent(contract, ContractEvent.START_CONTRACT);
    }

    private void writeContractStateLogs(Contract contract) {
        ContractStateLog contractStateLog = new ContractStateLog(contract);
        contractStateLogRepository.save(contractStateLog);
    }

    public ResponseEntity<SingleResultDto<ContractResponseDto>> pauseContract(Long id) {
        Contract contract = contractRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_NOT_FOUND, "Contract", MODULE));

        contractStateMachineService.sendEvent(contract, ContractEvent.PAUSE_CONTRACT);
        Contract updatedContract = contractRepository.save(contract);
        ContractResponseDto response = modelMapper.map(updatedContract, ContractResponseDto.class);
        response.setEndDate(calculateEndDate(updatedContract.getStartDate(), updatedContract.getDuration()));
        return ResponseEntity.ok(SingleResultDto.<ContractResponseDto>builder()
                .data(response)
                .build());
    }

    public ResponseEntity<SingleResultDto<ContractResponseDto>> resumeContract(Long id) {
        Contract contract = contractRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_NOT_FOUND, "Contract", MODULE));

        contractStateMachineService.sendEvent(contract, ContractEvent.RESUME_CONTRACT);
        Contract updatedContract = contractRepository.save(contract);
        ContractResponseDto response = modelMapper.map(updatedContract, ContractResponseDto.class);
        response.setEndDate(calculateEndDate(updatedContract.getStartDate(), updatedContract.getDuration()));
        return ResponseEntity.ok(SingleResultDto.<ContractResponseDto>builder()
                .data(response)
                .build());
    }

    @Transactional
    public ResponseEntity<PageDto<ContractLogDto>> contractLogs(Long id) {
        contractRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_NOT_FOUND, "Contract", MODULE));

        List<ContractStateLog> contractStateLogs = contractStateLogRepository.findByContractIdAndDeletedAtIsNull(id);
        List<ContractLogDto> contractLogs = contractStateLogs.stream()
                .map(contractStateLog -> modelMapper.map(contractStateLog, ContractLogDto.class))
                .toList();
        return ResponseEntity.ok(PageDto.<ContractLogDto>builder()
                .data(contractLogs)
                .total(contractLogs.size())
                .build());
    }
}
