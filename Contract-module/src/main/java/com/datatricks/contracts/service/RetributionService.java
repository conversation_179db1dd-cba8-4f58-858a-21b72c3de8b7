package com.datatricks.contracts.service;

import com.datatricks.contracts.enums.TypeUnitePeriode;
import com.datatricks.contracts.exception.BusinessException;
import com.datatricks.contracts.exception.ConflictException;
import com.datatricks.contracts.exception.ResourcesNotFoundException;
import com.datatricks.contracts.exception.handler.InformativeMessage;
import com.datatricks.contracts.model.*;
import com.datatricks.contracts.model.dto.*;
import com.datatricks.contracts.model.dto.external.EngineRubriqueFrontDto;
import com.datatricks.contracts.model.dto.external.EngineScheduleLine;
import com.datatricks.contracts.model.dto.external.EngineTimetableLevelFrontDto;
import com.datatricks.contracts.producer.ContractProducer;
import com.datatricks.contracts.repository.*;
import com.datatricks.contracts.utils.ContractUtils;
import com.datatricks.contracts.utils.JpaQueryFilters;
import com.datatricks.contracts.utils.TransactionSynchronizationUtil;
import com.datatricks.kafkacommondomain.enums.OperationType;
import jakarta.transaction.Transactional;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class RetributionService {

    private final RetributionRepository retributionRepository;
    private final LevelRepository levelRepository;
    private final ContractActorRepository contractActorRepository;
    private final TimetableRepository timetableRepository;
    private final TimetableItemRepository timetableItemRepository;
    private final ModelMapper modelMapper;
    private final RestTemplate restTemplate;
    private final TransactionSynchronizationUtil transactionSynchronizationUtil;
    private final ContractProducer contractProducer;
    @Value("${spring.services.engine.url}")
    private String engineServiceUrl;
    private static final String RETRIBUTION_NOT_FOUND = "Retribution not found";
    private static final String CONTRACT_ACTOR_NOT_FOUND = "Contract actor not found";
    private static final String MODULE = "RETRIBUTION";
    private static final String RETRIBUTION_UPDATE_FORBIDDEN = "Cannot update a Retribution in a service state";
    private static final String RETRIBUTION_ALREADY_IN_SERVICE = "Retribution is already in service state";
    private final LineTypeRepository lineTypeRepository;

    public RetributionService(
            RetributionRepository retributionRepository,
            LevelRepository levelRepository,
            TimetableRepository timetableRepository,
            TimetableItemRepository timetableItemRepository,
            RestTemplate restTemplate,
            ContractActorRepository contractActorRepository,
            ModelMapper modelMapper, LineTypeRepository lineTypeRepository, TransactionSynchronizationUtil transactionSynchronizationUtil, ContractProducer contractProducer) {
        this.retributionRepository = retributionRepository;
        this.levelRepository = levelRepository;
        this.timetableRepository = timetableRepository;
        this.timetableItemRepository = timetableItemRepository;
        this.contractActorRepository = contractActorRepository;
        this.modelMapper = modelMapper;
        this.restTemplate = restTemplate;
        this.lineTypeRepository = lineTypeRepository;
        this.transactionSynchronizationUtil = transactionSynchronizationUtil;
        this.contractProducer = contractProducer;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<RetributionResponseDto>> createRetribution(Long id, RetributionDto retributionDto) {
        var contractActor = contractActorRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "contract_actor", MODULE)
        );
        LineType lineType = lineTypeRepository.findByCode(retributionDto.getLineTypeCode()).orElseThrow(
                () -> new ResourcesNotFoundException("LineType not found", "LineType", MODULE));
        Timetable timetable = timetableRepository.save(new Timetable());
        Retribution retribution = modelMapper.map(retributionDto, Retribution.class);
        retribution.setId(null);
        retribution.setTimetableId(timetable);
        retribution.setCalculationBasis(retributionDto.getCalculationBasis());
        retribution.setCalculationMode(retributionDto.getCalculationMode());
        retribution.setContractActorId(new ContractActor(id));
        retribution.setLineType(lineType);
        retribution.setCreatedAt(new Date());
        retribution.setStatus(retributionDto.getStatus());
        if (contractActor.getContract() != null) {
            retribution.setStartDate(contractActor.getContract().getStartDate());
            retribution.setEndDate(contractActor.getContract().getDeadline());
        } else {
            retribution.setStartDate(retributionDto.getStartDate());
            retribution.setEndDate(retributionDto.getEndDate());
        }
        Retribution newRetribution = retributionRepository.save(retribution);
        RetributionDto newRetributionDto = modelMapper.map(newRetribution, RetributionDto.class);
        newRetributionDto.setRetributionLevelsList(new ArrayList<>());
        retributionDto.getRetributionLevelsList().forEach(levelDto -> {
            Level level = modelMapper.map(levelDto, Level.class);
            level.setId(null);
            level.setTimetableId(timetable);
            level.setCreatedAt(new Date());
            Level newLevel = levelRepository.save(level);
            LevelDto newLevelDto = modelMapper.map(newLevel, LevelDto.class);
            newRetributionDto.getRetributionLevelsList().add(newLevelDto);
        });
        if (!CollectionUtils.isEmpty(newRetributionDto.getRetributionLevelsList())) {
            createSchedule(id, newRetributionDto);
        }

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<RetributionResponseDto>builder()
                        .data(modelMapper.map(newRetributionDto, RetributionResponseDto.class)).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<RetributionResponseDto>> updateRetribution(
            Long id,
            Long retributionId,
            RetributionDto retributionDto) {

        Retribution retributionToUpdate = retributionRepository.findByContractActorIdIdAndContractActorIdDeletedAtIsNullAndIdAndDeletedAtIsNull(id, retributionId);
        if (retributionToUpdate == null) {
            throw new ResourcesNotFoundException(RETRIBUTION_NOT_FOUND, "Retribution", MODULE);
        }
        if (FinancingStatus.EN_SERVICE.equals(retributionToUpdate.getStatus())) {
            throw new ConflictException(
                    RETRIBUTION_UPDATE_FORBIDDEN,
                    "RETRIBUTION_UPDATE_FORBIDDEN",
                    RETRIBUTION_UPDATE_FORBIDDEN,
                    MODULE);
        }

        Date newDate = new Date();
        Retribution retribution = new Retribution(retributionDto);
        retribution.setId(retributionToUpdate.getId());
        retribution.setLineType(retributionToUpdate.getLineType());
        retribution.setContractActorId(new ContractActor(id));
        retribution.setTimetableId(retributionToUpdate.getTimetableId());
        retribution.setCreatedAt(retributionToUpdate.getCreatedAt());
        retribution.setModifiedAt(newDate);

        retributionRepository.save(retribution);

        levelRepository.deleteAllByTimetableIdIdAndDeletedAtIsNull(retribution.getTimetableId().getId());
        List<Level> newLevels = new ArrayList<>();
        List<LevelDto> newSortedLevelsDto = retributionDto.getRetributionLevelsList().stream()
                .sorted(Comparator.comparing(LevelDto::getStartDate))
                .toList();
        for (var i = 0; i< newSortedLevelsDto.size(); i++) {
            Level level = modelMapper.map(newSortedLevelsDto.get(i), Level.class);
            level.setId(null);
            level.setOrder(i+1);
            level.setTimetableId(retribution.getTimetableId());
            level.setCreatedAt(newDate);
            level.setModifiedAt(newDate);
            newLevels.add(level);
        }

        levelRepository.saveAll(newLevels);
        ResponseEntity<SingleResultDto<RetributionResponseDto>> createdRetribution = getRetribution(id, retributionId);
        if (!CollectionUtils.isEmpty((
                Objects.requireNonNull(createdRetribution.getBody()).getData())
                .getRetributionLevelsList())) {
            createSchedule(
                    id,
                    modelMapper.map(
                            Objects.requireNonNull(createdRetribution.getBody())
                                    .getData(),
                            RetributionDto.class));
        }

        return createdRetribution;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<RetributionResponseDto>> updateRetributionToServiceState(Long retributionId) {

        Retribution retributionToUpdate = retributionRepository.findByIdAndDeletedAtIsNull(retributionId).orElseThrow(
                () -> new ResourcesNotFoundException(RETRIBUTION_NOT_FOUND, "Retribution", MODULE));

        if (FinancingStatus.EN_SERVICE.equals(retributionToUpdate.getStatus())) {
            throw new ConflictException(
                    RETRIBUTION_ALREADY_IN_SERVICE,
                    "RETRIBUTION_ALREADY_IN_SERVICE",
                    RETRIBUTION_ALREADY_IN_SERVICE,
                    MODULE);
        }

        retributionToUpdate.setStatus(FinancingStatus.EN_SERVICE);
        retributionToUpdate.setModifiedAt(new Date());
        Retribution updatedRetribution = retributionRepository.save(retributionToUpdate);

        return ResponseEntity.ok(SingleResultDto.<RetributionResponseDto>builder()
                .data(modelMapper.map(updatedRetribution, RetributionResponseDto.class)).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<RetributionResponseDto>> getRetribution(Long id, Long retributionId) {

        Retribution retribution =
                retributionRepository
                        .findByContractActorIdIdAndContractActorIdDeletedAtIsNullAndIdAndDeletedAtIsNull(id, retributionId);
        if (retribution == null) {
            throw new ResourcesNotFoundException(RETRIBUTION_NOT_FOUND, "retribution", MODULE);
        }
        RetributionResponseDto retributionDto = modelMapper.map(retribution, RetributionResponseDto.class);
        retributionDto.getContractActorId().getContract()
                .setEndDate(ContractUtils.calculateEndDate(retributionDto.getContractActorId().getContract().getStartDate(),
                        retributionDto.getContractActorId().getContract().getDuration()));
        retributionDto.setTimetableId(modelMapper.map(retribution.getTimetableId(), TimetableDto.class));
        List<Level> levels = levelRepository.findByTimetableIdIdAndDeletedAtIsNull(
                retribution.getTimetableId().getId());
        retributionDto.setRetributionLevelsList(levels.stream()
                .map(level -> modelMapper.map(level, LevelDto.class))
                .sorted(Comparator.comparing(LevelDto::getStartDate))
                .toList());
        return ResponseEntity.ok(SingleResultDto.<RetributionResponseDto>builder().data(retributionDto).build());
    }

    @Transactional
    public ResponseEntity<PageDto<RetributionResponseDto>> getRetributions(Long id, Map<String, String> params) {

        JpaQueryFilters<Retribution> filters = new JpaQueryFilters<>(params, Retribution.class);
        Specification<Retribution> retributionSpec = (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("contractActorId").get("contract").get("id"), id);
        Specification<Retribution> contractActorSpec = (root, query, builder) ->
                builder.isNull(root.get("contractActorId").get("deletedAt"));

        Page<Retribution> page = retributionRepository.findAll(
                filters.getSpecification().and(retributionSpec).and(contractActorSpec),
                filters.getPageable()
        );
        List<RetributionResponseDto> retributions = page.stream()
                .map(retribution -> {
                    RetributionResponseDto retributionDto = modelMapper.map(retribution, RetributionResponseDto.class);
                    retributionDto.getContractActorId().getContract()
                            .setEndDate(ContractUtils.calculateEndDate(retributionDto.getContractActorId().getContract().getStartDate(),
                                    retributionDto.getContractActorId().getContract().getDuration()));
                    List<Level> levels = levelRepository.findByTimetableIdIdAndDeletedAtIsNull(
                            retribution.getTimetableId().getId());
                    retributionDto.setRetributionLevelsList(levels.stream()
                            .map(level -> modelMapper.map(level, LevelDto.class))
                            .sorted(Comparator.comparing(LevelDto::getStartDate))
                            .toList());
                    return retributionDto;
                })
                .toList();
        return ResponseEntity.ok(PageDto.<RetributionResponseDto>builder()
                .data(retributions).total(page.getTotalElements()).build());
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteRetribution(Long id, Long retributionId) {

        Retribution retribution =
                retributionRepository
                        .findByContractActorIdIdAndContractActorIdDeletedAtIsNullAndIdAndDeletedAtIsNull(id, retributionId);
        if (retribution == null) {
            throw new ResourcesNotFoundException(RETRIBUTION_NOT_FOUND, "retribution", MODULE);
        }
        Date deletedDate = new Date();
        retribution.setDeletedAt(deletedDate);
        retributionRepository.save(retribution);
        List<Level> levels = levelRepository.findByTimetableIdIdAndDeletedAtIsNull(
                retribution.getTimetableId().getId());
        levels.forEach(level -> level.setDeletedAt(deletedDate));
        levelRepository.saveAll(levels);
        retribution.getTimetableId().setDeletedAt(deletedDate);
        timetableRepository.save(retribution.getTimetableId());
        timetableItemRepository.deleteAllByTimetableIdId(
                retribution.getTimetableId().getId());
        return ResponseEntity.ok(new InformativeMessage("Resource with ID " + retributionId + " has been deleted successfully"));
    }

    @Transactional
    public ResponseEntity<PageDto<TimetableItemDto>> createSchedule(Long id, RetributionDto lastRetributionDto) {
        List<EngineScheduleLine> generatedSchedule = generateSchedule(lastRetributionDto);

        Long finalLastRetributionDto = lastRetributionDto.getTimetableId();
        List<TimetableItem> allByTimetableIdId = timetableItemRepository.findByTimetableIdId(finalLastRetributionDto);
        timetableItemRepository.deleteAllByTimetableIdId(finalLastRetributionDto);

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractTimetableItemsMessage(allByTimetableIdId, OperationType.POST, MODULE));

        if (generatedSchedule != null) {
            List<TimetableItem> timetableItems = generatedSchedule.stream()
                    .map(engineScheduleLine -> {
                        TimetableItem timetableItem = new TimetableItem();
                        timetableItem.setDueDate(engineScheduleLine.getDueDate());
                        timetableItem.setStartDate(engineScheduleLine.getStartDate());
                        timetableItem.setEndDate(engineScheduleLine.getEndDate());
                        timetableItem.setUnpaid(engineScheduleLine.getUnpaid());
                        timetableItem.setAmortization(engineScheduleLine.getAmortization());
                        timetableItem.setInterest(engineScheduleLine.getInterest());
                        timetableItem.setRate(engineScheduleLine.getRate());
                        timetableItem.setNominalRate(engineScheduleLine.getNominalRate());
                        timetableItem.setRent(engineScheduleLine.getRent());
                        timetableItem.setTaxAmount(engineScheduleLine.getTaxAmount());
                        timetableItem.setResidualValue(engineScheduleLine.getCumulativeResidualValue());
                        timetableItem.setStatus("INITIALIZED");
                        timetableItem.setTimetableId(new Timetable(finalLastRetributionDto));
                        return timetableItem;
                    })
                    .toList();
            List<TimetableItem> createdTimetableItems = timetableItemRepository.saveAll(timetableItems);

            // send message to the kafka topic
            transactionSynchronizationUtil.executeAfterCommit(
                    () -> contractProducer.sendContractTimetableItemsMessage(createdTimetableItems, OperationType.POST, MODULE));

            return ResponseEntity.ok(PageDto.<TimetableItemDto>builder()
                    .data(timetableItems.stream()
                            .map(timetableItem -> modelMapper.map(timetableItem, TimetableItemDto.class))
                            .toList())
                    .total(timetableItems.size())
                    .build());
        } else {
            throw new BusinessException("error creating schedule", MODULE);
        }
    }

    @Transactional
    public ResponseEntity<PageDto<TimetableItemDto>> getAllRetributionsSchedule(Long id) {
        List<ContractActor> contractActors = contractActorRepository.findAllByContractIdAndDeletedAtIsNull(id);
        List<Long> contractActorsIds =
                contractActors.stream().map(ContractActor::getId).toList();
        List<Retribution> contractActorRetributions =
                retributionRepository.findByContractActorIdIdInAndDeletedAtIsNull(contractActorsIds);
        List<TimetableItemDto> timetableItemDtoList = new ArrayList<>();
        for (Retribution retribution : contractActorRetributions) {
            List<TimetableItem> timetableItems = timetableItemRepository.findByTimetableIdId(
                    retribution.getTimetableId().getId());
            List<TimetableItemDto> timetableItemsDtos = timetableItems.stream()
                    .map(timetableItem -> {
                        TimetableItemDto timetableItemDto = modelMapper.map(timetableItem, TimetableItemDto.class);
                        timetableItemDto.setType("RETRIBUTION");
                        if (retribution.getLineType() != null)
                            timetableItemDto.setLineTypeLabel(retribution.getLineType().getLabel());
                        return timetableItemDto;
                    })
                    .toList();
            timetableItemDtoList.addAll(timetableItemsDtos);
        }
        return ResponseEntity.ok(
                PageDto.<TimetableItemDto>builder()
                        .data(timetableItemDtoList.stream()
                                .sorted(Comparator.comparing(TimetableItemDto::getDueDate))
                                .toList())
                        .total(timetableItemDtoList.size())
                        .build());
    }

    private List<EngineScheduleLine> generateSchedule(RetributionDto retributionDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = getEngineRubriqueFrontDto(retributionDto);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<EngineRubriqueFrontDto> requestEntity = new HttpEntity<>(rubriqueFrontDto, headers);

        ResponseEntity<PageDto<EngineScheduleLine>> schedule = restTemplate.exchange(
                engineServiceUrl + "finance-calculator/calculate-schedule",
                HttpMethod.POST,
                requestEntity,
                new ParameterizedTypeReference<PageDto<EngineScheduleLine>>() {
                }
        );

        if (schedule.getStatusCode() == HttpStatus.OK) {
            return Objects.requireNonNull(schedule.getBody()).getData();
        }

        return Collections.emptyList();
    }

    private static EngineRubriqueFrontDto getEngineRubriqueFrontDto(RetributionDto retributionDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = getRetributionFrontDto(retributionDto);
        for (int i = 0; i < retributionDto.getRetributionLevelsList().size(); i++) {
            EngineTimetableLevelFrontDto timetableLevel = new EngineTimetableLevelFrontDto();
            LevelDto levelDto = retributionDto.getRetributionLevelsList().get(i);
            timetableLevel.setOrder(i + 1);
            timetableLevel.setStartDate(levelDto.getStartDate());
            timetableLevel.setTypeUnitePeriode(levelDto.getPeriod().name());
            timetableLevel.setFrequency(1);
            timetableLevel.setDuration(levelDto.getPeriodNumber());
            timetableLevel.setAmount(levelDto.getRent());
            timetableLevel.setMultiple(levelDto.getPeriodMultiple());
            rubriqueFrontDto.getTimetableLevelFrontDtoList().add(timetableLevel);
        }
        return rubriqueFrontDto;
    }

    private static EngineRubriqueFrontDto getRetributionFrontDto(RetributionDto retributionDto) {
        EngineRubriqueFrontDto retribution = new EngineRubriqueFrontDto();
        retribution.setBase(retributionDto.getCalculationBasis().name());
        retribution.setDecompte(retributionDto.getCalculationMode().name());
        retribution.setStartDate(ContractUtils.convertToDate(retributionDto.getStartDate()));
        retribution.setEndDate(ContractUtils.convertToDate(retributionDto.getEndDate()));
        retribution.setAccessory(true);
        retribution.setTaxValue(retributionDto.getTax() == null ? 0.0 : retributionDto.getTaxRate());
        retribution.setMultiple(1);
        retribution.setRatePeriod(TypeUnitePeriode.ENUM_MOIS);
        return retribution;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<FinancialItemsDto>> updateRetributionDetails(Long id, FinancialItemsDto financialItemsDto) {
        Retribution retribution = retributionRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(RETRIBUTION_NOT_FOUND, "Retribution", MODULE));

        retribution.setTitle(financialItemsDto.getTitle());
        retribution.setSuspendedInvoice(financialItemsDto.getSuspendedInvoice());
        retribution.setSeparateInvoice(financialItemsDto.getSeparateInvoice());
        retribution.setContractActorId(contractActorRepository.findByIdAndDeletedAtIsNull(retribution.getContractActorId().getId()).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE)));
        retribution.setStatus(financialItemsDto.getStatus());
        retribution.setLineType(lineTypeRepository.findByCode(financialItemsDto.getLineTypeCode()).orElseThrow(
                () -> new ResourcesNotFoundException("LineType not found", "LineType", MODULE)));

        retributionRepository.save(retribution);
        return ResponseEntity.ok(SingleResultDto.<FinancialItemsDto>builder().data(financialItemsDto).build());
    }
}
