package com.datatricks.contracts.service;

import com.datatricks.contracts.exception.ConflictException;
import com.datatricks.contracts.exception.ResourcesNotFoundException;
import com.datatricks.contracts.exception.handler.InformativeMessage;
import com.datatricks.contracts.model.Address;
import com.datatricks.contracts.model.ContractActor;
import com.datatricks.contracts.model.ContractActorAddress;
import com.datatricks.contracts.model.dto.ContractActorAddressDto;
import com.datatricks.contracts.model.dto.ContractActorAddressInput;
import com.datatricks.contracts.model.dto.SingleResultDto;
import com.datatricks.contracts.repository.AddressRepository;
import com.datatricks.contracts.repository.ContractActorAddressRepository;
import com.datatricks.contracts.repository.ContractActorRepository;
import jakarta.transaction.Transactional;
import org.modelmapper.ModelMapper;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

import static com.datatricks.contracts.utils.ContractUtils.handleException;

@Service
public class ContractActorAddressService {
    private final ContractActorAddressRepository contractActorAddressRepository;
    private final ModelMapper modelMapper;
    private final AddressRepository addressRepository;
    private final ContractActorRepository contractActorRepository;
    private static final String CONTRACT_ACTOR_NOT_FOUND = "Contract Actor not found";
    private static final String CONTRACT_ACTOR_ADDRESS_NOT_FOUND = "Contract Actor Address not found";
    private static final String ACTOR_ADDRESS_NOT_FOUND = "Address not found for the actor";
    private static final String ADDRESS_NOT_BILLING = "The address should be billing address";
    private static final String MODULE = "Contract Actor Address";

    public ContractActorAddressService(
            ContractActorAddressRepository contractActorAddressRepository,
            ModelMapper modelMapper,
            AddressRepository addressRepository,
            ContractActorRepository contractActorRepository) {
        this.contractActorAddressRepository = contractActorAddressRepository;
        this.modelMapper = modelMapper;
        this.addressRepository = addressRepository;
        this.contractActorRepository = contractActorRepository;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractActorAddressDto>> createContractActorAddress(
            Long contractActorId, ContractActorAddressInput contractActorAddressInput) {
        try {
            ContractActor contractActor =
                    contractActorRepository.findById(contractActorId).orElseThrow(
                            () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "contract_actor", MODULE));

            if (contractActorAddressRepository.existsByContractActorIdIdAndDeletedAtIsNull(contractActorId)) {
                throw new ConflictException(
                        "Contract Actor Address already exists",
                        "CONTRACT_ACTOR_ADDRESS_ALREADY_EXISTS",
                        "Contract Actor Address already exists",
                        MODULE
                );
            }
            // Check if the actor has the address
            Address address = addressRepository.findByActorIdIdAndIdAndDeletedAtIsNull(
                    contractActor.getActor().getId(), contractActorAddressInput.getAddressId());
            if (address == null) {
                throw new ConflictException(
                        "Address not found for this actor",
                        "ACTOR_ADDRESS_NOT_FOUND",
                        "Address not found for this actor",
                        MODULE);
            }
            // Check if the Contract Actor has the role of Client then the address should be billing address
            if (Boolean.TRUE.equals(contractActor.getRole().getAssociatedTo().getIsExclusive())
                    && (Boolean.FALSE.equals(address.getIsBilling()))) {
                throw new ConflictException(
                        "The address should be billing address",
                        "ADDRESS_NOT_BILLING",
                        "The address should be billing address",
                        MODULE);
            }
            ContractActorAddress contractActorAddress = new ContractActorAddress();
            contractActorAddress.setContractActorId(new ContractActor(contractActorId));
            contractActorAddress.setAddressId(new Address(contractActorAddressInput.getAddressId()));
            contractActorAddress.setStartDate(contractActorAddressInput.getStartDate());
            contractActorAddress.setEndDate(contractActorAddressInput.getEndDate());
            contractActorAddress.setCreatedAt(new Date());
            ContractActorAddress savedContractActorAddress = contractActorAddressRepository.save(contractActorAddress);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(SingleResultDto.<ContractActorAddressDto>builder()
                            .data(modelMapper.map(savedContractActorAddress, ContractActorAddressDto.class))
                            .build());
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractActorAddressDto>> updateContractActorAddress(
            Long contractActorId, Long contractActorAddressId, ContractActorAddressInput contractActorAddressInput) {
        try {
            ContractActorAddress contractActorAddress = contractActorAddressRepository
                    .findByIdAndDeletedAtIsNull(contractActorAddressId)
                    .orElseThrow(
                            () -> new ResourcesNotFoundException(CONTRACT_ACTOR_ADDRESS_NOT_FOUND, "contract_actor_address", MODULE)
                    );

            ContractActor contractActor = contractActorRepository
                    .findByIdAndDeletedAtIsNull(contractActorId)
                    .orElseThrow(
                            () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "contract_actor", MODULE)
                    );
            // Check if the actor has the address
            Address address = addressRepository.findByActorIdIdAndIdAndDeletedAtIsNull(
                    contractActor.getActor().getId(), contractActorAddressInput.getAddressId());
            if (address == null) {
                throw new ConflictException(
                        ACTOR_ADDRESS_NOT_FOUND,
                        "ACTOR_ADDRESS_NOT_FOUND",
                        ACTOR_ADDRESS_NOT_FOUND,
                        MODULE);
            }
            // Check if the Contract Actor has the role of Client then the address should be billing address
            if (Boolean.TRUE.equals(contractActor.getRole().getAssociatedTo().getIsExclusive())
                    && (Boolean.FALSE.equals(address.getIsBilling()))) {
                throw new ConflictException(
                        ADDRESS_NOT_BILLING,
                        "ADDRESS_NOT_BILLING",
                        ADDRESS_NOT_BILLING,
                        MODULE);
            }
            contractActorAddress.setAddressId(new Address(contractActorAddressInput.getAddressId()));
            contractActorAddress.setEndDate(contractActorAddressInput.getEndDate());
            contractActorAddress.setStartDate(contractActorAddressInput.getStartDate());
            contractActorAddress.setModifiedAt(new Date());
            contractActorAddressRepository.save(contractActorAddress);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(SingleResultDto.<ContractActorAddressDto>builder()
                            .data(modelMapper.map(contractActorAddress, ContractActorAddressDto.class))
                            .build());
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteContractActorAddress(Long contractActorId, Long contractActorAddressId) {
        contractActorRepository.findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "contract_actor", MODULE)
        );
        ContractActorAddress contractActorAddress =
                contractActorAddressRepository.findByIdAndDeletedAtIsNull(contractActorAddressId).orElseThrow(
                        () -> new ResourcesNotFoundException(CONTRACT_ACTOR_ADDRESS_NOT_FOUND, "contract_actor_address", MODULE)
                );
        contractActorAddress.setDeletedAt(new Date());
        contractActorAddressRepository.save(contractActorAddress);
        return ResponseEntity.ok(new InformativeMessage(
                "Resource with ID " + contractActorAddressId + " has been deleted successfully"));
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteContractActorAddressByContractActorId(Long contractActorId) {
        ContractActorAddress contractActorAddress =
                contractActorAddressRepository.findByContractActorIdIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                        () -> new ResourcesNotFoundException(CONTRACT_ACTOR_ADDRESS_NOT_FOUND, "contract_actor_address", MODULE));
        contractActorAddress.setDeletedAt(new Date());
        contractActorAddressRepository.save(contractActorAddress);
        return ResponseEntity.ok(new InformativeMessage(
                "Resource with ID " + contractActorId + " has been deleted successfully"));
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractActorAddressDto>> getContractActorAddresses(Long contractActorId) {

        Optional<ContractActorAddress> contractActorAddress =
                contractActorAddressRepository.findByContractActorIdIdAndDeletedAtIsNull(contractActorId);

        Optional<ContractActorAddressDto> contractActorAddressPages = contractActorAddress.stream()
                .filter(address -> address.getContractActorId().getId().equals(contractActorId) && address.getDeletedAt() == null)
                .map(element -> modelMapper.map(element, ContractActorAddressDto.class))
                .findFirst();

        return new ResponseEntity<>(
                SingleResultDto.<ContractActorAddressDto>builder()
                        .data(contractActorAddressPages.orElse(null))
                        .build(),
                HttpStatus.OK);
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractActorAddressDto>> getContractActorAddresseById
            (Long contractActorId, Long contractActorAddressId) {

        contractActorRepository.findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "contract_actor", MODULE)
        );
        ContractActorAddress contractActorAddress =
                contractActorAddressRepository.findByIdAndDeletedAtIsNull(contractActorAddressId).orElseThrow(
                        () -> new ResourcesNotFoundException(CONTRACT_ACTOR_ADDRESS_NOT_FOUND, "contract_actor_address", MODULE)
                );
        return ResponseEntity.ok(
                        SingleResultDto.<ContractActorAddressDto>builder()
                            .data(modelMapper.map(contractActorAddress, ContractActorAddressDto.class))
                            .build()
        );
    }
}
