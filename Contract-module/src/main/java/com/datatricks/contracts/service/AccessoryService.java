package com.datatricks.contracts.service;

import com.datatricks.contracts.enums.TypeUnitePeriode;
import com.datatricks.contracts.exception.BusinessException;
import com.datatricks.contracts.exception.ConflictException;
import com.datatricks.contracts.exception.ResourcesNotFoundException;
import com.datatricks.contracts.exception.TechnicalException;
import com.datatricks.contracts.exception.handler.InformativeMessage;
import com.datatricks.contracts.model.*;
import com.datatricks.contracts.model.dto.*;
import com.datatricks.contracts.model.dto.external.EngineRubriqueFrontDto;
import com.datatricks.contracts.model.dto.external.EngineScheduleLine;
import com.datatricks.contracts.model.dto.external.EngineTimetableLevelFrontDto;
import com.datatricks.contracts.producer.ContractProducer;
import com.datatricks.contracts.repository.*;
import com.datatricks.contracts.utils.ContractUtils;
import com.datatricks.contracts.utils.JpaQueryFilters;
import com.datatricks.contracts.utils.TransactionSynchronizationUtil;
import com.datatricks.kafkacommondomain.enums.OperationType;
import jakarta.transaction.Transactional;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class AccessoryService {

    private final AccessoryRepository accessoryRepository;
    private final LevelRepository levelRepository;
    private final ContractActorRepository contractActorRepository;
    private final TimetableRepository timetableRepository;
    private final TimetableItemRepository timetableItemRepository;
    private final RepaymentRepository repaymentRepository;
    private final ModelMapper modelMapper;
    private final RestTemplate restTemplate;
    @Value("${spring.services.engine.url}")
    private String ENGINE_SERVER_URL;
    private final ContractProducer contractProducer;
    private final ContractActorAssetRepository contractActorAssetRepository;
    private final TimetableAssetRepository timetableAssetRepository;
    private static final String ACCESSORY_NOT_FOUND = "Accessory not found";
    private static final String CONTRACT_ACTOR_NOT_FOUND = "Contract actor not found";
    private static final String CONTRACT_ACTOR_ASSET_NOT_FOUND = "Contract actor asset not found";
    private static final String MODULE = "ACCESSORY";
    private static final String ACCESSORY_UPDATE_FORBIDDEN = "Cannot update accessory in a service state";
    private final TransactionSynchronizationUtil transactionSynchronizationUtil;
    private final LineTypeRepository lineTypeRepository;

    public AccessoryService(
            AccessoryRepository accessoryRepository,
            LevelRepository levelRepository,
            TimetableRepository timetableRepository,
            TimetableItemRepository timetableItemRepository,
            RestTemplate restTemplate,
            ContractActorRepository contractActorRepository,
            TimetableAssetRepository timetableAssetRepository,
            ModelMapper modelMapper,
            ContractActorAssetRepository contractActorAssetRepository,
            RepaymentRepository repaymentRepository,
            ContractProducer contractProducer,
            TransactionSynchronizationUtil transactionSynchronizationUtil, LineTypeRepository lineTypeRepository) {
        this.accessoryRepository = accessoryRepository;
        this.levelRepository = levelRepository;
        this.timetableRepository = timetableRepository;
        this.timetableItemRepository = timetableItemRepository;
        this.contractActorRepository = contractActorRepository;
        this.modelMapper = modelMapper;
        this.restTemplate = restTemplate;
        this.contractActorAssetRepository = contractActorAssetRepository;
        this.repaymentRepository = repaymentRepository;
        this.contractProducer = contractProducer;
        this.transactionSynchronizationUtil = transactionSynchronizationUtil;
        this.timetableAssetRepository = timetableAssetRepository;
        this.lineTypeRepository = lineTypeRepository;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AccessoryResponseDto>> createAccessory(Long id, AccessoryDto accessoryDto) {

        var contractActor = contractActorRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE)
        );
        LineType lineType = lineTypeRepository.findByCode(accessoryDto.getLineTypeCode()).orElseThrow(
                () -> new ResourcesNotFoundException("LineType not found", "LineType", MODULE));
        Timetable timetable = timetableRepository.save(new Timetable());
        Accessory accessory = modelMapper.map(accessoryDto, Accessory.class);
        accessory.setId(null);
        accessory.setTimetableId(timetable);
        accessory.setCalculationBasis(accessoryDto.getCalculationBasis());
        accessory.setCalculationMode(accessoryDto.getCalculationMode());
        accessory.setContractActorId(new ContractActor(id));
        accessory.setLineType(lineType);
        accessory.setCreatedAt(new Date());
        accessory.setAmount(accessoryDto.getOriginalAmount());
        accessory.setStatus(accessoryDto.getStatus());
        if (contractActor.getContract() != null) {
            accessory.setStartDate(contractActor.getContract().getStartDate());
            accessory.setEndDate(contractActor.getContract().getDeadline());
        } else {
            accessory.setStartDate(accessoryDto.getStartDate());
            accessory.setEndDate(accessoryDto.getEndDate());
        }
        Accessory newAccessory = accessoryRepository.save(accessory);
        AccessoryDto newAccessoryDto = modelMapper.map(newAccessory, AccessoryDto.class);
        newAccessoryDto.setAccessoryLevelsList(new ArrayList<>());
        accessoryDto.getAccessoryLevelsList().forEach(levelDto -> {
            Level level = modelMapper.map(levelDto, Level.class);
            level.setId(null);
            level.setTimetableId(timetable);
            level.setCreatedAt(new Date());
            Level newLevel = levelRepository.save(level);
            LevelDto newLevelDto = modelMapper.map(newLevel, LevelDto.class);
            newAccessoryDto.getAccessoryLevelsList().add(newLevelDto);
        });
        if (!CollectionUtils.isEmpty(newAccessoryDto.getAccessoryLevelsList())) {
            createSchedule(id, newAccessoryDto);
        }

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractAccessoryMessage(newAccessory, OperationType.POST, MODULE));

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<AccessoryResponseDto>builder()
                        .data(modelMapper.map(newAccessoryDto, AccessoryResponseDto.class)).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AccessoryResponseDto>> updateAccessory(
            Long id,
            Long accessoryId,
            AccessoryDto accessoryDto) {

        Accessory accessoryToUpdate =
                accessoryRepository
                        .findByContractActorIdIdAndContractActorIdDeletedAtIsNullAndIdAndDeletedAtIsNull(id, accessoryId);
        if (accessoryToUpdate == null) {
            throw new ResourcesNotFoundException(ACCESSORY_NOT_FOUND, "Accessory", MODULE);
        }

        if (FinancingStatus.EN_SERVICE.equals(accessoryToUpdate.getStatus())) {
            throw new ConflictException(
                    ACCESSORY_UPDATE_FORBIDDEN,
                    "ACCESSORY_UPDATE_FORBIDDEN",
                    ACCESSORY_UPDATE_FORBIDDEN,
                    MODULE);
        }

        List<Repayment> repayments =
                repaymentRepository.findByTimetableIdIdAndDeletedAtIsNull(accessoryToUpdate.getTimetableId().getId());

        if (repayments != null) {
            repayments.forEach(repayment -> {
                if (repayment.getContractActor().getId().equals(id)) {
                    throw new ConflictException(
                            "accessory contract actor id CANNOT be the same as the contract actor of its repayment",
                            "ACCESSORY_REPAYMENT_CONFLICT",
                            "accessory contract actor id CANNOT be the same as the contract actor of its repayment",
                            MODULE);
                }
            });
        }
        Date newDate = new Date();
        Accessory accessory = new Accessory(accessoryDto);
        accessory.setId(accessoryToUpdate.getId());
        accessory.setLineType(accessoryToUpdate.getLineType());
        accessory.setContractActorId(new ContractActor(id));
        accessory.setTimetableId(accessoryToUpdate.getTimetableId());
        accessory.setCreatedAt(accessoryToUpdate.getCreatedAt());
        accessory.setModifiedAt(newDate);

        var updatedAccessory = accessoryRepository.save(accessory);

        levelRepository.deleteAllByTimetableIdIdAndDeletedAtIsNull(accessory.getTimetableId().getId());
        List<Level> newLevels = new ArrayList<>();
        List<LevelDto> newSortedLevelsDto = accessoryDto.getAccessoryLevelsList().stream()
                .sorted(Comparator.comparing(LevelDto::getStartDate))
                .toList();
        for (var i = 0; i< newSortedLevelsDto.size(); i++) {
            Level level = modelMapper.map(newSortedLevelsDto.get(i), Level.class);
            level.setId(null);
            level.setOrder(i+1);
            level.setTimetableId(accessory.getTimetableId());
            level.setCreatedAt(newDate);
            level.setModifiedAt(newDate);
            newLevels.add(level);
        }

        levelRepository.saveAll(newLevels);
        ResponseEntity<SingleResultDto<AccessoryResponseDto>> createdAccessory = getAccessory(id, accessoryId);
        if (!CollectionUtils.isEmpty(
                Objects.requireNonNull(createdAccessory.getBody()).getData().getAccessoryLevelsList())) {
            createSchedule(
                    id,
                    modelMapper.map(
                            Objects.requireNonNull(createdAccessory.getBody())
                                    .getData(),
                            AccessoryDto.class));
        }

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractAccessoryMessage(updatedAccessory, OperationType.PUT, MODULE));
        return createdAccessory;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AccessoryResponseDto>> updateAccessoryToServiceState(Long accessoryId) {

        Accessory accessoryToUpdate = accessoryRepository.findByIdAndDeletedAtIsNull(accessoryId).orElseThrow(
                () -> new ResourcesNotFoundException(ACCESSORY_NOT_FOUND, "Accessory", MODULE));

        if (FinancingStatus.EN_SERVICE.equals(accessoryToUpdate.getStatus())) {
            throw new ConflictException(
                    "Accessory is already in service state",
                    "ACCESSORY_ALREADY_IN_SERVICE",
                    "Accessory is already in service state",
                    MODULE);
        }

        accessoryToUpdate.setStatus(FinancingStatus.EN_SERVICE);
        accessoryToUpdate.setModifiedAt(new Date());
        Accessory updatedAccessory = accessoryRepository.save(accessoryToUpdate);

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractAccessoryMessage(updatedAccessory, OperationType.PUT, MODULE));

        return ResponseEntity.ok(SingleResultDto.<AccessoryResponseDto>builder()
                .data(modelMapper.map(updatedAccessory, AccessoryResponseDto.class)).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<AccessoryResponseDto>> getAccessory(Long id, Long accessoryId) {

        Accessory accessory =
                accessoryRepository
                        .findByContractActorIdIdAndContractActorIdDeletedAtIsNullAndIdAndDeletedAtIsNull(id, accessoryId);
        if (accessory == null) {
            throw new ResourcesNotFoundException(ACCESSORY_NOT_FOUND, "Accessory", MODULE);
        }
        AccessoryResponseDto accessoryDto = modelMapper.map(accessory, AccessoryResponseDto.class);
        accessoryDto.getContractActorId().getContract()
                .setEndDate(ContractUtils.calculateEndDate(accessoryDto.getContractActorId().getContract().getStartDate(),
                        accessoryDto.getContractActorId().getContract().getDuration()));
        accessoryDto.setTimetableId(modelMapper.map(accessory.getTimetableId(), TimetableDto.class));
        List<Level> levels = levelRepository.findByTimetableIdIdAndDeletedAtIsNull(
                accessory.getTimetableId().getId());
        accessoryDto.setAccessoryLevelsList(levels.stream()
                .map(level -> modelMapper.map(level, LevelDto.class))
                .sorted(Comparator.comparing(LevelDto::getStartDate))
                .toList());
        return ResponseEntity.ok(SingleResultDto.<AccessoryResponseDto>builder().data(accessoryDto).build());
    }

    @Transactional
    public ResponseEntity<PageDto<AccessoryResponseDto>> getAccessories(Long id, Map<String, String> params) {

        JpaQueryFilters<Accessory> filters = new JpaQueryFilters<>(params, Accessory.class);
        Specification<Accessory> accessorySpec = (root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("contractActorId").get("contract").get("id"), id);
        Specification<Accessory> contractActorSpec = (root, query, builder) ->
                builder.isNull(root.get("contractActorId").get("deletedAt"));

        Page<Accessory> page = accessoryRepository.findAll(
                filters.getSpecification().and(accessorySpec).and(contractActorSpec),
                filters.getPageable()
        );
        List<AccessoryResponseDto> accessoryDtos = page.stream()
                .map(accessory -> {
                    AccessoryResponseDto accessoryDto = modelMapper.map(accessory, AccessoryResponseDto.class);
                    accessoryDto.getContractActorId().getContract()
                            .setEndDate(ContractUtils.calculateEndDate(accessoryDto.getContractActorId().getContract().getStartDate(),
                                    accessoryDto.getContractActorId().getContract().getDuration()));
                    List<Level> levels = levelRepository
                            .findByTimetableIdIdAndDeletedAtIsNull(accessory.getTimetableId().getId());
                    accessoryDto.setAccessoryLevelsList(levels.stream()
                            .map(level -> modelMapper.map(level, LevelDto.class))
                            .sorted(Comparator.comparing(LevelDto::getStartDate))
                            .toList());
                    return accessoryDto;
                })
                .toList();
        return ResponseEntity.ok(PageDto.<AccessoryResponseDto>builder()
                .data(accessoryDtos)
                .total(page.getTotalElements()).build());
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteAccessory(Long id, Long accessoryId) {

        Accessory accessory =
                accessoryRepository
                        .findByContractActorIdIdAndContractActorIdDeletedAtIsNullAndIdAndDeletedAtIsNull(id, accessoryId);
        if (accessory == null) {
            throw new ResourcesNotFoundException(ACCESSORY_NOT_FOUND, "Accessory", MODULE);
        }
        Date deletedDate = new Date();
        accessory.setDeletedAt(deletedDate);
        accessoryRepository.save(accessory);
        List<Level> levels = levelRepository.findByTimetableIdIdAndDeletedAtIsNull(
                accessory.getTimetableId().getId());
        levels.forEach(level -> level.setDeletedAt(deletedDate));
        levelRepository.saveAll(levels);
        accessory.getTimetableId().setDeletedAt(deletedDate);
        timetableRepository.save(accessory.getTimetableId());
        timetableItemRepository.deleteAllByTimetableIdId(accessory.getTimetableId().getId());

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractAccessoryMessage(accessory, OperationType.DELETE, MODULE));

        return ResponseEntity.ok(new InformativeMessage("Resource with ID " + accessoryId + " has been deleted successfully"));
    }

    @Transactional
    public ResponseEntity<PageDto<TimetableItemDto>> createSchedule(Long id, AccessoryDto lastAccessoryDto) {
        List<EngineScheduleLine> generatedSchedule = generateSchedule(lastAccessoryDto);

        Long finalLastAccessoryDto = lastAccessoryDto.getTimetableId();
        List<TimetableItem> allByTimetableIdId = timetableItemRepository.findByTimetableIdId(finalLastAccessoryDto);
        timetableItemRepository.deleteAllByTimetableIdId(finalLastAccessoryDto);

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractTimetableItemsMessage(allByTimetableIdId, OperationType.DELETE, MODULE));

        if (generatedSchedule != null) {
            List<TimetableItem> timetableItems = generatedSchedule.stream()
                    .map(engineScheduleLine -> {
                        TimetableItem timetableItem = new TimetableItem();
                        timetableItem.setDueDate(engineScheduleLine.getDueDate());
                        timetableItem.setStartDate(engineScheduleLine.getStartDate());
                        timetableItem.setEndDate(engineScheduleLine.getEndDate());
                        timetableItem.setUnpaid(engineScheduleLine.getUnpaid());
                        timetableItem.setAmortization(engineScheduleLine.getAmortization());
                        timetableItem.setInterest(engineScheduleLine.getInterest());
                        timetableItem.setRate(engineScheduleLine.getRate());
                        timetableItem.setNominalRate(engineScheduleLine.getNominalRate());
                        timetableItem.setRent(engineScheduleLine.getRent());
                        timetableItem.setTaxAmount(engineScheduleLine.getTaxAmount());
                        timetableItem.setResidualValue(engineScheduleLine.getCumulativeResidualValue());
                        timetableItem.setStatus("INITIALIZED");
                        timetableItem.setTimetableId(new Timetable(finalLastAccessoryDto));
                        return timetableItem;
                    })
                    .toList();
            List<TimetableItem> createdTimetableItems = timetableItemRepository.saveAll(timetableItems);

            // send message to the kafka topic
            transactionSynchronizationUtil.executeAfterCommit(
                    () -> contractProducer.sendContractTimetableItemsMessage(createdTimetableItems, OperationType.POST, MODULE));

            return ResponseEntity.ok(PageDto.<TimetableItemDto>builder()
                    .data(timetableItems.stream()
                            .map(element -> modelMapper.map(element, TimetableItemDto.class))
                            .toList())
                    .total(timetableItems.size())
                    .build());
        } else {
            throw new BusinessException("error creating schedule", MODULE);
        }
    }

    @Transactional
    public ResponseEntity<PageDto<TimetableItemDto>> getAllAccessoriesSchedule(Long id) {
        List<ContractActor> contractActors = contractActorRepository.findAllByContractIdAndDeletedAtIsNull(id);
        List<Long> contractActorsIds =
                contractActors.stream().map(ContractActor::getId).toList();
        List<Accessory> contractActorAccessories =
                accessoryRepository.findByContractActorIdIdInAndDeletedAtIsNull(contractActorsIds);
        List<TimetableItemDto> timetableItemDtoList = new ArrayList<>();
        for (Accessory accessory : contractActorAccessories) {
            List<TimetableItem> timetableItems = timetableItemRepository.findByTimetableIdId(
                    accessory.getTimetableId().getId());
            List<TimetableItemDto> timetableItemsDtos = timetableItems.stream()
                    .map(timetableItem -> {
                        TimetableItemDto timetableItemDto = modelMapper.map(timetableItem, TimetableItemDto.class);
                        timetableItemDto.setType("ACCESSORY");
                        if (accessory.getLineType() != null)
                            timetableItemDto.setLineTypeLabel(accessory.getLineType().getLabel());
                        return timetableItemDto;
                    })
                    .toList();
            timetableItemDtoList.addAll(timetableItemsDtos);
        }
        return ResponseEntity.ok(
                PageDto.<TimetableItemDto>builder()
                        .data(timetableItemDtoList.stream()
                                .sorted(Comparator.comparing(TimetableItemDto::getDueDate))
                                .toList())
                        .total(timetableItemDtoList.size())
                        .build());
    }

    private List<EngineScheduleLine> generateSchedule(AccessoryDto accessoryDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = getEngineRubriqueFrontDto(accessoryDto);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<EngineRubriqueFrontDto> requestEntity = new HttpEntity<>(rubriqueFrontDto, headers);

        ResponseEntity<PageDto<EngineScheduleLine>> schedule = restTemplate.exchange(
                ENGINE_SERVER_URL + "finance-calculator/calculate-schedule",
                HttpMethod.POST,
                requestEntity,
                new ParameterizedTypeReference<PageDto<EngineScheduleLine>>() {}
        );

        if (schedule.getStatusCode() == HttpStatus.OK) {
            return Objects.requireNonNull(schedule.getBody()).getData();
        }

        return Collections.emptyList();
    }

    private static EngineRubriqueFrontDto getEngineRubriqueFrontDto(AccessoryDto accessoryDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = getRubriqueFrontDto(accessoryDto);
        for (int i = 0; i < accessoryDto.getAccessoryLevelsList().size(); i++) {
            EngineTimetableLevelFrontDto timetableLevel = new EngineTimetableLevelFrontDto();
            LevelDto levelDto = accessoryDto.getAccessoryLevelsList().get(i);
            timetableLevel.setOrder(i + 1);
            timetableLevel.setStartDate(levelDto.getStartDate());
            timetableLevel.setTypeUnitePeriode(levelDto.getPeriod().name());
            timetableLevel.setFrequency(1);
            timetableLevel.setDuration(levelDto.getPeriodNumber());
            timetableLevel.setAmount(levelDto.getRent());
            timetableLevel.setMultiple(levelDto.getPeriodMultiple());
            rubriqueFrontDto.getTimetableLevelFrontDtoList().add(timetableLevel);
        }
        return rubriqueFrontDto;
    }

    private static EngineRubriqueFrontDto getRubriqueFrontDto(AccessoryDto accessoryDto) {
        EngineRubriqueFrontDto rubriqueFrontDto = new EngineRubriqueFrontDto();
        rubriqueFrontDto.setBase(accessoryDto.getCalculationBasis().name());
        rubriqueFrontDto.setDecompte(accessoryDto.getCalculationMode().name());
        rubriqueFrontDto.setFinancialAmount(
                accessoryDto.getOriginalAmount() == null ? 0.0 : accessoryDto.getOriginalAmount());
        rubriqueFrontDto.setStartDate(ContractUtils.convertToDate(accessoryDto.getStartDate()));
        rubriqueFrontDto.setEndDate(ContractUtils.convertToDate(accessoryDto.getEndDate()));
        rubriqueFrontDto.setAccessory(true);
        rubriqueFrontDto.setTaxValue(accessoryDto.getTax() == null ? 0.0 : accessoryDto.getTaxRate());
        rubriqueFrontDto.setMultiple(1);
        rubriqueFrontDto.setRatePeriod(TypeUnitePeriode.ENUM_MOIS);
        return rubriqueFrontDto;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractActorAssetDto>> createAccessoryAsset(
            Long contractActorId,
            Long accessoryId,
            Long contractActorAssetId) {

        contractActorRepository
                .findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                        () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE));

        var accessory = accessoryRepository.findByIdAndDeletedAtIsNull(accessoryId).orElseThrow(
                () -> new ResourcesNotFoundException(ACCESSORY_NOT_FOUND, "Accessory", MODULE));
        try {

            var contractActorAsset =
                    contractActorAssetRepository
                            .findByIdAndDeletedAtIsNull(contractActorAssetId).orElseThrow(
                                    () -> new ResourcesNotFoundException(CONTRACT_ACTOR_ASSET_NOT_FOUND, "ContractActorAsset", MODULE));

            if (contractActorAsset.getContract() != null
                    && !contractActorAsset.getContract().getId().equals(accessory.getContractActorId().getContract().getId())) {
                throw new ConflictException(
                        "Asset not in the same contract",
                        "ASSET_NOT_IN_SAME_CONTRACT",
                        "Asset not in the same contract",
                        MODULE);
            }

            if (accessory.getContractActorId().getActor() != null) {
                contractActorAsset.setActor(
                        accessory.getContractActorId().getActor());
            }

            TimetableAsset timetableAsset = new TimetableAsset();
            timetableAsset.setTimetable(accessory.getTimetableId());
            timetableAsset.setContractActorAsset(contractActorAsset);
            TimetableAsset createdTimetableAsset = timetableAssetRepository.save(timetableAsset);

            // send message to the kafka topic
            transactionSynchronizationUtil.executeAfterCommit(
                    () -> contractProducer.sendTimetableAsset(createdTimetableAsset, OperationType.POST, MODULE));

            return ResponseEntity.ok(
                    SingleResultDto.<ContractActorAssetDto>builder()
                            .data(this.modelMapper.map(contractActorAsset, ContractActorAssetDto.class)).build());
        } catch (BusinessException ex) {
            throw ex;
        }
    }

    @Transactional
    public ResponseEntity<PageDto<ContractActorAssetDto>> getAccessoryAssets(Long contractActorId, Long accessoryId) {

        contractActorRepository
                .findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                        () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE));

        var accessory = accessoryRepository.findByIdAndDeletedAtIsNull(accessoryId).orElseThrow(
                () -> new ResourcesNotFoundException(ACCESSORY_NOT_FOUND, "Rental", MODULE));

        try {
            List<ContractActorAssetDto> assets = timetableAssetRepository
                    .findAllByTimetableIdAndDeletedAtIsNull(accessory.getTimetableId().getId())
                    .stream()
                    .map(timetableAsset -> this.modelMapper.map(timetableAsset.getContractActorAsset(), ContractActorAssetDto.class))
                    .toList();

            return ResponseEntity.ok(PageDto.<ContractActorAssetDto>builder().data(assets).total(assets.size()).build());
        } catch (BusinessException ex) {
            throw ex;
        }
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractActorAssetDto>> updateAccessoryAsset(
            Long contractActorId,
            Long accessoryId,
            Long contractActorAssetId,
            AssetInput assetInput) {

        contractActorRepository.findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE));

        var accessory = accessoryRepository.findByIdAndDeletedAtIsNull(accessoryId).orElseThrow(
                () -> new ResourcesNotFoundException(ACCESSORY_NOT_FOUND, "Accessory", MODULE));
        //Remove the old relation between the asset and the accessory
        var oldContractActorAsset =
                contractActorAssetRepository
                        .findByIdAndDeletedAtIsNull(contractActorAssetId).orElse(null);
        TimetableAsset oldTimetableAsset = new TimetableAsset();
        if (oldContractActorAsset != null) {
            oldTimetableAsset = timetableAssetRepository
                    .findByTimetableIdAndContractActorAssetIdAndDeletedAtIsNull(accessory.getTimetableId().getId(),  oldContractActorAsset.getId())
                    .orElseThrow(() -> new ResourcesNotFoundException("TimetableAsset not found", "TimetableAsset", MODULE));
        }
        //Add the new relation between the asset and the rental
        var contractActorAssetToUpdate =
                contractActorAssetRepository
                        .findByIdAndDeletedAtIsNull(assetInput.getContractActorAssetId()).orElseThrow(
                                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_ASSET_NOT_FOUND, "ContractActorAsset", MODULE));

        var accessoryToUpdate = accessoryRepository.findByIdAndDeletedAtIsNull(assetInput.getAccessoryId()).orElseThrow(
                () -> new ResourcesNotFoundException(ACCESSORY_NOT_FOUND, "Accessory", MODULE));

        if (contractActorAssetToUpdate.getContract() != null
               && !contractActorAssetToUpdate.getContract().getId().equals(accessoryToUpdate.getContractActorId().getContract().getId())) {

            throw new ConflictException(
                    "Asset is affected to another contract",
                    "ASSET_ALREADY_AFFECTED",
                    "Asset is affected to another contract",
                    MODULE);
        }

        oldTimetableAsset.setTimetable(accessoryToUpdate.getTimetableId());
        oldTimetableAsset.setContractActorAsset(contractActorAssetToUpdate);
        oldTimetableAsset.setModifiedAt(new Date());
        TimetableAsset savedTimetableAsset = timetableAssetRepository.save(oldTimetableAsset);

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendTimetableAsset(savedTimetableAsset, OperationType.PUT, MODULE));

        return ResponseEntity.ok(SingleResultDto.<ContractActorAssetDto>builder()
                .data(this.modelMapper.map(contractActorAssetToUpdate, ContractActorAssetDto.class)).build());
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteAccessoryAsset(
            Long contractActorId,
            Long accessoryId,
            Long contractActorAssetId) {

        var contractActor =
                contractActorRepository
                        .findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE));

        var accessory = accessoryRepository.findByIdAndDeletedAtIsNull(accessoryId).orElseThrow(
                () -> new ResourcesNotFoundException(ACCESSORY_NOT_FOUND, "Accessory", MODULE));

        var contractActorAsset =
                contractActorAssetRepository
                        .findByIdAndDeletedAtIsNull(contractActorAssetId).orElseThrow(
                                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_ASSET_NOT_FOUND, "ContractActorAsset", MODULE));

        TimetableAsset timetableAsset = timetableAssetRepository
                .findByTimetableIdAndContractActorAssetIdAndDeletedAtIsNull(accessory.getTimetableId().getId(), contractActorAsset.getId())
                .orElseThrow(() -> new ResourcesNotFoundException("TimetableAsset not found", "TimetableAsset", MODULE));

        timetableAsset.setDeletedAt(new Date());
        timetableAssetRepository.save(timetableAsset);

        // send message to the kafka topic
        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendTimetableAsset(timetableAsset, OperationType.DELETE, MODULE));

        return ResponseEntity.ok(
                new InformativeMessage("Resource with ID " + contractActorAssetId + " has been deleted successfully"));
    }

    @Transactional
    public ResponseEntity<PageDto<AccessoryDto>> getAccessoriesByContractAndActor(Long contractId, Long actorId, Map<String, String> params) {
        List<ContractActor> contractActors = contractActorRepository.findAllByContractIdAndActorIdAndDeletedAtIsNull(contractId, actorId);
        if (contractActors.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
        List<AccessoryDto> results = new ArrayList<>();
        for (ContractActor contractActor : contractActors) {
            params.put("PEqual_contract_Actor", contractActor.getId().toString());
            JpaQueryFilters<Accessory> filters = new JpaQueryFilters<>(params, Accessory.class);
            Page<Accessory> page = accessoryRepository.findAll(filters.getSpecification(), filters.getPageable());
            if (page.isEmpty()) {
                continue;
            }
            results.addAll(page.stream()
                    .map(accessory -> this.modelMapper.map(accessory, AccessoryDto.class))
                    .toList());
        }
        return ResponseEntity.ok(PageDto.<AccessoryDto>builder().data(results).total(results.size()).build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<FinancialItemsDto>> updateAccessoryDetails(Long id, FinancialItemsDto financialItemsDto) {
        Accessory accessory = accessoryRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                () -> new ResourcesNotFoundException(ACCESSORY_NOT_FOUND, "Accessory", MODULE));

        accessory.setTitle(financialItemsDto.getTitle());
        accessory.setSuspendedInvoice(financialItemsDto.getSuspendedInvoice());
        accessory.setMobileExtension(financialItemsDto.getMobileExtension());
        accessory.setSeparateInvoice(financialItemsDto.getSeparateInvoice());
        accessory.setContractActorId(contractActorRepository.findByIdAndDeletedAtIsNull(financialItemsDto.getContractActorId()).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "ContractActor", MODULE)));
        accessory.setStatus(financialItemsDto.getStatus());
        accessory.setLineType(lineTypeRepository.findByCode(financialItemsDto.getLineTypeCode()).orElseThrow(
                () -> new ResourcesNotFoundException("LineType not found", "LineType", MODULE)));
        accessory.setModifiedAt(new Date());
        Accessory updatedAccessory = accessoryRepository.save(accessory);

        transactionSynchronizationUtil.executeAfterCommit(
                () -> contractProducer.sendContractAccessoryMessage(updatedAccessory, OperationType.PUT, MODULE));

        return ResponseEntity.ok(SingleResultDto.<FinancialItemsDto>builder()
                .data(modelMapper.map(updatedAccessory, FinancialItemsDto.class)).build());
    }
}
