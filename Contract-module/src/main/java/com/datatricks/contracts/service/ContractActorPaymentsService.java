package com.datatricks.contracts.service;

import com.datatricks.contracts.exception.ConflictException;
import com.datatricks.contracts.exception.ResourcesNotFoundException;
import com.datatricks.contracts.exception.handler.InformativeMessage;
import com.datatricks.contracts.model.*;
import com.datatricks.contracts.model.dto.ContractActorPaymentsDto;
import com.datatricks.contracts.model.dto.ContractActorPaymentsInput;
import com.datatricks.contracts.model.dto.PageDto;
import com.datatricks.contracts.model.dto.SingleResultDto;
import com.datatricks.contracts.repository.BankAccountRepository;
import com.datatricks.contracts.repository.ContractActorPaymentsRepository;
import com.datatricks.contracts.repository.ContractActorRepository;
import com.datatricks.contracts.repository.PaymentMethodRepository;
import jakarta.transaction.Transactional;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class ContractActorPaymentsService {

    private final ContractActorPaymentsRepository contractActorPaymentsRepository;
    private final ContractActorRepository contractActorRepository;
    private final ModelMapper modelMapper;
    private final BankAccountRepository bankAccountRepository;
    private static final String CONTRACT_ACTOR_PAYMENT_NOT_FOUND = "Contract Actor Payment not found";
    private static final String CONTRACT_ACTOR_NOT_FOUND = "Contract Actor not found";
    private static final String BANK_ACCOUNT_NOT_FOUND = "Bank Account not found";
    private static final String PAYMENT_METHOD_NOT_FOUND = "Payment method not found";
    private static final String CONTRACT_ACTOR_PAYMENT_ALREADY_EXISTS =
            "Contract Actor Payment already exists for this contract actor";
    private static final String MODULE = "Contract Actor Payments";
    private final PaymentMethodRepository paymentMethodRepository;

    public ContractActorPaymentsService(
            ContractActorPaymentsRepository contractActorPaymentsRepository,
            ContractActorRepository contractActorRepository,
            ModelMapper modelMapper,
            BankAccountRepository bankAccountRepository,
            PaymentMethodRepository paymentMethodRepository) {
        this.contractActorPaymentsRepository = contractActorPaymentsRepository;
        this.contractActorRepository = contractActorRepository;
        this.modelMapper = modelMapper;
        this.bankAccountRepository = bankAccountRepository;
        this.paymentMethodRepository = paymentMethodRepository;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractActorPaymentsDto>> addContractActorPayments(
            Long contractActorId, ContractActorPaymentsInput contractActorPaymentsInput) {

        ContractActor contractActor = contractActorRepository.findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "contract_actor", MODULE));

        ContractActorPayments contractActorPayments = new ContractActorPayments();
        contractActorPayments.setContractActor(contractActor);
        PaymentMethod paymentMethod = paymentMethodRepository.findByCode(
                contractActorPaymentsInput.getPaymentMethodCode()).orElseThrow(
                        () -> new ResourcesNotFoundException(PAYMENT_METHOD_NOT_FOUND, "payment_method", MODULE));

        contractActorPayments.setPaymentMethod(paymentMethod);
        if(paymentMethod.getRequiresBankAccount()) {
            var bankAccountId = contractActorPaymentsInput.getBankAccountId();
            if (bankAccountId == null || bankAccountId == 0) {
                throw new ResourcesNotFoundException(BANK_ACCOUNT_NOT_FOUND, "bank_account", MODULE);
            }
            BankAccount bankAccount = bankAccountRepository.findByActorIdIdAndIdAndDeletedAtIsNull(
                    contractActorPaymentsInput.getType() != ContractActorPaymentsType.Target ?
                            contractActor.getActor().getId() :
                            contractActor.getContract().getManagementCompany().getId(),
                    bankAccountId).orElseThrow(
                            () -> new ResourcesNotFoundException(BANK_ACCOUNT_NOT_FOUND, "bank_account", MODULE));
            contractActorPayments.setBankAccount(bankAccount);
        }
        Optional<ContractActorPayments> duplicatePayment = contractActorPaymentsRepository
                .findByContractActorIdAndTypeAndDeletedAtIsNull(contractActorId, contractActorPaymentsInput.getType());
        if (duplicatePayment.isPresent()) {
            throw new ConflictException(
                    CONTRACT_ACTOR_PAYMENT_ALREADY_EXISTS,
                    "CONTRACT_ACTOR_PAYMENT_ALREADY_EXISTS",
                    CONTRACT_ACTOR_PAYMENT_ALREADY_EXISTS,
                    MODULE);
        }

        contractActorPayments.setType(contractActorPaymentsInput.getType());
        contractActorPayments.setStartDate(contractActorPaymentsInput.getStartDate());

        contractActorPayments.setCreatedAt(new Date());
        contractActorPaymentsRepository.save(contractActorPayments);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(SingleResultDto.<ContractActorPaymentsDto>builder()
                        .data(modelMapper.map(contractActorPayments, ContractActorPaymentsDto.class))
                        .build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractActorPaymentsDto>> updateContractActorPayments(
            Long contractActorId, Long contractActorPaymentId, ContractActorPaymentsInput contractActorPaymentsInput) {

        ContractActor contractActor = contractActorRepository.findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "contract_actor", MODULE));

        ContractActorPayments contractActorPayments =
                contractActorPaymentsRepository.findByIdAndDeletedAtIsNull(contractActorPaymentId).orElseThrow(
                        () -> new ResourcesNotFoundException(CONTRACT_ACTOR_PAYMENT_NOT_FOUND, "contract_actor_payment", MODULE)
                );
        PaymentMethod paymentMethod = paymentMethodRepository.findByCode(
                contractActorPaymentsInput.getPaymentMethodCode()).orElseThrow(
                () -> new ResourcesNotFoundException(PAYMENT_METHOD_NOT_FOUND, "payment_method", MODULE));

        contractActorPayments.setPaymentMethod(paymentMethod);
        if(paymentMethod.getRequiresBankAccount()) {
            var bankAccountId = contractActorPaymentsInput.getBankAccountId();
            if (bankAccountId == null || bankAccountId == 0) {
                throw new ResourcesNotFoundException(BANK_ACCOUNT_NOT_FOUND, "bank_account", MODULE);
            }
            BankAccount bankAccount = bankAccountRepository.findByActorIdIdAndIdAndDeletedAtIsNull(
                    contractActorPaymentsInput.getType() != ContractActorPaymentsType.Target ?
                            contractActor.getActor().getId() :
                            contractActor.getContract().getManagementCompany().getId(),
                    bankAccountId).orElseThrow(
                    () -> new ResourcesNotFoundException(BANK_ACCOUNT_NOT_FOUND, "bank_account", MODULE));
            contractActorPayments.setBankAccount(bankAccount);
        } else {
            contractActorPayments.setBankAccount(null);
        }

        Optional<ContractActorPayments> duplicatePayment = contractActorPaymentsRepository
                .findByContractActorIdAndTypeAndDeletedAtIsNull(contractActorId, contractActorPaymentsInput.getType());
        if (duplicatePayment.isPresent() && !duplicatePayment.get().getId().equals(contractActorPaymentId)) {
            throw new ConflictException(
                    CONTRACT_ACTOR_PAYMENT_ALREADY_EXISTS,
                    "CONTRACT_ACTOR_PAYMENT_ALREADY_EXISTS",
                    CONTRACT_ACTOR_PAYMENT_ALREADY_EXISTS,
                    MODULE);
        }

        contractActorPayments.setType(contractActorPaymentsInput.getType());
        contractActorPayments.setStartDate(contractActorPaymentsInput.getStartDate());

        contractActorPayments.setModifiedAt(new Date());
        contractActorPaymentsRepository.save(contractActorPayments);

        return ResponseEntity.status(HttpStatus.OK)
                .body(SingleResultDto.<ContractActorPaymentsDto>builder()
                        .data(modelMapper.map(contractActorPayments, ContractActorPaymentsDto.class))
                        .build());
    }

    @Transactional
    public ResponseEntity<InformativeMessage> deleteContractActorPayments(Long contractActorId, Long id) {
        contractActorRepository.findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "contract_actor", MODULE)
        );
        ContractActorPayments contractActorPayments =
                contractActorPaymentsRepository.findByIdAndDeletedAtIsNull(id).orElseThrow(
                        () -> new ResourcesNotFoundException(CONTRACT_ACTOR_PAYMENT_NOT_FOUND, "contract_actor_payment", MODULE)
                );
        contractActorPayments.setDeletedAt(new Date());
        contractActorPaymentsRepository.save(contractActorPayments);
        return ResponseEntity.status(HttpStatus.OK)
                .body(new InformativeMessage("Resource with ID " + id + " has been deleted successfully"));
    }

    @Transactional
    public ResponseEntity<PageDto<ContractActorPaymentsDto>> getContractActorPayments(Long contractActorId) {
        List<ContractActorPayments> contractActorPayments =
                contractActorPaymentsRepository.findByContractActorIdAndDeletedAtIsNull(contractActorId);
        List<ContractActorPaymentsDto> pageableDto = contractActorPayments.stream()
                .map(contractActorPayment -> modelMapper.map(contractActorPayment, ContractActorPaymentsDto.class))
                .toList();
        return ResponseEntity.ok(PageDto.<ContractActorPaymentsDto>builder()
                .data(pageableDto)
                .total(contractActorPayments.size())
                .build());
    }

    @Transactional
    public ResponseEntity<SingleResultDto<ContractActorPaymentsDto>> getContractActorPaymentsById(Long contractActorId, Long contractActorPaymentsId) {
        contractActorRepository.findByIdAndDeletedAtIsNull(contractActorId).orElseThrow(
                () -> new ResourcesNotFoundException(CONTRACT_ACTOR_NOT_FOUND, "contract_actor", MODULE)
        );
        ContractActorPayments contractActorPayments = contractActorPaymentsRepository.findByIdAndDeletedAtIsNull(contractActorPaymentsId)
                .orElseThrow(() -> new ResourcesNotFoundException(CONTRACT_ACTOR_PAYMENT_NOT_FOUND, "contract_actor_payment", MODULE));
        ContractActorPaymentsDto response = modelMapper.map(contractActorPayments, ContractActorPaymentsDto.class);
        return new ResponseEntity<>(
                SingleResultDto.<ContractActorPaymentsDto>builder()
                        .data(response)
                        .build(),
                HttpStatus.OK);
    }
}
