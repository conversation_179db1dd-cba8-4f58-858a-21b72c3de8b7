package com.datatricks.contracts.utils;

import com.datatricks.contracts.model.*;
import com.datatricks.contracts.model.dto.JpaQueryDto;

import java.util.Map;

public class JpaQueryUtils {

    private JpaQueryUtils() {
        // Utility class
    }

    // This class MUST remain immutable
    // spotless:off
    // @formatter:off

    // [         KEY      : [       KEY     : [                     VALUE1                     |              VALUE2
    //           |    VALUE3     |  VALUE4]]
    // [ Principal entity : [Param from URL : [ joinColumn between Principal and joined entity | column to filter in
    // joined entity | Joined entity | next join]]

    private static final Map<Class<?>, Map<String, JpaQueryDto>> correspondanceEntityMap = Map.of(
            Contract.class,
                    Map.of(
                            "contract_actor", new JpaQueryDto("contractActor", "actor_id", ContractActor.class, null),
                            "contract_actor_name",
                                    new JpaQueryDto(
                                            "contractActor", "actor_id", ContractActor.class, "join_actor_name"),
                            "contract_actor_identity",
                                    new JpaQueryDto(
                                            "contractActor", "actor_id", ContractActor.class, "join_actor_identity"),
                            "contract_actor_role",
                                    new JpaQueryDto(
                                            "contractActor",
                                            "actor_id",
                                            ContractActor.class,
                                            "join_contract_actor_role"),
                            "phase_code", new JpaQueryDto("phaseId", "code", Phase.class, null),
                            "contract_actor_code",
                                    new JpaQueryDto(
                                            "contractActor", "actor_id", ContractActor.class, "join_actor_code"),
                            "contractActor_role",
                            new JpaQueryDto(
                                    "contractActor", "role_id", ContractActor.class, null)
                    ),
            ContractActor.class,
                    Map.of(
                            "join_actor_name", new JpaQueryDto("actorId", "name", Actor.class, null),
                            "join_actor_identity", new JpaQueryDto("actorId", "national_identity", Actor.class, null),
                            "join_contract_actor_role",
                                    new JpaQueryDto("actorId", "role_id", Actor.class, "join_actor_role"),
                            "join_actor_code", new JpaQueryDto("actorId", "reference", Actor.class, null),
                            "join_contract_actor_role_id",
                                    new JpaQueryDto("actor_id", "role_id", ContractActor.class, null),
                            "role_associated_to", new JpaQueryDto("role", "code", Role.class, "join_static_role")
                    ),
            Actor.class, Map.of("join_actor_role", new JpaQueryDto("actorRole", "role_id", ActorRole.class, null)),
            Rental.class, Map.of("contract_Actor", new JpaQueryDto("contractActorId", "id", ContractActor.class, null),
                    "contract", new JpaQueryDto("contractActorId", "contract_id", ContractActor.class, null)
            ),
            Accessory.class, Map.of("contract_Actor", new JpaQueryDto("contractActorId", "id", ContractActor.class, null)),
            Repayment.class, Map.of(
                    "timetable", new JpaQueryDto("timetableId", "id", Timetable.class, null),
                    "contract_actor", new JpaQueryDto("contractActor", "id", ContractActor.class, null)
            ),
            ContractSummary.class, Map.of(
                    "phase_code", new JpaQueryDto("phase", "code", Phase.class, null),
                    "milestone_code", new JpaQueryDto("milestone", "code", Milestone.class, null),
                    "business_name", new JpaQueryDto("managementCompany", "name", Actor.class, null)
            ),
            Role.class, Map.of(
                    "join_static_role", new JpaQueryDto("associatedTo", "code", StaticRole.class, null)
            )
    );
    // @formatter:on
    // spotless:off
    public static JpaQueryDto getParameterMapping(Class<?> clazz, String param) {
        Map<String, JpaQueryDto> entityMap = correspondanceEntityMap.get(clazz);
        if (entityMap != null) {
            return entityMap.get(param);
        } else {
            return null;
        }
    }
}
