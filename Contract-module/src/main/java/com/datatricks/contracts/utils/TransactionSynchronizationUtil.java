package com.datatricks.contracts.utils;

import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

@Component
public class TransactionSynchronizationUtil {

    /**
     * Register a callback to be executed after a successful transaction commit.
     * @param action The action to execute after commit.
     */
    public void executeAfterCommit(Runnable action) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                action.run();
            }
        });
    }
}



