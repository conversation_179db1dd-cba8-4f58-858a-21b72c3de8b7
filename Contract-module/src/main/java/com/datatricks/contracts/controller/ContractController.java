package com.datatricks.contracts.controller;

import com.datatricks.contracts.exception.handler.InformativeMessage;
import com.datatricks.contracts.model.dto.*;
import com.datatricks.contracts.service.ContractService;
import com.datatricks.contracts.service.FinancialItemsService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.Explode;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.enums.ParameterStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Map;

import static com.datatricks.contracts.utils.ContractUtils.handleException;
import static com.toedter.spring.hateoas.jsonapi.MediaTypes.JSON_API_VALUE;

@RestController
@RequestMapping("/api")
public class ContractController {

    private final ContractService contractService;
    private final FinancialItemsService financialItemsService;

    ContractController(ContractService contractService,
                       FinancialItemsService financialItemsService) {
        this.contractService = contractService;
        this.financialItemsService = financialItemsService;
    }

    @Cacheable(value = "contractsSummary", key = "#params.toString()")
    @GetMapping(value = "/v1/contracts-summary")
    public ResponseEntity<PageDto<ContractSummaryDto>> getAllSummaryContracts(
            @Parameter(name = "params",
                    in = ParameterIn.QUERY,
                    schema = @Schema(type = "object", implementation = ContractSummaryParam.class),
                    style = ParameterStyle.FORM,
                    explode = Explode.TRUE)
            @RequestParam Map<String, String> params) {
        try {
            return contractService.getAllSummaryContracts(params);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatusCode.valueOf(200)).body(PageDto.<ContractSummaryDto>builder()
                    .data(new ArrayList<>())
                    .total(0).build());
        }
    }

    @Cacheable(value = "contractsList", key = "#params.toString()")
    @GetMapping(value = "/v1/contracts")
    public ResponseEntity<PageDto<ContractResponseDto>> getAllFilteredContracts(
            @Parameter(name = "params",
                    in = ParameterIn.QUERY,
                    schema = @Schema(type = "object", implementation = ContractParam.class),
                    style = ParameterStyle.FORM,
                    explode = Explode.TRUE)
            @RequestParam Map<String, String> params) {
        try {
            return contractService.getAllFilteredContracts(params);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatusCode.valueOf(200)).body(PageDto.<ContractResponseDto>builder()
                    .data(new ArrayList<>())
                    .total(0).build());
        }
    }

    @GetMapping(value = "/v1/contracts/all-contracts", produces = JSON_API_VALUE)
    public ResponseEntity<RepresentationModel<?>> getAllContracts(@Parameter(name = "params",
                                                                          in = ParameterIn.QUERY,
                                                                          schema = @Schema(type = "object", implementation = ContractParam.class),
                                                                          style = ParameterStyle.FORM,
                                                                          explode = Explode.TRUE)
                                                                  @RequestParam Map<String, String> params,
                                                                  @RequestParam(value = "include", required = false) String[] include,
                                                                  @RequestParam(value = "fields[movies]", required = false) String[] fieldsMovies) {
        try {
            return contractService.findAllContracts(include, params, fieldsMovies);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Cacheable(value = "contracts", key = "#id")
    @GetMapping(value = "/v1/contracts/{id}")
    public ResponseEntity<SingleResultDto<ContractResponseDto>> getContractById(@PathVariable Long id) {
        try {
            return contractService.getContractById(id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Cacheable(value = "contracts", key = "#reference", unless = "#result.statusCode.value() == 404")
    @GetMapping(value = "/v1/contracts/reference/{reference}")
    public ResponseEntity<SingleResultDto<ContractResponseDto>> getContractByReference(@PathVariable String reference) {
        try {
            return contractService.getContractByReference(reference);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            put = @CachePut(value = "contracts", key = "#result.body.data.id"),
            evict = {
                    @CacheEvict(value = "contractsList", allEntries = true)
            }
    )
    @PostMapping(value = "/v1/contracts")
    public ResponseEntity<SingleResultDto<ContractResponseDto>> createContract(@RequestBody @Valid ContractDto contract) {
        try {
            return contractService.createContract(contract);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            evict = {
                    @CacheEvict(value = "contracts", key = "#id"),
                    @CacheEvict(value = "contractsList", allEntries = true)
            }
    )
    @PutMapping(value = "/v1/contracts/{id}")
    public ResponseEntity<SingleResultDto<ContractResponseDto>> updateContract(
            @PathVariable Long id, @RequestBody @Valid ContractDto newContract) {

        try {
            return contractService.updateContract(id, newContract);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    //@PutMapping(value = "/v1/contracts/complete")
    //public ResponseEntity<PageDto<ContractResponseDto>> completeContract(@RequestBody List<Long> ids) {
//
    //    try {
    //        return contractService.completeContract(ids);
    //    } catch (DataIntegrityViolationException e) {
    //        throw handleException(e);
    //    }
    //}

    @PatchMapping(value = "/v1/contracts/{id}")
    public ResponseEntity<PatchResponseDto> updateContract(
            @PathVariable Long id, @RequestBody PatchDto<PatchContractDto> newContractDto) {

        try {
            return contractService.patchContract(id, newContractDto);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            evict = {
                    @CacheEvict(value = "contracts", key = "#id"),
                    @CacheEvict(value = "contractsList", allEntries = true)
            }
    )
    @DeleteMapping(value = "/v1/contracts/{id}")
    public ResponseEntity<InformativeMessage> deleteContractById(@PathVariable Long id) {
        try {
            return contractService.deleteContractById(id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @GetMapping(value = "/v1/contracts/{id}/financial-items")
    public ResponseEntity<PageDto<FinanceElementsViewDto>> getFinances(
            @PathVariable Long id,
            @Parameter(name = "params",
                    in = ParameterIn.QUERY,
                    schema = @Schema(type = "object", implementation = FinanceElementsParam.class),
                    style = ParameterStyle.FORM,
                    explode = Explode.TRUE)
            @RequestParam Map<String, String> params) {
        try {
            return contractService.getFinances(params, id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @PostMapping(value = "/v1/contracts/{id}/financial-items")
    public ResponseEntity<SingleResultDto<FinancialItemsDto>> createFinance(
            @PathVariable Long id, @RequestBody @Valid FinancialItemsDto finance) {
        try {
            return contractService.createFinancialItem(id, finance);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @PutMapping(value = "/v1/contracts/{id}/financial-items/{financial_item_id}")
    public ResponseEntity<SingleResultDto<FinancialItemsDto>> updateFinance(
            @PathVariable Long id, @PathVariable Long financial_item_id, @RequestBody @Valid FinancialItemsDto finance) {
        try {
            return contractService.updateFinancialItem(id, financial_item_id, finance);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Cacheable(value = "financeItemsScheduleList", key = "#contract_id")
    @GetMapping(value = "/v1/contracts/{contract_id}/financial-items/schedule")
    public ResponseEntity<PageDto<TimetableItemDto>> getAllFinancialItemsSchedule(@PathVariable Long contract_id) {
        try {
            return financialItemsService.getAllFinancialItemsSchedule(contract_id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @PutMapping(value = "/v1/contracts/{id}/pause")
    public ResponseEntity<SingleResultDto<ContractResponseDto>> pauseContract(
            @PathVariable Long id) {
        try {
            return contractService.pauseContract(id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @PutMapping(value = "/v1/contracts/{id}/resume")
    public ResponseEntity<SingleResultDto<ContractResponseDto>> resumeContract(
            @PathVariable Long id) {
        try {
            return contractService.resumeContract(id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @GetMapping(value = "/v1/contracts/{id}/logs")
    public ResponseEntity<PageDto<ContractLogDto>> contractLogs(@PathVariable Long id) {
            return contractService.contractLogs(id);
    }
}
