package com.datatricks.contracts.controller;

import com.datatricks.contracts.model.dto.inject.ContractInjectDto;
import com.datatricks.contracts.service.ContractInjectService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.hateoas.EntityModel;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.hateoas.RepresentationModel;


import static com.toedter.spring.hateoas.jsonapi.MediaTypes.JSON_API_VALUE;

@RestController
@RequestMapping(value = "/api", produces = JSON_API_VALUE)
@Tag(name = "Contract inject", description = "Contract inject API")
public class ContractInjectController {
    private final ContractInjectService contractInjectService;

    public ContractInjectController(ContractInjectService contractInjectService) {
        this.contractInjectService = contractInjectService;
    }

    @PostMapping(value = "/v1/contracts/inject", consumes = JSON_API_VALUE, produces = JSON_API_VALUE)
    public ResponseEntity<RepresentationModel<?>> injectContract(@RequestBody @Valid EntityModel<ContractInjectDto> contractInjectDto) throws InterruptedException {
        try{
            return contractInjectService.injectContract(contractInjectDto.getContent());
        }catch (Exception e){
            throw e;
        }
    }

//    @GetMapping("/v1/contracts/inject/{id}")
//    @Transactional
//    public ResponseEntity<? extends RepresentationModel<?>> findOne(
//            @PathVariable Long id,
//            @RequestParam(value = "include", required = false) String[] include,
//            @RequestParam(value = "fields[actors]", required = false) String[] fieldsMovies) {
//        var contract = repository.findById(id).orElseThrow(() -> new ResourceNotFoundException("Contract not found for id: " + id));
//        RepresentationModel<?> contractJsonApiModel = genericAssembler.toJsonApiModel(contract, fieldsMovies);
//        JsonApiModelBuilder jsonApiModelBuilder = JsonApiModelBuilder.jsonApiModel()
//                .model(contractJsonApiModel);
//        inclusionService.processIncludes(contract, include, jsonApiModelBuilder);
//        return ResponseEntity.ok(jsonApiModelBuilder.build());
//
//    }
}
