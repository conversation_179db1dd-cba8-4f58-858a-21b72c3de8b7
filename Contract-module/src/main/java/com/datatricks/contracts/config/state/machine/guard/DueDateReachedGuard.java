package com.datatricks.contracts.config.state.machine.guard;

import com.datatricks.contracts.enums.ContractEvent;
import com.datatricks.contracts.enums.ContractState;
import com.datatricks.contracts.exception.ResourcesNotFoundException;
import com.datatricks.contracts.model.Contract;
import com.datatricks.contracts.model.dto.PageDto;
import com.datatricks.contracts.model.dto.TimetableItemDto;
import com.datatricks.contracts.repository.ContractRepository;
import com.datatricks.contracts.service.FinancialItemsService;
import org.springframework.http.ResponseEntity;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.guard.Guard;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

@Component
public class DueDateReachedGuard implements Guard<ContractState, ContractEvent> {

    private static final String TIMETABLE_ITEM_READY_FOR_PAYMENT = "INVOICE_GENERATED";
    private final ContractRepository contractRepository;
    private final FinancialItemsService financialItemsService;

    DueDateReachedGuard(ContractRepository contractRepository, FinancialItemsService financialItemsService) {
        this.contractRepository = contractRepository;
        this.financialItemsService = financialItemsService;
    }

    private boolean isTimetableItemOverdue(TimetableItemDto timetableItem) {
        return TIMETABLE_ITEM_READY_FOR_PAYMENT.equals(timetableItem.getStatus())
                && timetableItem.getDueDate().isBefore(LocalDate.now());
    }
    public List<TimetableItemDto> getAllTimetableItems(Contract contract) {
        ResponseEntity<PageDto<TimetableItemDto>> allFinancialItemsSchedule = financialItemsService.getAllFinancialItemsSchedule(contract.getId());
        if (allFinancialItemsSchedule.getBody() == null || allFinancialItemsSchedule.getBody().getData() == null) {
            return null;
        }
        return allFinancialItemsSchedule.getBody().getData().stream()
                .toList();
    }

    @Override
    public boolean evaluate(StateContext<ContractState, ContractEvent> stateContext) {
        Contract contract = contractRepository.findByIdAndDeletedAtIsNull((Long) stateContext.getMessageHeader("contract_id")).orElseThrow(
                () -> new ResourcesNotFoundException("Contract not found", "CONTRACT", "STATE_MACHINE"));
        if (!contract.getStatus().equals(ContractState.IN_SERVICE)) return false;
        List<TimetableItemDto> unpaidTimetableItems = getAllTimetableItems(contract);
        if (unpaidTimetableItems == null) return false;
        for (TimetableItemDto installment : unpaidTimetableItems) {
            if (isTimetableItemOverdue(installment)) {
                stateContext.getExtendedState().getVariables().put("unpaid-timetable-item-id", installment.getId());
                return true;
            }
        }
        return false;
    }
}