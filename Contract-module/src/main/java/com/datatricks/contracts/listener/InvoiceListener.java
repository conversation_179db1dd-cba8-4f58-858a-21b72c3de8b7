package com.datatricks.contracts.listener;

import com.datatricks.contracts.exception.BusinessException;
import com.datatricks.contracts.model.*;
import com.datatricks.contracts.repository.TimetableItemRepository;
import com.datatricks.contracts.service.*;
import com.datatricks.kafkacommondomain.model.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.common.errors.ResourceNotFoundException;
import org.modelmapper.ModelMapper;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Service
public class InvoiceListener {

    private final ObjectMapper objectMapper;
    private static final String ERRORMESSAGE = "Error while processing message ";
    private final InvoiceService invoiceService;
    private final ModelMapper modelMapper;
    private final String KAFKAERROR = "DATA-SYNC-ERROR";
    private final TimetableItemRepository timetableItemRepository;

    public InvoiceListener(ObjectMapper objectMapper,
                           InvoiceService invoiceService,
                           ModelMapper modelMapper, TimetableItemRepository timetableItemRepository) {
        this.objectMapper = objectMapper;
        this.invoiceService = invoiceService;
        this.modelMapper = modelMapper;
        this.timetableItemRepository = timetableItemRepository;
    }


    @KafkaListener(topics = "${spring.kafka.topic.listenTo.invoice-contract-timetable-items-topic}", groupId = "${spring.kafka.consumer.group-id}")
    public void listenTimetableItem(String message, Acknowledgment acknowledgment) throws BusinessException {
        try {
            KafkaMessage<?> kafkaMessage = objectMapper.readValue(message, KafkaMessage.class);
            TimetableItemStreamDto timetableItemStreamDto = modelMapper.map(kafkaMessage.getData(), TimetableItemStreamDto.class);
            TimetableItem timetableItem = timetableItemRepository.findByIdAndDeletedAtIsNull(timetableItemStreamDto.getId()).orElseThrow(
                    () -> new ResourceNotFoundException("TimetableItem not found", "timetableItem"));

            timetableItem.setStatus(timetableItemStreamDto.getStatus());
            timetableItem.setModifiedAt(timetableItemStreamDto.getModifiedAt());
            timetableItem.setDeletedAt(timetableItemStreamDto.getDeletedAt());
            invoiceService.updateTimetableItem(timetableItem, acknowledgment);


        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }
}

