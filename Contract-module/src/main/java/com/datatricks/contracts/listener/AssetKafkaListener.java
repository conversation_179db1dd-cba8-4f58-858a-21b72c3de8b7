package com.datatricks.contracts.listener;

import com.datatricks.contracts.exception.BusinessException;
import com.datatricks.contracts.model.Asset;
import com.datatricks.contracts.model.ContractActorAsset;
import com.datatricks.contracts.model.TimetableAsset;
import com.datatricks.contracts.service.AssetService;
import com.datatricks.contracts.service.ContractAssetService;
import com.datatricks.contracts.service.ContractTimetableAssetService;
import com.datatricks.kafkacommondomain.model.ContractActorAssetStreamDto;
import com.datatricks.kafkacommondomain.model.KafkaMessage;
import com.datatricks.kafkacommondomain.model.TimetableAssetStreamDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.modelmapper.ModelMapper;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Service
public class AssetKafkaListener {
    private final ObjectMapper objectMapper;
    private static final String ERRORMESSAGE = "Error while processing message ";
    private final AssetService assetService;
    private final ContractAssetService contractAssetService;
    private final ModelMapper modelMapper;
    private final String KAFKAERROR = "DATA-SYNC-ERROR";
    private final ContractTimetableAssetService contractTimetableAssetService;

    public AssetKafkaListener(ObjectMapper objectMapper, AssetService assetService, ContractAssetService contractAssetService, ModelMapper modelMapper, ContractTimetableAssetService contractTimetableAssetService) {
        this.objectMapper = objectMapper;
        this.assetService = assetService;
        this.contractAssetService = contractAssetService;
        this.modelMapper = modelMapper;
        this.contractTimetableAssetService = contractTimetableAssetService;
    }

    @KafkaListener(topics = "${spring.kafka.topic.listenTo.asset-topic}", groupId = "${spring.kafka.consumer.group-id}")
    public void listenAssetTopic(String message, Acknowledgment acknowledgment) throws BusinessException {
        try{
            KafkaMessage<?> kafkaMessage = objectMapper.readValue(message, KafkaMessage.class);
            var asset = modelMapper.map(kafkaMessage.getData(), Asset.class);

            switch (kafkaMessage.getOperation()){
                case POST -> assetService.createAsset(asset, acknowledgment);
                case PUT -> assetService.updateAsset(asset, acknowledgment);
                case DELETE -> assetService.deleteAsset(asset, acknowledgment);
            }
        } catch (Exception e){
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    @KafkaListener(topics = "${spring.kafka.topic.listenTo.asset-contract-actor-topic}", groupId = "${spring.kafka.consumer.group-id}")
    public void listenContractAssetTopic(String message, Acknowledgment acknowledgment) throws BusinessException {
        try{
            KafkaMessage<?> kafkaMessage = objectMapper.readValue(message, KafkaMessage.class);
            var contractActorAssetDto = modelMapper.map(kafkaMessage.getData(), ContractActorAssetStreamDto.class);
            if (contractActorAssetDto.getActor().getId() == null) {
                contractActorAssetDto.setActor(null);
            } if (contractActorAssetDto.getContract().getId() == null) {
                contractActorAssetDto.setContract(null);
            }
            var contractActorAsset = modelMapper.map(contractActorAssetDto, ContractActorAsset.class);

            contractActorAsset.setCreatedAt(contractActorAssetDto.getCreatedAt());
            contractActorAsset.setModifiedAt(contractActorAssetDto.getModifiedAt());
            contractActorAsset.setDeletedAt(contractActorAssetDto.getDeletedAt());
            switch (kafkaMessage.getOperation()){
                case POST -> contractAssetService.createContractAsset(contractActorAsset, acknowledgment);
                case PUT -> contractAssetService.updateContractAsset(contractActorAsset, acknowledgment);
                case DELETE -> contractAssetService.deleteContractAsset(contractActorAsset, acknowledgment);
            }
        } catch (Exception e){
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    @KafkaListener(topics = "${spring.kafka.topic.listenTo.asset-timetable-asset-topic}", groupId = "${spring.kafka.consumer.group-id}")
    public void listenContractTimetableAssetTopic(String message, Acknowledgment acknowledgment) throws BusinessException {
        try {
            var kafkaMessage = objectMapper.readValue(message, KafkaMessage.class);
            var timetableAssetDto = modelMapper.map(kafkaMessage.getData(), TimetableAssetStreamDto.class);
            var timetableAsset = modelMapper.map(timetableAssetDto, TimetableAsset.class);

            switch (kafkaMessage.getOperation()) {
                case POST -> contractTimetableAssetService.createTimetableAsset(timetableAsset, acknowledgment);
                case PUT -> contractTimetableAssetService.modifyTimetableAsset(timetableAsset, acknowledgment);
                case DELETE -> contractTimetableAssetService.deleteTimetableAsset(timetableAsset, acknowledgment);
            }
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), "ContractListener");
        }
    }
}
