package com.datatricks.contracts.exception.handler;

import com.datatricks.contracts.exception.ConflictException;
import com.datatricks.contracts.model.dto.ConflictDto;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class ConflictExceptionHandler {
    @Value("${api.response.activateDebugInfo}")
    private boolean isDebugActive;

    @ApiResponse(responseCode = "409", description = "Conflict",
            content = @Content(schema = @Schema(implementation = ConflictDto.class)))
    @ExceptionHandler(ConflictException.class)
    public ResponseEntity<TechnicalError> handleConflict(ConflictException ex) {
        TechnicalError technicalErrorResponse = new TechnicalError(ex.getCode());
        technicalErrorResponse.setMessage(ex.getMessage());
        if (isDebugActive) {
            technicalErrorResponse.getTechnicalDetail().setDetail(ex.getDetails());
            technicalErrorResponse.getTechnicalDetail().setSource(ex.getModuleName());
        }
        return ResponseEntity.status(HttpStatus.CONFLICT).body(technicalErrorResponse);
    }
}