package com.datatricks.contracts.repository;

import com.datatricks.contracts.model.Rental;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface RentalRepository extends JpaRepository<Rental, Long>, JpaSpecificationExecutor<Rental> {

    List<Rental> findByContractActorIdIdAndDeletedAtIsNull(Long contractActorId);

    List<Rental> findByContractActorIdIdInAndDeletedAtIsNull(List<Long> contractActorIds);

    Rental findByContractActorIdIdAndContractActorIdDeletedAtIsNullAndIdAndDeletedAtIsNull(Long contractActorId, Long id);

    Optional<Rental> findByIdAndDeletedAtIsNull(Long rentalId);

	Rental findByTimetableIdIdAndDeletedAtIsNull(Long timetableId);

    Optional<Rental> findFirstByContractActorIdIdAndDeletedAtIsNull(Long contractActorId);

    boolean existsByIdAndDeletedAtIsNull(Long rentalId);

    boolean existsByContractActorIdIdAndDeletedAtIsNull(Long contractActorId);
    
}
