package com.datatricks.contracts.repository;

import com.datatricks.contracts.model.LeaseTransfer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface LeaseTransferRepository extends JpaRepository<LeaseTransfer, Long>, JpaSpecificationExecutor<LeaseTransfer> {
    Optional<LeaseTransfer> findByContractReferenceAndDeletedAtIsNull(String contractReference);
}
