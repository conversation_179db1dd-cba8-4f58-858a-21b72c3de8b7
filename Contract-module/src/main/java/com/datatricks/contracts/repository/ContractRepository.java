package com.datatricks.contracts.repository;

import com.datatricks.contracts.enums.ContractState;
import com.datatricks.contracts.model.Contract;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface ContractRepository extends JpaRepository<Contract, Long>, JpaSpecificationExecutor<Contract> {

    Optional<Contract> findByIdAndDeletedAtIsNull(Long id);

    boolean existsByIdAndDeletedAtIsNull(Long id);

    Optional<Contract> findByReferenceAndDeletedAtIsNull(String contractReference);

    Optional<Contract> findByReference(String contractReference);
    List<Contract> findByIdInAndDeletedAtIsNull(List<Long> ids);

    List<Contract> findByStatusIn(List<ContractState> statuses);
}
