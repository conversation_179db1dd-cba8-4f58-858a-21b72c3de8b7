package com.datatricks.contracts.repository;

import com.datatricks.contracts.model.Accessory;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface AccessoryRepository extends JpaRepository<Accessory, Long>, JpaSpecificationExecutor<Accessory> {

    List<Accessory> findByContractActorIdIdAndDeletedAtIsNull(Long contractActorId);

    List<Accessory> findByContractActorIdIdInAndDeletedAtIsNull(List<Long> contractActorIds);

    Accessory findByContractActorIdIdAndContractActorIdDeletedAtIsNullAndIdAndDeletedAtIsNull(Long contractActorId, Long id);

	Accessory findByTimetableIdIdAndDeletedAtIsNull(Long timetableId);

    Optional<Accessory> findByIdAndDeletedAtIsNull(Long rentalId);
}
