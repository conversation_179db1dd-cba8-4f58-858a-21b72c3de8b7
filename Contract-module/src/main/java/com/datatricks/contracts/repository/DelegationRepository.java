package com.datatricks.contracts.repository;

import com.datatricks.contracts.model.Delegation;
import jakarta.validation.constraints.NotBlank;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface DelegationRepository extends JpaRepository<Delegation, Long> {
    Optional<Delegation> findByCode(String code);

    void deleteByCode(@NotBlank(message = "contract_actor.delegation.code:please provide a code") String delegation);
}
