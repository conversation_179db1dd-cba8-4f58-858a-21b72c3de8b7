package com.datatricks.contracts.repository;

import com.datatricks.contracts.model.ContractActorPayments;
import com.datatricks.contracts.model.ContractActorPaymentsType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface ContractActorPaymentsRepository
        extends JpaRepository<ContractActorPayments, Long>, JpaSpecificationExecutor<ContractActorPayments> {

    List<ContractActorPayments> findByContractActorId(Long id);

    List<ContractActorPayments> findByContractActorIdAndDeletedAtIsNull(Long contractId);

    Optional<ContractActorPayments> findByIdAndDeletedAtIsNull(Long id);
    Optional<ContractActorPayments> findByContractActorIdAndTypeAndDeletedAtIsNull(
            Long contractId, ContractActorPaymentsType type);
}
