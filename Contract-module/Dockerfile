FROM public.ecr.aws/docker/library/maven:3-amazoncorretto-21 as builder

# Set the working directory
WORKDIR /app

# Define build arguments
ARG CI_PROJECT_ID
ARG CI_API_V4_URL
ARG CI_JOB_TOKEN
ARG TOKEN_TYPE="Job-Token"

# Copy the Maven project files
COPY pom.xml ./
COPY src ./src

# Create the .m2 directory and add the settings.xml file
RUN mkdir -p /root/.m2 && \
    cat <<EOF > /root/.m2/settings.xml
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 https://maven.apache.org/xsd/settings-1.0.0.xsd">
  <servers>
    <server>
      <id>gitlab-maven</id>
      <configuration>
        <httpHeaders>
          <property>
            <name>${TOKEN_TYPE}</name>
            <value>${CI_JOB_TOKEN}</value>
          </property>
        </httpHeaders>
      </configuration>
    </server>
  </servers>
</settings>
EOF

# Build and deploy the project
RUN mvn package

# Use Eclipse Temurin JRE Alpine image for a lightweight runtime
FROM public.ecr.aws/docker/library/eclipse-temurin:21-jre-alpine AS runtime
# Install Infisical
RUN apk add --no-cache bash curl && curl -1sLf \
'https://dl.cloudsmith.io/public/infisical/infisical-cli/setup.alpine.sh' | bash \
&& apk add infisical
# Set working directory
WORKDIR /app
# Copy the Infisical script
COPY infisical.sh .
RUN chmod +x infisical.sh
# Copy the built application JAR file from the builder stage
COPY --from=builder /app/target/*.jar app.jar
# Expose the application port
EXPOSE 8802
# Run the application
CMD ["java", "-jar", "app.jar"]
