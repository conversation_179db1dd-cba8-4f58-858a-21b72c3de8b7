<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="WP-3759-1" author="HoussemMoussa">
        <sql>
            insert into dt_products (id, code, label)
                select 71, 'CES-MEA', 'Assignment with billing mandate'
                where not exists (select 1 from dt_products where code = 'CES-MEA');
            insert into dt_products (id, code, label)
                select 72, 'CES-MFI', 'Assignment with financial mandate'
                where not exists (select 1 from dt_products where code = 'CES-MFI');
            insert into dt_products (id, code, label)
                select 73, 'CES-SIM', 'Simple assignment'
                where not exists (select 1 from dt_products where code = 'CES-SIM');
            insert into dt_products (id, code, label)
                select 74, 'LCD-FPR', 'Short-term rental own funds'
                where not exists (select 1 from dt_products where code = 'LCD-FPR');
        </sql>
    </changeSet>
</databaseChangeLog>