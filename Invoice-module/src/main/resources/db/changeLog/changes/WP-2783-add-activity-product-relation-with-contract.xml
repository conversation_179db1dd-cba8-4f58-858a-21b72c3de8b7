<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
                   http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd
                   http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="WP-2783-relation-1" author="Houssem">
    <addForeignKeyConstraint baseColumnNames="activity_code" baseTableName="dt_contracts"
                             constraintName="fk40b57grgr57mlm25fiqaaa" deferrable="false"
                             initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                             referencedColumnNames="code" referencedTableName="dt_activities" validate="true"/>
    </changeSet>
    <changeSet id="WP-2783-relation-2" author="Houssem">
    <addForeignKeyConstraint baseColumnNames="product_code" baseTableName="dt_contracts"
                             constraintName="fk7848rzeijz459zf5fiqaaa" deferrable="false"
                             initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                             referencedColumnNames="code" referencedTableName="dt_products" validate="true"/>
    </changeSet>
</databaseChangeLog>