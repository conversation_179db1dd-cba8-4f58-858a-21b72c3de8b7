insert into dt_milestones (id, code, label, language)
values  (226, 'TRANS', 'Contrat encours de transfert', null),
        (1, 'ALIPEND', 'Procédure de validation en cours', null),
        (2, 'DWPCHK', 'Downpayment paye par le client', null),
        (3, 'STUDY', 'Acteur dupliqué', null),
        (4, 'TTSB', 'Transfer to special bucket', null),
        (5, 'EXTEND', 'Extended', null),
        (6, 'COTR', 'Collector transfer requested', null),
        (7, 'CTER', 'Client Center transfer rejected', null),
        (8, 'CTEA', 'Client Center transfer accepted', null),
        (9, 'CTRE', 'Center transfer rejected', null),
        (10, 'CTAC', 'Center transfer accepted', null),
        (11, 'CTTR', 'Center transfer requested', null),
        (12, 'WOTR', 'Workqueue transfer requesetd', null),
        (13, 'WORE', 'Workqueue transfer rejected', null),
        (14, 'PROLONG', 'Validité prolongée', null),
        (15, 'AUDITED', 'Auditer', null),
        (16, 'CONANN', 'Congé annulé', null),
        (17, 'CONENR', 'Congé enregistré', null),
        (18, 'FEDVAL', 'Biens fédéres validés', null),
        (19, 'CAPP', 'Risque approuvé', null),
        (20, 'ANNUL', 'Annulé', null),
        (21, 'NOVA', 'Novation', null),
        (22, 'RENFIN', 'Contrat ré-echelonné', null),
        (23, 'VT', 'Vente totale', null),
        (24, 'VA', 'Vente anticipée', null),
        (25, 'ECLATE', 'Immobilisation éclatée', null),
        (26, 'MOD', 'Modéle', null),
        (27, 'AVP', 'Avoir partiel', null),
        (28, 'AVT', 'Avoir total', null),
        (29, 'ERREUR', 'Mise en exploitation annulée aprés erreur', null),
        (30, 'FIN', 'Fin normale', null),
        (31, 'CONREN', 'Congé avec offre de renouvellement', null),
        (32, 'OFFER', 'Offre préparée', null),
        (33, 'DATREPO', 'Signature reportée', null),
        (34, 'REACTIV', 'Ré-activée', null),
        (35, 'STOCK', 'Immobilisation TNL', null),
        (36, 'WRITOFF', 'Perte sur créances', null),
        (37, 'ERREC', 'Annulation de la phase réalisation suite é erreur', null),
        (38, 'ARRETPR', 'Arrét provisoire sur réalisé', null),
        (39, 'TERDECH', 'Déchu - Résilié', null),
        (40, 'CTNP', 'Cession totale par duplication', null),
        (41, 'APURDEC', 'Apurement déchu', null),
        (42, 'PLFI', 'Passage en compte loyers et frais impayés', null),
        (43, 'STOP', 'Code darrét', null),
        (44, 'AJOUBIM', 'Ajout dun bien fédéré', null),
        (45, 'TRF', 'Transféré', null),
        (46, 'NEGO', 'Désaccord du prospect suite é proposition', null),
        (47, 'SIGNE', 'Contrat signé par le Client', null),
        (48, 'NOTIFIE', 'Accord du comité notifié au prospect', null),
        (49, 'REPRISE', 'Reprise', null),
        (50, 'PASSAGE', 'Dossier prét a passer au comité dengagement', null),
        (51, 'REFCOM', 'Refus du financement par le comité', null),
        (52, 'ETUDE', 'Offre faite au client', null),
        (53, 'ACCORD', 'Retour de la notification signée du prospect', null),
        (54, 'AJOURNE', 'Accord de financement ajourné', null),
        (55, 'ES', 'En service', null),
        (56, 'EC', 'En cours', null),
        (57, 'ENG', 'Engagement synallagmatique des parties', null),
        (58, 'IMPORT', 'Importé', null),
        (59, 'MIGRE', 'Migration automatique', null),
        (60, 'REFIN', 'Immobilisation refinancée', null),
        (61, 'SSUITE', 'Proposition sans suite', null),
        (62, 'RESILIE', 'Auto-Résiliation anticipée', null),
        (63, 'REBUTE', 'Mise au rebut', null),
        (64, 'OUVERT', 'Nouveau', null),
        (65, 'PROPAL', 'Proposition transmise au prospect', null),
        (66, 'FUSION', 'Dissous par fusion / absorbtion', null),
        (67, 'LIQJUD', 'Liquidation judiciaire', null),
        (68, 'DECES', 'Décédé', null),
        (69, 'RACTOT', 'Rachat anticipé total du bien financé', null),
        (70, 'RESJUG', 'Clause résolutoire jugée', null),
        (71, 'RESACQ', 'Clause résolutoire acquise', null),
        (72, 'FINNL', 'Fin normale sans levée option achat finale', null),
        (73, 'FINL', 'Fin normale avec levée option achat finale', null),
        (74, 'CESST', 'Cession é agence de recouvrement', null),
        (75, 'CESSP', 'Contrat cédé partiellement', null),
        (76, 'PRORO', 'Contrat prorogé', null),
        (77, 'RENEGO', 'Contrat renégocié', null),
        (78, 'INBONI', 'In Boni', null),
        (79, 'ARRET', 'Arrété définitif', null),
        (80, 'RESOLU', 'Exercice de la clause resolutoire par le bailleur', null),
        (81, 'CONT', 'Contrat en contentieux', null),
        (82, 'DOUTEUX', 'Douteux', null),
        (83, 'TRFLS', 'Immobilisation transferée pour exploitation en LS', null),
        (84, 'TRFCB', 'Immobilisation transferée pour exploitation en CB', null),
        (85, 'TRFIMMP', 'Immobilisation transferée pour exploitation propre', null),
        (86, 'ITNL', 'Immobilisation temporairement non louée', null),
        (87, 'NOTSSUI', 'Notification sans suite', null),
        (88, 'REFUS', 'Refus du prospect sur lettre accord transmise', null),
        (89, 'OKPROP', 'Accord du prospect sur proposition transmise', null),
        (90, 'RESERVE', 'Dossier accepté avec réserves par le comité', null),
        (91, 'OK', 'En attente de validation', null),
        (92, 'MAJ', 'En cours de saisie', null),
        (93, 'ABPF', 'En attente bon é payer tresorerie', null),
        (94, 'SUSPEND', 'Facturation suspendue', null),
        (95, 'TERSSUE', 'Annulé', null),
        (96, 'TERSSUC', 'Classement sans suite avant engagement', null),
        (97, 'ABPT', 'En attente de bon é payer technique', null),
        (98, 'BPF', 'Bon é payer trésorerie', null),
        (99, 'POSIT', 'Migré dans cette phase', null),
        (100, 'ACCEPTE', 'Accord sur financement', null),
        (101, 'VP', 'Rachat partiel', null),
        (102, 'AVTVALI', 'Avenant validé', null),
        (103, 'CAS', 'Cassation', null),
        (104, 'CONSAVT', 'Constation dun avenant', null),
        (105, 'CPSVICR', 'Commandement payer sans viser clause résolutoire', null),
        (106, 'CPVICR', 'Commandement payer visant clause résolutoire', null),
        (107, 'INJP', 'Injonction de payer', null),
        (108, 'SOMP', 'Sommation de payer', null),
        (109, 'APPEL', 'Appel', null),
        (110, 'PINS', 'Premiére instance', null),
        (111, 'ERRENG', 'Passage en phase Engagement annulé aprés erreur', null),
        (112, 'FSUS', 'Fin de suspension de facturation', null),
        (113, 'FINCP', 'Fin de Contrat', null),
        (114, 'NOUCP', 'Nouveau Contrat', null),
        (115, 'SIGNFIX', 'Date de signature fixée', null),
        (116, 'SIGREP', 'Signature repoussée', null),
        (117, 'GPC', 'Gestion pour compte', null),
        (118, 'ANNOCT', 'Notification doctroi de subvention annulée', null),
        (119, 'EARB', 'En Attente de Restitution du Bien', null),
        (120, 'DETPREC', 'Détention précaire pour les rubriques immobilisati', null),
        (121, 'APPERTE', 'Appel en perte final', null),
        (122, 'ARRETPB', 'Arrét provisoire sur budgeté', null),
        (123, 'CONCON', 'Congé confirmé', null),
        (124, 'CONMOD', 'Congé modifié', null),
        (125, 'INCOLL', 'En recouvrement', null),
        (126, 'PART', 'Mise en loyer partielle', null),
        (127, 'DATSIGN', 'Contrat signé par le client', null),
        (128, 'ASSETIN', 'Acquisition matériel achevée', null),
        (129, 'ASSETKO', 'Anomalie Acquisition du matériel', null),
        (130, 'ASSETWI', 'Acquisition matériel en cours', null),
        (131, 'PVT', 'Vente en cours', null),
        (132, 'FINREFI', 'REFI-Résiliation anticipée', null),
        (133, 'REFI', 'Cédé é un bailleur', null),
        (134, 'BLOCKED', 'Paiement  bloqué - Blacklist', null),
        (135, 'INI', 'Initial', null),
        (136, 'ANALYST', 'Assigné pour analyse', null),
        (137, 'D_CREE', 'Demande enregistrée', null),
        (138, 'D_DECID', 'Décision ', null),
        (139, 'D_ETUD', 'Dossier détude créé', null),
        (140, 'D_FIGER', 'Dossier gelé', null),
        (141, 'D_SSUIT', 'Demande sans suite', null),
        (142, 'D_VALID', 'Demande validée', null),
        (143, 'TRANSOK', 'OK', null),
        (144, 'TRAWAIT', 'Transfert en file dattente', null),
        (145, 'TRAKO', 'Transfert KO', null),
        (146, 'TRARUN', 'Transfert en cours', null),
        (147, 'AUDITTD', 'Audit é réaliser', null),
        (148, 'TBREW', 'A revoir', null),
        (149, 'ARRETD', 'Montant de financement arrété', null),
        (150, 'SUBR', 'Subrogation', null),
        (151, 'SIMULA', 'Simulation en cours', null),
        (152, 'SIMRA', 'Analyse Interne en cours', null),
        (153, 'SIMIRKO', 'Rejetée par Analyste', null),
        (154, 'SIMIV', 'Validation Client en cours', null),
        (155, 'SIMCRKO', 'Rejetée par Client', null),
        (156, 'SIMCV', 'Enregistrement Notarial en cours', null),
        (157, 'USESIM', 'Acceptée', null),
        (158, 'REQUEST', 'Demandée', null),
        (159, 'WAITING', 'En attente livraison', null),
        (160, 'TEMPO', 'Carte temporaire', null),
        (161, 'DELIV', 'Livré', null),
        (162, 'ACTIVE', 'Active', null),
        (163, 'EXP', 'Expiré', null),
        (164, 'LOST', 'Perdue', null),
        (165, 'STOLEN', 'Volée', null),
        (166, 'FRAUD', 'Fraude', null),
        (167, 'SEC', 'Titrisé', null),
        (168, 'FRAUDB', 'Major Fraud', null),
        (169, 'ACFC', 'Acceptance for cancellation', null),
        (170, 'ACFT', 'Acceptance for termination', null),
        (171, 'CORE', 'TR Refused by manager of next CC', null),
        (172, 'TRAC', 'TR Approved by manager of CC', null),
        (173, 'TRRE', 'TR Refused by manager of CC', null),
        (174, 'COAC', 'TR Approved by manager of next CC', null),
        (175, 'REQTER', 'Request for Termination', null),
        (176, 'REQCAN', 'Request for Cancellation', null),
        (177, 'ALOCK', 'Archive - bloqué', null),
        (178, 'CRYPTED', 'Crypté', null),
        (179, 'F_CREE', 'Case Created', null),
        (180, 'WOAC', 'Workqueue transfer accepted', null),
        (181, 'TRR', 'Transfer request rejected', null),
        (182, 'TTDB', 'Transfer to default bucket', null),
        (183, 'CTRR', 'Cancel transfer request rejected', null),
        (184, 'RWCA', 'Request for Charge-off', null),
        (185, 'COWCA', 'Charge-off confirmation', null),
        (186, 'CAWCA', 'Charge-off cancellation', null),
        (187, 'SUCCED', 'Contract succeeded', null),
        (188, 'F_CLOSE', 'Case Closed', null),
        (189, 'FSEC', 'rachat aprés titrisation', null),
        (190, 'CHOFF', 'Charge Off Confirmed', null),
        (191, 'REOPEN', 'Reopen Case', null),
        (192, 'F_SUITE', 'Canceled', null),
        (193, 'SELL', 'Bond sold', null),
        (194, 'REGDEP', 'Enregistrement du dépét', null),
        (195, 'WAITIN', 'Infos internes attendues', null),
        (196, 'WAITOUT', 'Infos externes attendues', null),
        (197, 'WAITCLI', 'Infos client attendues', null),
        (198, 'FORMA', 'Simulation en cours', null),
        (199, 'VALID', 'Terminated', null),
        (200, 'DNC', 'Douteux non compromis', null),
        (201, 'SAIN', 'Sain', null),
        (202, 'PDR', 'Promesse de paiement', null),
        (203, 'CABEXT', 'Cabinet externe', null),
        (204, 'ATTREP', 'Attente retour partenaire', null),
        (205, 'RESILA', 'Résiliation anticipée', null),
        (206, 'LITIGE', 'Litige', null),
        (207, 'RESIL', 'Résiliation é terme', null),
        (208, 'PRECONT', 'Précontentieux', null),
        (209, 'CONTENT', 'Contentieux', null),
        (210, 'JURID', 'Juridique', null),
        (211, 'TER', 'Terminé', null),
        (212, 'INC', 'Incomplet', null),
        (213, 'ASUR', 'A surveiller', null),
        (214, 'PDP', 'Promesse de paiement', null),
        (215, 'PRC', 'Procedure collective', null),
        (216, 'ATTRES', 'En attente de restitution', null),
        (217, 'BIENRES', 'Bien restitué', null),
        (218, 'DENONC1', 'Dénonciation N+1', null),
        (219, 'LIT', 'Litigation', null),
        (220, 'WIT', 'Write Off', null),
        (221, 'INR', 'INR', null),
        (222, 'FINAER', 'Fin normale avec ER', null),
        (223, 'FINSER', 'Fin normale sans ER', null),
        (224, 'PPERTE', 'Passage en perte', null),
        (225, 'WAFBIL', 'en attente de facturation', null);