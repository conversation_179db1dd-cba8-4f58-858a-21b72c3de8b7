spring:
  application:
    name: InvoiceService
  services:
    actor:
      url: http://actor:8802/api/v1/
    contract:
      url: http://contract:8803/api/v1/
  datasource:
    driver-class-name: org.postgresql.Driver
    hikari:
      maxLifetime: 270000
  jpa:
    hibernate:
      naming:
        physical-strategy: com.datatricks.invoices.config.CustomHibernateNamingStrategy
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  kafka:
    listener:
      ack-mode: MANUAL
    consumer:
      enable-auto-commit: false
      group-id: invoice-group
      auto-offset-reset: earliest
      key-serializer: org.apache.kafka.common.serialization.StringDeserializer
      value-serializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        spring:
          json:
            trusted:
              packages: "*"
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      transaction-id-prefix: "tx-invoice-producer-"
      properties:
        spring:
          json:
            add-type-info: true
    topic:
      produceTo:
        invoice-topic: invoice-topic
        invoice-contract-timetable-items-topic: invoice-contract-timetable-items-topic
      listenTo:
        actor-topic: actor-topic
        actor-address-topic: actor-address-topic
        actor-role-topic: actor-role-topic
        actor-bank-account-topic: actor-bank-account-topic
        actor-contact-topic: actor-contact-topic
        actor-communication-mean-topic: actor-communication-mean-topic
        actor-settlement-means-topic: actor-settlement-means-topic
        contract-topic: contract-topic
        batch-invoice-topic: batch-invoice-topic
        static-tables-topic: static-tables-topic
        asset-element-topic: asset-element-topic
      replicationFactor: 2
      numPartitions: 1
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.xml
    enabled: false
  data:
    redis:
      cluster:
        nodes:
          - ${REDIS_CLUSTER_HOST}:${REDIS_CLUSTER_PORT} 
  redis:
    ttl: 1     # time to live in days
api:
  response:
    activateDebugInfo: true
server:
  port: 8804
  tomcat:
    additional-tld-skip-patterns: "*.tld"
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
# local profile configuration
---
spring:
  services:
    actor:
      url: http://localhost:8802/api/v1/
    engine:
      url: http://localhost:8806/api/v1/
    asset:
      url: http://localhost:8805/api/v1/
    contract:
      url: http://localhost:8803/api/v1/ 
  config:
    activate:
      on-profile: local
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      transaction-id-prefix: "tx-invoice-producer-"
  datasource:
    url: *******************************************
    username: postgres
    password: U6GjpKQpsrwjZI
  jpa:
    hibernate:
      ddl-auto: validate
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  liquibase:
    change-log: classpath:db/changeLog/db.changelog-master.xml
    enabled: true
logging:
  level:
    root: info
redis:
    prefix: local


springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
# docker local profile configuration
---
spring:
  kafka:
    bootstrap-servers: broker:9092
    producer:
      transaction-id-prefix: "tx-invoice-producer-"
  config:
    activate:
      on-profile: docker-local
  datasource:
    url: ******************************************
    username: postgres
    password: U6GjpKQpsrwjZI
  jpa:
    hibernate:
      ddl-auto: validate
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  liquibase:
    change-log: classpath:db/changeLog/db.changelog-master.xml
    enabled: true
  data:
    redis:
      cluster:
        nodes:
          - ${REDIS_CLUSTER_HOST}:${REDIS_CLUSTER_PORT}
logging:
  level:
    root: INFO
redis:
  prefix: docker-local

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
# DEV profile configuration
---
spring:
  config:
    activate:
      on-profile: dev
  kafka:
    bootstrap-servers: ${KAFKA_HOST}:${KAFKA_PORT}
    producer:
      transaction-id-prefix: "dev-tx-invoice-producer-"
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?prepareThreshold=0
    username: ${DB_USER}
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      ddl-auto: validate
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  liquibase:
    change-log: classpath:db/changeLog/db.changelog-master.xml
    enabled: true
logging:
  level:
    root: DEBUG          # Enable debug logging for the whole application
    org.apache.kafka: INFO  # Set Kafka's logging to INFO (or WARN)
redis:
    prefix: dev

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
# TEST profile configuration
---
spring:
  config:
    activate:
      on-profile: test
  kafka:
    bootstrap-servers: ${KAFKA_HOST}:${KAFKA_PORT}
    producer:
      transaction-id-prefix: "test-tx-invoice-producer-"
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?prepareThreshold=0
    username: ${DB_USER}
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      ddl-auto: none
    open-in-view: false
    show-sql: false
    properties:
      hibernate:
        format_sql: true
  liquibase:
    change-log: classpath:db/changeLog/db.changelog-master.xml
    enabled: true
logging:
  level:
    root: INFO
redis:
    prefix: test


# STAGING profile configuration
---
spring:
  config:
    activate:
      on-profile: staging
  kafka:
    bootstrap-servers: ${KAFKA_HOST}:${KAFKA_PORT}
    producer:
      transaction-id-prefix: "staging-tx-invoice-producer-"
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?prepareThreshold=0
    username: ${DB_USER}
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      ddl-auto: validate
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  liquibase:
    change-log: classpath:db/changeLog/db.changelog-master.xml
    enabled: true
logging:
  level:
    root: INFO
redis:
    prefix: staging
