package com.datatricks.invoices.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import java.util.List;
import java.util.Set;

import lombok.*;
import com.datatricks.invoices.utils.DateUtils;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "actors")
public class Actor extends BaseEntity {

    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @Column(name = "short_name")
    @JsonProperty("short_name")
    private String shortName;

    @Column(name = "name")
    @JsonProperty("name")
    private String name;

    @Column(name = "type")
    @JsonProperty("type")
    @Enumerated(EnumType.STRING)
    private ActorTypes type;

    @Column(name = "national_identity", unique = true)
    @JsonProperty("national_identity")
    @NotBlank(message = "national_identity:please provide a national identity")
    private String nationalIdentity;

    @OneToMany(mappedBy = "actorId", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<ActorRole> actorRole;

    @OneToMany(mappedBy = "actorId", fetch = FetchType.LAZY)
    private Set<BankAccount> bankAccounts;

    @OneToMany(mappedBy = "actorId", fetch = FetchType.LAZY)
    private Set<Address> addresses;

    @OneToMany(mappedBy = "client")
    private List<Invoice> client;

    @OneToMany(mappedBy = "provider")
    private List<Invoice> provider;

    public Actor(Long id) {
        this.id = id;
    }
}
