package com.datatricks.invoices.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "addresses")
public class Address extends BaseEntity {

    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", unique = true)
    @JsonProperty("reference")
    private String reference;

    @Column(name = "summary")
    @JsonProperty("summary")
    private String summary;

    @Column(name = "is_billing")
    @JsonProperty("is_billing")
    private Boolean isBilling;

    @Column(name = "is_delivery")
    @JsonProperty("is_delivery")
    private Boolean isDelivery;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actorId")
    private Actor actorId;

    public Address(Long id) {
        // For database insertion
        this.id = id;
    }
}
