package com.datatricks.invoices.model;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "natures")
public class Nature extends BaseEntity {
    @Id
    private Long id;
    private String code;
    private String label;

    public Nature(String natureCode) {
        this.code = natureCode;
    }
}
