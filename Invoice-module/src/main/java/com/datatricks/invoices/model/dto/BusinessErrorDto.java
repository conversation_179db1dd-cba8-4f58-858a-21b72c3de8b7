package com.datatricks.invoices.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
@Getter
@Setter
public class BusinessErrorDto {
    @JsonProperty("details")
    @Schema(description = "Business details of the error")
    private List<BusinessDetailsDto> businessDetails;

    @JsonProperty("errorCode")
    @Schema(description = "Error code", example = "INVALID_REQUEST")
    private String code;

    @JsonProperty("message")
    @Schema(description = "Error message", example = "Invalid request")
    private String message;
}
