package com.datatricks.invoices.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "products")
public class Product {
    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "label")
    @JsonProperty("label")
    @NotBlank(message = "please provide a product label")
    private String label;

    @Column(name = "code")
    @JsonProperty("code")
    @NotBlank(message = "please provide a product code")
    private String code;

    @ManyToOne
    @JoinColumn(name = "activity_code", referencedColumnName = "code")
    private Activity activity;

    @Column(name = "active")
    @JsonProperty("active")
    private Boolean active;

    public Product(Long productId) {
        this.id = productId;
    }

    public Product(Long id, String label, String code, String activityCode, Boolean active) {
        this.id = id;
        this.label = label;
        this.code = code;
        this.activity = new Activity(activityCode);
        this.active = active;
    }

    public Product(String productCode) {
        this.code = productCode;
    }
}
