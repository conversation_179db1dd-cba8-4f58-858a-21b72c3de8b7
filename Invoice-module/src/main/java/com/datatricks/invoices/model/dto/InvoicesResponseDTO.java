package com.datatricks.invoices.model.dto;

import com.datatricks.invoices.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
public class InvoicesResponseDTO implements PageableDto {
    private Long id;
    @Schema(description = "Reference of invoice", example = "IVC_632519832")
    private String reference;
    @Schema(description = "Type of invoice", example = "Separate or Not Separate")
    private String type;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("invoice_date")
    @Schema(description = "Date of invoice", example = "2021-12-31")
    private LocalDate invoiceDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("due_date")
    @Schema(description = "Due date of invoice", example = "2021-12-31")
    private LocalDate dueDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @JsonProperty("limit_date")
    @Schema(description = "Limit date of invoice", example = "2021-12-31")
    private LocalDate limitDate;
    @JsonProperty("client_address")
    @Schema(description = "Client address ", example = "1")
    private AddressDto clientAddressId;
    @JsonProperty("provider_address")
    @Schema(description = "Provider address", example = "2")
    private AddressDto providerAddressId;
    @Schema(description = "Status of invoice", example = "PAID or UNPAID")
    private String status;
    @JsonProperty("credit_reference")
    @Schema(description = "Credit reference of invoice", example = "CR_632519832")
    private String creditReference;
    @Schema(description = "Client of invoice")
    private ActorDto client;
    @Schema(description = "Provider of invoice")
    private ActorCompanyDto provider;
    @Schema(description = "Contract of invoice")
    private ContractResponseDto contract;
    @Schema(description = "Invoice items")
    @JsonProperty("invoice_items")
    private List<InvoiceItemsResponseDTO> invoiceItems;

}
