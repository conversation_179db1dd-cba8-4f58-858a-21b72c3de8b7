package com.datatricks.invoices.model;

import com.datatricks.invoices.model.dto.ExpenseDto;
import com.datatricks.invoices.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;
import java.util.List;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "expenses", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"reference"})
})
public class Expense extends BaseEntity {

    @Id
    @GeneratedValue
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference")
    @JsonProperty("reference")
    @NotNull(message = "please provide a reference")
    private String reference;

    @Column(name = "type")
    @JsonProperty("type")
    @NotNull(message = "please provide an invoice type")
    private String type;

    @Column(name = "invoice_date")
    @JsonProperty("invoice_date")
    @NotNull(message = "please provide an invoice date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate invoiceDate;

    @Column(name = "due_date")
    @JsonProperty("due_date")
    @NotNull(message = "please provide a due date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate dueDate;

    @Column(name = "limit_date")
    @JsonProperty("limit_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate limitDate;

    @Column(name = "status")
    @JsonProperty("status")
    @Enumerated(EnumType.STRING)
    private InvoiceStatusEnum status;

    @Column(name = "credit_reference")
    @JsonProperty("credit_reference")
    private String creditReference;

    @Column(name = "is_draft")
    @NotNull(message = "please provide a draft status")
    @JsonProperty("is_draft")
    private boolean isDraft;

    @Column(name = "comments")
    @JsonProperty("comments")
    private String comments;

    /*
     * Foreign keys columns
     */
    @OneToOne(mappedBy = "expense", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JoinColumn(name = "provider_expense_payments_id")
    private ProviderExpensePayments provider_expense_payments;

    @OneToMany(mappedBy = "expense", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    private List<ClientExpensePayments> client_expense_payments;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_id")
    private Actor client;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "provider_id")
    private Actor provider;

    @OneToMany(mappedBy = "expense", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private List<ExpenseItems> expenseItems;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_address_id")
    private Address clientAddress;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "provider_address_id")
    private Address providerAddress;

    //@OneToOne(fetch = FetchType.LAZY)
    //@JoinColumn(name = "timeTable_item_id")
    //private TimeTableItems timeTableItemId;

    public Expense(Long id, ExpenseDto expenseDto, Address clientAddress) {
        this.id = id;
        this.type = expenseDto.getType();
        this.invoiceDate = expenseDto.getInvoiceDate();
        this.dueDate = expenseDto.getDueDate();
        this.limitDate = expenseDto.getLimitDate();
        this.clientAddress = clientAddress;
        this.providerAddress = expenseDto.getProviderAddressId() != null ? new Address(expenseDto.getProviderAddressId()) : null;
        this.status = expenseDto.getStatus();
        this.creditReference = expenseDto.getCreditReference();
        this.comments = expenseDto.getComments();
        this.client = new Actor(expenseDto.getClientId());
        this.provider = new Actor(expenseDto.getProviderId());
    }

    public Expense(ExpenseDto expenseDto, Address clientAddress) {
        this.type = expenseDto.getType();
        this.invoiceDate = expenseDto.getInvoiceDate();
        this.dueDate = expenseDto.getDueDate();
        this.limitDate = expenseDto.getLimitDate();
        this.clientAddress = clientAddress;
        this.providerAddress = expenseDto.getProviderAddressId() != null ? new Address(expenseDto.getProviderAddressId()) : null;
        this.status = expenseDto.getStatus();
        this.creditReference = expenseDto.getCreditReference();
        this.comments = expenseDto.getComments();
        this.isDraft = expenseDto.isDraft();
    }
}
