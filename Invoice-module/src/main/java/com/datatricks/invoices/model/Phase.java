package com.datatricks.invoices.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "phases")
@Getter
@Setter
public class Phase {
    @Id
    private Long id;

    @Column(name = "code")
    private String code;

    @Column(name = "label")
    private String label;

    @Column(name = "associated_to")
    private String associatedTo;
}
