package com.datatricks.invoices.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "communication_means")
public class CommunicationMean extends BaseEntity {

    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "type")
    @JsonProperty("type")
    @NotNull(message = "type:please provide a type")
    @Enumerated(EnumType.STRING)
    private CommunicationMeanTypes type;

    @Column(name = "reference")
    @JsonProperty("reference")
    @NotBlank(message = "reference:please provide a reference")
    private String reference;

    @Column(name = "preferred")
    @JsonProperty("preferred")
    private Boolean preferred;

    @ManyToOne
    @JoinColumn(name = "contactId")
    private Contact contactId;
}
