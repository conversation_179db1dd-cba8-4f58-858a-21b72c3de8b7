package com.datatricks.invoices.model.dto;
import java.util.Date;
import lombok.*;

@Getter
@Setter
public class InvoiceItemDto {

    private Long id;
    private String reference;
    private String comment;
    private String description;
    private Integer allocationId;
    private Double taxRate;
    private Double tax;
    private Long contractId;
    private String creditReference;
    private Double itemUnit;
    private Double itemAmount;
    private Date createdAt;
    private Date modifiedAt;
    private Date deletedAt;
}
