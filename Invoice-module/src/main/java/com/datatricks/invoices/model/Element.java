package com.datatricks.invoices.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "elements")
public class Element extends BaseEntity {

    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "serial_number", unique = true)
    @JsonProperty("serial_number")
    private String serialNumber;

    @Column(name = "label")
    @JsonProperty("label")
    private String label;

    @Column(name = "tax_rate")
    @JsonProperty("tax_rate")
    private Double taxRate;

    @Column(name = "order_number")
    @JsonProperty("order_number")
    private long orderNumber;

    @Column(name = "acquisition_value_HT")
    @JsonProperty("acquisition_value_HT")
    private BigDecimal acquisitionValueHt;

    @Column(name = "description")
    @JsonProperty("description")
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "phase_id")
    @JsonProperty("phase")
    private Phase phase;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "milestone_id")
    @JsonProperty("milestone")
    private Milestone milestone;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "currency_id")
    @JsonProperty("currency")
    private Currency currency;

    public Element(Long id) {
        this.id = id;
    }
}

