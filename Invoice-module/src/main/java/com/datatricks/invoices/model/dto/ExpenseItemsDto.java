package com.datatricks.invoices.model.dto;

import com.datatricks.invoices.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
public class ExpenseItemsDto {

    @Schema(description = "ID of expense item", example = "1")
    private Long id;

    @Schema(description = "Reference of expense item", example = "IVC_632519832")
    private String reference;

    private String type;

    @JsonProperty("allocation_code")
    @Schema(description = "Allocation code of expense item", example = "RLOCINT")
    private String allocationCode;

    @JsonProperty("tax_rate")
    @Schema(description = "Tax rate of expense item", example = "2")
    private BigDecimal taxRate;

    @JsonProperty("tax_code")
    @Schema(description = "Tax code of expense item", example = "TVA")
    private String taxCode;

    @Schema(description = "Notes of expense item", example = "note")
    private String notes;

    @Schema(description = "Description of expense item", example = "description")
    private String description;

    @JsonProperty("contract_ref")
    @NotNull(message = "contract_ref: contract_ref is required")
    @Schema(description = "Contract reference of expense item", example = "CBI-202501553CN-0")
    private String contractRef;

    @JsonProperty("credit_reference")
    @Schema(description = "Credit reference of expense item", example = "CR_632519832")
    private String creditReference;

    @JsonProperty("item_unit")
    @NotNull(message = "item_unit: item_unit is required")
    @Schema(description = "Item unit of expense item", example = "1.0")
    private Double itemUnit;

    @JsonProperty("item_amount")
    @NotNull(message = "item_amount: item_amount is required")
    @Schema(description = "Item amount of expense item", example = "1000.0")
    private BigDecimal itemAmount;

    @JsonProperty("total_amount")
    @NotNull(message = "please provide an total amount")
    @Schema(description = "Total amount of expense item", example = "1000.0")
    private BigDecimal totalAmount;

    @JsonProperty("start_period_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start period date of expense item", example = "2021-12-31")
    private LocalDate startPeriodDate;

    @JsonProperty("end_period_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End period date of expense item", example = "2021-12-31")
    private LocalDate endPeriodDate;

    @JsonProperty("element_id")
    @Schema(description = "Element id of expense item", example = "1")
    private Long elementId;
}
