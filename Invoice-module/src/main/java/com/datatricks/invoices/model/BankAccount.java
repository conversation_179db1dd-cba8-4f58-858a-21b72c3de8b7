package com.datatricks.invoices.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "bank_accounts")
public class BankAccount extends BaseEntity {

    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "iban")
    @JsonProperty("iban")
    @NotBlank(message = "iban:please provide an iban")
    private String iban;

	@Column(name = "is_principal")
	@JsonProperty("is_principal")
	private Boolean isPrincipal;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actorId")
    private Actor actorId;

    public BankAccount(Long id) {
        this.id = id;
    }
}
