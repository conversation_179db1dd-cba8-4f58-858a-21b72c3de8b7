package com.datatricks.invoices.model;

import com.datatricks.invoices.model.dto.ExpenseItemsDto;
import com.datatricks.invoices.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "expense_items")
public class ExpenseItems extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference")
    @JsonProperty("reference")
    @NotBlank(message = "please provide a reference")
    private String reference;

    @Column(name = "type")
    @JsonProperty("type")
    private String type;

    @Column(name = "allocation_code")
    @JsonProperty("allocation_code")
    private String allocationCode;

    @Column(name = "tax_rate")
    @JsonProperty("tax_rate")
    private BigDecimal taxRate;

    @Column(name = "tax_code")
    @JsonProperty("tax_code")
    private String taxCode;

    @Column(name = "notes")
    @JsonProperty("notes")
    private String notes;

    @Column(name = "description")
    @JsonProperty("description")
    private String description;

    @Column(name = "credit_reference")
    @JsonProperty("credit_reference")
    private String creditReference;

    @Column(name = "item_unit")
    @JsonProperty("item_unit")
    @NotNull(message = "please provide an item unit")
    private Double itemUnit;

    @Column(name = "item_amount")
    @JsonProperty("item_amount")
    @NotNull(message = "please provide an item amount")
    private BigDecimal itemAmount;

    @Column(name = "total_amount")
    @JsonProperty("total_amount")
    @NotNull(message = "please provide an total amount")
    private BigDecimal totalAmount;

    @Column(name = "start_period_date")
    @JsonProperty("start_period_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate startPeriodDate;

    @Column(name = "end_period_date")
    @JsonProperty("end_period_date")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate endPeriodDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "expense_id")
    private Expense expense;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "contract_reference", referencedColumnName = "reference")
    private Contract contract;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "element_id")
    private Element element;

    public ExpenseItems (ExpenseItemsDto expenseItemsDto) {
        this.type = expenseItemsDto.getType();
        this.allocationCode = expenseItemsDto.getAllocationCode();
        this.taxRate = expenseItemsDto.getTaxRate();
        this.taxCode = expenseItemsDto.getTaxCode();
        this.notes = expenseItemsDto.getNotes();
        this.description = expenseItemsDto.getDescription();
        this.creditReference = expenseItemsDto.getCreditReference();
        this.itemUnit = expenseItemsDto.getItemUnit();
        this.itemAmount = expenseItemsDto.getItemAmount();
        this.totalAmount = expenseItemsDto.getTotalAmount();
        this.startPeriodDate = expenseItemsDto.getStartPeriodDate();
        this.endPeriodDate = expenseItemsDto.getEndPeriodDate();
        this.element = expenseItemsDto.getElementId() != null ? new Element(expenseItemsDto.getElementId()) : null;
    }
}
