package com.datatricks.invoices.model.dto;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TimeTableItemsDto {
    private Long id;
    private Long totalDuration;
    private Date startDate;
    private Date endDate;
    private String occurrence;
    private String status;
}
