package com.datatricks.invoices.model.dto;

import com.datatricks.invoices.model.Invoice;
import com.datatricks.invoices.model.InvoiceStatusEnum;
import com.datatricks.invoices.model.group.ValidationGroupCollector;
import com.datatricks.invoices.model.validationLevel.*;
import com.datatricks.invoices.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PatchInvoiceDto extends ValidationGroupCollector {

    @NotBlank(message = "type: type is required", groups = {LevelOne.class})
    @Schema(description = "Type of invoice", example = "Separate or Not Separate")
    private String type;

    @JsonProperty("invoice_date")
    @NotNull(message = "invoice_date: invoice_date is required", groups = {LevelTwo.class})
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Date of invoice", example = "2021-12-31")
    private LocalDate invoiceDate;

    @JsonProperty("due_date")
    @NotNull(message = "due_date: due_date is required", groups = {LevelThree.class})
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Due date of invoice", example = "2021-12-31")
    private LocalDate dueDate;

    @JsonProperty("limit_date")
    @NotNull(message = "limit_date: limit_date is required", groups = {LevelFour.class})
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Limit date of invoice", example = "2021-12-31")
    private LocalDate limitDate;

    @JsonProperty("client_id")
    @NotNull(message = "client_id: client_id is required", groups = {LevelFive.class})
    @Schema(description = "Client id", example = "1")
    private Long clientId;

    @JsonProperty("provider_id")
    @NotNull(message = "provider_id: provider_id is required", groups = {LevelSix.class})
    @Schema(description = "Provider id", example = "2")
    private Long providerId;

    @JsonProperty("client_address_id")
    @NotNull(message = "client_address_id: client_address_id is required", groups = {LevelSeven.class})
    @Schema(description = "Client address id", example = "1")
    private Long clientAddressId;

    @JsonProperty("provider_address_id")
    @NotNull(message = "provider_address_id: provider_address_id is required", groups = {LevelEight.class})
    @Schema(description = "Provider address id", example = "2")
    private Long providerAddressId;

    @Schema(description = "Status of invoice", example = "PAID or UNPAID")
    private InvoiceStatusEnum status;

    @JsonProperty("credit_reference")
    @Schema(description = "Credit reference of invoice", example = "CR_632519832")
    private String creditReference;

    @JsonProperty("contract_id")
    @NotNull(message = "contract_id: contract_id is required", groups = {LevelNine.class})
    @Schema(description = "Contract id", example = "1")
    private Long contractId;

    @JsonProperty("contract_ref")
    @NotBlank(message = "contract_ref: contract_ref is required", groups = {LevelNine.class})
    @Schema(description = "Contract reference", example = "SOC_632519832")
    private String contractRef;

    public PatchInvoiceDto(Invoice invoice) {
        this.type = invoice.getType();
        this.invoiceDate = invoice.getInvoiceDate();
        this.dueDate = invoice.getDueDate();
        this.limitDate = invoice.getLimitDate();
        this.clientId = invoice.getClient().getId();
        this.providerId = invoice.getProvider().getId();
        this.clientAddressId = invoice.getClientAddress().getId();
        this.providerAddressId = invoice.getProviderAddress().getId();
        this.status = invoice.getStatus();
        this.creditReference = invoice.getCreditReference();
    }

    @Override
    public Set<Class<?>> getValidationGroups() {
        Set<Class<?>> groups = new HashSet<>();
        if (this.type != null) {
            groups.add(LevelOne.class);
        }
        if (this.invoiceDate != null) {
            groups.add(LevelTwo.class);
        }
        if (this.dueDate != null) {
            groups.add(LevelThree.class);
        }
        if (this.limitDate != null) {
            groups.add(LevelFour.class);
        }
        if (this.clientId != null) {
            groups.add(LevelFive.class);
        }
        if (this.providerId != null) {
            groups.add(LevelSix.class);
        }
        if (this.clientAddressId != null) {
            groups.add(LevelSeven.class);
        }
        if (this.providerAddressId != null) {
            groups.add(LevelEight.class);
        }
        if (this.contractId != null) {
            groups.add(LevelNine.class);
        }
        if (this.contractRef != null) {
            groups.add(LevelNine.class);
        }
        return groups;
    }
}

