package com.datatricks.invoices.repository;

import com.datatricks.invoices.model.Actor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;
import java.util.Set;

public interface ActorRepository extends JpaRepository<Actor, Long>, JpaSpecificationExecutor<Actor> {
    Optional<Actor> findByIdAndDeletedAtIsNull(Long actorId);
}
