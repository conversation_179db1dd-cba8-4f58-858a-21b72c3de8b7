package com.datatricks.invoices.repository;

import com.datatricks.invoices.model.Invoice;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface InvoiceRepository extends JpaRepository<Invoice, Long>, JpaSpecificationExecutor<Invoice> {
//    @Query("SELECT i FROM Invoice i WHERE i.timeTableItemId= :timetableId")
//    List<Invoice> getInvoiceByTimeTableItemId(@Param("timetableId") Long timetableId);

    Optional<Invoice> findByIdAndDeletedAtIsNull(@Param("id") Long id);
    Page<Invoice> findAllByDeletedAtIsNull(Specification<Invoice> specification, Pageable pageable);
    List<Optional<Invoice>> findByIdInAndDeletedAtIsNull(List<Long> ids);

    Optional<Invoice> findByReferenceAndDeletedAtIsNull(String reference);
}
