package com.datatricks.invoices.repository;

import com.datatricks.invoices.model.ProviderInvoicePayments;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


public interface ProviderInvoicePaymentsRepository extends JpaRepository<ProviderInvoicePayments, Long> {

    Optional<ProviderInvoicePayments> findByInvoiceIdAndIdAndDeletedAtIsNull(@Param("invoice_id") Long invoiceId, @Param("id") Long id);
    List<ProviderInvoicePayments> findAllByInvoiceIdAndDeletedAtIsNull(@Param("invoice_id") Long invoiceId);
}
