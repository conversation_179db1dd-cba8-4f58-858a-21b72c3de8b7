package com.datatricks.invoices.repository;

import com.datatricks.invoices.model.ExpenseItems;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ExpenseItemsRepository extends JpaRepository<ExpenseItems, Long> {
    Optional<ExpenseItems> findByExpenseIdAndIdAndDeletedAtIsNull(@Param("expense_id") Long expenseId, @Param("id") Long expenseItemsId);
    List<ExpenseItems> findAllByExpenseIdAndDeletedAtIsNull(@Param("expense_id") Long expenseId);
}
