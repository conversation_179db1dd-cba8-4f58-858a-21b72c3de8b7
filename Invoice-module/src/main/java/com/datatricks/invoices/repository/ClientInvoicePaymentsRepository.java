package com.datatricks.invoices.repository;

import com.datatricks.invoices.model.ClientInvoicePayments;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ClientInvoicePaymentsRepository extends JpaRepository<ClientInvoicePayments, Long> {
    Optional<ClientInvoicePayments> findByInvoiceIdAndIdAndDeletedAtIsNull(@Param("invoice_id") Long invoiceId, @Param("id") Long id);
    List<ClientInvoicePayments> findAllByInvoiceIdAndDeletedAtIsNull(@Param("invoice_id") Long invoiceId);
}
