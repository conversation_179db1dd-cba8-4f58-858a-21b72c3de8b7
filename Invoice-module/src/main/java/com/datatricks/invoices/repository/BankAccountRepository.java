package com.datatricks.invoices.repository;

import com.datatricks.invoices.model.BankAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface BankAccountRepository extends JpaRepository<BankAccount, Long>, JpaSpecificationExecutor<BankAccount> {
    Optional<BankAccount> findByIdAndDeletedAtIsNull(Long id);
}
