package com.datatricks.invoices.exception.handler;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

@Getter
public class TechnicalError implements Serializable {
    @Serial
    private static final long serialVersionUID = 645162121L;

    private static final String DEFAULT_ERROR_MESSAGE = "Internal Server Error";

    @JsonProperty("details")
    private final TechnicalDetail technicalDetail = new TechnicalDetail();

    @Setter
    @JsonProperty("errorCode")
    private String code;

    @Setter
    @JsonProperty("message")
    private String message = DEFAULT_ERROR_MESSAGE;

    public TechnicalError() {}

    public TechnicalError(String code) {
        this.code = code;
    }
    public TechnicalError(String code, String message) {
        this.code = code;
        this.message = message;
    }

}

@Getter
@Setter
@NoArgsConstructor
class TechnicalDetail implements Serializable {
    @Serial
    private static final long serialVersionUID = 64592121L;
    private String detail;
    private String source;
}
