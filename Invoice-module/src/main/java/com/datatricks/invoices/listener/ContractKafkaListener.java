package com.datatricks.invoices.listener;

import com.datatricks.invoices.exception.BusinessException;
import com.datatricks.invoices.model.Contract;
import com.datatricks.invoices.model.Expense;
import com.datatricks.invoices.model.TimetableItem;
import com.datatricks.invoices.service.*;
import com.datatricks.kafkacommondomain.model.ContractStreamDto;
import com.datatricks.kafkacommondomain.model.ExpenseStreamDto;
import com.datatricks.kafkacommondomain.model.KafkaMessage;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import org.modelmapper.ModelMapper;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ContractKafkaListener {

    private final ObjectMapper objectMapper;
    private static final String ERRORMESSAGE = "Error while processing message ";
    private final ContractService contractService;
    private static final String KAFKAERROR = "DATA-SYNC-ERROR";
    private final ModelMapper modelMapper;

    public ContractKafkaListener(ObjectMapper objectMapper,
                                 ContractService contractService,
                                 ModelMapper modelMapper) {
        this.objectMapper = objectMapper;
        this.contractService = contractService;
        this.modelMapper = modelMapper;
    }


    @KafkaListener(topics = "${spring.kafka.topic.listenTo.contract-topic}", groupId = "${spring.kafka.consumer.group-id}")
    public void listenContractTopic(String message, Acknowledgment acknowledgment) throws BusinessException {
        try {
            KafkaMessage<?> kafkaMessage = objectMapper.readValue(message, KafkaMessage.class);
            switch (kafkaMessage.getClassName()) {
                case "Contract" -> {
                    var contractDto = objectMapper.convertValue(kafkaMessage.getData(), ContractStreamDto.class);
                    var contract = modelMapper.map(contractDto, Contract.class);
                    contract.setCreatedAt(contractDto.getCreatedAt());
                    contract.setModifiedAt(contractDto.getModifiedAt());
                    contract.setDeletedAt(contractDto.getDeletedAt());
                    switch (kafkaMessage.getOperation()) {
                        case POST -> contractService.createContract(contract, acknowledgment);
                        case PUT -> contractService.updateContract(contract, acknowledgment);
                        case DELETE -> contractService.deleteContract(contract, acknowledgment);
                    }
                }
                case "TimetableItem" -> {
                    List<TimetableItem> timetableItems = objectMapper.convertValue(
                            kafkaMessage.getData(),
                            new TypeReference<List<TimetableItem>>() {});
                    switch (kafkaMessage.getOperation()) {
                        case POST -> contractService.createTimetableItem(timetableItems, acknowledgment);
                        case DELETE -> contractService.deleteTimetableItem(timetableItems, acknowledgment);
                    }
                }
                case "Expense" -> {
                    List<ExpenseStreamDto> expensesDto = objectMapper.convertValue(
                            kafkaMessage.getData(),
                            new TypeReference<List<ExpenseStreamDto>>() {
                            });
                    List<Expense> expenses = expensesDto.stream()
                            .map(expenseDto -> modelMapper.map(expenseDto, Expense.class))
                            .toList();
                    switch (kafkaMessage.getOperation()) {
                        case DELETE -> contractService.deleteExpense(expenses, acknowledgment);
                        case null, default -> System.out.println("No operation");
                    }
                }
            }
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }
}