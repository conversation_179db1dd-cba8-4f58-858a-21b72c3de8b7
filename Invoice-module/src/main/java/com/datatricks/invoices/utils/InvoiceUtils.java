package com.datatricks.invoices.utils;


import com.datatricks.invoices.exception.ConflictException;
import com.datatricks.invoices.exception.ResourcesNotFoundException;
import com.datatricks.invoices.exception.TechnicalException;
import com.datatricks.invoices.exception.UnicityViolationException;
import org.springframework.dao.DataIntegrityViolationException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class InvoiceUtils {

    private static final Random RANDOM = new Random();

    private InvoiceUtils() {
        // Utility class
    }

    public static String createReference() {
        return (int) (Math.abs(System.currentTimeMillis()) % 10000) + String.format("%05d", RANDOM.nextInt(99999));
    }

    public static RuntimeException handleException(DataIntegrityViolationException e) {
        if (e.getMessage().contains("ORA-02291")) {
            throw new ResourcesNotFoundException(e.getMessage(), "Invoice-module");
        } else if (e.getMessage().contains("ORA-00001")) {
            throw new UnicityViolationException(e.getMessage(), "Invoice-module");
        } else if (e.getMessage().contains("could not execute statement")) {
            String constraintName = extractConstraintName(e.getMessage());
            throw new ConflictException("Duplicate key value violates unique constraint: " + constraintName, "DUPLICATE_KEY_" + constraintName, e.getMessage(), "Actor");
        } else {
            throw new TechnicalException("An error occurred while processing your request", e.getMessage(), "Actor");
            }
        }

    public static String extractConstraintName(String exceptionMessage) {
        Pattern pattern = Pattern.compile("constraint \\[(.*?)\\]");
        Matcher matcher = pattern.matcher(exceptionMessage);

        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    public static BigDecimal calculateTTC(BigDecimal price, BigDecimal tva) {
        if (price == null || tva == null) {
            return null;
        }
        return price.multiply(BigDecimal.valueOf(1.0)
                        .add(tva.divide(BigDecimal.valueOf(100.0), 3, RoundingMode.UP)))
                .setScale(2, RoundingMode.UP);
    }
}
