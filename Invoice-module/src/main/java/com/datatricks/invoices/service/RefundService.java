package com.datatricks.invoices.service;
import com.datatricks.invoices.exception.handler.InformativeMessage;
import com.datatricks.invoices.model.Refund;
import com.datatricks.invoices.model.dto.PageDto;
import com.datatricks.invoices.model.dto.RefundDto;
import com.datatricks.invoices.model.dto.SingleResultDto;

import java.util.List;

public interface RefundService {
    PageDto getRefunds(Long invoiceId, Long instalmentId, Long paymentId);
    SingleResultDto getRefundsById(Long invoiceId, Long instalmentId, Long paymentId, Long id);
    SingleResultDto createRefund(Long invoiceId, Long instalmentId, Long paymentId, RefundDto refundDto);
    SingleResultDto updateRefund(Long invoiceId, Long instalmentId, Long paymentId, Long id, RefundDto refundDto);
    InformativeMessage deleteRefund(Long invoiceId, Long instalmentId, Long paymentId, Long id);
}
