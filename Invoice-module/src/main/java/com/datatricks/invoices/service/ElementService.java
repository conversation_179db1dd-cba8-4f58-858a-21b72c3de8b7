package com.datatricks.invoices.service;

import com.datatricks.invoices.exception.BusinessException;
import com.datatricks.invoices.model.Element;
import com.datatricks.invoices.repository.ElementRepository;
import com.datatricks.invoices.utils.TransactionSynchronizationUtil;
import jakarta.transaction.Transactional;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class ElementService {
    private static final String ERRORMESSAGE = "Error while processing message ";
    private final TransactionSynchronizationUtil transactionSynchronizationUtil;
    private static final String KAFKAERROR = "DATA-SYNC-ERROR";
    private final ElementRepository elementRepository;

    public ElementService(TransactionSynchronizationUtil transactionSynchronizationUtil,
                          ElementRepository elementRepository) {
        this.transactionSynchronizationUtil = transactionSynchronizationUtil;
        this.elementRepository = elementRepository;
    }

    @Transactional
    public void createElement(Element newElement, Acknowledgment acknowledgment) throws BusinessException {
        try {
            elementRepository.save(newElement);
            transactionSynchronizationUtil.executeAfterCommit(acknowledgment::acknowledge);
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    @Transactional
    public void updateElement(Element updatedElement, Acknowledgment acknowledgment) throws BusinessException {
        try {
            elementRepository.save(updatedElement);
            transactionSynchronizationUtil.executeAfterCommit(acknowledgment::acknowledge);
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }

    @Transactional
    public void deleteElement(Element deletedElement, Acknowledgment acknowledgment) throws BusinessException {
        try {
            deletedElement.setDeletedAt(new Date());
            elementRepository.save(deletedElement);
            transactionSynchronizationUtil.executeAfterCommit(acknowledgment::acknowledge);
        } catch (Exception e) {
            throw new BusinessException(ERRORMESSAGE + e.getMessage(), KAFKAERROR);
        }
    }
}