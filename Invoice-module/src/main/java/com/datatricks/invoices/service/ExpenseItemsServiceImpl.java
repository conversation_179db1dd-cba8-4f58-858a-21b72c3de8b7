package com.datatricks.invoices.service;

import com.datatricks.invoices.exception.ResourcesNotFoundException;
import com.datatricks.invoices.exception.handler.InformativeMessage;
import com.datatricks.invoices.model.Contract;
import com.datatricks.invoices.model.Element;
import com.datatricks.invoices.model.Expense;
import com.datatricks.invoices.model.ExpenseItems;
import com.datatricks.invoices.model.dto.*;
import com.datatricks.invoices.repository.ContractRepository;
import com.datatricks.invoices.repository.ElementRepository;
import com.datatricks.invoices.repository.ExpenseItemsRepository;
import com.datatricks.invoices.repository.ExpenseRepository;
import com.datatricks.kafkacommondomain.model.ExpenseItemsResponseDto;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import static com.datatricks.invoices.utils.InvoiceUtils.createReference;
import com.datatricks.kafkacommondomain.model.SingleResultDto;
import com.datatricks.kafkacommondomain.model.PageDto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Service
public class ExpenseItemsServiceImpl {

    private final ExpenseItemsRepository expenseItemsRepository;
    private final ExpenseRepository expenseRepository;
    private final ContractRepository contractRepository;
    private final ModelMapper modelMapper;
    private final ElementRepository elementRepository;

    public ExpenseItemsServiceImpl(ExpenseItemsRepository expenseItemsRepository,
                                   ExpenseRepository expenseRepository,
                                   ContractRepository contractRepository,
                                   ModelMapper modelMapper, ElementRepository elementRepository) {
        this.expenseItemsRepository = expenseItemsRepository;
        this.expenseRepository = expenseRepository;
        this.contractRepository = contractRepository;
        this.modelMapper = modelMapper;
        this.elementRepository = elementRepository;
    }

    @Transactional
    public PageDto<ExpenseItemsResponseDto> getExpenseItems(Long expenseId) {
        List<ExpenseItemsResponseDto> expenseItemsResponse = expenseItemsRepository.findAllByExpenseIdAndDeletedAtIsNull(expenseId).stream()
                .map((element) -> modelMapper.map(element, ExpenseItemsResponseDto.class))
                .toList();
        return PageDto.<ExpenseItemsResponseDto>builder().total(expenseItemsResponse.size()).data(expenseItemsResponse).build();
    }

    @Transactional
    public SingleResultDto<ExpenseItemsResponseDto> getExpenseItemsById(Long expenseId, Long id) {
        ExpenseItemsResponseDto expenseItemResponse = expenseItemsRepository.findByExpenseIdAndIdAndDeletedAtIsNull(expenseId, id)
                .map((element) -> modelMapper.map(element, ExpenseItemsResponseDto.class))
                .orElseThrow(
                        () -> new ResourcesNotFoundException("The requested resource was not found", "Expense Items")
                );
        return SingleResultDto.<ExpenseItemsResponseDto>builder().data(expenseItemResponse).build();
    }

    @Transactional
    public SingleResultDto<ExpenseItemsResponseDto> createExpenseItems(Long expenseId, @Valid ExpenseItemsDto expenseItemsDto) {
        Expense expense = expenseRepository.findById(expenseId)
                .orElseThrow(
                        () -> new ResourcesNotFoundException("The requested resource was not found", "Expense Items")
                );
        Contract contract = contractRepository.findByReferenceAndDeletedAtIsNull(expenseItemsDto.getContractRef())
                .orElseThrow(
                        () -> new ResourcesNotFoundException("The requested resource was not found", "Contract")
                );
        if (expenseItemsDto.getElementId() != null) {
            elementRepository.findByIdAndDeletedAtIsNull(expenseItemsDto.getElementId())
                    .orElseThrow(
                            () -> new ResourcesNotFoundException("The requested resource was not found", "Element")
                    );
        }
        ExpenseItems expenseItems = new ExpenseItems(expenseItemsDto);
        expenseItems.setReference("EXI_" + createReference());
        expenseItems.setExpense(expense);
        expenseItems.setContract(contract);
        ExpenseItems expenseItemsResponse = expenseItemsRepository.save(expenseItems);
        return SingleResultDto.<ExpenseItemsResponseDto>builder().data(modelMapper.map(expenseItemsResponse, ExpenseItemsResponseDto.class)).build();
    }

    @Transactional
    public SingleResultDto<ExpenseItemsResponseDto> updateExpenseItems(Long expenseId, Long expenseItemsId, @Valid ExpenseItemsDto expenseItemsDto) {
        ExpenseItems expenseItems = expenseItemsRepository.findByExpenseIdAndIdAndDeletedAtIsNull(expenseId, expenseItemsId)
                .orElseThrow(
                        () -> new ResourcesNotFoundException("The requested resource was not found", "Expense Items")
                );
        Contract contract = contractRepository.findByReferenceAndDeletedAtIsNull(expenseItemsDto.getContractRef())
                .orElseThrow(
                        () -> new ResourcesNotFoundException("The requested resource was not found", "Contract")
                );
        if (expenseItemsDto.getElementId() != null) {
            elementRepository.findByIdAndDeletedAtIsNull(expenseItemsDto.getElementId())
                    .orElseThrow(
                            () -> new ResourcesNotFoundException("The requested resource was not found", "Element")
                    );
        }
        ExpenseItems newExpenseItems = new ExpenseItems(expenseItemsDto);
        newExpenseItems.setId(expenseItemsId);
        newExpenseItems.setReference(expenseItems.getReference());
        newExpenseItems.setExpense(expenseItems.getExpense());
        newExpenseItems.setCreatedAt(expenseItems.getCreatedAt());
        newExpenseItems.setContract(contract);
        ExpenseItems updatedExpenseItems = expenseItemsRepository.save(newExpenseItems);
        return SingleResultDto.<ExpenseItemsResponseDto>builder().data(modelMapper.map(updatedExpenseItems, ExpenseItemsResponseDto.class)).build();
    }

    @Transactional
    public InformativeMessage deleteExpenseItems(Long expenseId, Long id) {
        ExpenseItems expenseItems = expenseItemsRepository.findByExpenseIdAndIdAndDeletedAtIsNull(expenseId, id)
                .orElseThrow(
                        () -> new ResourcesNotFoundException("The requested resource was not found", "Expense Items")
                );
        expenseItems.setExpense(null);
        expenseItems.setDeletedAt(new Date());
        expenseItemsRepository.save(expenseItems);
        return new InformativeMessage("Resource with ID " + id + " has been deleted successfully");
    }

    @Transactional
    public boolean checkExpenseItemAmount(Long expenseId, Long expenseItemId) {
        ExpenseItems expenseItems = expenseItemsRepository.findByExpenseIdAndIdAndDeletedAtIsNull(expenseId, expenseItemId)
                .orElseThrow(() -> new ResourcesNotFoundException("The requested resource was not found", "Expense Items"));
        if (expenseItems.getElement() == null) return true;
        return expenseItems.getItemAmount().compareTo(expenseItems.getElement().getAcquisitionValueHt()) ==  0 &&
            expenseItems.getTaxRate().compareTo(BigDecimal.valueOf(expenseItems.getElement().getTaxRate())) == 0;
    }
}
