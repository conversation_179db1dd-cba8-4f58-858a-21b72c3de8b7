package com.datatricks.invoices.service;

import com.datatricks.invoices.model.Expense;
import com.datatricks.invoices.repository.ExpenseRepository;
import com.datatricks.kafkacommondomain.enums.CheckStatus;
import com.datatricks.kafkacommondomain.model.*;
import jakarta.transaction.Transactional;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CheckExpenseServiceImpl {

    private final ExpenseServiceImpl expenseService;
    private final ExpenseRepository expenseRepository;

    public CheckExpenseServiceImpl(ExpenseServiceImpl expenseService, ExpenseRepository expenseRepository) {
        this.expenseService = expenseService;
        this.expenseRepository = expenseRepository;
    }

    @Transactional
    public ResponseEntity<SingleResultDto<PageableHashMap<String, CheckResult>>> checkExpensesByContract(String contractReference) {
        List<Expense> draftExpenses = expenseRepository.findDraftExpensesByContractReference(contractReference);
        Map<String, CheckResult> map = new HashMap<>();
        CheckResult sdMel01 = CheckResult.builder()
                .status(CheckStatus.FAILED)
                .message("Expenses not found")
                .type(CheckType.FINANCIAL_INFORMATION)
                .build();
        CheckResult sdMel02 = CheckResult.builder()
                .status(CheckStatus.FAILED)
                .message("Expense items not found")
                .type(CheckType.FINANCIAL_INFORMATION)
                .build();
        if (draftExpenses.isEmpty()) {
            map.put("sd_mel_05", sdMel01);
            map.put("sd_mel_06", sdMel02);
            PageableHashMap<String, CheckResult> financialCheck = new PageableHashMap<String, CheckResult>(map);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(SingleResultDto.<PageableHashMap<String, CheckResult>>builder()
                            .data(financialCheck)
                            .build());
        }
        for (Expense expense : draftExpenses) {
            sdMel01 = expenseService.checkAllExpenseItemAmount(expense.getId());
            sdMel02 = expenseService.checkSumExpenseItemAmount(expense.getId());
            if (sdMel01.getStatus() == CheckStatus.FAILED || sdMel02.getStatus() == CheckStatus.FAILED) {
                break;
            }
        }
        map.put("sd_mel_01", sdMel01);
        map.put("sd_mel_02", sdMel02);
        PageableHashMap<String, CheckResult> financialCheck = new PageableHashMap<String, CheckResult>(map);

        return ResponseEntity.status(HttpStatus.OK)
                .body(SingleResultDto.<PageableHashMap<String, CheckResult>>builder()
                        .data(financialCheck)
                        .build());
    }
}
